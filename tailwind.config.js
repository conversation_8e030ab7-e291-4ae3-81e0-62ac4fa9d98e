/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-07-17 15:34:35
 */
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      boxShadow: {
        lesson: "0 2px 4px #0000000d",
      },
      fontSize: {
        // 自定义字体大小，使用 CSS 变量
        h1: ["var(--h1-font-size)", { lineHeight: "var(--h1-line-height)" }],
        h2: ["var(--h2-font-size)", { lineHeight: "var(--h2-line-height)" }],
        h3: ["var(--h3-font-size)", { lineHeight: "var(--h3-line-height)" }],
        h4: ["var(--h4-font-size)", { lineHeight: "var(--h4-line-height)" }],
        h5: ["var(--h5-font-size)", { lineHeight: "var(--h5-line-height)" }],
        h6: ["var(--h6-font-size)", { lineHeight: "var(--h6-line-height)" }],
        "body-large": [
          "var(--body-large-font-size)",
          { lineHeight: "var(--body-large-line-height)" },
        ],
        "paragraph-code": [
          "var(--paragraph-code-font-size)",
          { lineHeight: "var(--paragraph-code-line-height)" },
        ],
        "lesson-markdown": [
          "var(--font-size-lesson-markdown)",
          { lineHeight: "var(--line-height-lesson-markdown)" },
        ],
      },
      fontFamily: {
        // 自定义字体族
        custom: [
          '"Helvetica Neue"',
          "SF Pro Display",
          "Arial",
          "Roboto",
          "system-ui",
          "sans-serif",
        ],
        sans: [
          '"Helvetica Neue"',
          "SF Pro Display",
          "Arial",
          "Roboto",
          "system-ui",
          "sans-serif",
        ],
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("tailwind-scrollbar")],
};
