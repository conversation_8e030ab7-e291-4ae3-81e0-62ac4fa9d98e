#!/bin/bash
set -e  # 如果有任何命令失败，则脚本退出

# Docker 镜像标签配置 - 只需要在这里修改一次
IMAGE_NAME="luxurydata_web"
IMAGE_TAG="v1.2-beta"
FULL_TAG="${IMAGE_NAME}:${IMAGE_TAG}"

# 保存当前路径
WORK_DIR=$(pwd)

# 预先提示 sudo 密码（只输入一次）
if ! docker info > /dev/null 2>&1; then
  echo "🔐 Docker 需要 sudo 权限，请输入密码..."
  sudo -v
fi

# 判断是否需要 sudo 执行 docker
DOCKER_CMD="docker"
if ! docker info > /dev/null 2>&1; then
  DOCKER_CMD="sudo docker"
fi

echo "🐳 Building Docker image in $(pwd)..."
$DOCKER_CMD build -t "$FULL_TAG" .

# Step 5: 回到原始工作目录
cd "$WORK_DIR"

# Step 6: 保存镜像为压缩包
OUTPUT_PATH="$HOME/docker_images/${IMAGE_NAME}_${IMAGE_TAG}.tar.gz"
echo "📤 Saving Docker image to $OUTPUT_PATH ..."
mkdir -p "$(dirname "$OUTPUT_PATH")"
$DOCKER_CMD save "$FULL_TAG" | gzip | sudo tee "$OUTPUT_PATH" > /dev/null

echo "✅ Done. Docker image '$FULL_TAG' built and saved to $OUTPUT_PATH"
