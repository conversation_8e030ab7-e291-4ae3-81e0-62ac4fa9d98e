<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" id="Layer_1" version="1.1" viewBox="0 0 639.1 492.4">
  <defs>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient" x1="4.5" x2="633.5" y1="105" y2="105">
      <stop offset="0" stop-color="#f5f5ff"/>
      <stop offset=".2" stop-color="#d6d6ff"/>
      <stop offset=".4" stop-color="#f1f1ff"/>
      <stop offset=".6" stop-color="#e7e7ff"/>
      <stop offset=".8" stop-color="#bfbeff"/>
      <stop offset="1" stop-color="#aaa9ff"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient1" x1="349.9" x2="53.6" y1="115.6" y2="-55.1">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient2" x1="317.6" x2="648.5" y1="162.7" y2="-27.9">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient3" x1="115" x2="115.1" y1="51.6" y2="51.6">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient4" x1="115" x2="115.1" y1="58.4" y2="58.4">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient5" x1="251.9" x2="416.7" y1="142.9" y2="142.9">
      <stop offset="0" stop-color="#f5f5ff"/>
      <stop offset=".3" stop-color="#f1f1ff"/>
      <stop offset=".5" stop-color="#e7e7ff"/>
      <stop offset=".7" stop-color="#d6d6ff"/>
      <stop offset=".9" stop-color="#bfbeff"/>
      <stop offset="1" stop-color="#aaa9ff"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient6" x1="256.5" x2="411.9" y1="95.5" y2="95.5">
      <stop offset="0" stop-color="#f5f5ff"/>
      <stop offset=".3" stop-color="#f1f1ff"/>
      <stop offset=".5" stop-color="#e7e7ff"/>
      <stop offset=".7" stop-color="#d6d6ff"/>
      <stop offset=".9" stop-color="#bfbeff"/>
      <stop offset="1" stop-color="#aaa9ff"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient7" x1="37.7" x2="94.8" y1="200" y2="167.1">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient8" x1="43.6" x2="48" y1="198.3" y2="195.8">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient9" x1="36.7" x2="41.1" y1="186.4" y2="183.8">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient10" x1="51.5" x2="85.6" y1="193.7" y2="174">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient11" x1="44.6" x2="78.7" y1="181.8" y2="162.1">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient12" x1="54.5" x2="97.8" y1="259.6" y2="234.7">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient13" x1="60.9" x2="104.2" y1="274.5" y2="249.5">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient14" x1="54.9" x2="92.2" y1="257.1" y2="235.6">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient15" x1="609.2" x2="556.2" y1="190.4" y2="159.8">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient16" x1="601.3" x2="548.3" y1="208.6" y2="178.1">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient17" x1="608.1" x2="562.5" y1="187" y2="160.8">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient18" x1="333.4" x2="343.1" y1="-69.6" y2="-75.2">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient19" x1="628.5" x2="638.2" y1="-61.4" y2="-67">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient20" x1="285.9" x2="292.4" y1="135.2" y2="131.5">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient21" x1="115" x2="115.9" y1="195" y2="195">
      <stop offset="0" stop-color="#f5f5ff"/>
      <stop offset=".3" stop-color="#f1f1ff"/>
      <stop offset=".5" stop-color="#e7e7ff"/>
      <stop offset=".7" stop-color="#d6d6ff"/>
      <stop offset=".9" stop-color="#bfbeff"/>
      <stop offset="1" stop-color="#aaa9ff"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient22" x1="256.5" x2="411.9" y1="147.6" y2="147.6">
      <stop offset="0" stop-color="#f5f5ff"/>
      <stop offset=".3" stop-color="#f1f1ff"/>
      <stop offset=".5" stop-color="#e7e7ff"/>
      <stop offset=".7" stop-color="#d6d6ff"/>
      <stop offset=".9" stop-color="#bfbeff"/>
      <stop offset="1" stop-color="#aaa9ff"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient23" x1="115" x2="115.9" y1="251.2" y2="251.2">
      <stop offset="0" stop-color="#f5f5ff"/>
      <stop offset=".3" stop-color="#f1f1ff"/>
      <stop offset=".5" stop-color="#e7e7ff"/>
      <stop offset=".7" stop-color="#d6d6ff"/>
      <stop offset=".9" stop-color="#bfbeff"/>
      <stop offset="1" stop-color="#aaa9ff"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient24" x1="256.5" x2="411.9" y1="203.8" y2="203.8">
      <stop offset="0" stop-color="#f5f5ff"/>
      <stop offset=".3" stop-color="#f1f1ff"/>
      <stop offset=".5" stop-color="#e7e7ff"/>
      <stop offset=".7" stop-color="#d6d6ff"/>
      <stop offset=".9" stop-color="#bfbeff"/>
      <stop offset="1" stop-color="#aaa9ff"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient25" x1="202.1" x2="216.3" y1="274.9" y2="274.9">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient26" x1="127.5" x2="286.5" y1="312.5" y2="312.5">
      <stop offset="0" stop-color="#f5f5ff"/>
      <stop offset=".3" stop-color="#f1f1ff"/>
      <stop offset=".5" stop-color="#e7e7ff"/>
      <stop offset=".7" stop-color="#d6d6ff"/>
      <stop offset=".9" stop-color="#bfbeff"/>
      <stop offset="1" stop-color="#aaa9ff"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient27" x1="448" x2="483.4" y1="184.9" y2="184.9">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient28" x1="448" x2="506.9" y1="193.5" y2="193.5">
      <stop offset="0" stop-color="#f5f5ff"/>
      <stop offset=".3" stop-color="#f1f1ff"/>
      <stop offset=".5" stop-color="#e7e7ff"/>
      <stop offset=".7" stop-color="#d6d6ff"/>
      <stop offset=".9" stop-color="#bfbeff"/>
      <stop offset="1" stop-color="#aaa9ff"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient29" x1="483.4" x2="506.9" y1="181.5" y2="181.5">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <linearGradient gradientTransform="matrix(1 0 0 -1 0 401.9)" gradientUnits="userSpaceOnUse" id="linear-gradient30" x1="425.3" x2="524" y1="237.4" y2="237.4">
      <stop offset="0" stop-color="#7775ff"/>
      <stop offset=".3" stop-color="#6f6df8"/>
      <stop offset=".7" stop-color="#5c59e7"/>
      <stop offset="1" stop-color="#4744d4"/>
    </linearGradient>
    <clipPath id="clippath">
      <path class="st0" d="M4.5 114.9h629v364H4.5z"/>
    </clipPath>
    <clipPath id="clippath-1">
      <path class="st0" d="M115 12h409v376H115z"/>
    </clipPath>
    <style>
      .st0 {
        fill: none;
      }
      .st13 {
        fill-rule: evenodd;
      }
      .st18 {
        fill: #facc15;
      }
      .st13,
      .st33 {
        fill: #aaa9ff;
      }
      .st35 {
        fill: #fcfcfd;
      }
      .st37 {
        fill: #4744d4;
      }
      .st39 {
        fill: #01e6f7;
      }
    </style>
  </defs>
  <g style="clip-path: url(#clippath)">
    <path d="M633.5 297.2 318.1 114.9c0 .1 0 0 0 0L4.5 296.1l.2.3-.2.3L319.9 479c0-.1 0 0 0 0l313.4-181.1-.2-.3.2-.3ZM319.7 271l43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm43.4 26-43.9 25.4-44.2-25.5 43.9-25.4 44.2 25.5Zm-44.2-26.5L274.7 245l43.9-25.4 44.2 25.5-43.9 25.4Zm-.8.5-43.9 25.4-44.2-25.5 43.9-25.4 44.2 25.5Zm-44.8 25.9-43.9 25.4-44.2-25.5 43.9-25.4 44.2 25.5Zm.9.5 44.2 25.5-43.9 25.4-44.2-25.5 43.9-25.4Zm45 26 44.2 25.5-43.9 25.4-44.2-25.5 43.9-25.4Zm.8-.5 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm44.8-25.9 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm44.7-25.8 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm-.8-.5-44.2-25.5 43.9-25.4 44.2 25.5-43.9 25.4Zm-45.1-26-44.2-25.5 43.9-25.4 44.2 25.5-43.9 25.4Zm-45-26-44.2-25.5 43.9-25.4 44.2 25.5-43.9 25.4Zm-.8.4-43.9 25.4-44.2-25.5 43.9-25.4 44.2 25.5ZM273 245l-43.9 25.4-44.2-25.5 43.9-25.4L273 245Zm-44.7 25.8-43.9 25.4-44.2-25.5 43.9-25.4 44.2 25.5Zm-44.7 25.9-43.9 25.4-44.2-25.5 43.9-25.4 44.2 25.5Zm.8.5 44.2 25.5-43.9 25.4-44.2-25.5 43.9-25.4Zm45 26 44.2 25.5-43.9 25.4-44.2-25.5 43.9-25.4Zm45 26 44.2 25.5-43.9 25.4-44.2-25.5 43.9-25.4Zm45.1 26 44.2 25.5-43.9 25.4-44.2-25.5 43.9-25.4Zm.8-.4 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm44.7-25.9 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm44.8-25.9 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm44.7-25.8 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm44.8-25.9 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm-.9-.5-44.2-25.5 43.9-25.4 44.2 25.5-43.9 25.4Zm-45-26-44.2-25.5 43.9-25.4 44.2 25.5-43.9 25.4Zm-45-26-44.2-25.5 43.9-25.4 44.2 25.5-43.9 25.4Zm-45-26-44.2-25.5 43.9-25.4 44.2 25.5-43.9 25.4Zm-45.1-26-44.2-25.5 43.9-25.4 44.2 25.5-43.9 25.4Zm-.8.5-43.9 25.4-44.2-25.5 43.9-25.4 44.2 25.5Zm-44.7 25.8-43.9 25.4-44.2-25.5 43.9-25.4 44.2 25.5ZM228 219l-43.9 25.4-44.2-25.5 43.9-25.4L228 219Zm-44.7 25.8-43.9 25.4-44.2-25.5 43.9-25.4 44.2 25.5Zm-44.8 25.9-43.9 25.4-44.2-25.5 43.9-25.4 44.2 25.5Zm-44.7 25.8-43.9 25.4-44.2-25.5L49.6 271l44.2 25.5Zm.8.5 44.2 25.5-43.9 25.4-44.2-25.5L94.6 297Zm45.1 26 44.2 25.5-43.9 25.4-44.2-25.5 43.9-25.4Zm45 26.1 44.2 25.5L185 400l-44.2-25.5 43.9-25.4Zm45 26 44.2 25.5L230 426l-44.2-25.5 43.9-25.4Zm45 26 44.2 25.5L275 452l-44.2-25.5 43.9-25.4Zm45 26 44.2 25.5L320 478l-44.2-25.5 43.9-25.4Zm.9-.5 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm44.7-25.8 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm44.8-25.9 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm44.7-25.8 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm44.7-25.9 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Zm44.8-25.8 43.9-25.4 44.2 25.5-43.9 25.4-44.2-25.5Z" style="fill: url(#linear-gradient)"/>
  </g>
  <g style="clip-path: url(#clippath-1)">
    <path d="M476.5 213.1 180.6 384.4" style="stroke: url(#linear-gradient1); stroke-miterlimit: 10; fill: none"/>
    <path d="M476.5 378.5 209.2 223.8v-94.9" style="stroke: url(#linear-gradient2); stroke-miterlimit: 10; fill: none"/>
    <path d="M181.5 315.4v69.9" style="stroke: url(#linear-gradient3); stroke-miterlimit: 10; fill: none"/>
    <path d="M476.5 308.5v69.9" style="stroke: url(#linear-gradient4); stroke-miterlimit: 10; fill: none"/>
    <path class="st39" d="M310.5 232.8c.2 0 .5.2.7.3-.2 0-.3-.1-.5-.2h-.2ZM319.7 235.5c-.2 0-.4 0-.5-.1.2 0 .4.1.7.1h-.2ZM323.6 258.8c1.3.2 2.5.3 3.8.4-1.3-.1-2.6-.2-3.8-.4ZM323.1 236.1c.2 0 .4 0 .7.1h-.7ZM335.5 236.9h-.4.9-.5ZM338.7 236.7c-.6 0-1.1 0-1.7.1.6 0 1.2 0 1.7-.1ZM340.5 236.5h-.7.8ZM345 235.8h-.3c.2 0 .4 0 .7-.1h-.4ZM343.7 236.1c.3 0 .7-.1 1-.2-.3 0-.7.1-1 .2ZM350.6 257.1c.6-.2 1.2-.3 1.7-.5-.6.2-1.2.4-1.7.5ZM371.4 242.2c.1-.3.2-.6.3-.8-.1.3-.2.6-.3.8ZM319.2 255.5c.1 0 .3.1.4.2-.1 0-.2 0-.3-.1h-.1ZM324.8 257.1h-.3.4ZM326.9 257.5h.4-.3ZM327.8 257.6h-.5.5ZM334.5 257.9h-.3.6-.3ZM336.5 257.8h-1.1 1.1ZM337.6 257.7h-.4.5ZM340.3 257.3h-.2.4-.2ZM339.6 257.5c.2 0 .4 0 .6-.1-.2 0-.4 0-.6.1ZM356.6 261.2c0-.2.2-.3.2-.5 0 .2-.1.3-.2.5ZM353.6 265.2ZM329.6 257.8h-.6.6Z"/>
    <path d="M411.9 252.3c6.4 3.7 6.4 9.7 0 13.4L346.1 304c-6.4 3.7-16.8 3.7-23.2 0l-66.2-38.3c-6.4-3.7-6.4-9.7 0-13.5l65.8-38.3c6.4-3.7 16.8-3.7 23.2 0l66.2 38.3Z" style="fill: url(#linear-gradient5)"/>
    <path class="st35" d="M416.7 259.2v38.9c-.1.1-.1.2-.2.4v1.2c0 .1 0 .2-.1.3 0 .2-.1.3-.2.5 0 0 0 .1-.1.2 0 0 0 .2-.1.2-.1.2-.3.4-.4.7 0 .1-.2.2-.3.3-.3.3-.5.6-.9.9 0 0-.1.1-.2.1-.1.1-.3.2-.4.3-.1.1-.3.2-.4.3 0 0-.1 0-.2.1-.1 0-.2.2-.3.2-.2.1-.3.2-.5.3 0 0-.1 0-.2.1 0 0-.1 0-.2.1l-65.8 38.3c-.3.2-.5.3-.8.4h-.2c-.2.1-.5.2-.7.3h-.2c-.3.1-.6.2-.9.4-.3.1-.6.2-1 .3-.2 0-.5.2-.7.2-.2 0-.3.1-.5.1s-.3 0-.5.1c-.2 0-.4.1-.7.1-.2 0-.3 0-.5.1h-.2c-.2 0-.5 0-.7.1h-.4c-.3 0-.7 0-1.1.1H331.2c-.3 0-.5 0-.8-.1h-.7c-.3 0-.6-.1-.9-.2h-.2c-.2 0-.5-.1-.7-.2-.3 0-.5-.1-.8-.2-.3 0-.6-.2-1-.3-.3 0-.5-.2-.8-.3h-.2c-.4-.2-.8-.3-1.2-.5-.4-.2-.8-.4-1.1-.6l-66.2-38.3c-3.2-1.9-4.8-4.3-4.8-6.8v-39c.1 2.5 1.7 4.9 5 6.8l66.2 38.3.6.3 1.2.6c.2 0 .4.2.6.3h.1l.9.3c.3.1.7.2 1 .3.2 0 .5.1.7.2.3 0 .6.2 1 .2.5.1 1.1.2 1.6.3.3 0 .5 0 .8.1h7.1c.3 0 .6-.1 1-.2h.2c.2 0 .3 0 .5-.1h.2c.2 0 .3 0 .5-.1.2 0 .4-.1.6-.2.2 0 .4-.1.5-.2.2 0 .4-.1.5-.2l.9-.3c.3-.1.6-.2.9-.4h.2c.2-.1.5-.2.7-.3h.2c.3-.1.5-.3.8-.4l65.8-38.3s.1 0 .2-.1c.2-.1.4-.2.6-.4.2-.1.4-.3.5-.4.2-.1.3-.3.5-.4.1-.1.3-.2.4-.4.1 0 .2-.2.3-.3l.4-.4c.1-.1.2-.3.4-.4.1-.1.2-.2.3-.4.1-.1.2-.3.3-.4 0 0 0-.1.1-.2v-.2c0-.1.1-.3.2-.4 0 0 0-.2.1-.2V258.8Z"/>
    <path d="m411.9 285.8-65.8 38.3c-6.4 3.7-16.8 3.7-23.2 0l-66.2-38.3" style="stroke: url(#linear-gradient6); stroke-miterlimit: 10; fill: none"/>
    <path class="st37" d="M369.1 304.3v12.4c0 1.6-1 3.4-2.2 4.1l-11.5 6.7c-1.1.6-2 0-2.1-1.3V313.5c0-1.6 1-3.4 2.1-4.1l10.5-6.1 1-.6c1.2-.7 2.2 0 2.2 1.6Z"/>
    <path class="st33" d="M366 303.5v12.4c0 1.6-1 3.4-2.1 4.1l-10.5 6.1V313.4c0-1.6 1-3.4 2.1-4.1l10.5-6.1v.3Z"/>
    <path class="st18" d="M275.7 296.3c0 2.5-1.8 3.5-3.9 2.2s-3.9-4.3-3.9-6.8 1.8-3.5 3.9-2.2c2.2 1.3 3.9 4.3 3.9 6.8Z"/>
    <path class="st39" d="M340.3 271.2ZM319.3 269.3ZM372.7 237v-.3.3ZM366.7 248.7ZM361.7 229.8c.1 0 .2-.1.4-.2-.1 0-.2.2-.4.2ZM344.9 258.4h.1-.1ZM327.5 236.7h.5-.5ZM327.5 236.7h-1 1ZM310.6 255.4h-.1.1ZM350.6 222.7c.6-.2 1.2-.3 1.7-.5-.6.2-1.2.4-1.7.5ZM371.4 207.8c.1-.3.2-.6.3-.8-.1.3-.2.6-.3.8ZM372.7 202.7v-.3.3ZM366.7 214.3ZM361.7 195.5c.1 0 .2-.1.4-.2-.1 0-.2.2-.4.2ZM344.9 224h.1-.1ZM310.6 221.1h-.1.1ZM371.4 173.5c.1-.3.2-.6.3-.8-.1.3-.2.6-.3.8ZM372.7 168.3v-.3.3ZM366.7 179.9ZM361.7 161.1c.1 0 .2-.1.4-.2-.1 0-.2.2-.4.2Z"/>
    <path d="M212.2 277.9v49.2c0 4.5-3.2 6.4-7.1 4.1l-42.8-24.8c-3.9-2.3-7.1-7.8-7.1-12.3v-49.2c0-4.5 3.2-6.4 7.1-4.1l42.8 24.8c3.9 2.3 7.1 7.8 7.1 12.3Z" style="fill: url(#linear-gradient7)"/>
    <path class="st18" d="M210 279.6v4.2l-57.1-33v-4.2c0-4.5 3.2-6.4 7.1-4.1l42.8 24.8c3.9 2.3 7.1 7.8 7.1 12.3Z"/>
    <path class="st35" d="M210 283.8v45c0 4.5-3.2 6.4-7.1 4.1l-42.8-24.8c-3.9-2.3-7.1-7.8-7.1-12.3v-45l57.1 33Z"/>
    <path d="M162.5 269.6c1.2.7 2.2 2.4 2.2 3.8s-1 2-2.2 1.3c-1.2-.7-2.2-2.4-2.2-3.8s1-2 2.2-1.3Z" style="fill: url(#linear-gradient8)"/>
    <path d="M162.5 285.4c1.2.7 2.2 2.4 2.2 3.8s-1 2-2.2 1.3c-1.2-.7-2.2-2.4-2.2-3.8s1-2 2.2-1.3Z" style="fill: url(#linear-gradient9)"/>
    <path d="m168.2 275.4 34.1 19.7" style="stroke: url(#linear-gradient10); stroke-miterlimit: 10; fill: none"/>
    <path d="m168.2 291.2 34.1 19.7" style="stroke: url(#linear-gradient11); stroke-miterlimit: 10; fill: none"/>
    <path d="M185.2 199v37.3c0 3.4-2.4 4.8-5.4 3.1l-32.4-18.8c-3-1.7-5.4-5.9-5.4-9.3V174c0-3.4 2.4-4.8 5.4-3.1l32.4 18.8c3 1.7 5.4 5.9 5.4 9.3Z" style="fill: url(#linear-gradient12)"/>
    <path d="M183.6 199.7v3.2l-43.2-25v-3.2c0-3.4 2.4-4.8 5.4-3.1l32.4 18.8c3 1.7 5.4 5.9 5.4 9.3Z" style="fill: url(#linear-gradient13)"/>
    <path class="st35" d="M183.6 202.9V237c0 3.4-2.4 4.8-5.4 3.1l-32.4-18.8c-3-1.7-5.4-5.9-5.4-9.4v-34.1l43.2 25Z"/>
    <path d="m143.6 207.2 9.8-5.7 1.8 8.1 10.5-3.4.9 14 14-14.3" style="stroke: url(#linear-gradient14);&#xA;        stroke-linecap: round;&#xA;        stroke-linejoin: round;&#xA;        fill: none;"/>
    <path d="m455.7 269.6 39.7-23c3.7-2.1 6.6-.4 6.6 3.8v45.7c0 4.2-3 9.3-6.6 11.5l-39.7 23c-3.7 2.1-6.6.4-6.6-3.8v-45.7c0-4.2 3-9.3 6.6-11.4Z" style="fill: url(#linear-gradient15)"/>
    <path d="M451 281.8c0-4.2 3-9.3 6.6-11.4l39.7-23c3.7-2.1 6.6-.4 6.6 3.8v3.9l-53 30.7v-3.9Z" style="fill: url(#linear-gradient16)"/>
    <path class="st35" d="M504 255.1v41.8c0 4.2-3 9.3-6.6 11.5l-39.7 23c-3.7 2.1-6.6.4-6.6-3.8v-41.8l53-30.7Z"/>
    <path d="m454.9 289.3 17.1 17.6 1.1-17.2 12.9 4.2 2.2-9.9 11.9 7" style="stroke-linecap: round;&#xA;        stroke-linejoin: round;&#xA;        stroke: url(#linear-gradient17);&#xA;        fill: none;"/>
    <path d="M186.3 386.8c-2.7 1.5-7 1.5-9.6 0-2.7-1.5-2.7-4 0-5.6 2.7-1.5 7-1.5 9.6 0 2.7 1.5 2.7 4 0 5.6Z" style="fill: url(#linear-gradient18)"/>
    <path d="M481.3 378.7c-2.7 1.5-7 1.5-9.6 0-2.7-1.5-2.7-4 0-5.6 2.7-1.5 7-1.5 9.6 0 2.7 1.5 2.7 4 0 5.6Z" style="fill: url(#linear-gradient19)"/>
    <path d="M212.4 224.3c-1.8 1-4.7 1-6.4 0-1.8-1-1.8-2.7 0-3.7s4.7-1 6.4 0c1.8 1 1.8 2.7 0 3.7Z" style="fill: url(#linear-gradient20)"/>
    <path d="M411.9 200.2c6.4 3.7 6.4 9.7 0 13.4l-65.8 38.3c-6.4 3.7-16.8 3.7-23.2 0l-66.2-38.3c-6.4-3.7-6.4-9.7 0-13.4l65.8-38.3c6.4-3.7 16.8-3.7 23.2 0l66.2 38.3Z" style="fill: url(#linear-gradient21)"/>
    <path class="st35" d="M416.7 207.1V246c-.1.1-.1.2-.2.4v1.2c0 .1 0 .2-.1.3 0 .2-.1.3-.2.5 0 0 0 .1-.1.2 0 0 0 .2-.1.2-.1.2-.3.4-.4.7 0 .1-.2.2-.3.3-.3.3-.5.6-.9.9 0 0-.1.1-.2.1-.1.1-.3.2-.4.3-.1.1-.3.2-.4.3 0 0-.1 0-.2.1-.1 0-.2.2-.3.2-.2.1-.3.2-.5.3 0 0-.1 0-.2.1 0 0-.1 0-.2.1l-65.8 38.3c-.3.2-.5.3-.8.4h-.2c-.2.1-.5.2-.7.3h-.2c-.3.1-.6.2-.9.4-.3.1-.6.2-1 .3-.2 0-.5.2-.7.2-.2 0-.3.1-.5.1s-.3 0-.5.1c-.2 0-.4.1-.7.1-.2 0-.3 0-.5.1h-.2c-.2 0-.5 0-.7.1h-.4c-.3 0-.7 0-1.1.1H331.2c-.3 0-.5 0-.8-.1h-.7c-.3 0-.6-.1-.9-.2h-.2c-.2 0-.5-.1-.7-.2-.3 0-.5-.1-.8-.2-.3 0-.6-.2-1-.3-.3 0-.5-.2-.8-.3h-.2c-.4-.2-.8-.3-1.2-.5-.4-.2-.8-.4-1.1-.6L256.6 252c-3.2-1.9-4.8-4.3-4.8-6.8v-39c.1 2.5 1.7 4.9 5 6.8l66.2 38.3.6.3 1.2.6c.2 0 .4.2.6.3h.1l.9.3c.3.1.7.2 1 .3.2 0 .5.1.7.2.3 0 .6.2 1 .2.5.1 1.1.2 1.6.3.3 0 .5 0 .8.1h7.1c.3 0 .6-.1 1-.2h.2c.2 0 .3 0 .5-.1h.2c.2 0 .3 0 .5-.1.2 0 .4-.1.6-.2.2 0 .4-.1.5-.2.2 0 .4-.1.5-.2l.9-.3c.3-.1.6-.2.9-.4h.2c.2-.1.5-.2.7-.3h.2c.3-.1.5-.3.8-.4l65.8-38.3s.1 0 .2-.1c.2-.1.4-.2.6-.4.2-.1.4-.3.5-.4.2-.1.3-.3.5-.4.1-.1.3-.2.4-.4.1 0 .2-.2.3-.3l.4-.4c.1-.1.2-.3.4-.4.1-.1.2-.2.3-.4.1-.1.2-.3.3-.4 0 0 0-.1.1-.2v-.2c0-.1.1-.3.2-.4 0 0 0-.2.1-.2V206.7Z"/>
    <path d="M411.9 233.7 346.1 272c-6.4 3.7-16.8 3.7-23.2 0l-66.2-38.3" style="stroke: url(#linear-gradient22); stroke-miterlimit: 10; fill: none"/>
    <path class="st37" d="M369.1 252.2v12.4c0 1.6-1 3.4-2.2 4.1l-11.5 6.7c-1.1.6-2 0-2.1-1.3V261.4c0-1.6 1-3.4 2.1-4.1l10.5-6.1 1-.6c1.2-.7 2.2 0 2.2 1.6Z"/>
    <path class="st33" d="M366 251.4v12.4c0 1.6-1 3.4-2.1 4.1l-10.5 6.1V261.3c0-1.6 1-3.4 2.1-4.1l10.5-6.1v.3Z"/>
    <path class="st18" d="M275.7 244.2c0 2.5-1.8 3.5-3.9 2.2-2.2-1.3-3.9-4.3-3.9-6.8s1.8-3.5 3.9-2.2c2.2 1.3 3.9 4.3 3.9 6.8Z"/>
    <path d="M411.9 144c6.4 3.7 6.4 9.7 0 13.4l-65.8 38.3c-6.4 3.7-16.8 3.7-23.2 0l-66.2-38.3c-6.4-3.7-6.4-9.7 0-13.5l65.8-38.3c6.4-3.7 16.8-3.7 23.2 0l66.2 38.3Z" style="fill: url(#linear-gradient23)"/>
    <path class="st37" d="M397.9 154.1c-.6-.7-1.4-1.3-2.4-1.9l-52.3-30.3c-5.1-2.9-13.2-2.9-18.3 0L273 152.1c-1 .6-1.7 1.2-2.3 1.9-2.5-2.8-1.7-6.3 2.3-8.7l51.9-30.2c5-2.9 13.2-2.9 18.3 0l52.3 30.2c4.1 2.4 4.9 5.9 2.4 8.8Z"/>
    <path class="st33" d="M397.9 154.1c-.6.7-1.4 1.3-2.3 1.9l-51.9 30.2c-5 2.9-13.2 2.9-18.3 0L273.1 156c-1-.6-1.8-1.2-2.4-1.9.6-.7 1.4-1.3 2.3-1.9l51.9-30.2c5-2.9 13.2-2.9 18.3 0l52.3 30.2c1 .6 1.8 1.2 2.4 1.9Z"/>
    <path class="st35" d="M397.9 154.1c-.6.7-1.4 1.3-2.3 1.9l-51.9 30.2c-5 2.9-13.2 2.9-18.3 0L273.1 156c-1-.6-1.8-1.2-2.4-1.9.6-.7 1.4-1.3 2.3-1.9l51.9-30.2c5-2.9 13.2-2.9 18.3 0l52.3 30.2c1 .6 1.8 1.2 2.4 1.9Z"/>
    <path class="st35" d="M416.7 150.9v38.9c-.1.1-.1.2-.2.4v1.2c0 .1 0 .2-.1.3 0 .2-.1.3-.2.5 0 0 0 .1-.1.2 0 0 0 .2-.1.2-.1.2-.3.4-.4.7 0 .1-.2.2-.3.3-.3.3-.5.6-.9.9 0 0-.1.1-.2.1-.1.1-.3.2-.4.3-.1.1-.3.2-.4.3 0 0-.1 0-.2.1-.1 0-.2.2-.3.2-.2.1-.3.2-.5.3 0 0-.1 0-.2.1 0 0-.1 0-.2.1l-65.8 38.3c-.3.2-.5.3-.8.4h-.2c-.2.1-.5.2-.7.3h-.2c-.3.1-.6.2-.9.4-.3.1-.6.2-1 .3-.2 0-.5.2-.7.2-.2 0-.3.1-.5.1s-.3 0-.5.1c-.2 0-.4.1-.7.1-.2 0-.3 0-.5.1h-.2c-.2 0-.5 0-.7.1h-.4c-.3 0-.7 0-1.1.1H331.2c-.3 0-.5 0-.8-.1h-.7c-.3 0-.6-.1-.9-.2h-.2c-.2 0-.5-.1-.7-.2-.3 0-.5-.1-.8-.2-.3 0-.6-.2-1-.3-.3 0-.5-.2-.8-.3h-.2c-.4-.2-.8-.3-1.2-.5-.4-.2-.8-.4-1.1-.6l-66.2-38.3c-3.2-1.9-4.8-4.3-4.8-6.8v-39c.1 2.5 1.7 4.9 5 6.8l66.2 38.3.6.3 1.2.6c.2 0 .4.2.6.3h.1l.9.3c.3.1.7.2 1 .3.2 0 .5.1.7.2.3 0 .6.2 1 .2.5.1 1.1.2 1.6.3.3 0 .5 0 .8.1h7.1c.3 0 .6-.1 1-.2h.2c.2 0 .3 0 .5-.1h.2c.2 0 .3 0 .5-.1.2 0 .4-.1.6-.2.2 0 .4-.1.5-.2.2 0 .4-.1.5-.2l.9-.3c.3-.1.6-.2.9-.4h.2c.2-.1.5-.2.7-.3h.2c.3-.1.5-.3.8-.4l65.8-38.3s.1 0 .2-.1c.2-.1.4-.2.6-.4.2-.1.4-.3.5-.4.2-.1.3-.3.5-.4.1-.1.3-.2.4-.4.1 0 .2-.2.3-.3l.4-.4c.1-.1.2-.3.4-.4.1-.1.2-.2.3-.4.1-.1.2-.3.3-.4 0 0 0-.1.1-.2v-.2c0-.1.1-.3.2-.4 0 0 0-.2.1-.2V150.5Z"/>
    <path d="m411.9 177.5-65.8 38.3c-6.4 3.7-16.8 3.7-23.2 0l-66.2-38.3" style="stroke-miterlimit: 10; stroke: url(#linear-gradient24); fill: none"/>
    <path class="st37" d="M369.1 196v12.4c0 1.6-1 3.4-2.2 4.1l-11.5 6.7c-1.1.6-2 0-2.1-1.3V205.2c0-1.6 1-3.4 2.1-4.1l10.5-6.1 1-.6c1.2-.7 2.2 0 2.2 1.6Z"/>
    <path class="st33" d="M366 195.2v12.4c0 1.6-1 3.4-2.1 4.1l-10.5 6.1V205.1c0-1.6 1-3.4 2.1-4.1l10.5-6.1v.3Z"/>
    <path class="st18" d="M275.7 188c0 2.5-1.8 3.5-3.9 2.2-2.2-1.3-3.9-4.3-3.9-6.8s1.8-3.5 3.9-2.2c2.2 1.3 3.9 4.3 3.9 6.8Z"/>
    <path class="st13" d="m213.4 137.5-8.4 1.4v13c0 .6.4 1.2 1.2 1.7 1.6.9 4.3.9 5.9 0 .8-.5 1.2-1.1 1.2-1.7v-14.4Z"/>
    <path d="m216.3 112.8-14.1 8.1v16.2c0 1 .7 2.1 2.1 2.9 2.8 1.6 7.2 1.6 10 0 1.4-.8 2.1-1.8 2.1-2.9v-24.3Z" style="fill-rule: evenodd; fill: url(#linear-gradient25)"/>
    <path class="st13" d="m278.8 31.4-12.4-7.2c-4.8-2.8-11.5-2.4-18.8 1.8l-3.8 2.2c0-1.9-.2-3.6-.5-5.2l6.4-2.4-12.4-7.2c-4.1-2.4-9.9-2.1-16.2 1.5-2.7 1.6-5.4 3.7-7.9 6.2l-7.9-4.5c-7.7-4.6-18.3-3.8-29.7 2.7-23 13.3-41.7 45.7-41.7 72.3v1.3c-4.2 3.9-8.1 8.7-11.2 14.2-5.1 8.7-7.8 18-7.8 26.2s2.9 14.3 7.7 17.1h.1l12.5 7.3-1.9-5.9c2.6-.5 5.3-1.6 8.1-3.2l106-61.4c7.1-4.1 13.8-11.2 18.7-19.8 5-8.7 7.8-18 7.8-26.2s-.4-5.6-1-7.9l5.7-2.1Z"/>
    <path d="m260 33.2-3.8 2.2c0-14.5-10.2-20.4-22.7-13.2-3.2 1.9-6.4 4.5-9.3 7.7-7.4-10.6-21.1-12-36.1-3.3-23 13.3-41.7 45.7-41.7 72.3v1.3c-4.2 3.9-8.1 8.7-11.2 14.2-5 8.7-7.8 18-7.8 26.2 0 16.9 11.9 23.8 26.5 15.3l106-61.4c7.1-4.1 13.8-11.2 18.7-19.8 5-8.7 7.8-18 7.8-26.2 0-16.9-11.9-23.8-26.5-15.3" style="fill: url(#linear-gradient26)"/>
    <path class="st13" d="M205.7 80.1c4.7-2.7 8.4-.5 8.4 4.9s-3.8 12-8.4 14.7c-4.7 2.7-8.4.5-8.4-4.9s3.8-12 8.4-14.7ZM180.3 94.8c4.7-2.7 8.4-.5 8.4 4.9s-3.8 12-8.4 14.7c-4.7 2.7-8.4.5-8.4-4.9s3.8-12 8.4-14.7ZM231 65.4c4.7-2.7 8.4-.5 8.4 4.9s-3.8 12-8.4 14.7c-4.7 2.7-8.4.5-8.4-4.9s3.8-12 8.4-14.7Z"/>
    <path d="M483.4 229v-3.6L448.1 205v3.6l35.3 20.4Z" style="fill: url(#linear-gradient27)"/>
    <path d="m506.9 211.8-23.5 13.6-35.3-20.4 23.6-13.6 35.3 20.5Z" style="fill: url(#linear-gradient28)"/>
    <path d="m483.4 229 23.5-13.6v-3.6l-23.5 13.6v3.6Z" style="fill: url(#linear-gradient29)"/>
    <path d="M429.3 99.2H430.2c.1 0 .3 0 .4.2.2 0 .3.2.5.3l89.1 51.6c.4.3.9.6 1.3 1l.4.4c.1.1.2.2.3.4 0 .1.2.2.2.3 0 .1.2.2.2.3 0 .1.1.2.2.3 0 .1.1.2.2.3s.1.2.2.3c0 .1.1.2.2.3 0 .1 0 .2.1.3 0 .1 0 .2.1.4 0 .1 0 .3.1.4 0 .1 0 .3.1.4v1.2l-.2 67.4c0 1.2-.4 2-1.1 2.4l-3.7 2.2c.7-.4 1.1-1.2 1.1-2.4l.2-67.4v-1.2c0-.1 0-.3-.1-.4 0-.1 0-.3-.1-.4 0-.1 0-.2-.1-.4 0-.1 0-.2-.1-.3 0-.1-.1-.2-.2-.3 0-.1-.1-.2-.2-.3 0-.1-.1-.2-.2-.3 0-.1-.1-.2-.2-.3 0-.1-.1-.2-.2-.3 0-.1-.2-.2-.3-.3-.1-.1-.2-.3-.3-.4-.1-.2-.3-.3-.4-.4-.4-.4-.8-.7-1.3-1l-89.1-51.6c-.2-.1-.4-.2-.5-.3-.2 0-.3-.1-.4-.2h-1l3.7-2.2h.2Z" style="fill: url(#linear-gradient30)"/>
    <path d="M432.2 108.3H432.8l-3.7 2.2H428.5l3.7-2.2Z" style="fill: #3264b9"/>
    <path class="st33" d="M427.4 101.9c-2.1-1.2-3.8-.2-3.8 2.2l-.2 67.4c0 2.4 1.7 5.3 3.8 6.5l89.1 51.6c2.1 1.2 3.8.2 3.8-2.2l.2-67.4c0-2.4-1.7-5.3-3.8-6.5l-89.1-51.6Z"/>
    <path class="st35" d="m426.6 110.2-.2 60.3c0 .5.3 1 .7 1.3l87.9 50.8c1 .6 2.2-.1 2.2-1.3V161c0-.5-.3-1-.7-1.3l-87.7-50.8c-1-.6-2.2.1-2.2 1.3Z"/>
  </g>
</svg>