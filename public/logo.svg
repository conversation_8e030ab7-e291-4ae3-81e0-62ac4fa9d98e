<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 主要渐变 -->
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5"/>
      <stop offset="100%" style="stop-color:#7C3AED"/>
    </linearGradient>
  </defs>
  
  <!-- 书本页面 -->
  <rect x="4" y="6" width="12" height="14" rx="1" fill="url(#iconGradient)" opacity="0.2"/>
  
  <!-- 流动的页面效果 -->
  <path d="M6 8 C8 6, 12 7, 16 8 C18 9, 19 12, 18 15 C16 17, 12 16, 8 15 C6 14, 6 10, 6 8 Z" 
        fill="url(#iconGradient)"/>
  
  <!-- AI节点 -->
  <circle cx="9" cy="11" r="1" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="13" cy="13" r="1" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="15" cy="9" r="1" fill="#FFFFFF" opacity="0.9"/>
  
  <!-- 连接线 -->
  <path d="M9 11 L13 13 M13 13 L15 9 M9 11 L15 9" 
        stroke="#FFFFFF" stroke-width="0.5" opacity="0.7"/>
</svg>