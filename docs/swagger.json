{"openapi": "3.0.0", "info": {"title": "Pointer Center API", "description": "智能学习路径管理系统 API - 提供个性化学习路径、节点管理、课程内容和用户画像功能", "version": "1.0.0", "contact": {"name": "Pointer Center Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080", "description": "本地开发环境"}, {"url": "https://api.pointercenter.com", "description": "生产环境"}], "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT Bearer Token 认证"}}, "schemas": {"Response": {"type": "object", "properties": {"success": {"type": "boolean", "description": "请求是否成功"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "object", "description": "响应数据"}, "timestamp": {"type": "string", "format": "date-time", "description": "响应时间戳"}}, "required": ["success", "message"]}, "PaginatedResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object"}}, "total": {"type": "integer", "description": "总记录数"}, "page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页大小"}}}}}, "User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "用户唯一标识符"}, "username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "format": "email", "description": "邮箱地址"}, "first_name": {"type": "string", "description": "名字"}, "last_name": {"type": "string", "description": "姓氏"}, "avatar": {"type": "string", "description": "头像URL"}, "bio": {"type": "string", "description": "个人简介"}, "phone": {"type": "string", "description": "电话号码"}, "company": {"type": "string", "description": "公司"}, "country": {"type": "string", "description": "国家"}, "role": {"type": "string", "enum": ["user", "admin", "moderator"], "description": "用户角色"}, "status": {"type": "string", "enum": ["active", "inactive", "suspended"], "description": "账户状态"}, "origin": {"type": "string", "description": "注册来源"}, "email_verified": {"type": "boolean", "description": "邮箱是否已验证"}, "last_login": {"type": "string", "format": "date-time", "description": "最后登录时间"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "LearningPath": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "学习路径唯一标识符"}, "creator_id": {"type": "string", "format": "uuid", "description": "创建者ID"}, "goal": {"type": "string", "description": "学习目标"}, "goal_category": {"type": "string", "description": "目标类别"}, "title": {"type": "string", "description": "学习路径标题"}, "description": {"type": "string", "description": "学习路径描述"}, "path_type": {"type": "string", "enum": ["personalized", "standard", "custom", "adaptive"], "description": "路径类型"}, "difficulty": {"type": "integer", "minimum": 1, "maximum": 10, "description": "难度级别(1-10)"}, "estimated_times": {"type": "integer", "description": "预估学习时长(小时)"}, "total_nodes": {"type": "integer", "description": "总节点数"}, "suitable_for": {"type": "string", "description": "适用人群(JSON数组)"}, "learning_outcomes": {"type": "string", "description": "学习成果(JSON数组)"}, "is_public": {"type": "boolean", "description": "是否公开可复用"}, "usage_count": {"type": "integer", "description": "被使用次数"}, "rating": {"type": "number", "format": "float", "description": "用户评分"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "LearningNode": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "学习节点唯一标识符"}, "title": {"type": "string", "description": "节点标题"}, "description": {"type": "string", "description": "节点描述"}, "estimated_times": {"type": "integer", "description": "预计学习时长(小时)"}, "difficulty": {"type": "integer", "minimum": 1, "maximum": 10, "description": "难度级别(1-10)"}, "prerequisites": {"type": "string", "description": "前置条件(JSON数组)"}, "status": {"type": "string", "enum": ["draft", "active", "archived", "deprecated"], "description": "节点状态"}, "created_by": {"type": "string", "format": "uuid", "description": "创建者ID"}, "updated_by": {"type": "string", "format": "uuid", "description": "更新者ID"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "LearningLesson": {"type": "object", "properties": {"id": {"type": "string", "description": "课程唯一标识符(MongoDB ObjectID)"}, "lesson_id": {"type": "string", "description": "课程ID"}, "title": {"type": "string", "description": "课程标题"}, "description": {"type": "string", "description": "课程描述"}, "type": {"type": "string", "enum": ["text", "interactive", "quiz", "code", "project", "live", "assignment"], "description": "课程类型"}, "estimated_minutes": {"type": "integer", "description": "预计学习时长(分钟)"}, "difficulty": {"type": "integer", "minimum": 1, "maximum": 10, "description": "难度级别(1-10)"}, "content_flow": {"type": "array", "items": {"$ref": "#/components/schemas/AtomicContent"}, "description": "内容流"}, "learning_objectives": {"type": "array", "items": {"type": "string"}, "description": "学习目标"}, "common_misconceptions": {"type": "array", "items": {"type": "string"}, "description": "常见误解"}, "extension_idea": {"type": "string", "description": "扩展想法"}, "student_profile_association": {"type": "string", "description": "学生画像关联"}, "status": {"type": "string", "enum": ["draft", "active", "archived", "deprecated"], "description": "课程状态"}, "created_by": {"type": "string", "description": "创建者ID"}, "updated_by": {"type": "string", "description": "更新者ID"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "AtomicContent": {"type": "object", "properties": {"type": {"type": "string", "enum": ["text_explanation", "code_snippet", "diagram_description", "flowchart_description", "multiple_choice_quiz", "fill_in_blank_quiz", "practice_exercise", "math_formula"], "description": "原子内容类型"}, "order": {"type": "integer", "description": "内容顺序"}, "data": {"type": "object", "description": "内容数据(根据类型变化)"}}}, "StaticProfile": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "静态画像唯一标识符"}, "user_id": {"type": "string", "format": "uuid", "description": "关联用户ID"}, "age": {"type": "integer", "description": "学生年龄"}, "gender": {"type": "string", "description": "性别"}, "preferred_language": {"type": "string", "description": "首选语言"}, "education_experience": {"type": "string", "description": "教育经历"}, "major": {"type": "string", "description": "专业"}, "graduation_year": {"type": "integer", "description": "毕业年份"}, "current_role": {"type": "string", "description": "当前职位"}, "industry": {"type": "string", "description": "所在行业"}, "work_experience": {"type": "integer", "description": "工作经验年数"}, "learning_style": {"type": "string", "description": "学习风格偏好"}, "study_time_per_week": {"type": "integer", "description": "每周期望学习时间(小时)"}, "preferred_study_time": {"type": "string", "description": "偏好学习时间段"}, "learning_pace": {"type": "string", "description": "学习节奏偏好"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "TechCompetencyGraph": {"type": "object", "properties": {"id": {"type": "string", "description": "技术能力图唯一标识符(MongoDB ObjectID)"}, "user_id": {"type": "string", "format": "uuid", "description": "关联用户ID"}, "nodes": {"type": "array", "items": {"$ref": "#/components/schemas/CompetencyNode"}, "description": "能力节点列表"}, "edges": {"type": "array", "items": {"$ref": "#/components/schemas/CompetencyEdge"}, "description": "能力关系边列表"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "CompetencyNode": {"type": "object", "properties": {"id": {"type": "string", "description": "节点ID"}, "name": {"type": "string", "description": "技术名称"}, "level": {"type": "integer", "minimum": 0, "maximum": 5, "description": "掌握程度(0-5)"}, "category": {"type": "string", "description": "技术分类"}}}, "CompetencyEdge": {"type": "object", "properties": {"from": {"type": "string", "description": "源节点ID"}, "to": {"type": "string", "description": "目标节点ID"}, "relationship": {"type": "string", "enum": ["prerequisite", "related", "advanced"], "description": "关系类型"}}}, "GoalPlan": {"type": "object", "properties": {"recommended_direction": {"type": "string", "description": "AI推荐的目标方向"}, "final_goal_description": {"type": "string", "description": "最终目标描述建议"}, "reasoning": {"type": "string", "description": "推荐理由"}, "estimated_timeline": {"type": "string", "description": "预估时间线"}, "key_milestones": {"type": "array", "items": {"type": "string"}, "description": "关键里程碑"}}}, "APIError": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "description": "错误消息"}, "error": {"type": "string", "description": "详细错误信息"}, "timestamp": {"type": "string", "format": "date-time", "description": "错误时间戳"}}}}}, "paths": {"/health": {"get": {"summary": "健康检查", "description": "检查服务健康状态", "tags": ["Health"], "responses": {"200": {"description": "服务正常", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "healthy"}, "timestamp": {"type": "string", "format": "date-time"}, "service": {"type": "string", "example": "pointer_center"}, "version": {"type": "string", "example": "1.0.0"}}}}}}}}}, "/ready": {"get": {"summary": "就绪检查", "description": "检查服务是否就绪", "tags": ["Health"], "responses": {"200": {"description": "服务就绪", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ready"}, "checks": {"type": "object", "properties": {"database": {"type": "string", "example": "ok"}}}}}}}}}}}, "/live": {"get": {"summary": "存活检查", "description": "检查服务是否存活", "tags": ["Health"], "responses": {"200": {"description": "服务存活", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "alive"}}}}}}}}}, "/api/v1/auth/register": {"post": {"summary": "用户注册", "description": "创建新用户账户", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["username", "email", "password"], "properties": {"username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "format": "email", "description": "邮箱地址"}, "password": {"type": "string", "minLength": 8, "description": "密码(至少8位)"}, "first_name": {"type": "string", "description": "名字"}, "last_name": {"type": "string", "description": "姓氏"}}}}}}, "responses": {"201": {"description": "注册成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/User"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "409": {"description": "用户已存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/auth/login": {"post": {"summary": "用户登录", "description": "支持邮箱密码登录和Google OAuth登录", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "邮箱地址(普通登录)"}, "password": {"type": "string", "description": "密码(普通登录)"}, "id_token": {"type": "string", "description": "Google ID Token(Google登录)"}, "auth_code": {"type": "string", "description": "授权码(服务器流程)"}, "redirect_uri": {"type": "string", "description": "重定向URI"}}}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "access_token": {"type": "string"}, "refresh_token": {"type": "string"}, "expires_at": {"type": "string", "format": "date-time"}, "is_new_user": {"type": "boolean"}, "login_time": {"type": "string", "format": "date-time"}, "message": {"type": "string"}}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "认证失败", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/auth/logout": {"post": {"summary": "用户登出", "description": "注销当前用户会话", "tags": ["Authentication"], "security": [{"BearerAuth": []}], "responses": {"200": {"description": "登出成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/auth/refresh": {"post": {"summary": "刷新访问令牌", "description": "使用刷新令牌获取新的访问令牌", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["refresh_token"], "properties": {"refresh_token": {"type": "string", "description": "刷新令牌"}}}}}}, "responses": {"200": {"description": "刷新成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"access_token": {"type": "string"}, "expires_at": {"type": "string", "format": "date-time"}}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "刷新令牌无效", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/auth/me": {"get": {"summary": "获取当前用户信息", "description": "获取当前登录用户的详细信息", "tags": ["Authentication"], "security": [{"BearerAuth": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/User"}}}]}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/goals/plan": {"post": {"summary": "AI目标规划", "description": "基于用户原始目标与当前能力图，返回AI推荐的目标方向与最终目标描述建议", "tags": ["Goals"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["raw_goal"], "properties": {"raw_goal": {"type": "string", "description": "用户原始目标描述"}, "context": {"type": "string", "description": "额外上下文信息"}}}}}}, "responses": {"201": {"description": "目标规划成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GoalPlan"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-paths": {"get": {"summary": "获取学习路径列表", "description": "分页获取学习路径列表，支持筛选和搜索", "tags": ["Learning Paths"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "page_size", "schema": {"type": "integer", "default": 10}, "description": "每页大小"}, {"in": "query", "name": "path_type", "schema": {"type": "string", "enum": ["personalized", "standard", "custom", "adaptive"]}, "description": "路径类型筛选"}, {"in": "query", "name": "difficulty", "schema": {"type": "integer", "minimum": 1, "maximum": 10}, "description": "难度级别筛选"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/PaginatedResponse"}}}]}}}}}}, "post": {"summary": "创建学习路径", "description": "创建新的学习路径", "tags": ["Learning Paths"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["goal", "title"], "properties": {"goal": {"type": "string", "description": "学习目标"}, "goal_category": {"type": "string", "description": "目标类别"}, "title": {"type": "string", "description": "学习路径标题"}, "description": {"type": "string", "description": "学习路径描述"}, "path_type": {"type": "string", "enum": ["personalized", "standard", "custom", "adaptive"], "description": "路径类型"}, "difficulty": {"type": "integer", "minimum": 1, "maximum": 10, "description": "难度级别(1-10)"}, "estimated_times": {"type": "integer", "description": "预估学习时长(小时)"}, "suitable_for": {"type": "array", "items": {"type": "string"}, "description": "适用人群"}, "learning_outcomes": {"type": "array", "items": {"type": "string"}, "description": "学习成果"}, "is_public": {"type": "boolean", "description": "是否公开可复用"}}}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningPath"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-paths/generate": {"post": {"summary": "AI生成学习路径", "description": "使用AI根据用户需求生成个性化学习路径", "tags": ["Learning Paths"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["goal"], "properties": {"goal": {"type": "string", "description": "学习目标"}, "goal_category": {"type": "string", "description": "目标类别"}, "difficulty_preference": {"type": "integer", "minimum": 1, "maximum": 10, "description": "难度偏好(1-10)"}, "time_commitment": {"type": "integer", "description": "时间投入(小时/周)"}, "learning_style": {"type": "string", "description": "学习风格偏好"}, "background_info": {"type": "string", "description": "背景信息"}}}}}}, "responses": {"201": {"description": "生成成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningPath"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-paths/{id}": {"get": {"summary": "获取学习路径详情", "description": "根据ID获取学习路径的详细信息", "tags": ["Learning Paths"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习路径ID"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningPath"}}}]}}}}, "404": {"description": "学习路径不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}, "put": {"summary": "更新学习路径", "description": "更新学习路径信息", "tags": ["Learning Paths"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习路径ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"goal": {"type": "string"}, "goal_category": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "difficulty": {"type": "integer", "minimum": 1, "maximum": 10}, "estimated_times": {"type": "integer"}, "is_public": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningPath"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "学习路径不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}, "delete": {"summary": "删除学习路径", "description": "删除指定的学习路径", "tags": ["Learning Paths"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习路径ID"}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "学习路径不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/user/master-path": {"get": {"summary": "获取用户主路径", "description": "获取当前用户的主学习路径", "tags": ["User Master Path"], "security": [{"BearerAuth": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningPath"}}}]}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "用户主路径不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}, "post": {"summary": "创建用户主路径", "description": "为用户创建主学习路径", "tags": ["User Master Path"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["current_goal_title"], "properties": {"current_goal_title": {"type": "string", "description": "当前目标标题"}}}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningPath"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "409": {"description": "用户已有主路径", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/user/master-path/add-path": {"post": {"summary": "添加路径到主路径", "description": "将学习路径添加到用户的主路径中", "tags": ["User Master Path"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["path_id"], "properties": {"path_id": {"type": "string", "format": "uuid", "description": "要添加的学习路径ID"}}}}}}, "responses": {"200": {"description": "添加成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/user/master-path/unsubscribe": {"post": {"summary": "取消订阅路径", "description": "从用户主路径中移除指定的学习路径", "tags": ["User Master Path"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["path_id"], "properties": {"path_id": {"type": "string", "format": "uuid", "description": "要移除的学习路径ID"}}}}}}, "responses": {"200": {"description": "移除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/user/master-path/mark-completed": {"post": {"summary": "标记路径完成", "description": "标记用户主路径中的某个学习路径为已完成", "tags": ["User Master Path"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["path_id"], "properties": {"path_id": {"type": "string", "format": "uuid", "description": "要标记完成的学习路径ID"}}}}}}, "responses": {"200": {"description": "标记成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/user/master-path/adapt": {"post": {"summary": "适应主路径", "description": "根据新目标调整用户的主学习路径", "tags": ["User Master Path"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"new_goal_title": {"type": "string", "description": "新的目标标题（可选）"}}}}}}, "responses": {"200": {"description": "适应成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-nodes": {"get": {"summary": "获取学习节点列表", "description": "分页获取学习节点列表", "tags": ["Learning Nodes"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "page_size", "schema": {"type": "integer", "default": 10}, "description": "每页大小"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["draft", "active", "archived", "deprecated"]}, "description": "节点状态筛选"}, {"in": "query", "name": "difficulty", "schema": {"type": "integer", "minimum": 1, "maximum": 10}, "description": "难度级别筛选"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/PaginatedResponse"}}}]}}}}}}, "post": {"summary": "创建学习节点", "description": "创建新的学习节点", "tags": ["Learning Nodes"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string", "description": "节点标题"}, "description": {"type": "string", "description": "节点描述"}, "estimated_times": {"type": "integer", "description": "预计学习时长(小时)"}, "difficulty": {"type": "integer", "minimum": 1, "maximum": 10, "description": "难度级别(1-10)"}, "prerequisites": {"type": "array", "items": {"type": "string"}, "description": "前置条件"}}}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningNode"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-nodes/generate": {"post": {"summary": "AI生成学习节点", "description": "使用AI生成学习节点", "tags": ["Learning Nodes"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["topic"], "properties": {"topic": {"type": "string", "description": "节点主题"}, "learning_objective": {"type": "string", "description": "学习目标"}, "difficulty_level": {"type": "integer", "minimum": 1, "maximum": 10, "description": "难度级别(1-10)"}, "estimated_times": {"type": "integer", "description": "预计学习时长(小时)"}, "context": {"type": "string", "description": "上下文信息"}}}}}}, "responses": {"201": {"description": "生成成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningNode"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-nodes/{id}": {"get": {"summary": "获取学习节点详情", "description": "根据ID获取学习节点的详细信息", "tags": ["Learning Nodes"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习节点ID"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningNode"}}}]}}}}, "404": {"description": "学习节点不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}, "put": {"summary": "更新学习节点", "description": "更新学习节点信息", "tags": ["Learning Nodes"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习节点ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "estimated_times": {"type": "integer"}, "difficulty": {"type": "integer", "minimum": 1, "maximum": 10}, "prerequisites": {"type": "array", "items": {"type": "string"}}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningNode"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "学习节点不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}, "delete": {"summary": "删除学习节点", "description": "删除指定的学习节点", "tags": ["Learning Nodes"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习节点ID"}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "学习节点不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-nodes/{id}/status": {"put": {"summary": "更新节点状态", "description": "更新学习节点的状态", "tags": ["Learning Nodes"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习节点ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["draft", "active", "archived", "deprecated"], "description": "节点状态"}}}}}}, "responses": {"200": {"description": "状态更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "学习节点不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-nodes/path-nodes": {"get": {"summary": "获取路径节点", "description": "获取指定学习路径的所有节点", "tags": ["Learning Nodes"], "parameters": [{"in": "query", "name": "path_id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习路径ID"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LearningNode"}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-nodes/generate-node-and-lessons": {"post": {"summary": "生成节点并立即生成课程", "description": "使用AI生成学习节点，然后立即为该节点生成课程内容", "tags": ["Learning Nodes"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["node_description"], "properties": {"node_description": {"type": "string", "description": "节点描述"}, "lessons": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "estimated_minutes": {"type": "integer"}, "type": {"type": "string"}}}, "description": "可选的课程大纲"}}}}}}, "responses": {"201": {"description": "生成成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"node": {"$ref": "#/components/schemas/LearningNode"}, "lessons": {"type": "array", "items": {"$ref": "#/components/schemas/LearningLesson"}}}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}}, "/api/v1/learning-nodes/{node_id}/generate-course": {"post": {"summary": "从节点生成课程", "description": "基于学习节点生成完整的课程内容", "tags": ["Learning Nodes"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "node_id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习节点ID"}], "responses": {"201": {"description": "生成成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"outline": {"type": "object", "description": "课程大纲"}, "lessons": {"type": "array", "items": {"$ref": "#/components/schemas/LearningLesson"}}}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "学习节点不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-nodes/{node_id}/generate-lessons": {"post": {"summary": "从大纲生成课程", "description": "基于课程大纲为节点生成具体的学习课程", "tags": ["Learning Nodes"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "node_id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习节点ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lessons"], "properties": {"lessons": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "estimated_minutes": {"type": "integer"}, "order": {"type": "integer"}}}, "description": "课程大纲列表"}}}}}}, "responses": {"201": {"description": "生成成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LearningLesson"}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-nodes/add-to-path": {"post": {"summary": "将节点添加到学习路径", "description": "将学习节点添加到指定的学习路径中", "tags": ["Learning Nodes"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["node_id", "path_id"], "properties": {"node_id": {"type": "string", "format": "uuid", "description": "学习节点ID"}, "path_id": {"type": "string", "format": "uuid", "description": "学习路径ID"}, "order": {"type": "integer", "description": "在路径中的顺序"}}}}}}, "responses": {"200": {"description": "添加成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}}, "/api/v1/learning-nodes/remove-from-path": {"delete": {"summary": "从路径移除节点", "description": "从指定的学习路径中移除学习节点", "tags": ["Learning Nodes"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["node_id", "path_id"], "properties": {"node_id": {"type": "string", "format": "uuid", "description": "学习节点ID"}, "path_id": {"type": "string", "format": "uuid", "description": "学习路径ID"}}}}}}, "responses": {"200": {"description": "移除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-nodes/{node_id}/assign-lesson": {"post": {"summary": "分配课程到节点", "description": "将学习课程分配到指定的学习节点", "tags": ["Learning Nodes"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "node_id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习节点ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lesson_id"], "properties": {"lesson_id": {"type": "string", "description": "学习课程ID"}, "order": {"type": "integer", "description": "在节点中的顺序"}}}}}}, "responses": {"201": {"description": "分配成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "description": "节点课程关联信息"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "409": {"description": "课程已分配到此节点", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-nodes/{node_id}/lessons": {"get": {"summary": "获取节点的课程列表", "description": "获取指定学习节点关联的所有课程", "tags": ["Learning Nodes"], "parameters": [{"in": "path", "name": "node_id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习节点ID"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LearningLesson"}}}}]}}}}, "404": {"description": "节点不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}}, "/api/v1/learning-lessons": {"get": {"summary": "获取学习课程列表", "description": "分页获取学习课程列表，支持筛选和搜索", "tags": ["Learning Lessons"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "page_size", "schema": {"type": "integer", "default": 10}, "description": "每页大小"}, {"in": "query", "name": "type", "schema": {"type": "string", "enum": ["text", "interactive", "quiz", "code", "project", "live", "assignment"]}, "description": "课程类型筛选"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["draft", "active", "archived", "deprecated"]}, "description": "课程状态筛选"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/PaginatedResponse"}}}]}}}}}}, "post": {"summary": "创建学习课程", "description": "创建新的学习课程", "tags": ["Learning Lessons"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["title", "type"], "properties": {"title": {"type": "string", "description": "课程标题"}, "description": {"type": "string", "description": "课程描述"}, "type": {"type": "string", "enum": ["text", "interactive", "quiz", "code", "project", "live", "assignment"], "description": "课程类型"}, "estimated_minutes": {"type": "integer", "description": "预计学习时长(分钟)"}, "difficulty": {"type": "integer", "minimum": 1, "maximum": 10, "description": "难度级别(1-10)"}, "content_flow": {"type": "array", "items": {"$ref": "#/components/schemas/AtomicContent"}, "description": "内容流"}, "learning_objectives": {"type": "array", "items": {"type": "string"}, "description": "学习目标"}, "node_id": {"type": "string", "format": "uuid", "description": "关联的学习节点ID(可选)"}, "order": {"type": "integer", "description": "在节点中的顺序(可选)"}}}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningLesson"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}}, "/api/v1/learning-lessons/generate": {"post": {"summary": "AI生成学习课程", "description": "使用AI根据描述生成个性化学习课程", "tags": ["Learning Lessons"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["lesson_description"], "properties": {"lesson_description": {"type": "string", "description": "课程描述"}, "user_id": {"type": "string", "description": "用户ID"}}}}}}, "responses": {"201": {"description": "生成成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningLesson"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}}, "/api/v1/learning-lessons/{id}": {"get": {"summary": "获取学习课程详情", "description": "根据ID获取学习课程的详细信息", "tags": ["Learning Lessons"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "学习课程ID(MongoDB ObjectID)"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningLesson"}}}]}}}}, "404": {"description": "学习课程不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}, "put": {"summary": "更新学习课程", "description": "更新学习课程信息", "tags": ["Learning Lessons"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "学习课程ID(MongoDB ObjectID)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["text", "interactive", "quiz", "code", "project", "live", "assignment"]}, "estimated_minutes": {"type": "integer"}, "difficulty": {"type": "integer", "minimum": 1, "maximum": 10}, "content_flow": {"type": "array", "items": {"$ref": "#/components/schemas/AtomicContent"}}, "learning_objectives": {"type": "array", "items": {"type": "string"}}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningLesson"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "404": {"description": "学习课程不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}, "delete": {"summary": "删除学习课程", "description": "删除指定的学习课程", "tags": ["Learning Lessons"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "学习课程ID(MongoDB ObjectID)"}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "404": {"description": "学习课程不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}}, "/api/v1/learning-lessons/type": {"get": {"summary": "按类型获取课程", "description": "根据课程类型获取学习课程列表", "tags": ["Learning Lessons"], "parameters": [{"in": "query", "name": "type", "required": true, "schema": {"type": "string", "enum": ["text", "interactive", "quiz", "code", "project", "live", "assignment"]}, "description": "课程类型"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "page_size", "schema": {"type": "integer", "default": 10}, "description": "每页大小"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/PaginatedResponse"}}}]}}}}}}}, "/api/v1/learning-lessons/search": {"get": {"summary": "搜索课程", "description": "根据关键词搜索学习课程", "tags": ["Learning Lessons"], "parameters": [{"in": "query", "name": "q", "required": true, "schema": {"type": "string"}, "description": "搜索关键词"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "page_size", "schema": {"type": "integer", "default": 10}, "description": "每页大小"}], "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/PaginatedResponse"}}}]}}}}}}}, "/api/v1/learning-nodes/{node_id}/lesson-ids": {"get": {"summary": "获取节点课程ID列表", "description": "获取指定学习节点的所有课程ID", "tags": ["Learning Lessons"], "parameters": [{"in": "path", "name": "node_id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习节点ID"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "string"}, "description": "课程ID列表"}}}]}}}}, "404": {"description": "学习节点不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-lessons/{id}/status": {"put": {"summary": "更新课程状态", "description": "更新学习课程的状态", "tags": ["Learning Lessons"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "学习课程ID(MongoDB ObjectID)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["draft", "active", "archived", "deprecated"], "description": "新状态"}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "学习课程不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-lessons/{id}/duplicate": {"post": {"summary": "复制课程", "description": "复制指定的学习课程", "tags": ["Learning Lessons"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "学习课程ID(MongoDB ObjectID)"}], "responses": {"201": {"description": "复制成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/LearningLesson"}}}]}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "学习课程不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-lessons/{id}/archive": {"post": {"summary": "归档课程", "description": "归档指定的学习课程", "tags": ["Learning Lessons"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "学习课程ID(MongoDB ObjectID)"}], "responses": {"200": {"description": "归档成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "学习课程不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/learning-lessons/{id}/publish": {"post": {"summary": "发布课程", "description": "发布指定的学习课程", "tags": ["Learning Lessons"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "学习课程ID(MongoDB ObjectID)"}], "responses": {"200": {"description": "发布成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "学习课程不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/profiles/static": {"get": {"summary": "获取静态画像", "description": "获取当前用户的静态画像信息", "tags": ["User Profiles"], "security": [{"BearerAuth": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/StaticProfile"}}}]}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "404": {"description": "画像不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}, "post": {"summary": "创建静态画像", "description": "为当前用户创建静态画像", "tags": ["User Profiles"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"age": {"type": "integer", "description": "年龄"}, "gender": {"type": "string", "description": "性别"}, "preferred_language": {"type": "string", "description": "首选语言"}, "education_experience": {"type": "string", "description": "教育经历"}, "major": {"type": "string", "description": "专业"}, "graduation_year": {"type": "integer", "description": "毕业年份"}, "current_role": {"type": "string", "description": "当前职位"}, "industry": {"type": "string", "description": "所在行业"}, "work_experience": {"type": "integer", "description": "工作经验年数"}, "learning_style": {"type": "string", "description": "学习风格偏好"}, "study_time_per_week": {"type": "integer", "description": "每周期望学习时间(小时)"}, "preferred_study_time": {"type": "string", "description": "偏好学习时间段"}, "learning_pace": {"type": "string", "description": "学习节奏偏好"}}}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/StaticProfile"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}, "put": {"summary": "更新静态画像", "description": "更新当前用户的静态画像信息", "tags": ["User Profiles"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"age": {"type": "integer"}, "gender": {"type": "string"}, "preferred_language": {"type": "string"}, "education_experience": {"type": "string"}, "major": {"type": "string"}, "graduation_year": {"type": "integer"}, "current_role": {"type": "string"}, "industry": {"type": "string"}, "work_experience": {"type": "integer"}, "learning_style": {"type": "string"}, "study_time_per_week": {"type": "integer"}, "preferred_study_time": {"type": "string"}, "learning_pace": {"type": "string"}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/StaticProfile"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "404": {"description": "画像不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}}, "/api/v1/profiles/tech-competency": {"get": {"summary": "获取技术能力图", "description": "获取当前用户的技术能力图", "tags": ["User Profiles"], "security": [{"BearerAuth": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/TechCompetencyGraph"}}}]}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "404": {"description": "能力图不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}, "post": {"summary": "更新技术能力图", "description": "通过操作更新用户的技术能力图", "tags": ["User Profiles"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["action"], "properties": {"action": {"type": "string", "enum": ["add_node", "update_node", "remove_node", "add_edge", "remove_edge"], "description": "操作类型"}, "node_data": {"$ref": "#/components/schemas/CompetencyNode", "description": "节点数据(当action为add_node或update_node时)"}, "edge_data": {"$ref": "#/components/schemas/CompetencyEdge", "description": "边数据(当action为add_edge时)"}, "node_id": {"type": "string", "description": "节点ID(当action为remove_node时)"}, "edge_id": {"type": "string", "description": "边ID(当action为remove_edge时)"}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/TechCompetencyGraph"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/APIError"}}}}}}}, "/api/v1/alipay/qrcode": {"post": {"summary": "创建支付宝二维码支付", "description": "生成支付宝扫码支付的二维码链接", "tags": ["Payment - <PERSON><PERSON><PERSON>"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_id", "amount", "subject"], "properties": {"user_id": {"type": "string", "format": "uuid", "description": "用户ID"}, "amount": {"type": "number", "format": "float", "description": "支付金额(元)"}, "subject": {"type": "string", "description": "商品标题"}, "body": {"type": "string", "description": "商品描述"}, "order_id": {"type": "string", "description": "业务订单ID"}, "expire_time": {"type": "integer", "description": "过期时间(分钟，默认30分钟)"}, "extra_data": {"type": "object", "description": "额外数据"}}}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"out_trade_no": {"type": "string", "description": "商户订单号"}, "qr_code": {"type": "string", "description": "二维码链接"}, "expire_time": {"type": "string", "description": "过期时间"}}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/alipay/status": {"get": {"summary": "查询支付状态", "description": "查询支付宝支付订单状态", "tags": ["Payment - <PERSON><PERSON><PERSON>"], "security": [{"BearerAuth": []}], "parameters": [{"in": "query", "name": "out_trade_no", "required": true, "schema": {"type": "string"}, "description": "商户订单号"}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"out_trade_no": {"type": "string", "description": "商户订单号"}, "trade_no": {"type": "string", "description": "支付宝交易号"}, "trade_status": {"type": "string", "description": "交易状态"}, "total_amount": {"type": "string", "description": "订单金额"}, "receipt_amount": {"type": "string", "description": "实收金额"}, "gmt_payment": {"type": "string", "description": "支付时间"}}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/alipay/notify": {"post": {"summary": "支付宝异步通知", "description": "接收支付宝的异步通知，更新订单状态", "tags": ["Payment - <PERSON><PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "description": "支付宝异步通知参数"}}}}, "responses": {"200": {"description": "处理成功", "content": {"text/plain": {"schema": {"type": "string", "example": "success"}}}}, "400": {"description": "处理失败", "content": {"text/plain": {"schema": {"type": "string", "example": "fail"}}}}}}}, "/api/v1/wechat/qrcode": {"post": {"summary": "创建微信扫码支付", "description": "生成微信扫码支付的二维码", "tags": ["Payment - WeChat"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_id", "total_fee", "body"], "properties": {"user_id": {"type": "string", "format": "uuid", "description": "用户ID"}, "total_fee": {"type": "integer", "description": "支付金额(分)"}, "body": {"type": "string", "description": "商品描述"}, "detail": {"type": "string", "description": "商品详情"}, "order_id": {"type": "string", "description": "业务订单ID"}, "expire_time": {"type": "integer", "description": "过期时间(分钟，默认30分钟)"}, "extra_data": {"type": "object", "description": "额外数据"}}}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"out_trade_no": {"type": "string", "description": "商户订单号"}, "code_url": {"type": "string", "description": "二维码链接"}, "expire_time": {"type": "string", "description": "过期时间"}}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}}}