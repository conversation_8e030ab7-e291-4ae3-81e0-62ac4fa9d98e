{"openapi": "3.0.0", "info": {"title": "User Lesson Progress API", "description": "用户课程学习进度管理 API - 提供课程进度跟踪、内容流完成状态管理功能", "version": "1.0.0", "contact": {"name": "Pointer Center Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080", "description": "本地开发环境"}, {"url": "https://api.pointercenter.com", "description": "生产环境"}], "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT Bearer Token 认证"}}, "schemas": {"Response": {"type": "object", "properties": {"success": {"type": "boolean", "description": "请求是否成功"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "object", "description": "响应数据"}, "timestamp": {"type": "string", "format": "date-time", "description": "响应时间戳"}}, "required": ["success", "message"]}, "UserLessonProgress": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "用户课程进度唯一标识符"}, "user_id": {"type": "string", "format": "uuid", "description": "用户ID"}, "lesson_id": {"type": "string", "description": "课程ID(MongoDB)"}, "node_id": {"type": "string", "format": "uuid", "description": "学习节点ID"}, "learning_path_id": {"type": "string", "format": "uuid", "description": "学习路径ID"}, "is_completed": {"type": "boolean", "description": "是否完成"}, "completed_at": {"type": "string", "format": "date-time", "description": "完成时间"}, "score": {"type": "number", "format": "float", "description": "课程分数(0-100)"}, "time_spent": {"type": "integer", "description": "学习时间(分钟)"}, "attempts": {"type": "integer", "description": "尝试次数"}, "last_accessed_at": {"type": "string", "format": "date-time", "description": "最后访问时间"}, "completed_content_flow_ids": {"type": "array", "items": {"type": "string"}, "description": "已完成的ContentFlow ID数组"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "UpsertCompletedContentFlowRequest": {"type": "object", "required": ["lesson_id", "content_flow_id"], "properties": {"path_id": {"type": "string", "format": "uuid", "description": "学习路径ID(可选，与node_id互斥)"}, "node_id": {"type": "string", "format": "uuid", "description": "学习节点ID(可选，与path_id互斥)"}, "lesson_id": {"type": "string", "description": "课程ID"}, "content_flow_id": {"type": "string", "description": "内容流ID"}}}, "RemoveCompletedContentFlowRequest": {"type": "object", "required": ["lesson_id", "content_flow_id"], "properties": {"path_id": {"type": "string", "format": "uuid", "description": "学习路径ID(可选，与node_id互斥)"}, "node_id": {"type": "string", "format": "uuid", "description": "学习节点ID(可选，与path_id互斥)"}, "lesson_id": {"type": "string", "description": "课程ID"}, "content_flow_id": {"type": "string", "description": "内容流ID"}}}}}, "paths": {"/api/v1/user-lesson-progress/content-flow/upsert": {"post": {"summary": "添加/更新已完成的内容流", "description": "标记用户课程的内容流项目为已完成(不存在则新增，存在则增量更新)", "tags": ["User Lesson Progress"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertCompletedContentFlowRequest"}}}}, "responses": {"200": {"description": "成功标记内容流为已完成", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"lesson_id": {"type": "string"}, "content_flow_id": {"type": "string"}}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/user-lesson-progress/content-flow/remove": {"post": {"summary": "移除已完成的内容流", "description": "标记用户课程的内容流项目为未完成", "tags": ["User Lesson Progress"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveCompletedContentFlowRequest"}}}}, "responses": {"200": {"description": "成功标记内容流为未完成", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"lesson_id": {"type": "string"}, "content_flow_id": {"type": "string"}}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/user-lesson-progress/all": {"get": {"summary": "获取所有进度", "description": "获取当前用户在所有路径和节点中的课程进度", "tags": ["User Lesson Progress"], "security": [{"BearerAuth": []}], "responses": {"200": {"description": "成功获取所有进度", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/UserLessonProgress"}, "description": "以lesson_id为键的进度数据映射"}}}]}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/user-lesson-progress/{node_path_id}/all": {"get": {"summary": "获取指定路径或节点的所有进度", "description": "获取当前用户在指定路径或节点中的所有课程进度(自动检测类型)", "tags": ["User Lesson Progress"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "node_path_id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习路径ID或学习节点ID(自动检测)"}], "responses": {"200": {"description": "成功获取路径或节点进度", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/UserLessonProgress"}, "description": "以lesson_id为键的进度数据映射"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/user-lesson-progress/{node_path_id}/lesson/{lesson_id}": {"get": {"summary": "获取指定路径或节点中单个课程的进度", "description": "获取当前用户在指定路径或节点中单个课程的进度信息(自动检测类型)", "tags": ["User Lesson Progress"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "node_path_id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习路径ID或学习节点ID(自动检测)"}, {"in": "path", "name": "lesson_id", "required": true, "schema": {"type": "string"}, "description": "课程ID"}], "responses": {"200": {"description": "成功获取课程进度", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/UserLessonProgress"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "404": {"description": "课程进度不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}, "/api/v1/user-lesson-progress/{node_path_id}/lesson/{lesson_id}/content-flows": {"get": {"summary": "获取指定路径或节点课程的已完成内容流", "description": "获取当前用户在指定路径或节点中指定课程的已完成内容流ID列表(自动检测类型)", "tags": ["User Lesson Progress"], "security": [{"BearerAuth": []}], "parameters": [{"in": "path", "name": "node_path_id", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "学习路径ID或学习节点ID(自动检测)"}, {"in": "path", "name": "lesson_id", "required": true, "schema": {"type": "string"}, "description": "课程ID"}], "responses": {"200": {"description": "成功获取已完成内容流", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Response"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid", "description": "用户ID"}, "lesson_id": {"type": "string", "description": "课程ID"}, "completed_content_flow_ids": {"type": "array", "items": {"type": "string"}, "description": "已完成的内容流ID列表"}, "count": {"type": "integer", "description": "已完成内容流的数量"}}}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Response"}}}}}}}}}