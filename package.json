{"name": "pageflux-web", "version": "0.1.0", "private": true, "description": "页问前端页面", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "check-i18n": "node ./src/i18n/check-i18n.js", "auto-gen-i18n": "node ./src/i18n/auto-gen-i18n.js"}, "dependencies": {"@codedevin/dify-chat": "^5.0.1", "@formatjs/intl-localematcher": "^0.6.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@maxgraph/core": "^0.21.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/line-clamp": "^0.4.4", "@types/negotiator": "^0.6.4", "@types/uuid": "^10.0.0", "axios": "^1.6.2", "bing-translate-api": "^4.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cytoscape": "^3.33.1", "docx-preview": "^0.3.4", "highlight.js": "^11.11.1", "i18next": "^25.0.1", "i18next-resources-to-backend": "^1.2.1", "express": "^4.19.2", "http-proxy-middleware": "^3.0.2", "js-cookie": "^3.0.5", "katex": "^0.16.22", "lucide-react": "^0.292.0", "magicast": "^0.3.5", "mammoth": "^1.9.0", "mermaid": "^11.9.0", "negotiator": "^0.6.3", "next": "14.0.3", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-infinite-scroll-component": "^6.1.0", "react-katex": "^3.1.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "sonner": "^1.2.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^4.4.6"}, "devDependencies": {"@playwright/test": "^1.54.1", "@svgr/webpack": "^8.1.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20.9.4", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.21", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "postcss": "^8.5.6", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.17", "typescript": "^5.3.2"}}