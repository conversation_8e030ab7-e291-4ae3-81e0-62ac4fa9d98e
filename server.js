/*
 * Custom dev server to bypass Next.js 30s dev timeout by proxying /api with
 * extended timeouts.
 */
const express = require("express");
const http = require("http");
const next = require("next");
const { createProxyMiddleware } = require("http-proxy-middleware");

const dev = process.env.NODE_ENV !== "production";
const hostname = process.env.HOST || "localhost";
const port = Number(process.env.PORT) || 3000;

// Backend API target; can override via env
const API_TARGET = process.env.API_TARGET || "http://localhost:8012/api/v1";

const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

// Extended timeouts (5 minutes) to avoid 30s dev timeout
const FIVE_MIN = 5 * 60 * 1000;

app
  .prepare()
  .then(() => {
    const server = express();

    // Trust proxy headers when running behind a proxy
    server.set("trust proxy", true);

    // API proxy with long timeouts and WS support
    server.use(
      ["/api/v1"],
      createProxyMiddleware({
        target: API_TARGET,
        changeOrigin: true,
        ws: true,
        secure: false,
        // Bump timeouts
        proxyTimeout: FIVE_MIN,
        timeout: FIVE_MIN,
        // Preserve request bodies for non-GET/HEAD
        onProxyReq: (proxyReq, req) => {
          if (!req.readableEnded && req.body) {
            const bodyData =
              typeof req.body === "string"
                ? req.body
                : JSON.stringify(req.body);
            if (bodyData) {
              proxyReq.setHeader("Content-Type", "application/json");
              proxyReq.setHeader("Content-Length", Buffer.byteLength(bodyData));
              proxyReq.write(bodyData);
            }
          }
        },
        onError: (err, req, res) => {
          const status = 502;
          const message =
            "Proxy error: " + (err && err.message ? err.message : "Unknown");
          if (!res.headersSent) {
            res.writeHead(status, { "Content-Type": "application/json" });
          }
          res.end(JSON.stringify({ code: status, message }));
        },
      })
    );

    // All other requests handled by Next
    server.all("*", (req, res) => handle(req, res));

    const httpServer = http.createServer(server);

    // Forward upgrade events to Next (for HMR / WebSocket)
    if (typeof app.getUpgradeHandler === "function") {
      httpServer.on("upgrade", app.getUpgradeHandler());
    }

    httpServer.listen(port, hostname, () => {
      // Increase Node server timeouts
      httpServer.headersTimeout = FIVE_MIN + 10_000; // must be > keepAliveTimeout
      httpServer.keepAliveTimeout = FIVE_MIN;
      httpServer.requestTimeout = 0; // disable per-request timeout
      if ("timeout" in httpServer) {
        // Some Node versions still use server.timeout
        // @ts-ignore
        httpServer.timeout = 0;
      }
      // eslint-disable-next-line no-console
      console.log(
        `Dev server ready on http://${hostname}:${port} (API → ${API_TARGET})`
      );
    });
  })
  .catch((err) => {
    // eslint-disable-next-line no-console
    console.error("Failed to start custom dev server:", err);
    process.exit(1);
  });
