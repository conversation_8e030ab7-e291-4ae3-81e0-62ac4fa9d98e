// utils/getUrl.ts
const ALIYUN_HOST = "https://file.shedangjia.com";
const MINIO_HOST = "http://************:9000";

export function isFullUrl(url?: string): boolean {
  return !!url && /^[a-zA-Z]+:\/\//.test(url);
}

export function getImageURL(url?: string): string {
  if (!url) return "";

  if (isFullUrl(url)) {
    return url;
  }

  if (url.includes("/minio")) {
    return MINIO_HOST + url;
  }

  return ALIYUN_HOST + url;
}
