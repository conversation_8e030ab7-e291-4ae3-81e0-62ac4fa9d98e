/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-08-05 15:15:52
 */
/**
 * 课程相关工具函数
 */

import { MockApiCourseData } from "@/types/mockapi";

/**
 *
 * 判断课程难度（字符串格式）
 * @param course MockAPI 课程数据
 * @returns 课程难度字符串
 */
export function getDifficulty(course: MockApiCourseData): string {
  // 优先使用 target_audience 字段
  if (course.courses.length > 0 && course.courses[0].target_audience) {
    const targetAudience = course.courses[0].target_audience.toLowerCase();
    if (targetAudience === "beginner") return "beginner";
    if (targetAudience === "intermediate") return "intermediate";
    if (targetAudience === "advanced") return "advanced";
  }

  // 回退到基于标签的判断
  const tags = course.tags.map((tag) => tag.toLowerCase());
  const learnerTags = course.learner_tags.map((tag) => tag.toLowerCase());

  if (tags.includes("beginner") || learnerTags.includes("beginner")) {
    return "beginner";
  }

  if (tags.includes("advanced") || learnerTags.includes("advanced")) {
    return "advanced";
  }

  // 如果没有 target_audience 字段且标签中也没有难度信息，默认为 beginner
  return "beginner";
}

/**
 * 判断课程难度（首字母大写格式）
 * @param course MockAPI 课程数据
 * @returns 课程难度字符串（首字母大写）
 */
export function getDifficultyCapitalized(course: MockApiCourseData): string {
  const difficulty = getDifficulty(course);
  return difficulty.charAt(0).toUpperCase() + difficulty.slice(1);
}

/**
 * 判断课程难度（标准格式：Beginner/Intermediate/Advanced）
 * @param course MockAPI 课程数据
 * @returns 课程难度字符串（标准格式）
 */
export function getDifficultyStandard(
  course: MockApiCourseData
): "Beginner" | "Intermediate" | "Advanced" {
  const difficulty = getDifficulty(course);
  return (difficulty.charAt(0).toUpperCase() + difficulty.slice(1)) as
    | "Beginner"
    | "Intermediate"
    | "Advanced";
}

/**
 * 获取课程难度（支持翻译）
 * @param course MockAPI 课程数据
 * @param t 翻译函数
 * @returns 翻译后的课程难度字符串
 */
export function getDifficultyWithTranslation(
  course: MockApiCourseData,
  t: any
): string {
  const difficulty = getDifficulty(course);
  return t(`common.course.difficulty.${difficulty}`);
}

/**
 * 获取课程标题
 * @param course MockAPI 课程数据
 * @returns 课程标题
 */
export function getCourseTitle(course: MockApiCourseData): string {
  return course.courses[0]?.title || "Untitled Course";
}

/**
 * 获取课程标题（支持翻译）
 * @param course MockAPI 课程数据
 * @param t 翻译函数
 * @returns 课程标题
 */
export function getCourseTitleWithTranslation(
  course: MockApiCourseData,
  t: any
): string {
  return course.courses[0]?.title || t("common.course.untitledCourse");
}

/**
 * 获取课程简要描述（用于 Header）
 * @param course MockAPI 课程数据
 * @returns 课程简要描述
 */
export function getCourseBriefSummary(course: MockApiCourseData): string {
  return course.courses[0]?.brief_summary || "";
}

/**
 * 获取课程详细描述（用于 Overview）
 * @param course MockAPI 课程数据
 * @returns 课程详细描述
 */
export function getCourseSummary(course: MockApiCourseData): string {
  return course.courses[0]?.summary || "";
}

/**
 * 获取课程描述（保持向后兼容）
 * @param course MockAPI 课程数据
 * @returns 课程描述
 */
export function getCourseDescription(course: MockApiCourseData): string {
  return course.courses[0]?.summary || course.courses[0]?.brief_summary || "";
}

/**
 * 获取课程学习目标
 * @param course MockAPI 课程数据
 * @returns 学习目标数组
 */
export function getCourseWhatYouWillLearn(course: MockApiCourseData): string[] {
  return course.courses[0]?.whatYouWillLearn || [];
}
