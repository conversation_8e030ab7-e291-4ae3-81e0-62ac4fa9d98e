export type ProductCategory = "WB" | "XB" | "ZB" | "XX" | "FS" | "PS" | "QT";

export const ProductCategories = {
  WB: "腕表",
  XB: "箱包",
  ZB: "珠宝",
  XX: "鞋靴",
  FS: "服饰",
  PS: "配饰",
  QT: "其他",
} as const;

export interface CategoryOption {
  value: ProductCategory;
  label: string;
}

/**
 * Returns an array of category options for use in select/dropdown components
 */
export const getCategoryOptions = (): CategoryOption[] => {
  return Object.entries(ProductCategories).map(([value, label]) => ({
    value: value as ProductCategory,
    label,
  }));
};

/**
 * Get the display name for a category code
 */
export const getCategoryLabel = (category: ProductCategory): string => {
  return ProductCategories[category];
};
