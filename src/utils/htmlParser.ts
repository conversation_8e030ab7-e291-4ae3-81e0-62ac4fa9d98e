import { TOCItem } from "@/components/ui/table-of-contents";

/**
 * 从HTML内容中自动提取目录项
 * @param htmlContent HTML字符串内容
 * @returns 目录项数组
 */
export function extractTableOfContents(htmlContent: string): TOCItem[] {
  // 创建一个临时的DOM元素来解析HTML
  if (typeof window === 'undefined') {
    // 服务端渲染时的处理
    return extractTOCFromString(htmlContent);
  }

  // 客户端处理
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;

  const tocItems: TOCItem[] = [];
  
  // 查找所有带有id的标题元素 (h1, h2, h3, h4, h5, h6)
  const headings = tempDiv.querySelectorAll('h1[id], h2[id], h3[id], h4[id], h5[id], h6[id]');
  
  headings.forEach((heading) => {
    const id = heading.getAttribute('id');
    const tagName = heading.tagName.toLowerCase();
    const level = parseInt(tagName.charAt(1)); // 提取数字 (h1 -> 1, h2 -> 2, etc.)
    
    // 获取标题文本，排除锚点符号
    let title = heading.textContent || '';
    // 移除可能的锚点符号 (#)
    title = title.replace(/\s*#\s*$/, '').trim();
    
    if (id && title) {
      tocItems.push({
        id,
        title,
        level,
      });
    }
  });

  return tocItems;
}

/**
 * 服务端字符串解析方式（备用方案）
 * @param htmlContent HTML字符串
 * @returns 目录项数组
 */
function extractTOCFromString(htmlContent: string): TOCItem[] {
  const tocItems: TOCItem[] = [];
  
  // 使用正则表达式匹配带有id的标题标签
  const headingRegex = /<(h[1-6])[^>]*id\s*=\s*["']([^"']+)["'][^>]*>(.*?)<\/\1>/gi;
  
  let match;
  while ((match = headingRegex.exec(htmlContent)) !== null) {
    const tagName = match[1]; // h1, h2, etc.
    const id = match[2]; // id属性值
    const content = match[3]; // 标题内容
    
    const level = parseInt(tagName.charAt(1)); // 提取级别数字
    
    // 清理标题文本，移除HTML标签和锚点符号
    let title = content.replace(/<[^>]*>/g, '').trim();
    title = title.replace(/\s*#\s*$/, '').trim();
    
    if (id && title) {
      tocItems.push({
        id,
        title,
        level,
      });
    }
  }

  return tocItems;
}

/**
 * 为HTML内容中的标题添加锚点链接样式（可选功能）
 * @param htmlContent 原始HTML内容
 * @returns 处理后的HTML内容
 */
export function addAnchorLinksToHeadings(htmlContent: string): string {
  // 这个函数可以用来为没有锚点的标题添加锚点样式
  // 目前保持原样，如果需要可以扩展
  return htmlContent;
}

/**
 * 验证目录项的有效性
 * @param tocItems 目录项数组
 * @returns 过滤后的有效目录项
 */
export function validateTOCItems(tocItems: TOCItem[]): TOCItem[] {
  return tocItems.filter(item => {
    // 确保必要字段存在
    return item.id && 
           item.title && 
           item.level && 
           item.level >= 1 && 
           item.level <= 6;
  });
}
