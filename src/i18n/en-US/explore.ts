const translation = {
  title: "Explore Learning",
  description:
    "Discover new learning opportunities and explore various technology fields and courses",
  searchBar: {
    placeholder: "Search courses, skills, projects or learning paths...",
    button: "Search",
    suggestions: "Search Suggestions",
    recentSearches: "Recent Searches",
    popularSearches: "Popular Searches",
    clearHistory: "Clear History",
  },
  featureCards: {
    title: "Featured Learning Content",
    subtitle: "Curated high-quality learning resources",
    courses: {
      title: "Premium Courses",
      description: "Courses carefully designed by industry experts",
      count: "Courses",
      viewAll: "View All Courses",
    },
    projects: {
      title: "Hands-on Projects",
      description: "Enhance practical skills through real projects",
      count: "Projects",
      viewAll: "View All Projects",
    },
    paths: {
      title: "Learning Paths",
      description: "Systematic skill enhancement solutions",
      count: "Paths",
      viewAll: "View All Paths",
    },
    labs: {
      title: "Cloud Labs",
      description: "Online lab environment, ready to use",
      count: "Labs",
      viewAll: "View All Labs",
    },
  },
  mostPopular: {
    title: "Most Popular",
    description: "Students' favorite trending content",
  },
  mostPopularCourses: {
    title: "Most Popular Courses",
    subtitle: "Students' favorite trending courses",
    viewAll: "View More",
    course: {
      students: "Students",
      rating: "Rating",
      reviews: "Reviews",
      duration: "Duration",
      level: "Level",
      free: "Free",
      premium: "Premium",
      bestseller: "Bestseller",
      new: "New",
      updated: "Updated",
    },
  },
  newAdditions: {
    title: "New Additions",
    description: "Recently released new courses and content",
    subtitle: "Recently released new courses and content",
    viewAll: "View All New Content",
    timeLabels: {
      today: "Today",
      yesterday: "Yesterday",
      thisWeek: "This Week",
      lastWeek: "Last Week",
      thisMonth: "This Month",
    },
  },
  categories: {
    title: "Browse by Category",
    subtitle: "Choose your area of interest in technology",
    viewAll: "View All Categories",
    webDevelopment: {
      title: "Web Development",
      description: "Frontend, backend and full-stack development",
      courses: "Courses",
      icon: "web",
    },
    mobileDevelopment: {
      title: "Mobile Development",
      description: "iOS, Android and cross-platform development",
      courses: "Courses",
      icon: "mobile",
    },
    dataScience: {
      title: "Data Science",
      description: "Data analysis, machine learning and AI",
      courses: "Courses",
      icon: "data",
    },
    cloudComputing: {
      title: "Cloud Computing",
      description: "AWS, Azure, GCP and other cloud services",
      courses: "Courses",
      icon: "cloud",
    },
    devOps: {
      title: "DevOps",
      description: "Automated deployment and operations management",
      courses: "Courses",
      icon: "devops",
    },
    cybersecurity: {
      title: "Cybersecurity",
      description: "Information security and penetration testing",
      courses: "Courses",
      icon: "security",
    },
    artificialIntelligence: {
      title: "Artificial Intelligence",
      description: "Machine learning, deep learning and AI applications",
      courses: "Courses",
      icon: "ai",
    },
    blockchain: {
      title: "Blockchain",
      description: "Blockchain technology and cryptocurrency",
      courses: "Courses",
      icon: "blockchain",
    },
    gamesDevelopment: {
      title: "Game Development",
      description: "Game engines and game programming",
      courses: "Courses",
      icon: "games",
    },
    uiUxDesign: {
      title: "UI/UX Design",
      description: "User interface and user experience design",
      courses: "Courses",
      icon: "design",
    },
  },
  recommendedPaths: {
    title: "Recommended Learning Paths",
    subtitle: "Learning paths recommended based on your interests",
    viewAll: "View All Paths",
    path: {
      duration: "Estimated Duration",
      courses: "Included Courses",
      projects: "Hands-on Projects",
      skills: "Skills",
      level: "Suitable Level",
      students: "Students",
      completion: "Completion Rate",
      certificate: "Certificate",
    },
  },
  skillAssessment: {
    title: "Skill Assessment",
    subtitle:
      "Test your skill level and get personalized learning recommendations",
    types: {
      programming: {
        title: "Programming Basics",
        description: "Test your fundamental programming knowledge",
        duration: "15 minutes",
        questions: "20 questions",
      },
      webDev: {
        title: "Web Development",
        description: "Assess your web development skills",
        duration: "20 minutes",
        questions: "25 questions",
      },
      dataScience: {
        title: "Data Science",
        description: "Test your data analysis capabilities",
        duration: "25 minutes",
        questions: "30 questions",
      },
    },
    startAssessment: "Start Assessment",
    viewResults: "View Results",
    retakeAssessment: "Retake Assessment",
  },
  learningStats: {
    title: "Learning Statistics",
    global: {
      totalLearners: "Total Learners",
      coursesCompleted: "Courses Completed",
      hoursLearned: "Hours Learned",
      certificatesEarned: "Certificates Earned",
    },
    personal: {
      yourProgress: "Your Progress",
      coursesEnrolled: "Courses Enrolled",
      coursesCompleted: "Courses Completed",
      hoursSpent: "Hours Spent",
      skillsLearned: "Skills Learned",
      certificatesEarned: "Certificates Earned",
      currentStreak: "Current Streak",
      longestStreak: "Longest Streak",
    },
  },
  communityActivity: {
    title: "Community Activity",
    subtitle: "See what other learners are doing",
    activities: {
      courseCompleted: "completed a course",
      projectSubmitted: "submitted a project",
      certificateEarned: "earned a certificate",
      pathStarted: "started a learning path",
      discussionPosted: "posted a discussion",
      questionAnswered: "answered a question",
    },
    timeAgo: {
      justNow: "Just now",
      minutesAgo: "minutes ago",
      hoursAgo: "hours ago",
      daysAgo: "days ago",
      weeksAgo: "weeks ago",
    },
  },
  actions: {
    startLearning: "Start Learning",
    continueReading: "Continue Reading",
    viewDetails: "View Details",
    enroll: "Enroll Now",
    preview: "Preview",
    bookmark: "Bookmark",
    share: "Share",
    download: "Download",
    print: "Print",
    export: "Export",
    filter: "Filter",
    sort: "Sort",
    refresh: "Refresh",
    loadMore: "Load More",
    showLess: "Show Less",
    expand: "Expand",
    collapse: "Collapse",
  },
  empty: {
    noContent: "No content available",
    noResults: "No relevant results found",
    noBookmarks: "No bookmarked content",
    noHistory: "No browsing history",
    suggestion: "We suggest you:",
    suggestions: {
      browseCategories: "Browse different categories",
      adjustFilters: "Adjust filter criteria",
      tryDifferentKeywords: "Try different keywords",
      checkSpelling: "Check spelling",
      explorePopular: "Explore popular content",
    },
  },
  loading: {
    content: "Loading content...",
    courses: "Loading courses...",
    projects: "Loading projects...",
    paths: "Loading learning paths...",
    search: "Searching...",
    more: "Loading more...",
  },
  suggestions: {
    trendingNowTitle: "Trending Now",
    trendingNow: ["Data Science", "Machine Learning", "Generative AI"],
    popularCourses: [
      {
        title: "The Way to Go",
        level: "Intermediate",
        duration: "25h",
      },
      {
        title: "Grokking the Low Level Design Interview Using OOD Principles",
        level: "Intermediate",
        duration: "50h",
      },
      {
        title: "Grokking the Modern System Design Interview",
        level: "Intermediate",
        duration: "26h",
      },
      {
        title: "Become an Effective Software Engineering Manager",
        level: "Beginner",
        duration: "10h 45min",
      },
    ],
  },
  categoryDescription: "Explore {{title}} courses and learning resources",
};

export default translation;
