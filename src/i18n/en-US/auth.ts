const translation = {
  validation: {
    fullName: "UserName must be at least 2 characters",
    email: "Please enter a valid email address",
    password: "Password must be at least 6 characters",
    confirmPassword: "Please confirm your password",
    passwordMatch: "Passwords do not match",
  },
  fields: {
    fullName: "UserName",
    email: "Email",
    password: "Password",
    confirmPassword: "Confirm Password",
  },
  signup: {
    title: "Create Your Free Account",
    submit: "Get Started for Free",
    creating: "Creating Account...",
    hasAccount: "Already have an account?",
    signInLink: "Sign In",
  },
  login: {
    title: "Sign In to Your Account",
    submit: "Sign In",
    loggingIn: "Signing In...",
    forgotPassword: "Forgot Password?",
    noAccount: "Don't have an account?",
    signUpLink: "Sign Up Now",
  },
  social: {
    continueWith: "Continue with {{provider}}",
    continueWithGoogle: "Continue with Google",
    continueWithEmail: "Continue with <PERSON><PERSON>",
    or: "or",
  },
  errors: {
    invalidCredentials: "Invalid email or password",
    accountNotFound: "Account not found",
    emailAlreadyExists: "This email is already registered",
    weakPassword: "Password is too weak",
    networkError: "Network connection failed, please try again",
    serverError: "Server error, please try again later",
    unknownError: "Unknown error, please contact support",
  },
  success: {
    accountCreated: "Account created successfully!",
    loginSuccess: "Login successful!",
    passwordReset: "Password reset email sent",
    emailVerified: "Email verified successfully",
  },
  terms: {
    agreement: "By continuing, you agree to our",
    privacyPolicy: "Privacy Policy",
    and: "and",
    termsOfService: "Terms of Service",
    recaptcha: "This site is protected by reCAPTCHA",
  },
};

export default translation
