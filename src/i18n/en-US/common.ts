const translation = {
  loading: "Loading",
  buttons: {
    save: "Save",
    cancel: "Cancel",
    confirm: "Confirm",
    delete: "Delete",
    edit: "Edit",
    add: "Add",
    remove: "Remove",
    submit: "Submit",
    reset: "Reset",
    search: "Search",
    filter: "Filter",
    sort: "Sort",
    export: "Export",
    import: "Import",
    download: "Download",
    upload: "Upload",
    share: "Share",
    copy: "Copy",
    paste: "Paste",
    cut: "Cut",
    undo: "Undo",
    redo: "Redo",
    refresh: "Refresh",
    reload: "Reload",
    close: "Close",
    open: "Open",
    view: "View",
    preview: "Preview",
    print: "Print",
    help: "Help",
    settings: "Settings",
    preferences: "Preferences",
    profile: "Profile",
    account: "Account",
    logout: "Logout",
    login: "Login",
    register: "Register",
    signUp: "Sign Up",
    signIn: "Sign In",
    forgotPassword: "Forgot Password",
    resetPassword: "Reset Password",
    changePassword: "Change Password",
    updateProfile: "Update Profile",
    back: "Back",
    next: "Next",
    previous: "Previous",
    continue: "Continue",
    finish: "Finish",
    start: "Start",
    stop: "Stop",
    pause: "Pause",
    resume: "Resume",
    play: "Play",
    skip: "Skip",
    retry: "Retry",
    more: "More",
    less: "Less",
    showMore: "Show More",
    showLess: "Show Less",
    expand: "Expand",
    collapse: "Collapse",
    select: "Select",
    selectAll: "Select All",
    deselectAll: "Deselect All",
    clear: "Clear",
    clearAll: "Clear All",
    apply: "Apply",
    ok: "OK",
    yes: "Yes",
    no: "No",
    enable: "Enable",
    disable: "Disable",
    activate: "Activate",
    deactivate: "Deactivate",
    publish: "Publish",
    unpublish: "Unpublish",
    archive: "Archive",
    unarchive: "Unarchive",
    bookmark: "Bookmark",
    unbookmark: "Unbookmark",
    like: "Like",
    unlike: "Unlike",
    follow: "Follow",
    unfollow: "Unfollow",
    subscribe: "Subscribe",
    unsubscribe: "Unsubscribe",
    join: "Join",
    leave: "Leave",
    invite: "Invite",
    accept: "Accept",
    decline: "Decline",
    approve: "Approve",
    reject: "Reject",
    block: "Block",
    unblock: "Unblock",
    report: "Report",
    flag: "Flag",
    unflag: "Unflag",
    pin: "Pin",
    unpin: "Unpin",
    lock: "Lock",
    unlock: "Unlock",
    hide: "Hide",
    show: "Show",
    minimize: "Minimize",
    maximize: "Maximize",
    fullscreen: "Fullscreen",
    exitFullscreen: "Exit Fullscreen",
  },
  status: {
    active: "Active",
    inactive: "Inactive",
    online: "Online",
    offline: "Offline",
    available: "Available",
    unavailable: "Unavailable",
    busy: "Busy",
    away: "Away",
    pending: "Pending",
    approved: "Approved",
    rejected: "Rejected",
    completed: "Completed",
    inProgress: "In Progress",
    notStarted: "Not Started",
    cancelled: "Cancelled",
    expired: "Expired",
    draft: "Draft",
    published: "Published",
    archived: "Archived",
    deleted: "Deleted",
    enabled: "Enabled",
    disabled: "Disabled",
    public: "Public",
    private: "Private",
    shared: "Shared",
    locked: "Locked",
    unlocked: "Unlocked",
    verified: "Verified",
    unverified: "Unverified",
    confirmed: "Confirmed",
    unconfirmed: "Unconfirmed",
    success: "Success",
    error: "Error",
    warning: "Warning",
    info: "Info",
    submitting: "Submitting...",
    empty: "Empty",
    noData: "No Data",
    notFound: "Not Found",
    unauthorized: "Unauthorized",
    forbidden: "Forbidden",
    serverError: "Server Error",
    networkError: "Network Error",
    timeout: "Timeout",
    maintenance: "Under Maintenance",
    loading: "Loading...",
    loadingCourses: "Loading courses...",
    loadingCourse: "Loading course...",
    errorLoadingCourses: "Error loading courses",
  },
  form: {
    required: "Required",
    optional: "Optional",
    placeholder: "Please enter...",
    selectPlaceholder: "Please select...",
    searchPlaceholder: "Search...",
    noOptions: "No options",
    noResults: "No results",
    validation: {
      required: "This field is required",
      email: "Please enter a valid email address",
      password: "Password must be at least 8 characters",
      confirmPassword: "Passwords do not match",
      minLength: "Minimum {min} characters required",
      maxLength: "Maximum {max} characters allowed",
      min: "Minimum value is {min}",
      max: "Maximum value is {max}",
      pattern: "Invalid format",
      url: "Please enter a valid URL",
      phone: "Please enter a valid phone number",
      number: "Please enter a valid number",
      integer: "Please enter an integer",
      positive: "Please enter a positive number",
      negative: "Please enter a negative number",
      date: "Please enter a valid date",
      time: "Please enter a valid time",
      dateTime: "Please enter a valid date and time",
      file: "Please select a file",
      fileSize: "File size cannot exceed {size}",
      fileType: "Unsupported file type",
    },
  },
  time: {
    now: "Now",
    today: "Today",
    yesterday: "Yesterday",
    tomorrow: "Tomorrow",
    thisWeek: "This Week",
    lastWeek: "Last Week",
    nextWeek: "Next Week",
    thisMonth: "This Month",
    lastMonth: "Last Month",
    nextMonth: "Next Month",
    thisYear: "This Year",
    lastYear: "Last Year",
    nextYear: "Next Year",
    seconds: "Seconds",
    minutes: "Minutes",
    hours: "Hours",
    days: "Days",
    weeks: "Weeks",
    months: "Months",
    years: "Years",
    ago: "Ago",
    later: "Later",
    duration: "Duration",
    startTime: "Start Time",
    endTime: "End Time",
    createdAt: "Created At",
    updatedAt: "Updated At",
    publishedAt: "Published At",
    expiredAt: "Expired At",
  },
  units: {
    piece: "Piece",
    item: "Item",
    count: "Count",
    total: "Total",
    subtotal: "Subtotal",
    quantity: "Quantity",
    amount: "Amount",
    price: "Price",
    cost: "Cost",
    fee: "Fee",
    tax: "Tax",
    discount: "Discount",
    percent: "Percent",
    rate: "Rate",
    ratio: "Ratio",
    score: "Score",
    point: "Point",
    level: "Level",
    rank: "Rank",
    grade: "Grade",
    size: "Size",
    weight: "Weight",
    length: "Length",
    width: "Width",
    height: "Height",
    depth: "Depth",
    area: "Area",
    volume: "Volume",
    capacity: "Capacity",
    speed: "Speed",
    frequency: "Frequency",
    temperature: "Temperature",
    pressure: "Pressure",
    voltage: "Voltage",
    current: "Current",
    power: "Power",
    energy: "Energy",
  },
  messages: {
    success: "Operation successful",
    error: "Operation failed",
    warning: "Warning",
    info: "Info",
    confirm: "Confirm operation",
    deleteConfirm: "Are you sure you want to delete?",
    saveSuccess: "Saved successfully",
    saveError: "Save failed",
    updateSuccess: "Updated successfully",
    updateError: "Update failed",
    deleteSuccess: "Deleted successfully",
    deleteError: "Delete failed",
    createSuccess: "Created successfully",
    createError: "Create failed",
    uploadSuccess: "Uploaded successfully",
    uploadError: "Upload failed",
    downloadSuccess: "Downloaded successfully",
    downloadError: "Download failed",
    copySuccess: "Copied successfully",
    copyError: "Copy failed",
    networkError: "Network connection failed",
    serverError: "Server error",
    unauthorized: "Unauthorized access",
    forbidden: "Access forbidden",
    notFound: "Page not found",
    timeout: "Request timeout",
    maintenance: "System under maintenance",
    comingSoon: "Coming soon",
    underConstruction: "Under construction",
    noPermission: "Insufficient permissions",
    sessionExpired: "Session expired, please login again",
    loginRequired: "Please login first",
    emailSent: "Email sent",
    passwordChanged: "Password changed successfully",
    profileUpdated: "Profile updated successfully",
    settingsSaved: "Settings saved successfully",
    dataExported: "Data exported successfully",
    dataImported: "Data imported successfully",
    operationCancelled: "Operation cancelled",
    changesSaved: "Changes saved",
    changesDiscarded: "Changes discarded",
    unsavedChanges: "You have unsaved changes",
    confirmLeave:
      "Are you sure you want to leave? Unsaved changes will be lost",
    loadingData: "Loading data...",
    processingRequest: "Processing request...",
    pleaseWait: "Please wait...",
    tryAgain: "Please try again",
    contactSupport: "Please contact support",
    checkConnection: "Please check your network connection",
    refreshPage: "Please refresh the page",
  },
  search: {
    placeholder: "Search...",
    filters: "Search filters",
    searchFilters: "Search Filters",
    clearAll: "Clear All",
    showMore: "Show More",
    showLess: "Show Less",
  },
  searchNotFound: {
    message: "We couldn't find any search results for {{query}}",
    clearButton: "Clear Search",
  },
  noResults: {
    title: "No results found",
    description: "Try adjusting your search or filters",
  },
  showMore: "Show More",
  showLess: "Show Less",
  types: {
    course: "Course",
    project: "Project",
    cloudLab: "Cloud Lab",
    skillPath: "Skill Path",
  },
  challenge: "Challenge",
  difficulty: {
    beginner: "Beginner",
    intermediate: "Intermediate",
    advanced: "Advanced",
  },
  accessibility: {
    readMore: "Read more about {{title}}",
  },
  faq: {
    whatIsPageflux: "What is Pageflux AI Unlimited?",
    whatIsPagefluxAnswer:
      "Pageflux AI Unlimited is an online learning platform that provides interactive and text-based coding courses for developers and tech professionals. Unlike traditional video-based learning platforms, Pageflux AI focuses on a text-based, hands-on learning approach, where learners can practice coding directly in the browser as they progress through the learning materials.",
    doesPagefluxHaveFree: "Does Pageflux AI have free courses?",
    doesPagefluxHaveFreeAnswer:
      "Pageflux AI offers a few interactive free courses for learners to start their learning journey without additional costs. We also offer amazing discounts on paid courses so learning does not get heavier on the pocket.",
    canPayInstallments: "Can I pay in installments?",
    canPayInstallmentsAnswer:
      "Yes, we offer flexible payment options including installment plans to make quality learning content more accessible to you.",
  },
  forms: {
    selectPlaceholder: "Select {{label}}",
  },
  codeEditor: {
    languages: {
      javascript: "JavaScript",
      python: "Python",
      java: "Java",
      cpp: "C++",
      typescript: "TypeScript",
    },
  },
  content: {
    noContentAvailable:
      "No content available. Please check the content type and data.",
  },
  formErrors: {
    missingRequiredFields:
      "Please fill in the following required fields: {{fields}}",
    submitError:
      "An error occurred while submitting the form. Please try again.",
  },
  metadata: {
    title: "Pageflux AI - AI-Powered Learning Platform",
    description:
      "Master programming skills and advance your career with our AI-driven personalized learning platform. Interactive courses, hands-on projects, and expert guidance.",
    processing: "Processing...",
    authCallback: "Authenticating...",
  },
  course: {
    untitledCourse: "Untitled Course",
    difficulty: {
      beginner: "Beginner",
      intermediate: "Intermediate",
      advanced: "Advanced",
    },
  },
};

export default translation;
