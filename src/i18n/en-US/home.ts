const translation = {
  "title": "Pageflux AI - Intelligent Learning Platform",
  "description": "Enhance your skills through AI-driven personalized learning experiences",
  "hero": {
    "title": "Handcrafted Courses for Developers.\nPersonalized by AI.",
    "subtitle": "Our AI-tailored courses and ready-to-run sandboxes handle all the heavy lifting. 💪\nYou just dive in and build something extraordinary. 🚀",
    "badge": "New",
    "card": {
      "category": "Full Loop Interview",
      "title": "The shortest path to interview mastery",
      "description1": "Skip the LeetCode grind with PAL (Personalized Adaptive Learning)",
      "description2": "Full-loop practice with",
      "aiMockInterviews": "AI Mock Interviews",
      "description3": "built into the curriculum"
    },
    "cta": {
      "primary": "Get Started",
      "secondary": "See how it works"
    },
    "companies": {
      "text": "Join",
      "count": "2.7 million",
      "suffix": "developers working at companies like"
    },
    "stats": {
      "learners": "Learners",
      "courses": "Courses",
      "projects": "Projects",
      "instructors": "Instructors"
    }
  },
  "introduce": {
    "title": "Begin Your Learning Journey",
    "subtitle": "From basics to advanced, we provide comprehensive learning resources for you",
    "features": {
      "interactive": {
        "title": "Interactive Learning",
        "description": "Understand concepts deeply through hands-on projects and coding exercises"
      },
      "personalized": {
        "title": "Personalized Paths",
        "description": "AI recommendation system customizes the most suitable learning path for you"
      },
      "community": {
        "title": "Community Support",
        "description": "Connect with global learners, share experiences and solve problems together"
      },
      "certification": {
        "title": "Industry Certification",
        "description": "Earn industry-recognized skill certificates to boost your career competitiveness"
      }
    }
  },
  "courseCategories": {
    "title": "Popular Course Categories",
    "subtitle": "Trending resources on in-demand topics. All recently published and/or updated.",
    "viewAll": "View All",
    "tabs": {
      "mostPopular": "Most Popular Courses",
      "systemDesign": "System Design",
      "interview": "Interview Prep",
      "aws": "AWS",
      "generativeAi": "Generative AI",
      "python": "Python",
      "javascript": "JavaScript"
    },
    "categories": {
      "webDevelopment": {
        "title": "Web Development",
        "description": "Learn modern web development technologies",
        "courses": "Courses"
      },
      "mobileDevelopment": {
        "title": "Mobile Development",
        "description": "iOS and Android app development",
        "courses": "Courses"
      },
      "dataScience": {
        "title": "Data Science",
        "description": "Data analysis and machine learning",
        "courses": "Courses"
      },
      "cloudComputing": {
        "title": "Cloud Computing",
        "description": "AWS, Azure, GCP and other cloud platforms",
        "courses": "Courses"
      },
      "devOps": {
        "title": "DevOps",
        "description": "Automated deployment and operations",
        "courses": "Courses"
      },
      "cybersecurity": {
        "title": "Cybersecurity",
        "description": "Information security and penetration testing",
        "courses": "Courses"
      }
    }
  },
  "aiLearning": {
    "title": "Hands-on Learning Powered by AI",
    "subtitle": "See how Pageflux AI uses AI to make your learning more immersive than ever before.",
    "cta": "Try now for free",
    "features": [
      {
        "title": "Personalized Interview Prep",
        "description": "Skip the LeetCode grind with a custom roadmap that adapts to your goals. Hands-on practice for Coding Interviews, System Design, and more.",
        "buttonText": "Get Your Free Roadmap",
        "buttonVariant": "primary"
      },
      {
        "title": "Mock Interviews",
        "description": "Test your skills in a simulated interview setting. Receive personalized feedback based on your performance. Available for Coding Interviews, System Design, and more.",
        "buttonText": "Try For Free",
        "buttonVariant": "outline"
      },
      {
        "title": "AI Prompt",
        "description": "Build prompt engineering skills. Practice implementing AI-informed solutions.",
        "buttonText": "Learn More",
        "buttonVariant": "outline"
      },
      {
        "title": "Code Feedback",
        "description": "Evaluate and debug your code with the click of a button. Get real-time feedback on test cases, including time and space complexity of your solutions.",
        "buttonText": "Try Now",
        "buttonVariant": "outline"
      },
      {
        "title": "Explain with AI",
        "description": "Select any text within any Pageflux AI course, and get an instant explanation — without ever leaving your browser.",
        "buttonText": "See Demo",
        "buttonVariant": "outline"
      },
      {
        "title": "AI Code Mentor",
        "description": "AI Code Mentor helps you quickly identify errors in your code, learn from your mistakes, and nudge you in the right direction — just like a 1:1 tutor!",
        "buttonText": "Get Started",
        "buttonVariant": "outline"
      }
    ]
  },
  "practice": {
    "title": "Practice and apply your skills",
    "cloudLabs": {
      "category": "Cloud Labs",
      "title": "cloud services with no setup required",
      "titleHighlight": "Payment-free",
      "features": [
        "💻 Payment-free cloud services with no setup or cleanup",
        "⚡ Instant, pain-free access to serverless computing"
      ],
      "buttonText": "Start Cloud Labs"
    },
    "handsonProjects": {
      "category": "Hands-on Projects",
      "title": "projects to learn by doing",
      "titleHighlight": "Real-world",
      "features": [
        "🚀 Build real applications and projects",
        "📱 Complete projects from mobile to web applications"
      ],
      "buttonText": "Browse Projects"
    }
  },
  "learningPaths": {
    "title": "Carefully Designed Learning Paths",
    "subtitle": "Systematic learning solutions from beginner to expert",
    "viewAll": "View All Paths",
    "paths": {
      "frontend": {
        "title": "Frontend Developer",
        "description": "Master HTML, CSS, JavaScript and modern frameworks",
        "duration": "3-6 months",
        "level": "Beginner to Intermediate",
        "skills": "Skills"
      },
      "backend": {
        "title": "Backend Developer",
        "description": "Learn server-side development and database management",
        "duration": "4-8 months",
        "level": "Beginner to Advanced",
        "skills": "Skills"
      },
      "fullstack": {
        "title": "Full Stack Developer",
        "description": "Comprehensive skills for both frontend and backend development",
        "duration": "6-12 months",
        "level": "Intermediate to Advanced",
        "skills": "Skills"
      },
      "dataAnalyst": {
        "title": "Data Analyst",
        "description": "Data processing, analysis and visualization skills",
        "duration": "3-6 months",
        "level": "Beginner to Intermediate",
        "skills": "Skills"
      }
    }
  },
  "testimonials": {
    "title": "Real Student Reviews",
    "subtitle": "Hear success stories from our students",
    "reviews": {
      "student1": {
        "name": "John Smith",
        "role": "Frontend Developer",
        "company": "Google",
        "content": "Through Pageflux AI's learning, I successfully transitioned to become a frontend developer. The course content is practical with rich project experience.",
        "rating": "5-star rating"
      },
      "student2": {
        "name": "Sarah Johnson",
        "role": "Data Scientist",
        "company": "Microsoft",
        "content": "The AI recommendation system helped me find the most suitable learning path, and now I'm working in the data science field.",
        "rating": "5-star rating"
      },
      "student3": {
        "name": "Mike Chen",
        "role": "Full Stack Developer",
        "company": "Amazon",
        "content": "From zero foundation to full stack development, Pageflux AI accompanied me throughout my learning journey. Thank you so much!",
        "rating": "5-star rating"
      }
    }
  },
  "freeCourses": {
    "title": "Featured Free Courses",
    "subtitle": "High-quality free learning resources to help you get started",
    "viewAll": "View More Free Courses",
    "courses": {
      "htmlBasics": {
        "title": "HTML Basics",
        "description": "Learn the fundamentals of web page structure",
        "duration": "2 hours",
        "students": "Students",
        "rating": "Rating"
      },
      "javascriptFundamentals": {
        "title": "JavaScript Fundamentals",
        "description": "Master the basics of JavaScript programming",
        "duration": "4 hours",
        "students": "Students",
        "rating": "Rating"
      },
      "pythonIntro": {
        "title": "Python Introduction",
        "description": "Python programming language basics tutorial",
        "duration": "3 hours",
        "students": "Students",
        "rating": "Rating"
      }
    }
  },
  "teamSkillsPromotion": {
    "badge": "DEVPATH",
    "title": "Level up your team's skills",
    "titleHighlight": "team's",
    "description": "Leverage Pageflux AI courses and internal knowledge to onboard, upskill, and train your developers at scale with DevPath.",
    "buttonText": "Visit Devpath",
    "cta": {
      "primary": "Contact Us",
      "secondary": "Learn More"
    },
    "features": {
      "customizedTraining": {
        "title": "Customized Training",
        "description": "Design exclusive training programs based on team needs"
      },
      "progressTracking": {
        "title": "Progress Tracking",
        "description": "Real-time monitoring of team members' learning progress and results"
      },
      "expertSupport": {
        "title": "Expert Support",
        "description": "Senior technical experts provide one-on-one guidance"
      },
      "certification": {
        "title": "Team Certification",
        "description": "Obtain team skill certification to enhance overall competitiveness"
      }
    }
  },
  "joinDevelopersGlobally": {
    "title": "Join more than",
    "titleHighlight": "2.7 million",
    "titleSuffix": "developers globally",
    "stats": [
      {
        "value": "1500+",
        "label": "Interactive Courses"
      },
      {
        "value": "300+",
        "label": "Real Projects"
      },
      {
        "value": "#1",
        "label": "in Interview Prep"
      }
    ],
    "cta": {
      "primary": "Join Now",
      "secondary": "Learn About Community"
    },
    "benefits": {
      "networking": {
        "title": "Global Network",
        "description": "Connect with developers from around the world"
      },
      "collaboration": {
        "title": "Collaborative Projects",
        "description": "Participate in open source projects and team collaboration"
      },
      "mentorship": {
        "title": "Mentorship",
        "description": "Get guidance from experienced developers"
      },
      "opportunities": {
        "title": "Career Opportunities",
        "description": "Discover global job and collaboration opportunities"
      }
    }
  }
}

export default translation
