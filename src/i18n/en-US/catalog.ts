/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-08-11 21:09:45
 */
const translation = {
  popular: {
    title: "Popular subjects & languages",
  },
  more: {
    title: "Find more subjects & languages",
  },
  hero: {
    coursesAndGuides: "courses and guides",
    startFreeTrial: "Start Your Free Trial",
    exploreAll: "Explore All",
  },
  defaults: {
    categoryName: "Programming Language",
    categoryDescription:
      "Learn programming fundamentals and advanced concepts.",
  },
  languages: {
    python: {
      name: "Python",
      description:
        "Python is one of the most popular programming languages in the world, known for its simplicity and readable syntax.",
    },
    javascript: {
      name: "JavaScript",
      description:
        "JavaScript is the programming language of the web, enabling interactive and dynamic web applications.",
    },
    java: {
      name: "Java",
      description:
        "Java is a robust, object-oriented programming language used for enterprise applications and Android development.",
    },
  },
  categories: {
    allCategories: "All Categories",
    programmingFundamentals: "Programming Fundamentals",
    interviewPrep: "Interview Prep",
    systemDesign: "System Design",
    machineLearning: "Machine Learning",
    aiTools: "AI Tools",
    webDevelopment: "Web Development",
    dataScience: "Data Science",
    frontendFrameworks: "Frontend Frameworks",
    backendDevelopment: "Backend Development",
    objectOrientedProgramming: "Object-Oriented Programming",
    springFramework: "Spring Framework",
    enterpriseDevelopment: "Enterprise Development",
    androidDevelopment: "Android Development",
    microservices: "Microservices",
    advancedProgramming: "Advanced Programming",
  },
};

export default translation;
