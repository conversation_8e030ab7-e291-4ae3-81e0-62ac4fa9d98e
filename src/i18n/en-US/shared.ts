const translation = {
  faq: {
    title: "Frequently Asked Questions",
    subtitle: "Find the answers you need",
    items: [
      {
        question: "What is Pageflux AI?",
        answer:
          "Pageflux AI is an AI-powered online learning platform that provides programming courses, hands-on projects, and skill assessments.",
      },
      {
        question: "How do I get started?",
        answer:
          "You can sign up for a free account and browse our course catalog to choose courses that match your skill level.",
      },
      {
        question: "Are the courses free?",
        answer:
          "We offer both free and paid courses. Free users can access basic content, while paid users get the complete learning experience.",
      },
      {
        question: "Do you provide certificates?",
        answer:
          "Yes, you'll receive a certificate of completion after finishing a course, which you can add to your resume or LinkedIn profile.",
      },
      {
        question: "How can I contact support?",
        answer:
          "You can reach our support team via <NAME_EMAIL> or through our live chat feature.",
      },
    ],
  },
  testimonials: {
    title: "Student Reviews",
    subtitle: "See what other learners are saying",
    items: [
      {
        name: "<PERSON>",
        role: "Frontend Developer",
        company: "Google",
        content:
          "Pageflux AI's course quality is excellent. The hands-on projects helped me quickly improve my programming skills.",
        rating: 5,
      },
      {
        name: "<PERSON>",
        role: "Full Stack Developer",
        company: "Microsoft",
        content:
          "The AI-assisted learning feature is amazing. It adapts the content difficulty based on my learning progress.",
        rating: 5,
      },
      {
        name: "Mike <PERSON>",
        role: "Data Scientist",
        company: "Amazon",
        content:
          "The cloud lab environment is very convenient. I can start coding practice without any local setup.",
        rating: 5,
      },
    ],
  },
  aiLearning: {
    title: "Hands-on Learning Powered by AI",
    subtitle:
      "See how Pageflux AI uses AI to make your learning more immersive than ever before.",
    ctaButton: "Try now for free",
    features: [
      {
        title: "Instant Code Feedback",
        description:
          "Evaluate and debug your code with the click of a button. Get real-time feedback on test cases, including time and space complexity of your solutions.",
        buttonText: "Try Now",
        buttonVariant: "outline",
      },
      {
        title: "AI Mock Interviews",
        description:
          "Test your skills in a simulated interview setting. Receive personalized feedback based on your performance. Available for Coding Interviews, System Design, and more.",
        buttonText: "Try For Free",
        buttonVariant: "primary",
      },
    ],
  },
};

export default translation;
