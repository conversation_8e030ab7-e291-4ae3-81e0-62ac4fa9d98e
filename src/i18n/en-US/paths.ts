const translation = {
  "title": "Skill Paths",
  "description": "Carefully designed systematic learning solutions to help you progress from beginner to expert",
  "hero": {
    "title": "Skill Paths",
    "subtitle": "All our Skill Paths are carefully curated to help you achieve a specific learning goal. Find the perfect Skill Path for your needs here.",
    "cta": {
      "primary": "Start Learning",
      "secondary": "Browse Paths"
    },
    "stats": {
      "totalPaths": "Learning Paths",
      "completedPaths": "Completed Paths",
      "activeStudents": "Active Students",
      "averageCompletion": "Average Completion Rate"
    }
  },
  "search": {
    "placeholder": "Search skill paths here",
    "button": "Search",
    "clear": "Clear",
    "noResults": "No relevant skill paths found",
    "resultsCount": "Found {{count}} skill paths"
  },
  "filters": {
    "title": "Filter Options",
    "clear": "Clear Filters",
    "apply": "Apply Filters",
    "skillLevel": {
      "title": "Skill Level",
      "beginner": "Beginner",
      "intermediate": "Intermediate",
      "advanced": "Advanced"
    },
    "duration": {
      "title": "Learning Duration",
      "short": "Short (1-3 months)",
      "medium": "Medium (3-6 months)",
      "long": "Long (6+ months)"
    },
    "topics": {
      "title": "Learning Topics",
      "webDevelopment": "Web Development",
      "mobileDevelopment": "Mobile Development",
      "dataScience": "Data Science",
      "machineLearning": "Machine Learning",
      "artificialIntelligence": "Artificial Intelligence",
      "cloudComputing": "Cloud Computing",
      "devOps": "DevOps",
      "cybersecurity": "Cybersecurity",
      "blockchain": "Blockchain",
      "gamesDevelopment": "Game Development",
      "uiUxDesign": "UI/UX Design",
      "digitalMarketing": "Digital Marketing",
      "projectManagement": "Project Management",
      "businessAnalysis": "Business Analysis",
      "qualityAssurance": "Quality Assurance"
    },
    "technologies": {
      "title": "Technology Stack",
      "javascript": "JavaScript",
      "typescript": "TypeScript",
      "python": "Python",
      "java": "Java",
      "csharp": "C#",
      "cpp": "C++",
      "go": "Go",
      "rust": "Rust",
      "php": "PHP",
      "ruby": "Ruby",
      "swift": "Swift",
      "kotlin": "Kotlin",
      "react": "React",
      "vue": "Vue.js",
      "angular": "Angular",
      "nodejs": "Node.js",
      "django": "Django",
      "flask": "Flask",
      "spring": "Spring",
      "dotnet": ".NET",
      "laravel": "Laravel",
      "rails": "Ruby on Rails",
      "mysql": "MySQL",
      "postgresql": "PostgreSQL",
      "mongodb": "MongoDB",
      "redis": "Redis",
      "aws": "AWS",
      "azure": "Azure",
      "gcp": "Google Cloud",
      "docker": "Docker",
      "kubernetes": "Kubernetes",
      "tensorflow": "TensorFlow",
      "pytorch": "PyTorch",
      "scikit": "Scikit-learn",
      "pandas": "Pandas",
      "numpy": "NumPy"
    },
    "careerGoal": {
      "title": "Career Goal",
      "frontendDeveloper": "Frontend Developer",
      "backendDeveloper": "Backend Developer",
      "fullstackDeveloper": "Full Stack Developer",
      "mobileAppDeveloper": "Mobile App Developer",
      "dataScientist": "Data Scientist",
      "dataAnalyst": "Data Analyst",
      "mlEngineer": "ML Engineer",
      "devOpsEngineer": "DevOps Engineer",
      "cloudArchitect": "Cloud Architect",
      "cybersecuritySpecialist": "Cybersecurity Specialist",
      "blockchainDeveloper": "Blockchain Developer",
      "gameDeveloper": "Game Developer",
      "uiUxDesigner": "UI/UX Designer",
      "productManager": "Product Manager",
      "projectManager": "Project Manager",
      "businessAnalyst": "Business Analyst",
      "qaEngineer": "QA Engineer",
      "systemAdministrator": "System Administrator",
      "databaseAdministrator": "Database Administrator",
      "softwareArchitect": "Software Architect"
    },
    "industry": {
      "title": "Industry Domain",
      "technology": "Technology",
      "finance": "Finance",
      "healthcare": "Healthcare",
      "education": "Education",
      "ecommerce": "E-commerce",
      "entertainment": "Entertainment",
      "gaming": "Gaming",
      "automotive": "Automotive",
      "manufacturing": "Manufacturing",
      "retail": "Retail",
      "consulting": "Consulting",
      "government": "Government",
      "nonprofit": "Non-profit",
      "startup": "Startup",
      "enterprise": "Enterprise"
    }
  },
  "sort": {
    "title": "Sort By",
    "relevance": "Relevance",
    "popularity": "Popularity",
    "rating": "Rating",
    "duration": "Duration",
    "difficulty": "Difficulty",
    "newest": "Newest",
    "completions": "Completions",
    "enrollments": "Enrollments"
  },
  "pathCard": {
    "new": "New",
    "popular": "Popular",
    "featured": "Featured",
    "updated": "Updated",
    "bestseller": "Bestseller",
    "duration": "Duration",
    "courses": "Courses",
    "projects": "Projects",
    "skills": "Skills",
    "level": "Level",
    "students": "Students",
    "rating": "Rating",
    "reviews": "Reviews",
    "completion": "Completion Rate",
    "certificate": "Certificate",
    "status": {
      "notStarted": "Not Started",
      "inProgress": "In Progress",
      "completed": "Completed",
      "paused": "Paused"
    },
    "startPath": "Start Learning",
    "continuePath": "Continue Learning",
    "viewPath": "View Path",
    "preview": "Preview",
    "bookmark": "Bookmark",
    "share": "Share",
    "enroll": "Enroll Now",
    "types": {
      "guided": "Guided",
      "selfPaced": "Self-paced",
      "mentored": "Mentored",
      "bootcamp": "Bootcamp",
      "specialization": "Specialization",
      "certification": "Certification"
    }
  },
  "pathDetails": {
    "tabs": {
      "overview": "Path Overview",
      "curriculum": "Curriculum",
      "projects": "Projects",
      "skills": "Skills Tree",
      "instructors": "Instructors",
      "reviews": "Reviews",
      "faq": "FAQ"
    },
    "overview": {
      "title": "Learning Path Overview",
      "description": "Description",
      "objectives": "Learning Objectives",
      "outcomes": "Learning Outcomes",
      "prerequisites": "Prerequisites",
      "targetAudience": "Target Audience",
      "careerOutcomes": "Career Outcomes",
      "estimatedTime": "Estimated Time",
      "difficulty": "Difficulty Level",
      "format": "Learning Format",
      "support": "Learning Support",
      "certificate": "Certificate"
    },
    "curriculum": {
      "title": "Curriculum",
      "totalCourses": "Total Courses",
      "totalHours": "Total Hours",
      "modules": "Learning Modules",
      "expandAll": "Expand All",
      "collapseAll": "Collapse All",
      "module": {
        "courses": "Courses",
        "hours": "Hours",
        "projects": "Projects",
        "quizzes": "Quizzes",
        "assignments": "Assignments",
        "optional": "Optional",
        "required": "Required",
        "prerequisite": "Prerequisite"
      }
    },
    "projects": {
      "title": "Hands-on Projects",
      "totalProjects": "Total Projects",
      "capstoneProject": "Capstone Project",
      "portfolioProjects": "Portfolio Projects",
      "project": {
        "duration": "Duration",
        "difficulty": "Difficulty",
        "technologies": "Technologies",
        "skills": "Skills Applied",
        "deliverables": "Deliverables",
        "mentorship": "Mentorship"
      }
    },
    "skills": {
      "title": "Skills Tree",
      "coreSkills": "Core Skills",
      "technicalSkills": "Technical Skills",
      "softSkills": "Soft Skills",
      "industrySkills": "Industry Skills",
      "levels": {
        "beginner": "Beginner",
        "intermediate": "Intermediate",
        "advanced": "Advanced",
        "expert": "Expert"
      },
      "status": {
        "notStarted": "Not Started",
        "learning": "Learning",
        "practiced": "Practiced",
        "mastered": "Mastered"
      }
    },
    "instructors": {
      "title": "Instructor Team",
      "leadInstructor": "Lead Instructor",
      "instructorTeam": "Instructor Team",
      "guestExperts": "Guest Experts",
      "instructor": {
        "experience": "Teaching Experience",
        "students": "Students Taught",
        "courses": "Courses Created",
        "rating": "Instructor Rating",
        "expertise": "Areas of Expertise",
        "background": "Educational Background",
        "achievements": "Key Achievements"
      }
    },
    "reviews": {
      "title": "Student Reviews",
      "averageRating": "Average Rating",
      "totalReviews": "Total Reviews",
      "ratingBreakdown": "Rating Breakdown",
      "mostHelpful": "Most Helpful",
      "recent": "Recent Reviews",
      "verified": "Verified Student",
      "dimensions": {
        "content": "Content Quality",
        "instruction": "Instruction Quality",
        "support": "Learning Support",
        "value": "Value for Money",
        "career": "Career Help"
      }
    },
    "faq": {
      "title": "Frequently Asked Questions",
      "general": "General Questions",
      "enrollment": "Enrollment",
      "learning": "Learning",
      "technical": "Technical",
      "certificate": "Certificate",
      "career": "Career Development"
    }
  },
  "progress": {
    "title": "Learning Progress",
    "overall": "Overall Progress",
    "currentModule": "Current Module",
    "nextModule": "Next Module",
    "completed": "Completed",
    "inProgress": "In Progress",
    "upcoming": "Upcoming",
    "stats": {
      "coursesCompleted": "Courses Completed",
      "projectsSubmitted": "Projects Submitted",
      "skillsLearned": "Skills Learned",
      "hoursSpent": "Hours Spent",
      "streak": "Learning Streak",
      "achievements": "Achievements Earned"
    },
    "milestones": {
      "title": "Learning Milestones",
      "completed": "Completed Milestones",
      "current": "Current Milestone",
      "upcoming": "Upcoming Milestones"
    }
  },
  "certification": {
    "title": "Certification",
    "pathCertificate": "Path Completion Certificate",
    "skillBadges": "Skill Badges",
    "industryRecognition": "Industry Recognition",
    "types": {
      "completion": "Completion Certificate",
      "proficiency": "Proficiency Certificate",
      "specialization": "Specialization Certificate",
      "professional": "Professional Certificate"
    },
    "requirements": {
      "courseCompletion": "Complete all courses",
      "projectSubmission": "Submit all projects",
      "skillAssessment": "Pass skill assessments",
      "finalExam": "Pass final exam",
      "peerReview": "Complete peer reviews"
    }
  },
  "messages": {
    "pathStarted": "Learning path started, happy learning!",
    "progressSaved": "Learning progress saved",
    "moduleCompleted": "Module completed",
    "pathCompleted": "Congratulations! You've completed the entire learning path",
    "certificateEarned": "Congratulations on earning your certificate!",
    "bookmarkAdded": "Added to bookmarks",
    "bookmarkRemoved": "Removed from bookmarks",
    "shareSuccess": "Share link copied",
    "enrollmentSuccess": "Enrollment successful",
    "enrollmentError": "Enrollment failed, please try again"
  },
  "empty": {
    "noPaths": "No skill paths available",
    "noResults": "No matching skill paths found",
    "noProgress": "Haven't started any learning paths yet",
    "noBookmarks": "No bookmarked paths",
    "suggestion": "We suggest you:",
    "suggestions": {
      "adjustFilters": "Adjust filter criteria",
      "browseCategories": "Browse different categories",
      "searchDifferent": "Try different search terms",
      "startBeginner": "Start with beginner paths",
      "explorePopular": "Explore popular paths"
    }
  }
}

export default translation
