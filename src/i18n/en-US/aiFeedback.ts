const translation = {
  "steps": {
    "communication": {
      "title": "Communication Analysis",
      "description": "Analyzing clarity, structure, and articulation of responses"
    },
    "technical": {
      "title": "Technical Knowledge",
      "description": "Evaluating depth of technical understanding and accuracy"
    },
    "problemSolving": {
      "title": "Problem-Solving Approach",
      "description": "Assessing methodology, logical thinking, and solution quality"
    },
    "codeQuality": {
      "title": "Code Quality",
      "description": "Reviewing code structure, efficiency, and best practices"
    },
    "overall": {
      "title": "Overall Assessment",
      "description": "Generating comprehensive feedback and recommendations"
    }
  },
  "status": {
    "analyzing": "Analyzing...",
    "complete": "Analysis Complete",
    "pending": "Pending"
  },
  "feedback": {
    "title": "AI Feedback",
    "score": "Score",
    "insights": "Insights",
    "recommendations": "Recommendations",
    "strengths": "Strengths",
    "improvements": "Areas for Improvement"
  },
  "buttons": {
    "generateFeedback": "Generate Feedback",
    "regenerate": "Regenerate",
    "viewDetails": "View Details"
  }
}

export default translation
