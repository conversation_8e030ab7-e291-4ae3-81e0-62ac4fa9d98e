const translation = {
  "codeEditor": {
    "title": "Code Editor",
    "placeholder": "Write your code here...",
    "run": "Run",
    "reset": "Reset",
    "copy": "Copy",
    "paste": "Paste",
    "undo": "Undo",
    "redo": "Redo",
    "format": "Format",
    "fullscreen": "Fullscreen",
    "exitFullscreen": "Exit Fullscreen"
  },
  "output": {
    "title": "Output",
    "console": "Console",
    "result": "Result",
    "error": "Error",
    "warning": "Warning",
    "clear": "Clear",
    "noOutput": "No output",
    "running": "Running...",
    "success": "Run successful",
    "failed": "Run failed"
  },
  "language": {
    "title": "Programming Language",
    "select": "Select Language",
    "javascript": "JavaScript",
    "python": "Python",
    "java": "Java",
    "cpp": "C++",
    "csharp": "C#",
    "go": "Go",
    "rust": "Rust",
    "php": "PHP",
    "ruby": "<PERSON>",
    "swift": "Swift",
    "kotlin": "Kotlin",
    "typescript": "TypeScript"
  },
  "settings": {
    "title": "Settings",
    "theme": "Theme",
    "fontSize": "Font Size",
    "tabSize": "Tab Size",
    "wordWrap": "Word Wrap",
    "minimap": "Minimap",
    "lineNumbers": "Line Numbers",
    "autoSave": "Auto Save",
    "autoComplete": "Auto Complete"
  },
  "themes": {
    "light": "Light",
    "dark": "Dark",
    "highContrast": "High Contrast"
  },
  "shortcuts": {
    "title": "Shortcuts",
    "run": "Ctrl+Enter",
    "save": "Ctrl+S",
    "format": "Shift+Alt+F",
    "comment": "Ctrl+/",
    "find": "Ctrl+F",
    "replace": "Ctrl+H"
  },
  "file": {
    "new": "New",
    "open": "Open",
    "save": "Save",
    "saveAs": "Save As",
    "export": "Export",
    "import": "Import",
    "delete": "Delete",
    "rename": "Rename"
  },
  "messages": {
    "codeEmpty": "Please enter code",
    "runSuccess": "Code executed successfully",
    "runError": "Code execution failed",
    "saveSuccess": "Saved successfully",
    "saveError": "Save failed",
    "copySuccess": "Copied successfully",
    "pasteSuccess": "Pasted successfully",
    "formatSuccess": "Formatted successfully",
    "resetConfirm": "Are you sure you want to reset the code?"
  }
}

export default translation
