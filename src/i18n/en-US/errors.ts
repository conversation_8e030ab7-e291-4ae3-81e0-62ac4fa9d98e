const translation = {
  "common": {
    "operationFailed": "Operation failed",
    "unknownError": "Unknown error",
    "networkError": "Network error",
    "timeout": "Request timeout",
    "permissionDenied": "Permission denied",
    "invalidInput": "Invalid input",
    "dataNotFound": "Data not found",
    "systemError": "System error"
  },
  "api": {
    "badRequest": "Bad request parameters",
    "unauthorized": "Unauthorized access",
    "forbidden": "Insufficient permissions",
    "notFound": "Resource not found",
    "conflict": "Resource conflict",
    "validationFailed": "Data validation failed",
    "tooManyRequests": "Too many requests, please try again later",
    "serverError": "Internal server error",
    "badGateway": "Bad gateway",
    "serviceUnavailable": "Service temporarily unavailable",
    "timeout": "Request timeout",
    "requestTimeout": "Request timeout, please try again",
    "networkError": "Network connection failed, please check your connection",
    "serverErrorWithCode": "Server error ({{code}})"
  },
  "auth": {
    "loginFailed": "Login failed",
    "invalidCredentials": "Invalid username or password",
    "accountLocked": "Account has been locked",
    "sessionExpired": "Session expired, please login again",
    "tokenInvalid": "Invalid token",
    "accessDenied": "Access denied",
    "accountNotFound": "Account not found",
    "emailNotVerified": "Email not verified",
    "passwordExpired": "Password expired"
  },
  "validation": {
    "required": "This field is required",
    "invalidEmail": "Please enter a valid email address",
    "invalidPhone": "Please enter a valid phone number",
    "passwordTooShort": "Password must be at least 6 characters",
    "passwordTooWeak": "Password is too weak",
    "passwordMismatch": "Passwords do not match",
    "invalidUrl": "Please enter a valid URL",
    "invalidDate": "Please enter a valid date",
    "fileTooLarge": "File size exceeds limit",
    "invalidFileType": "Unsupported file type",
    "minLength": "Must be at least {{min}} characters",
    "maxLength": "Must not exceed {{max}} characters"
  },
  "business": {
    "courseNotFound": "Course not found",
    "courseNotAvailable": "Course not available",
    "enrollmentFailed": "Enrollment failed",
    "paymentFailed": "Payment failed",
    "subscriptionExpired": "Subscription expired",
    "quotaExceeded": "Quota exceeded",
    "featureNotAvailable": "Feature not available",
    "maintenanceMode": "System under maintenance"
  },
  "upload": {
    "failed": "File upload failed",
    "tooLarge": "File size exceeds limit",
    "invalidType": "Unsupported file type",
    "networkError": "Network error, upload failed",
    "serverError": "Server error, upload failed"
  },
  "page": {
    "notFound": "Page not found",
    "serverError": "Server error",
    "maintenance": "System under maintenance",
    "accessDenied": "Access denied",
    "loadFailed": "Page load failed"
  },
  "operation": {
    "saveFailed": "Save failed",
    "deleteFailed": "Delete failed",
    "updateFailed": "Update failed",
    "createFailed": "Create failed",
    "loadFailed": "Load failed",
    "exportFailed": "Export failed",
    "importFailed": "Import failed"
  }
}

export default translation
