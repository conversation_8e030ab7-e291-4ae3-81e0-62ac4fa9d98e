const translation = {
  "title": "Course Center",
  "description": "Explore our rich course library and find the learning content that suits you",
  "search": {
    "placeholder": "Search courses, skills or topics...",
    "button": "Search",
    "clear": "Clear",
    "noResults": "No relevant courses found",
    "resultsCount": "Found {{count}} courses"
  },
  "filters": {
    "title": "Filter Options",
    "clear": "Clear Filters",
    "apply": "Apply Filters",
    "newOnly": "New Only",
    "level": {
      "title": "Difficulty Level",
      "beginner": "Beginner",
      "intermediate": "Intermediate",
      "advanced": "Advanced"
    },
    "duration": {
      "title": "Course Duration",
      "short": "Short (< 5 hours)",
      "medium": "Medium (5-20 hours)",
      "long": "Long (> 20 hours)"
    },
    "price": {
      "title": "Price",
      "free": "Free",
      "paid": "Paid",
      "premium": "Premium"
    },
    "category": {
      "title": "Category",
      "webDevelopment": "Web Development",
      "mobileDevelopment": "Mobile Development",
      "dataScience": "Data Science",
      "cloudComputing": "Cloud Computing",
      "devOps": "DevOps",
      "cybersecurity": "Cybersecurity",
      "artificialIntelligence": "Artificial Intelligence",
      "blockchain": "Blockchain",
      "gamesDevelopment": "Game Development",
      "uiUxDesign": "UI/UX Design"
    },
    "technology": {
      "title": "Technology Stack",
      "javascript": "JavaScript",
      "python": "Python",
      "java": "Java",
      "csharp": "C#",
      "cpp": "C++",
      "react": "React",
      "vue": "Vue.js",
      "angular": "Angular",
      "nodejs": "Node.js",
      "django": "Django",
      "spring": "Spring",
      "dotnet": ".NET",
      "aws": "AWS",
      "azure": "Azure",
      "docker": "Docker",
      "kubernetes": "Kubernetes"
    },
    "instructor": {
      "title": "Instructor",
      "topRated": "Top Rated",
      "certified": "Certified",
      "industry": "Industry Expert"
    }
  },
  "sort": {
    "title": "Sort By",
    "relevance": "Relevance",
    "popularity": "Popularity",
    "rating": "Rating",
    "newest": "Newest",
    "priceAsc": "Price: Low to High",
    "priceDesc": "Price: High to Low",
    "duration": "Duration"
  },
  "tabs": {
    "all": "All",
    "courses": "Courses",
    "cloudLabs": "Cloud Labs",
    "projects": "Projects",
    "paths": "Learning Paths",
    "assessments": "Assessments",
    "mockInterviews": "Mock Interviews"
  },
  "courseCard": {
    "free": "Free",
    "premium": "Premium",
    "bestseller": "Bestseller",
    "new": "New",
    "updated": "Updated",
    "duration": "Duration",
    "lessons": "Lessons",
    "students": "Students",
    "rating": "Rating",
    "reviews": "Reviews",
    "level": "Level",
    "instructor": "Instructor",
    "language": "Language",
    "subtitles": "Subtitles",
    "certificate": "Certificate",
    "lifetime": "Lifetime Access",
    "mobile": "Mobile Access",
    "preview": "Preview",
    "enroll": "Enroll Now",
    "addToCart": "Add to Cart",
    "addToWishlist": "Add to Wishlist",
    "share": "Share",
    "lastUpdated": "Last Updated",
    "whatYouWillLearn": "What You'll Learn",
    "requirements": "Requirements",
    "description": "Description",
    "curriculum": "Curriculum",
    "instructor_info": "Instructor",
    "reviews_section": "Reviews"
  },
  "courseDetails": {
    "overview": "Overview",
    "curriculum": "Curriculum",
    "instructor": "Instructor",
    "reviews": "Reviews",
    "faq": "FAQ",
    "info": {
      "duration": "Total Duration",
      "lessons": "Number of Lessons",
      "level": "Difficulty Level",
      "language": "Course Language",
      "subtitles": "Subtitle Languages",
      "certificate": "Certificate of Completion",
      "access": "Access Rights",
      "updates": "Content Updates",
      "support": "Learning Support"
    },
    "objectives": {
      "title": "Learning Objectives",
      "subtitle": "After completing this course, you will be able to:"
    },
    "requirements": {
      "title": "Requirements",
      "subtitle": "Before starting, you need:"
    },
    "audience": {
      "title": "Target Audience",
      "subtitle": "This course is suitable for:"
    },
    "syllabus": {
      "title": "Course Syllabus",
      "sections": "Sections",
      "lectures": "Lectures",
      "totalDuration": "Total Duration",
      "expandAll": "Expand All",
      "collapseAll": "Collapse All",
      "preview": "Preview",
      "quiz": "Quiz",
      "assignment": "Assignment",
      "project": "Project",
      "resource": "Resource"
    },
    "instructorInfo": {
      "title": "About the Instructor",
      "rating": "Instructor Rating",
      "students": "Students Taught",
      "courses": "Courses Created",
      "reviews": "Reviews Received",
      "experience": "Teaching Experience",
      "expertise": "Areas of Expertise",
      "bio": "Biography",
      "socialLinks": "Social Media"
    },
    "reviewsSection": {
      "title": "Student Reviews",
      "averageRating": "Average Rating",
      "totalReviews": "Total Reviews",
      "ratingBreakdown": "Rating Breakdown",
      "mostHelpful": "Most Helpful",
      "recent": "Recent Reviews",
      "writeReview": "Write a Review",
      "helpful": "Helpful",
      "notHelpful": "Not Helpful",
      "report": "Report",
      "verified": "Verified Purchase"
    },
    "faqSection": {
      "title": "Frequently Asked Questions",
      "general": "General Questions",
      "technical": "Technical Questions",
      "payment": "Payment Questions",
      "certificate": "Certificate Questions"
    }
  },
  "enrollment": {
    "price": "Price",
    "originalPrice": "Original Price",
    "discount": "Discount",
    "coupon": "Coupon",
    "applyCoupon": "Apply Coupon",
    "total": "Total",
    "enrollNow": "Enroll Now",
    "addToCart": "Add to Cart",
    "buyNow": "Buy Now",
    "gift": "Gift This Course",
    "payment": {
      "title": "Payment Methods",
      "creditCard": "Credit Card",
      "paypal": "PayPal",
      "alipay": "Alipay",
      "wechatPay": "WeChat Pay",
      "bankTransfer": "Bank Transfer"
    },
    "guarantee": {
      "title": "Learning Guarantee",
      "moneyBack": "30-Day Money Back Guarantee",
      "lifetimeAccess": "Lifetime Access",
      "mobileAccess": "Mobile Learning",
      "certificate": "Certificate of Completion",
      "support": "Learning Support"
    }
  },
  "progress": {
    "title": "Learning Progress",
    "completed": "Completed",
    "inProgress": "In Progress",
    "notStarted": "Not Started",
    "percentage": "Completion Rate",
    "timeSpent": "Time Spent",
    "lastAccessed": "Last Accessed",
    "continue": "Continue Learning",
    "restart": "Restart",
    "certificate": "Get Certificate"
  },
  "messages": {
    "enrollSuccess": "Enrollment successful! Welcome to start learning",
    "enrollError": "Enrollment failed, please try again",
    "addToCartSuccess": "Added to cart",
    "addToWishlistSuccess": "Added to wishlist",
    "removeFromWishlistSuccess": "Removed from wishlist",
    "shareSuccess": "Share link copied",
    "reviewSubmitted": "Review submitted successfully",
    "couponApplied": "Coupon applied successfully",
    "couponInvalid": "Coupon is invalid or expired",
    "paymentSuccess": "Payment successful",
    "paymentError": "Payment failed, please try again"
  },
  "empty": {
    "noCourses": "No courses available",
    "noResults": "No matching courses found",
    "noReviews": "No reviews yet",
    "noProgress": "Haven't started learning yet",
    "suggestion": "We suggest you:",
    "suggestions": {
      "adjustFilters": "Adjust filter criteria",
      "browseCategories": "Browse other categories",
      "searchDifferent": "Try different search terms",
      "checkSpelling": "Check spelling"
    }
  }
}

export default translation
