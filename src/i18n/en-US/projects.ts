const translation = {
  "title": "Hands-on Projects",
  "description": "Enhance your practical skills and project experience through real projects",
  "hero": {
    "title": "Learn by Doing, Master Real Skills",
    "subtitle": "Transform theoretical knowledge into practical abilities through real project challenges",
    "cta": {
      "primary": "Start Project",
      "secondary": "Browse Projects"
    },
    "stats": {
      "totalProjects": "Projects",
      "completedProjects": "Completed Projects",
      "activeUsers": "Active Users",
      "successRate": "Success Rate"
    }
  },
  "search": {
    "placeholder": "Search project names, tech stack or keywords...",
    "button": "Search",
    "clear": "Clear",
    "noResults": "No relevant projects found",
    "resultsCount": "Found {{count}} projects"
  },
  "filters": {
    "title": "Filter Options",
    "clear": "Clear Filters",
    "apply": "Apply Filters",
    "skillLevel": {
      "title": "Skill Level",
      "beginner": "Beginner",
      "intermediate": "Intermediate",
      "advanced": "Advanced"
    },
    "duration": {
      "title": "Project Duration",
      "short": "Short (1-7 days)",
      "medium": "Medium (1-4 weeks)",
      "long": "Long (1+ months)"
    },
    "difficulty": {
      "title": "Difficulty Level",
      "easy": "Easy",
      "medium": "Medium",
      "hard": "Hard",
      "expert": "Expert"
    },
    "topics": {
      "title": "Project Topics",
      "webApp": "Web Application",
      "mobileApp": "Mobile Application",
      "api": "API Development",
      "database": "Database",
      "frontend": "Frontend Development",
      "backend": "Backend Development",
      "fullstack": "Full Stack Development",
      "dataAnalysis": "Data Analysis",
      "machineLearning": "Machine Learning",
      "devOps": "DevOps",
      "cloudComputing": "Cloud Computing",
      "blockchain": "Blockchain",
      "iot": "Internet of Things",
      "gamesDevelopment": "Game Development",
      "cybersecurity": "Cybersecurity"
    },
    "technologies": {
      "title": "Technology Stack",
      "javascript": "JavaScript",
      "typescript": "TypeScript",
      "python": "Python",
      "java": "Java",
      "csharp": "C#",
      "cpp": "C++",
      "go": "Go",
      "rust": "Rust",
      "php": "PHP",
      "ruby": "Ruby",
      "swift": "Swift",
      "kotlin": "Kotlin",
      "react": "React",
      "vue": "Vue.js",
      "angular": "Angular",
      "nodejs": "Node.js",
      "express": "Express.js",
      "django": "Django",
      "flask": "Flask",
      "spring": "Spring",
      "dotnet": ".NET",
      "laravel": "Laravel",
      "rails": "Ruby on Rails",
      "mysql": "MySQL",
      "postgresql": "PostgreSQL",
      "mongodb": "MongoDB",
      "redis": "Redis",
      "aws": "AWS",
      "azure": "Azure",
      "gcp": "Google Cloud",
      "docker": "Docker",
      "kubernetes": "Kubernetes",
      "git": "Git",
      "jenkins": "Jenkins",
      "terraform": "Terraform"
    },
    "projectType": {
      "title": "Project Type",
      "guided": "Guided Project",
      "challenge": "Challenge Project",
      "openEnded": "Open-ended Project",
      "portfolio": "Portfolio Project",
      "realWorld": "Real-world Project",
      "collaborative": "Collaborative Project"
    },
    "industry": {
      "title": "Industry Domain",
      "ecommerce": "E-commerce",
      "fintech": "Fintech",
      "healthcare": "Healthcare",
      "education": "Education",
      "entertainment": "Entertainment",
      "socialMedia": "Social Media",
      "productivity": "Productivity Tools",
      "travel": "Travel",
      "food": "Food & Dining",
      "fitness": "Fitness",
      "news": "News",
      "weather": "Weather",
      "finance": "Finance",
      "realEstate": "Real Estate",
      "automotive": "Automotive"
    }
  },
  "sort": {
    "title": "Sort By",
    "relevance": "Relevance",
    "popularity": "Popularity",
    "difficulty": "Difficulty",
    "duration": "Duration",
    "newest": "Newest",
    "rating": "Rating",
    "completions": "Completions"
  },
  "projectCard": {
    "new": "New",
    "popular": "Popular",
    "featured": "Featured",
    "updated": "Updated",
    "duration": "Estimated Duration",
    "difficulty": "Difficulty",
    "participants": "Participants",
    "completions": "Completions",
    "rating": "Rating",
    "reviews": "Reviews",
    "technologies": "Tech Stack",
    "skills": "Skills",
    "status": {
      "notStarted": "Not Started",
      "inProgress": "In Progress",
      "completed": "Completed",
      "submitted": "Submitted",
      "reviewed": "Reviewed"
    },
    "startProject": "Start Project",
    "continueProject": "Continue Project",
    "viewProject": "View Project",
    "preview": "Preview",
    "bookmark": "Bookmark",
    "share": "Share",
    "types": {
      "guided": "Guided",
      "challenge": "Challenge",
      "openEnded": "Open-ended",
      "portfolio": "Portfolio",
      "realWorld": "Real-world",
      "collaborative": "Collaborative"
    }
  },
  "projectDetails": {
    "tabs": {
      "overview": "Project Overview",
      "requirements": "Requirements",
      "instructions": "Instructions",
      "resources": "Resources",
      "submissions": "Submissions",
      "discussions": "Discussions"
    },
    "overview": {
      "title": "Project Overview",
      "description": "Description",
      "objectives": "Learning Objectives",
      "outcomes": "Expected Outcomes",
      "prerequisites": "Prerequisites",
      "estimatedTime": "Estimated Time",
      "difficulty": "Difficulty Level",
      "technologies": "Technologies Used",
      "skills": "Skills Gained",
      "industry": "Industry Domain",
      "projectType": "Project Type"
    },
    "requirements": {
      "title": "Project Requirements",
      "technical": "Technical Requirements",
      "functional": "Functional Requirements",
      "design": "Design Requirements",
      "performance": "Performance Requirements",
      "deliverables": "Deliverables",
      "evaluation": "Evaluation Criteria"
    },
    "instructions": {
      "title": "Project Instructions",
      "gettingStarted": "Getting Started",
      "stepByStep": "Step-by-Step Guide",
      "tips": "Helpful Tips",
      "commonIssues": "Common Issues",
      "troubleshooting": "Troubleshooting",
      "bestPractices": "Best Practices"
    },
    "resources": {
      "title": "Learning Resources",
      "documentation": "Documentation",
      "tutorials": "Tutorial Videos",
      "codeExamples": "Code Examples",
      "templates": "Project Templates",
      "tools": "Recommended Tools",
      "references": "References",
      "externalLinks": "External Links"
    },
    "submissions": {
      "title": "Project Submissions",
      "mySubmission": "My Submission",
      "otherSubmissions": "Other Submissions",
      "submitProject": "Submit Project",
      "updateSubmission": "Update Submission",
      "viewSubmission": "View Submission",
      "feedback": "Feedback",
      "rating": "Rating",
      "comments": "Comments"
    },
    "discussions": {
      "title": "Discussions",
      "askQuestion": "Ask Question",
      "shareIdeas": "Share Ideas",
      "getHelp": "Get Help",
      "showSolution": "Show Solution",
      "generalDiscussion": "General Discussion",
      "technicalHelp": "Technical Help",
      "projectShowcase": "Project Showcase"
    }
  },
  "progress": {
    "title": "Project Progress",
    "overall": "Overall Progress",
    "currentPhase": "Current Phase",
    "nextPhase": "Next Phase",
    "completed": "Completed",
    "inProgress": "In Progress",
    "upcoming": "Upcoming",
    "phases": {
      "planning": "Planning",
      "design": "Design",
      "development": "Development",
      "testing": "Testing",
      "deployment": "Deployment",
      "review": "Review"
    },
    "milestones": {
      "title": "Project Milestones",
      "completed": "Completed Milestones",
      "current": "Current Milestone",
      "upcoming": "Upcoming Milestones"
    }
  },
  "collaboration": {
    "title": "Team Collaboration",
    "teamMembers": "Team Members",
    "inviteMembers": "Invite Members",
    "roles": {
      "owner": "Project Owner",
      "collaborator": "Collaborator",
      "reviewer": "Reviewer",
      "viewer": "Viewer"
    },
    "tools": {
      "chat": "Team Chat",
      "videoCall": "Video Call",
      "sharedWorkspace": "Shared Workspace",
      "versionControl": "Version Control",
      "taskManagement": "Task Management",
      "fileSharing": "File Sharing"
    }
  },
  "evaluation": {
    "title": "Project Evaluation",
    "criteria": "Evaluation Criteria",
    "rubric": "Scoring Rubric",
    "selfAssessment": "Self Assessment",
    "peerReview": "Peer Review",
    "mentorFeedback": "Mentor Feedback",
    "dimensions": {
      "functionality": "Functionality",
      "codeQuality": "Code Quality",
      "design": "Design",
      "usability": "Usability",
      "innovation": "Innovation",
      "documentation": "Documentation",
      "presentation": "Presentation"
    }
  },
  "achievements": {
    "title": "Achievement System",
    "certificate": "Project Certificate",
    "badges": "Skill Badges",
    "portfolio": "Portfolio",
    "types": {
      "firstProject": "First Project",
      "projectMaster": "Project Master",
      "collaborator": "Collaborator",
      "innovator": "Innovator",
      "mentor": "Project Mentor",
      "reviewer": "Excellent Reviewer"
    }
  },
  "messages": {
    "projectStarted": "Project started, happy learning!",
    "progressSaved": "Progress saved",
    "submissionSuccess": "Project submitted successfully",
    "submissionError": "Submission failed, please try again",
    "feedbackSubmitted": "Feedback submitted",
    "invitationSent": "Invitation sent",
    "memberAdded": "Member added successfully",
    "bookmarkAdded": "Added to bookmarks",
    "bookmarkRemoved": "Removed from bookmarks",
    "shareSuccess": "Share link copied"
  },
  "empty": {
    "noProjects": "No projects available",
    "noResults": "No matching projects found",
    "noSubmissions": "No submissions yet",
    "noDiscussions": "No discussions yet",
    "noProgress": "Haven't started any projects yet",
    "suggestion": "We suggest you:",
    "suggestions": {
      "adjustFilters": "Adjust filter criteria",
      "browseCategories": "Browse other categories",
      "searchDifferent": "Try different search terms",
      "startBeginner": "Start with beginner projects",
      "joinCommunity": "Join community discussions"
    }
  }
}

export default translation
