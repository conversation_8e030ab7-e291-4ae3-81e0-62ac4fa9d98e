/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-08-11 21:54:16
 */
const translation = {
  myCourses: {
    title: "My Courses",
    empty: {
      title: "No courses enrolled yet",
      description: "Start your learning journey by enrolling in courses",
      button: "Browse Courses",
    },
  },
  inProgress: {
    title: "In Progress",
    empty: {
      title: "No courses in progress",
      description: "Start a course to see your learning progress here",
      button: "Start Learning",
    },
  },
  completed: {
    title: "Completed",
    empty: {
      title: "No completed courses yet",
      description:
        "Complete your first course to earn certificates and achievements",
      button: "Start Learning",
    },
  },
  favorites: {
    title: "Favorites",
    empty: {
      title: "No favorites yet",
      description: "Favorite your preferred courses and projects",
      button: "Discover Content",
    },
  },
  recentlyViewed: {
    title: "Recently Viewed",
    empty: {
      title: "No recently viewed content",
      description: "Your recently browsed courses will appear here",
      button: "Start Browsing",
    },
  },
  search: {
    placeholder: "Search Courses",
  },
  common: {
    continueReading: "Continue Reading",
    startCourse: "Start Course",
    resume: "Resume",
    completed: "Completed",
    inProgress: "In Progress",
    notStarted: "Not Started",
    timeLeft: "Time Left",
    progress: "Progress",
    lesson: "Lesson",
    lessons: "Lessons",
    hour: "Hour",
    hours: "Hours",
    minute: "Minute",
    minutes: "Minutes",
  },
  header: {
    welcome: "Welcome back, ",
    subtitle: "Continue your learning journey",
    preferences: "Learning Preferences",
  },
  tabs: {
    myCourses: "My Courses",
    inProgress: "In Progress",
    favorites: "Favorites",
    completed: "Completed",
    recentlyViewed: "Recently Viewed",
  },
  actions: {
    viewAll: "View All",
    startLearning: "Start Learning",
    continueLearning: "Continue Learning",
    browseContent: "Browse Content",
    discoverContent: "Discover Content",
  },
  features: {
    mockInterview: {
      title: "Mock Interview",
      description:
        "Put your skills to the test with real-world Coding Interview and System Design challenges.",
    },
    aiCodeReview: {
      title: "AI Code Review",
      description: "Get AI-powered code feedback and improvement suggestions.",
    },
    personalizedPath: {
      title: "Personalized Path",
      description: "Customized learning paths based on your skills and goals.",
    },
    cloudLabs: {
      title: "Cloud Labs",
      description:
        "Get hands-on with AWS cloud computing skills — no setup, no cleanup, no hassle.",
    },
    projects: {
      title: "Projects",
      description:
        "Practice your skills by building 300+ full-featured projects, from AI & ML to Web Development.",
    },
  },
  sampleCourses: {
    javascriptFundamentals: {
      title: "JavaScript Fundamentals",
      description:
        "Master the basics of JavaScript programming language including variables, functions, objects, and modern ES6+ features.",
    },
    htmlCssEssentials: {
      title: "HTML & CSS Essentials",
      description:
        "Learn web development fundamentals with HTML and CSS including responsive design and modern layout techniques.",
    },
    gitVersionControl: {
      title: "Git Version Control",
      description:
        "Master Git for version control and collaboration including branching, merging, and advanced Git workflows.",
    },
  },
  kickstart: {
    title: "Kickstart your career in tech",
    description:
      "Break into tech with the skills you'd learn in a boot camp or university — at a fraction of the cost. From your first line of code to your first job, we'll guide you through the entire process.",
    button: "Start Learning",
  },
  sections: {
    mostPopularCourses: "Most Popular Courses",
    newAdditions: "New Additions",
    showMore: "Show More",
    showLess: "Show Less",
    discoverMoreFeatures: "Discover More Features",
    yourActivity: "Your Activity",
    learningStreak: "Learning Streak",
    viewAll: "View All",
    exploreAll: "Explore All",
  },
  goalCategories: [
    "Web Development",
    "Mobile Development",
    "Data Science",
    "Machine Learning",
    "DevOps",
    "Cloud Computing",
    "Cybersecurity",
    "UI/UX Design",
    "Backend Development",
    "Frontend Development",
    "Database Management",
    "Software Engineering",
  ],
};

export default translation;
