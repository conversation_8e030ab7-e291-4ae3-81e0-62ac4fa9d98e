const translation = {
  "faq": {
    "title": "Frequently Asked Questions"
  },
  "feedback": {
    "title": "Interview Results",
    "overallPerformance": "Overall Performance",
    "detailedAssessment": "Detailed Assessment",
    "categories": {
      "problemSolving": "Problem Solving",
      "communication": "Communication",
      "technicalKnowledge": "Technical Knowledge",
      "codeQuality": "Code Quality",
      "systemDesign": "System Design",
      "algorithms": "Algorithms"
    },
    "strengths": "Strengths",
    "areasForImprovement": "Areas for Improvement",
    "nextSteps": "Next Steps",
    "recommendations": "Recommendations",
    "scores": {
      "excellent": "Excellent",
      "good": "Good",
      "average": "Average",
      "needsImprovement": "Needs Improvement"
    },
    "actions": {
      "tryAgain": "Try Again",
      "nextInterview": "Next Interview",
      "viewDetails": "View Details",
      "saveResults": "Save Results"
    }
  },
  "timer": {
    "timeAlmostUp": "Time Almost Up!",
    "timeWarning": "Time Warning",
    "start": "Start",
    "pause": "Pause",
    "resume": "Resume",
    "stop": "Stop"
  },
  "search": {
    "placeholder": "Search interviews by title, description, or tags..."
  }
}

export default translation
