const translation = {
  title: "Profile",
  description:
    "Manage your personal information, learning progress, and account settings",
  viewProfile: "View Profile",
  tabs: {
    overview: "Overview",
    profile: "Profile",
    learning: "Learning History",
    achievements: "Achievements",
    certificates: "Certificates",
    settings: "Settings",
    billing: "Billing",
    security: "Security",
  },
  menu: {
    purchases: "Purchases",
    settings: "Settings",
    certificates: " My Certificates",
  },
  overview: {
    title: "Learning Overview",
    welcome: "Welcome back, {name}!",
    stats: {
      coursesEnrolled: "Courses Enrolled",
      coursesCompleted: "Courses Completed",
      hoursLearned: "Hours Learned",
      skillsLearned: "Skills Learned",
      certificatesEarned: "Certificates Earned",
      currentStreak: "Current Streak",
      totalPoints: "Total Points",
      rank: "Rank",
    },
    currentLearning: {
      title: "Currently Learning",
      noCourses: "No courses in progress",
      continueButton: "Continue Learning",
      viewAll: "View All",
    },
    recommendations: {
      title: "Recommended for You",
      subtitle: "Based on your learning history and interests",
      viewAll: "View More Recommendations",
    },
    goals: {
      title: "Learning Goals",
      setGoal: "Set Goal",
      editGoal: "Edit Goal",
      weeklyGoal: "Weekly Learning Goal",
      monthlyGoal: "Monthly Learning Goal",
      progress: "Progress",
      achieved: "Achieved",
      inProgress: "In Progress",
      notStarted: "Not Started",
    },
    recentActivity: {
      title: "Recent Activity",
      noActivity: "No recent activity",
      viewAll: "View All Activity",
      activities: {
        courseCompleted: "completed a course",
        lessonCompleted: "completed a lesson",
        projectSubmitted: "submitted a project",
        certificateEarned: "earned a certificate",
        skillBadgeEarned: "earned a skill badge",
        pathStarted: "started a learning path",
        pathCompleted: "completed a learning path",
      },
    },
  },
  profile: {
    title: "Profile Information",
    subtitle: "Manage your basic information and preferences",
    basicInfo: {
      title: "Basic Information",
      avatar: "Avatar",
      changeAvatar: "Change Avatar",
      firstName: "First Name",
      lastName: "Last Name",
      displayName: "Display Name",
      email: "Email Address",
      phone: "Phone Number",
      birthday: "Birthday",
      gender: "Gender",
      location: "Location",
      timezone: "Timezone",
      language: "Language Preference",
      bio: "Bio",
      website: "Website",
      genderOptions: {
        male: "Male",
        female: "Female",
        other: "Other",
        preferNotToSay: "Prefer not to say",
      },
    },
    careerInfo: {
      title: "Career Information",
      currentRole: "Current Role",
      company: "Company/Organization",
      industry: "Industry",
      experience: "Experience Level",
      skills: "Skills",
      interests: "Interests",
      careerGoals: "Career Goals",
      experienceOptions: {
        student: "Student",
        entry: "0-2 years",
        junior: "2-5 years",
        mid: "5-10 years",
        senior: "10+ years",
      },
    },
    socialMedia: {
      title: "Social Media",
      github: "GitHub",
      linkedin: "LinkedIn",
      twitter: "Twitter",
      wechat: "WeChat",
      qq: "QQ",
      weibo: "Weibo",
    },
    privacy: {
      title: "Privacy Settings",
      profileVisibility: "Profile Visibility",
      showEmail: "Show Email Address",
      showPhone: "Show Phone Number",
      showLocation: "Show Location",
      showProgress: "Show Learning Progress",
      showAchievements: "Show Achievements",
      allowMessages: "Allow Messages from Other Users",
      allowFollows: "Allow Other Users to Follow",
      visibilityOptions: {
        public: "Public",
        friends: "Friends Only",
        private: "Private",
      },
    },
  },
  learning: {
    title: "Learning History",
    subtitle: "View your complete learning history and progress",
    filters: {
      all: "All",
      inProgress: "In Progress",
      completed: "Completed",
      bookmarked: "Bookmarked",
      courses: "Courses",
      paths: "Learning Paths",
      projects: "Projects",
      assessments: "Assessments",
    },
    sort: {
      recent: "Recently Accessed",
      alphabetical: "Alphabetical",
      progress: "By Progress",
      rating: "By Rating",
      duration: "By Duration",
    },
    learningItem: {
      progress: "Progress",
      timeSpent: "Time Spent",
      lastAccessed: "Last Accessed",
      rating: "My Rating",
      certificate: "Certificate",
      continue: "Continue Learning",
      review: "Write Review",
      share: "Share",
      remove: "Remove",
    },
    statistics: {
      title: "Learning Statistics",
      totalTime: "Total Learning Time",
      averageDaily: "Average Daily Time",
      longestStreak: "Longest Streak",
      favoriteCategory: "Favorite Category",
      completionRate: "Completion Rate",
      averageRating: "Average Rating",
    },
  },
  achievements: {
    title: "Achievements",
    subtitle: "Showcase your learning accomplishments",
    categories: {
      all: "All Achievements",
      learning: "Learning Achievements",
      skill: "Skill Achievements",
      social: "Social Achievements",
      special: "Special Achievements",
    },
    status: {
      earned: "Earned",
      inProgress: "In Progress",
      locked: "Locked",
    },
    types: {
      firstCourse: "First Course",
      courseStreak: "Learning Streak",
      skillMaster: "Skill Master",
      projectCompleter: "Project Completer",
      communityHelper: "Community Helper",
      earlyAdopter: "Early Adopter",
      dedication: "Dedication",
      explorer: "Explorer",
      mentor: "Mentor",
      influencer: "Influencer",
    },
    achievementDetail: {
      description: "Description",
      requirements: "Requirements",
      progress: "Progress",
      earnedDate: "Earned Date",
      rarity: "Rarity",
      points: "Points Reward",
      share: "Share Achievement",
    },
  },
  certificates: {
    title: "Certificates",
    subtitle: "Manage all your earned certificates and certifications",
    status: {
      active: "Active",
      expired: "Expired",
      revoked: "Revoked",
    },
    types: {
      completion: "Completion Certificate",
      proficiency: "Proficiency Certificate",
      specialization: "Specialization Certificate",
      professional: "Professional Certificate",
    },
    actions: {
      download: "Download Certificate",
      verify: "Verify Certificate",
      share: "Share Certificate",
      print: "Print Certificate",
      addToLinkedIn: "Add to LinkedIn",
      addToResume: "Add to Resume",
    },
    certificateDetail: {
      issuer: "Issuer",
      issueDate: "Issue Date",
      expiryDate: "Expiry Date",
      credentialId: "Credential ID",
      skills: "Certified Skills",
      verificationUrl: "Verification URL",
      description: "Description",
    },
    verification: {
      title: "Certificate Verification",
      valid: "Certificate is valid",
      invalid: "Certificate is invalid",
      expired: "Certificate has expired",
      verifyButton: "Verify Certificate",
      verificationCode: "Verification Code",
    },
  },
  settings: {
    title: "Account Settings",
    subtitle: "Manage your account preferences and notifications",
    notifications: {
      title: "Notification Settings",
      email: "Email Notifications",
      push: "Push Notifications",
      sms: "SMS Notifications",
      types: {
        courseUpdates: "Course Updates",
        newCourses: "New Course Releases",
        achievements: "Achievement Earned",
        reminders: "Learning Reminders",
        messages: "Private Messages",
        promotions: "Promotions",
        newsletter: "Newsletter",
        systemUpdates: "System Updates",
      },
    },
    learningPreferences: {
      title: "Learning Preferences",
      autoplay: "Autoplay Videos",
      playbackSpeed: "Default Playback Speed",
      subtitles: "Show Subtitles by Default",
      darkMode: "Dark Mode",
      compactMode: "Compact Mode",
      reminderTime: "Learning Reminder Time",
      weeklyGoal: "Weekly Learning Goal",
      preferredLanguage: "Preferred Language",
      difficulty: "Preferred Difficulty",
    },
    privacyControls: {
      title: "Privacy Controls",
      dataCollection: "Data Collection",
      analytics: "Learning Analytics",
      personalization: "Personalized Recommendations",
      thirdPartySharing: "Third-party Sharing",
      marketingEmails: "Marketing Emails",
      profileIndexing: "Search Engine Indexing",
    },
  },
  billing: {
    title: "Billing Management",
    subtitle: "Manage your subscription plans and payment information",
    currentPlan: {
      title: "Current Plan",
      planName: "Plan Name",
      status: "Status",
      nextBilling: "Next Billing",
      amount: "Amount",
      upgrade: "Upgrade Plan",
      downgrade: "Downgrade Plan",
      cancel: "Cancel Subscription",
    },
    billingHistory: {
      title: "Billing History",
      date: "Date",
      description: "Description",
      amount: "Amount",
      status: "Status",
      invoice: "Invoice",
      download: "Download",
      statuses: {
        paid: "Paid",
        pending: "Pending",
        failed: "Failed",
        refunded: "Refunded",
      },
    },
    paymentMethods: {
      title: "Payment Methods",
      addMethod: "Add Payment Method",
      editMethod: "Edit",
      deleteMethod: "Delete",
      setDefault: "Set as Default",
      cardNumber: "Card Number",
      expiryDate: "Expiry Date",
      cardType: "Card Type",
    },
    invoiceSettings: {
      title: "Invoice Settings",
      companyName: "Company Name",
      taxId: "Tax ID",
      address: "Address",
      email: "Invoice Email",
      autoSend: "Auto-send Invoices",
    },
  },
  security: {
    title: "Security Settings",
    subtitle: "Protect your account security",
    password: {
      title: "Password Settings",
      currentPassword: "Current Password",
      newPassword: "New Password",
      confirmPassword: "Confirm Password",
      changePassword: "Change Password",
      passwordStrength: "Password Strength",
      requirements: "Password Requirements",
      strength: {
        weak: "Weak",
        medium: "Medium",
        strong: "Strong",
      },
    },
    twoFactor: {
      title: "Two-Factor Authentication",
      status: "Status",
      enable: "Enable",
      disable: "Disable",
      setup: "Setup Two-Factor Authentication",
      backupCodes: "Backup Codes",
      generateCodes: "Generate Backup Codes",
      methods: {
        app: "Authenticator App",
        sms: "SMS Verification",
        email: "Email Verification",
      },
    },
    loginHistory: {
      title: "Login History",
      device: "Device",
      location: "Location",
      time: "Time",
      status: "Status",
      current: "Current Session",
      statuses: {
        success: "Success",
        failed: "Failed",
        suspicious: "Suspicious",
      },
    },
    activeSessions: {
      title: "Active Sessions",
      device: "Device",
      location: "Location",
      lastActive: "Last Active",
      current: "Current",
      terminate: "Terminate Session",
      terminateAll: "Terminate All Other Sessions",
    },
    accountDeletion: {
      title: "Account Deletion",
      warning: "Danger Zone",
      description:
        "Deleting your account will permanently remove all data. This action cannot be undone.",
      deleteAccount: "Delete Account",
      confirmDeletion: "Confirm Deletion",
    },
  },
  actions: {
    save: "Save",
    cancel: "Cancel",
    edit: "Edit",
    delete: "Delete",
    update: "Update",
    confirm: "Confirm",
    back: "Back",
    next: "Next",
    previous: "Previous",
    finish: "Finish",
    close: "Close",
    refresh: "Refresh",
    export: "Export",
    import: "Import",
    upload: "Upload",
    download: "Download",
    share: "Share",
    copy: "Copy",
    reset: "Reset",
    clear: "Clear",
    search: "Search",
    filter: "Filter",
    sort: "Sort",
    viewAll: "View All",
    showMore: "Show More",
    showLess: "Show Less",
  },
  messages: {
    saveSuccess: "Saved successfully",
    saveError: "Save failed",
    updateSuccess: "Updated successfully",
    updateError: "Update failed",
    deleteSuccess: "Deleted successfully",
    deleteError: "Delete failed",
    uploadSuccess: "Uploaded successfully",
    uploadError: "Upload failed",
    copySuccess: "Copied successfully",
    invalidInput: "Invalid input",
    confirmDelete: "Are you sure you want to delete?",
    unsavedChanges: "You have unsaved changes",
    operationSuccess: "Operation successful",
    operationError: "Operation failed",
    networkError: "Network error",
    permissionDenied: "Permission denied",
    sessionExpired: "Session expired",
    passwordChanged: "Password changed successfully",
    emailVerified: "Email verified successfully",
    phoneVerified: "Phone verified successfully",
    twoFactorEnabled: "Two-factor authentication enabled",
    twoFactorDisabled: "Two-factor authentication disabled",
    subscriptionUpdated: "Subscription updated",
    paymentMethodAdded: "Payment method added",
    paymentMethodRemoved: "Payment method removed",
  },
};

export default translation;
