const translation = {
  title: "My Learning Preferences",
  description:
    "Share your preferences and let us recommend relevant learning content for your upskilling.",
  basicInfo: {
    title: "Basic Information",
    age: "Age",
    gender: "Gender",
    location: "Location",
    education: "Education Background",
    experience: "Work Experience",
  },
  learningGoals: {
    title: "Learning Goals",
    careerChange: "Career Change",
    skillUpgrade: "Skill Upgrade",
    certification: "Get Certified",
    hobby: "Personal Interest",
    academic: "Academic Research",
  },
  professionalBackground: {
    title: "Professional Background",
    currentRole: "Current Role",
    industry: "Industry",
    company: "Company Size",
    skills: "Skills",
    interests: "Areas of Interest",
  },
  learningPreferences: {
    title: "Learning Preferences",
    pace: "Learning Pace",
    format: "Learning Format",
    duration: "Course Duration",
    difficulty: "Difficulty Preference",
    language: "Language Preference",
  },
  options: {
    gender: {
      male: "Male",
      female: "Female",
      other: "Other",
      preferNotToSay: "Prefer not to say",
    },
    experience: {
      student: "Student",
      entry: "0-2 years",
      junior: "2-5 years",
      mid: "5-10 years",
      senior: "10+ years",
    },
    pace: {
      slow: "Slow pace",
      moderate: "Moderate pace",
      fast: "Fast-paced",
      intensive: "Intensive",
    },
    format: {
      visual: "Visual (diagrams, charts)",
      auditory: "Auditory (lectures, discussions)",
      kinesthetic: "Kinesthetic (hands-on practice)",
      reading: "Reading/Writing (text-based learning)",
      video: "Video courses",
      text: "Text tutorials",
      interactive: "Interactive exercises",
      project: "Hands-on projects",
    },
    difficulty: {
      beginner: "Beginner",
      intermediate: "Intermediate",
      advanced: "Advanced",
      expert: "Expert",
    },
  },
  placeholders: {
    age: "Enter your age",
    gender: "Select your gender",
    preferredLanguage: "Select your preferred language",
    educationExperience: "Select education level",
    major: "e.g., Computer Science, Business, Engineering",
    graduationYear: "e.g., 2020",
    currentRole: "e.g., Software Developer, Student, Manager",
    industry: "e.g., Technology, Finance, Healthcare",
    workExperience: "e.g., 3",
    learningStyle: "Select your learning style",
    studyTimePerWeek: "e.g., 10",
    preferredStudyTime: "Select preferred time",
    learningPace: "Select learning pace",
    company: "Select company size",
    skills: "Select your skills",
    interests: "Select areas of interest",
  },
  genderOptions: {
    male: "Male",
    female: "Female",
    other: "Other",
    preferNotToSay: "Prefer not to say",
  },
  buttons: {
    save: "Save Preferences",
    reset: "Reset",
    skip: "Skip",
    next: "Next",
    previous: "Previous",
  },
  messages: {
    saveSuccess: "Preferences saved successfully",
    saveError: "Failed to save, please try again",
    resetConfirm: "Are you sure you want to reset all preferences?",
    loading: "Loading...",
    saving: "Saving...",
  },
  onboarding: {
    title: "GET PERSONALIZED PLAN FOR FREE",
    steps: {
      basicInfo: {
        title: "Basic Information",
        description: "Tell us a bit about yourself",
        age: "Age",
        gender: "Gender",
        preferredLanguage: "Preferred Learning Language",
        ageRequired: "Age must be at least 13",
      },
      education: {
        title: "Educational Background",
        description: "Help us understand your educational experience",
        educationLevel: "Highest Education Level",
        major: "Major",
        graduationYear: "Graduation Year",
        educationOptions: {
          highSchool: "High School",
          bachelor: "Bachelor's Degree",
          master: "Master's Degree",
          other: "Other",
        },
      },
      career: {
        title: "Professional Background",
        description: "Tell us about your current professional situation",
        currentRole: "Current Role",
        industry: "Industry",
        workExperience: "Years of Work Experience",
        workExperienceOptions: {
          noExperience: "No experience",
          lessThan1: "Less than 1 year",
          "1to2": "1-2 years",
          "3to5": "3-5 years",
          "6to10": "6-10 years",
          "11to15": "11-15 years",
          "16plus": "16+ years",
        },
      },
      learningPreferences: {
        title: "Learning Preferences",
        description: "How do you prefer to learn?",
        learningStyle: "How do you learn best?",
        studyTime: "How much time can you dedicate to learning?",
        preferredTime: "When do you prefer to study?",
        learningPace: "What's your preferred learning pace?",
        learningStyleOptions: {
          visual: "Visual",
          visualDesc: "I prefer diagrams, charts, and visual aids",
          auditory: "Auditory",
          auditoryDesc: "I learn best through lectures and discussions",
          kinesthetic: "Hands-on",
          kinestheticDesc: "I prefer interactive exercises and practice",
          reading: "Reading/Writing",
          readingDesc: "I learn best through text-based content",
        },
        studyTimeOptions: {
          "1to3": "1-3 hours per week",
          "4to6": "4-6 hours per week",
          "7to10": "7-10 hours per week",
          "11to15": "11-15 hours per week",
          "16plus": "16+ hours per week",
        },
        preferredTimeOptions: {
          morning: "Morning (6AM - 12PM)",
          afternoon: "Afternoon (12PM - 6PM)",
          evening: "Evening (6PM - 10PM)",
          night: "Night (10PM - 6AM)",
          flexible: "Flexible",
        },
        learningPaceOptions: {
          slow: "Slow and steady",
          slowDesc:
            "I prefer to take my time and thoroughly understand each concept",
          moderate: "Moderate pace",
          moderateDesc: "I like a balanced approach with regular progress",
          fast: "Fast-paced",
          fastDesc: "I want to learn quickly and cover more ground",
          intensive: "Intensive",
          intensiveDesc: "I'm ready for an accelerated, immersive experience",
        },
      },
      goals: {
        title: "Learning Goals",
        description: "What do you want to achieve?",
        learningGoals: "Describe your learning goals",
        placeholder:
          "e.g., I want to become a front-end developer, learn Python for data analysis, improve my coding skills for career advancement...",
        examples: {
          title: "Example goals:",
          frontend:
            "• I want to become a front-end developer and build modern web applications",
          python:
            "• I need to learn Python for data analysis in my current job",
          transition:
            "• I want to transition from marketing to software development",
          promotion:
            "• I need to improve my coding skills to get promoted at work",
          startup:
            "• I want to start my own tech startup and need technical skills",
        },
      },
    },
    navigation: {
      next: "Next",
      previous: "Previous",
      complete: "Start Learning",
      skip: "Skip",
    },
    validation: {
      required: "This field is required",
      ageMinimum: "Age must be at least 13",
      emailInvalid: "Please enter a valid email address",
    },
    success: {
      title: "Welcome!",
      message: "Your learning preferences have been saved.",
    },
  },
};

export default translation;
