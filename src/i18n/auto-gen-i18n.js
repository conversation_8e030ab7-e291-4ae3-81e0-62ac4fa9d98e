/* eslint-disable no-eval */
const fs = require("node:fs");
const path = require("node:path");
const transpile = require("typescript").transpile;
const magicast = require("magicast");
const { parseModule, generateCode, loadFile } = magicast;
const bingTranslate = require("bing-translate-api");
const { translate } = bingTranslate;
const data = require("./languages.json");

const targetLanguage = "en-US";
// https://github.com/plainheart/bing-translate-api/blob/master/src/met/lang.json
const languageKeyMap = data.languages.reduce((map, language) => {
  if (language.supported) {
    if (language.value === "zh-CN") {
      // Replace zh-CN with zh-Hans for translate API compatibility
      map[language.value] = "zh-Hans";
    } else if (language.value === "zh-Hant") {
      map[language.value] = language.value;
    } else {
      map[language.value] = language.value.split("-")[0];
    }
  }

  return map;
}, {});

async function translateMissingKeyDeeply(sourceObj, targetObject, toLanguage) {
  await Promise.all(
    Object.keys(sourceObj).map(async (key) => {
      if (targetObject[key] === undefined) {
        if (typeof sourceObj[key] === "object") {
          targetObject[key] = {};
          await translateMissingKeyDeeply(
            sourceObj[key],
            targetObject[key],
            toLanguage
          );
        } else {
          const { translation } = await translate(
            sourceObj[key],
            null,
            languageKeyMap[toLanguage]
          );
          targetObject[key] = translation;
          // console.log(translation)
        }
      } else if (typeof sourceObj[key] === "object") {
        targetObject[key] = targetObject[key] || {};
        await translateMissingKeyDeeply(
          sourceObj[key],
          targetObject[key],
          toLanguage
        );
      }
    })
  );
}

async function autoGenTrans(fileName, toGenLanguage) {
  try {
    const fullKeyFilePath = path.join(
      __dirname,
      targetLanguage,
      `${fileName}.ts`
    );
    const toGenLanguageFilePath = path.join(
      __dirname,
      toGenLanguage,
      `${fileName}.ts`
    );

    // Read and transpile the source file
    const sourceContent = fs.readFileSync(fullKeyFilePath, "utf8");
    const transpiledSource = transpile(sourceContent, {
      target: "es2015",
      module: "commonjs",
      allowJs: true,
      esModuleInterop: true,
    });
    const fullKeyContent = eval(transpiledSource);

    // Read and parse the target file
    let toGenOutPut = {};
    if (fs.existsSync(toGenLanguageFilePath)) {
      const targetContent = fs.readFileSync(toGenLanguageFilePath, "utf8");
      const transpiledTarget = transpile(targetContent, {
        target: "es2015",
        module: "commonjs",
        allowJs: true,
        esModuleInterop: true,
      });
      toGenOutPut = eval(transpiledTarget);
    }

    // Translate missing keys
    await translateMissingKeyDeeply(fullKeyContent, toGenOutPut, toGenLanguage);

    // Generate the output file
    const output = `const translation = ${JSON.stringify(toGenOutPut, null, 2)}

export default translation
`;

    fs.writeFileSync(toGenLanguageFilePath, output);
    console.log(`Successfully processed ${fileName} for ${toGenLanguage}`);
  } catch (error) {
    console.error(`Error processing ${fileName} for ${toGenLanguage}:`, error);
  }
}

async function main() {
  const files = fs
    .readdirSync(path.join(__dirname, targetLanguage))
    .map((file) => file.replace(/\.ts/, ""))
    .filter((f) => f !== "app-debug"); // ast parse error in app-debug

  await Promise.all(
    files.map(async (file) => {
      await Promise.all(
        Object.keys(languageKeyMap).map(async (language) => {
          await autoGenTrans(file, language);
        })
      );
    })
  );
}

main().catch(console.error);
