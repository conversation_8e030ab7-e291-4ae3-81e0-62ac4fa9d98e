const translation = {
  title: "我的学习偏好",
  description: "分享您的偏好，让我们为您推荐相关的学习内容。",
  basicInfo: {
    title: "基本信息",
    age: "年龄",
    gender: "性别",
    location: "所在地区",
    education: "教育背景",
    experience: "工作经验",
  },
  learningGoals: {
    title: "学习目标",
    careerChange: "职业转换",
    skillUpgrade: "技能提升",
    certification: "获得认证",
    hobby: "兴趣爱好",
    academic: "学术研究",
  },
  professionalBackground: {
    title: "专业背景",
    currentRole: "当前职位",
    industry: "所属行业",
    company: "公司规模",
    skills: "技能标签",
    interests: "兴趣领域",
  },
  learningPreferences: {
    title: "学习偏好",
    pace: "学习节奏",
    format: "学习风格",
    duration: "课程时长",
    difficulty: "难度偏好",
    language: "语言偏好",
  },
  options: {
    gender: {
      male: "男",
      female: "女",
      other: "其他",
      preferNotToSay: "不愿透露",
    },
    experience: {
      student: "学生",
      entry: "0-2年",
      junior: "2-5年",
      mid: "5-10年",
      senior: "10年以上",
    },
    pace: {
      slow: "慢节奏",
      moderate: "中等节奏",
      fast: "快节奏",
      intensive: "密集型",
    },
    format: {
      visual: "视觉型（图表、图像）",
      auditory: "听觉型（讲座、讨论）",
      kinesthetic: "动手型（互动练习）",
      reading: "阅读/写作型（文本内容）",
      video: "视频课程",
      text: "文本教程",
      interactive: "互动练习",
      project: "项目实战",
    },
    difficulty: {
      beginner: "初学者",
      intermediate: "中级",
      advanced: "高级",
      expert: "专家级",
    },
  },
  placeholders: {
    age: "请输入您的年龄",
    gender: "请选择性别",
    preferredLanguage: "请选择首选语言",
    educationExperience: "请选择教育水平",
    major: "例如：计算机科学、商务、工程",
    graduationYear: "例如：2020",
    currentRole: "例如：软件开发工程师、学生、经理",
    industry: "例如：科技、金融、医疗",
    workExperience: "例如：3",
    learningStyle: "请选择学习风格",
    studyTimePerWeek: "例如：10",
    preferredStudyTime: "请选择偏好学习时间",
    learningPace: "请选择学习节奏",
    company: "请选择公司规模",
    skills: "请选择您的技能",
    interests: "请选择您感兴趣的领域",
  },
  genderOptions: {
    male: "男",
    female: "女",
    other: "其他",
    preferNotToSay: "不愿透露",
  },
  buttons: {
    save: "保存偏好",
    reset: "重置",
    skip: "跳过",
    next: "下一步",
    previous: "上一步",
  },
  messages: {
    saveSuccess: "偏好设置保存成功",
    saveError: "保存失败，请重试",
    resetConfirm: "确定要重置所有偏好设置吗？",
    loading: "正在加载...",
    saving: "正在保存...",
  },
  onboarding: {
    title: "获取免费个性化学习计划",
    steps: {
      basicInfo: {
        title: "基本信息",
        description: "告诉我们一些关于您的信息",
        age: "年龄",
        gender: "性别",
        preferredLanguage: "首选学习语言",
        ageRequired: "年龄必须至少13岁",
      },
      education: {
        title: "教育背景",
        description: "帮助我们了解您的教育经历",
        educationLevel: "最高学历",
        major: "专业",
        graduationYear: "毕业年份",
        educationOptions: {
          highSchool: "高中",
          bachelor: "学士学位",
          master: "硕士学位",
          other: "其他",
        },
      },
      career: {
        title: "职业背景",
        description: "告诉我们您当前的职业状况",
        currentRole: "当前职位",
        industry: "所在行业",
        workExperience: "工作经验年数",
        workExperienceOptions: {
          noExperience: "无经验",
          lessThan1: "少于1年",
          "1to2": "1-2年",
          "3to5": "3-5年",
          "6to10": "6-10年",
          "11to15": "11-15年",
          "16plus": "16年以上",
        },
      },
      learningPreferences: {
        title: "学习偏好",
        description: "您更喜欢如何学习？",
        learningStyle: "您如何学习效果最好？",
        studyTime: "您每周可以投入多少时间学习？",
        preferredTime: "您更喜欢什么时候学习？",
        learningPace: "您偏好的学习节奏是什么？",
        learningStyleOptions: {
          visual: "视觉型",
          visualDesc: "我更喜欢图表、图像和视觉辅助",
          auditory: "听觉型",
          auditoryDesc: "我通过讲座和讨论学习效果最好",
          kinesthetic: "动手型",
          kinestheticDesc: "我更喜欢互动练习和实践",
          reading: "阅读/写作型",
          readingDesc: "我通过基于文本的内容学习效果最好",
        },
        studyTimeOptions: {
          "1to3": "每周1-3小时",
          "4to6": "每周4-6小时",
          "7to10": "每周7-10小时",
          "11to15": "每周11-15小时",
          "16plus": "每周16小时以上",
        },
        preferredTimeOptions: {
          morning: "上午 (6AM - 12PM)",
          afternoon: "下午 (12PM - 6PM)",
          evening: "晚上 (6PM - 10PM)",
          night: "深夜 (10PM - 6AM)",
          flexible: "灵活安排",
        },
        learningPaceOptions: {
          slow: "慢而稳",
          slowDesc: "我更喜欢花时间彻底理解每个概念",
          moderate: "中等节奏",
          moderateDesc: "我喜欢平衡的方法，定期进步",
          fast: "快节奏",
          fastDesc: "我想快速学习，覆盖更多内容",
          intensive: "密集型",
          intensiveDesc: "我准备好接受加速、沉浸式的体验",
        },
      },
      goals: {
        title: "学习目标",
        description: "您想要实现什么？",
        learningGoals: "描述您的学习目标",
        placeholder:
          "例如：我想成为前端开发工程师，学习Python进行数据分析，提升编程技能以获得职业发展...",
        examples: {
          title: "目标示例：",
          frontend: "• 我想成为前端开发工程师，构建现代Web应用",
          python: "• 我需要学习Python在当前工作中进行数据分析",
          transition: "• 我想从市场营销转向软件开发",
          promotion: "• 我需要提升编程技能以在工作中获得晋升",
          startup: "• 我想创办自己的科技公司，需要技术技能",
        },
      },
    },
    navigation: {
      next: "下一步",
      previous: "上一步",
      complete: "开始学习",
      skip: "跳过",
    },
    validation: {
      required: "此字段为必填项",
      ageMinimum: "年龄必须至少13岁",
      emailInvalid: "请输入有效的邮箱地址",
    },
    success: {
      title: "欢迎！",
      message: "您的学习偏好已保存成功。",
    },
  },
};

export default translation;
