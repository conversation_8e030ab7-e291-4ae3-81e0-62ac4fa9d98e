const translation = {
  "title": "Pageflux AI - 智能学习平台",
  "description": "通过AI驱动的个性化学习体验，提升您的技能",
  "hero": {
    "title": "为开发者精心打造的课程。\n由AI个性化定制。",
    "subtitle": "我们的AI定制课程和即用沙盒环境处理所有繁重工作。💪\n您只需投入其中，构建非凡作品。🚀",
    "badge": "新功能",
    "card": {
      "category": "全栈面试",
      "title": "面试精通的最短路径",
      "description1": "通过PAL（个性化自适应学习）跳过LeetCode刷题",
      "description2": "内置",
      "aiMockInterviews": "AI模拟面试",
      "description3": "的全流程练习课程"
    },
    "cta": {
      "primary": "开始学习",
      "secondary": "查看工作原理"
    },
    "companies": {
      "text": "加入",
      "count": "270万",
      "suffix": "开发者，他们在以下公司工作"
    },
    "stats": {
      "learners": "学习者",
      "courses": "课程",
      "projects": "项目",
      "instructors": "讲师"
    }
  },
  "introduce": {
    "title": "开启您的学习之旅",
    "subtitle": "从基础到高级，我们为您提供全方位的学习资源",
    "features": {
      "interactive": {
        "title": "互动学习",
        "description": "通过实践项目和编程练习，深入理解概念"
      },
      "personalized": {
        "title": "个性化路径",
        "description": "AI推荐系统为您定制最适合的学习路径"
      },
      "community": {
        "title": "社区支持",
        "description": "与全球学习者交流，分享经验和解决问题"
      },
      "certification": {
        "title": "权威认证",
        "description": "获得行业认可的技能证书，提升职业竞争力"
      }
    }
  },
  "courseCategories": {
    "title": "热门课程分类",
    "subtitle": "最新发布和/或更新的热门主题资源。",
    "viewAll": "查看全部",
    "tabs": {
      "mostPopular": "最受欢迎的课程",
      "systemDesign": "系统设计",
      "interview": "面试准备",
      "aws": "AWS",
      "generativeAi": "生成式AI",
      "python": "Python",
      "javascript": "JavaScript"
    },
    "categories": {
      "webDevelopment": {
        "title": "Web开发",
        "description": "学习现代Web开发技术",
        "courses": "门课程"
      },
      "mobileDevelopment": {
        "title": "移动开发",
        "description": "iOS和Android应用开发",
        "courses": "门课程"
      },
      "dataScience": {
        "title": "数据科学",
        "description": "数据分析和机器学习",
        "courses": "门课程"
      },
      "cloudComputing": {
        "title": "云计算",
        "description": "AWS、Azure、GCP等云平台",
        "courses": "门课程"
      },
      "devOps": {
        "title": "DevOps",
        "description": "自动化部署和运维",
        "courses": "门课程"
      },
      "cybersecurity": {
        "title": "网络安全",
        "description": "信息安全和渗透测试",
        "courses": "门课程"
      }
    }
  },
  "aiLearning": {
    "title": "AI驱动的实践学习",
    "subtitle": "了解Pageflux AI如何使用AI让您的学习比以往更加沉浸式。",
    "cta": "免费试用",
    "features": [
      {
        "title": "个性化面试准备",
        "description": "跳过LeetCode刷题，使用适应您目标的定制路线图。编程面试、系统设计等实践练习。",
        "buttonText": "获取免费路线图",
        "buttonVariant": "primary"
      },
      {
        "title": "模拟面试",
        "description": "在模拟面试环境中测试您的技能。根据您的表现获得个性化反馈。适用于编程面试、系统设计等。",
        "buttonText": "免费试用",
        "buttonVariant": "outline"
      },
      {
        "title": "AI提示",
        "description": "构建提示工程技能。练习实施AI驱动的解决方案。",
        "buttonText": "了解更多",
        "buttonVariant": "outline"
      },
      {
        "title": "代码反馈",
        "description": "一键评估和调试您的代码。获得测试用例的实时反馈，包括解决方案的时间和空间复杂度。",
        "buttonText": "立即试用",
        "buttonVariant": "outline"
      },
      {
        "title": "AI解释",
        "description": "在任何Pageflux AI课程中选择任何文本，即可获得即时解释——无需离开浏览器。",
        "buttonText": "查看演示",
        "buttonVariant": "outline"
      },
      {
        "title": "AI代码导师",
        "description": "AI代码导师帮助您快速识别代码中的错误，从错误中学习，并为您指明正确的方向——就像一对一的导师！",
        "buttonText": "开始使用",
        "buttonVariant": "outline"
      }
    ]
  },
  "practice": {
    "title": "实践和应用您的技能",
    "cloudLabs": {
      "category": "云实验室",
      "title": "免费云服务，无需设置",
      "titleHighlight": "免费",
      "features": [
        "💻 免费云服务，无需设置或清理",
        "⚡ 即时、无痛访问无服务器计算"
      ],
      "buttonText": "开始云实验室"
    },
    "handsonProjects": {
      "category": "实践项目",
      "title": "通过真实世界项目学习",
      "titleHighlight": "真实世界",
      "features": [
        "🚀 构建真实的应用程序和项目",
        "📱 从移动应用到Web应用的完整项目"
      ],
      "buttonText": "浏览项目"
    }
  },
  "learningPaths": {
    "title": "精心设计的学习路径",
    "subtitle": "从入门到精通的系统化学习方案",
    "viewAll": "查看所有路径",
    "paths": {
      "frontend": {
        "title": "前端开发工程师",
        "description": "掌握HTML、CSS、JavaScript和现代框架",
        "duration": "3-6个月",
        "level": "初级到中级",
        "skills": "项技能"
      },
      "backend": {
        "title": "后端开发工程师",
        "description": "学习服务器端开发和数据库管理",
        "duration": "4-8个月",
        "level": "初级到高级",
        "skills": "项技能"
      },
      "fullstack": {
        "title": "全栈开发工程师",
        "description": "前后端全面发展的综合技能",
        "duration": "6-12个月",
        "level": "中级到高级",
        "skills": "项技能"
      },
      "dataAnalyst": {
        "title": "数据分析师",
        "description": "数据处理、分析和可视化技能",
        "duration": "3-6个月",
        "level": "初级到中级",
        "skills": "项技能"
      }
    }
  },
  "testimonials": {
    "title": "学员真实评价",
    "subtitle": "听听我们学员的成功故事",
    "reviews": {
      "student1": {
        "name": "张三",
        "role": "前端开发工程师",
        "company": "腾讯",
        "content": "通过Pageflux AI的学习，我成功转行成为了前端开发工程师。课程内容实用，项目经验丰富。",
        "rating": "5星评价"
      },
      "student2": {
        "name": "李四",
        "role": "数据科学家",
        "company": "阿里巴巴",
        "content": "AI推荐系统帮我找到了最适合的学习路径，现在我已经在数据科学领域工作了。",
        "rating": "5星评价"
      },
      "student3": {
        "name": "王五",
        "role": "全栈开发工程师",
        "company": "字节跳动",
        "content": "从零基础到全栈开发，Pageflux AI陪伴了我整个学习过程，非常感谢！",
        "rating": "5星评价"
      }
    }
  },
  "freeCourses": {
    "title": "精选免费课程",
    "subtitle": "高质量的免费学习资源，助您入门",
    "viewAll": "查看更多免费课程",
    "courses": {
      "htmlBasics": {
        "title": "HTML基础入门",
        "description": "学习网页结构的基础知识",
        "duration": "2小时",
        "students": "名学员",
        "rating": "评分"
      },
      "javascriptFundamentals": {
        "title": "JavaScript基础",
        "description": "掌握JavaScript编程基础",
        "duration": "4小时",
        "students": "名学员",
        "rating": "评分"
      },
      "pythonIntro": {
        "title": "Python入门",
        "description": "Python编程语言基础教程",
        "duration": "3小时",
        "students": "名学员",
        "rating": "评分"
      }
    }
  },
  "teamSkillsPromotion": {
    "badge": "DEVPATH",
    "title": "提升您的团队技能",
    "titleHighlight": "团队",
    "description": "利用Pageflux AI课程和内部知识，通过DevPath大规模地入职、提升技能和培训您的开发者。",
    "buttonText": "访问Devpath",
    "cta": {
      "primary": "联系我们",
      "secondary": "了解更多"
    },
    "features": {
      "customizedTraining": {
        "title": "定制化培训",
        "description": "根据团队需求设计专属的培训方案"
      },
      "progressTracking": {
        "title": "进度跟踪",
        "description": "实时监控团队成员的学习进度和成果"
      },
      "expertSupport": {
        "title": "专家支持",
        "description": "资深技术专家提供一对一指导"
      },
      "certification": {
        "title": "团队认证",
        "description": "获得团队技能认证，提升整体竞争力"
      }
    }
  },
  "joinDevelopersGlobally": {
    "title": "加入超过",
    "titleHighlight": "270万",
    "titleSuffix": "全球开发者",
    "stats": [
      {
        "value": "1500+",
        "label": "互动课程"
      },
      {
        "value": "300+",
        "label": "真实项目"
      },
      {
        "value": "#1",
        "label": "面试准备"
      }
    ],
    "cta": {
      "primary": "立即加入",
      "secondary": "了解社区"
    },
    "benefits": {
      "networking": {
        "title": "全球网络",
        "description": "与世界各地的开发者建立联系"
      },
      "collaboration": {
        "title": "协作项目",
        "description": "参与开源项目和团队协作"
      },
      "mentorship": {
        "title": "导师指导",
        "description": "获得经验丰富的开发者指导"
      },
      "opportunities": {
        "title": "职业机会",
        "description": "发现全球的工作和合作机会"
      }
    }
  }
}

export default translation
