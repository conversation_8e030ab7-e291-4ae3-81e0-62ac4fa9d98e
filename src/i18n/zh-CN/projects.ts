const translation = {
  "title": "实战项目",
  "description": "通过真实项目提升您的实践技能和项目经验",
  "hero": {
    "title": "动手实践，掌握真技能",
    "subtitle": "通过真实的项目挑战，将理论知识转化为实际能力",
    "cta": {
      "primary": "开始项目",
      "secondary": "浏览项目"
    },
    "stats": {
      "totalProjects": "个项目",
      "completedProjects": "已完成项目",
      "activeUsers": "活跃用户",
      "successRate": "成功率"
    }
  },
  "search": {
    "placeholder": "搜索项目名称、技术栈或关键词...",
    "button": "搜索",
    "clear": "清除",
    "noResults": "未找到相关项目",
    "resultsCount": "找到 {{count}} 个项目"
  },
  "filters": {
    "title": "筛选条件",
    "clear": "清除筛选",
    "apply": "应用筛选",
    "skillLevel": {
      "title": "技能等级",
      "beginner": "初级",
      "intermediate": "中级",
      "advanced": "高级"
    },
    "duration": {
      "title": "项目时长",
      "short": "短期 (1-7天)",
      "medium": "中期 (1-4周)",
      "long": "长期 (1个月以上)"
    },
    "difficulty": {
      "title": "难度等级",
      "easy": "简单",
      "medium": "中等",
      "hard": "困难",
      "expert": "专家级"
    },
    "topics": {
      "title": "项目主题",
      "webApp": "Web应用",
      "mobileApp": "移动应用",
      "api": "API开发",
      "database": "数据库",
      "frontend": "前端开发",
      "backend": "后端开发",
      "fullstack": "全栈开发",
      "dataAnalysis": "数据分析",
      "machineLearning": "机器学习",
      "devOps": "DevOps",
      "cloudComputing": "云计算",
      "blockchain": "区块链",
      "iot": "物联网",
      "gamesDevelopment": "游戏开发",
      "cybersecurity": "网络安全"
    },
    "technologies": {
      "title": "技术栈",
      "javascript": "JavaScript",
      "typescript": "TypeScript",
      "python": "Python",
      "java": "Java",
      "csharp": "C#",
      "cpp": "C++",
      "go": "Go",
      "rust": "Rust",
      "php": "PHP",
      "ruby": "Ruby",
      "swift": "Swift",
      "kotlin": "Kotlin",
      "react": "React",
      "vue": "Vue.js",
      "angular": "Angular",
      "nodejs": "Node.js",
      "express": "Express.js",
      "django": "Django",
      "flask": "Flask",
      "spring": "Spring",
      "dotnet": ".NET",
      "laravel": "Laravel",
      "rails": "Ruby on Rails",
      "mysql": "MySQL",
      "postgresql": "PostgreSQL",
      "mongodb": "MongoDB",
      "redis": "Redis",
      "aws": "AWS",
      "azure": "Azure",
      "gcp": "Google Cloud",
      "docker": "Docker",
      "kubernetes": "Kubernetes",
      "git": "Git",
      "jenkins": "Jenkins",
      "terraform": "Terraform"
    },
    "projectType": {
      "title": "项目类型",
      "guided": "引导式项目",
      "challenge": "挑战项目",
      "openEnded": "开放式项目",
      "portfolio": "作品集项目",
      "realWorld": "真实世界项目",
      "collaborative": "协作项目"
    },
    "industry": {
      "title": "行业领域",
      "ecommerce": "电子商务",
      "fintech": "金融科技",
      "healthcare": "医疗健康",
      "education": "教育培训",
      "entertainment": "娱乐媒体",
      "socialMedia": "社交媒体",
      "productivity": "生产力工具",
      "travel": "旅游出行",
      "food": "餐饮美食",
      "fitness": "健身运动",
      "news": "新闻资讯",
      "weather": "天气预报",
      "finance": "金融理财",
      "realEstate": "房地产",
      "automotive": "汽车交通"
    }
  },
  "sort": {
    "title": "排序方式",
    "relevance": "相关性",
    "popularity": "热门程度",
    "difficulty": "难度等级",
    "duration": "项目时长",
    "newest": "最新发布",
    "rating": "评分",
    "completions": "完成人数"
  },
  "projectCard": {
    "new": "新项目",
    "popular": "热门",
    "featured": "精选",
    "updated": "已更新",
    "duration": "预计时长",
    "difficulty": "难度",
    "participants": "参与者",
    "completions": "完成数",
    "rating": "评分",
    "reviews": "评价",
    "technologies": "技术栈",
    "skills": "技能点",
    "status": {
      "notStarted": "未开始",
      "inProgress": "进行中",
      "completed": "已完成",
      "submitted": "已提交",
      "reviewed": "已评审"
    },
    "startProject": "开始项目",
    "continueProject": "继续项目",
    "viewProject": "查看项目",
    "preview": "预览",
    "bookmark": "收藏",
    "share": "分享",
    "types": {
      "guided": "引导式",
      "challenge": "挑战",
      "openEnded": "开放式",
      "portfolio": "作品集",
      "realWorld": "真实项目",
      "collaborative": "协作"
    }
  },
  "projectDetails": {
    "tabs": {
      "overview": "项目概览",
      "requirements": "项目要求",
      "instructions": "项目说明",
      "resources": "学习资源",
      "submissions": "提交作品",
      "discussions": "讨论区"
    },
    "overview": {
      "title": "项目概览",
      "description": "项目描述",
      "objectives": "学习目标",
      "outcomes": "预期成果",
      "prerequisites": "前置要求",
      "estimatedTime": "预计时间",
      "difficulty": "难度等级",
      "technologies": "使用技术",
      "skills": "技能收获",
      "industry": "应用领域",
      "projectType": "项目类型"
    },
    "requirements": {
      "title": "项目要求",
      "technical": "技术要求",
      "functional": "功能要求",
      "design": "设计要求",
      "performance": "性能要求",
      "deliverables": "交付物",
      "evaluation": "评估标准"
    },
    "instructions": {
      "title": "项目说明",
      "gettingStarted": "开始指南",
      "stepByStep": "分步说明",
      "tips": "实用技巧",
      "commonIssues": "常见问题",
      "troubleshooting": "问题排查",
      "bestPractices": "最佳实践"
    },
    "resources": {
      "title": "学习资源",
      "documentation": "文档资料",
      "tutorials": "教程视频",
      "codeExamples": "代码示例",
      "templates": "项目模板",
      "tools": "推荐工具",
      "references": "参考资料",
      "externalLinks": "外部链接"
    },
    "submissions": {
      "title": "提交作品",
      "mySubmission": "我的提交",
      "otherSubmissions": "其他作品",
      "submitProject": "提交项目",
      "updateSubmission": "更新提交",
      "viewSubmission": "查看提交",
      "feedback": "反馈意见",
      "rating": "评分",
      "comments": "评论"
    },
    "discussions": {
      "title": "讨论区",
      "askQuestion": "提问",
      "shareIdeas": "分享想法",
      "getHelp": "寻求帮助",
      "showSolution": "展示方案",
      "generalDiscussion": "一般讨论",
      "technicalHelp": "技术帮助",
      "projectShowcase": "项目展示"
    }
  },
  "progress": {
    "title": "项目进度",
    "overall": "总体进度",
    "currentPhase": "当前阶段",
    "nextPhase": "下一阶段",
    "completed": "已完成",
    "inProgress": "进行中",
    "upcoming": "即将开始",
    "phases": {
      "planning": "项目规划",
      "design": "设计阶段",
      "development": "开发阶段",
      "testing": "测试阶段",
      "deployment": "部署阶段",
      "review": "评审阶段"
    },
    "milestones": {
      "title": "项目里程碑",
      "completed": "已完成里程碑",
      "current": "当前里程碑",
      "upcoming": "即将到来的里程碑"
    }
  },
  "collaboration": {
    "title": "团队协作",
    "teamMembers": "团队成员",
    "inviteMembers": "邀请成员",
    "roles": {
      "owner": "项目负责人",
      "collaborator": "协作者",
      "reviewer": "评审者",
      "viewer": "查看者"
    },
    "tools": {
      "chat": "团队聊天",
      "videoCall": "视频通话",
      "sharedWorkspace": "共享工作区",
      "versionControl": "版本控制",
      "taskManagement": "任务管理",
      "fileSharing": "文件共享"
    }
  },
  "evaluation": {
    "title": "项目评估",
    "criteria": "评估标准",
    "rubric": "评分标准",
    "selfAssessment": "自我评估",
    "peerReview": "同行评议",
    "mentorFeedback": "导师反馈",
    "dimensions": {
      "functionality": "功能实现",
      "codeQuality": "代码质量",
      "design": "设计美观",
      "usability": "用户体验",
      "innovation": "创新性",
      "documentation": "文档完整性",
      "presentation": "展示效果"
    }
  },
  "achievements": {
    "title": "成就系统",
    "certificate": "项目证书",
    "badges": "技能徽章",
    "portfolio": "作品集",
    "types": {
      "firstProject": "首个项目",
      "projectMaster": "项目大师",
      "collaborator": "协作达人",
      "innovator": "创新者",
      "mentor": "项目导师",
      "reviewer": "优秀评审"
    }
  },
  "messages": {
    "projectStarted": "项目已开始，祝您学习愉快！",
    "progressSaved": "进度已保存",
    "submissionSuccess": "项目提交成功",
    "submissionError": "提交失败，请重试",
    "feedbackSubmitted": "反馈已提交",
    "invitationSent": "邀请已发送",
    "memberAdded": "成员添加成功",
    "bookmarkAdded": "已添加到收藏",
    "bookmarkRemoved": "已从收藏移除",
    "shareSuccess": "分享链接已复制"
  },
  "empty": {
    "noProjects": "暂无项目",
    "noResults": "没有找到匹配的项目",
    "noSubmissions": "暂无提交作品",
    "noDiscussions": "暂无讨论",
    "noProgress": "尚未开始项目",
    "suggestion": "建议您：",
    "suggestions": {
      "adjustFilters": "调整筛选条件",
      "browseCategories": "浏览其他分类",
      "searchDifferent": "尝试不同的搜索词",
      "startBeginner": "从初级项目开始",
      "joinCommunity": "加入社区讨论"
    }
  }
}

export default translation
