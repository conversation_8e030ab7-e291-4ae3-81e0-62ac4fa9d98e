const translation = {
  popular: {
    title: "热门主题和编程语言",
  },
  more: {
    title: "查找更多主题和编程语言",
  },
  hero: {
    coursesAndGuides: "课程和指南",
    startFreeTrial: "开始免费试用",
    exploreAll: "探索全部",
  },
  defaults: {
    categoryName: "编程语言",
    categoryDescription: "学习编程基础知识和高级概念。",
  },
  languages: {
    python: {
      name: "Python",
      description:
        "Python是世界上最受欢迎的编程语言之一，以其简洁性和可读性语法而闻名。",
    },
    javascript: {
      name: "JavaScript",
      description: "JavaScript是Web的编程语言，支持交互式和动态Web应用程序。",
    },
    java: {
      name: "Java",
      description:
        "Java是一种强大的面向对象编程语言，用于企业应用程序和Android开发。",
    },
  },
  categories: {
    allCategories: "所有分类",
    programmingFundamentals: "编程基础",
    interviewPrep: "面试准备",
    systemDesign: "系统设计",
    machineLearning: "机器学习",
    aiTools: "AI工具",
    webDevelopment: "Web开发",
    dataScience: "数据科学",
    frontendFrameworks: "前端框架",
    backendDevelopment: "后端开发",
    objectOrientedProgramming: "面向对象编程",
    springFramework: "Spring框架",
    enterpriseDevelopment: "企业开发",
    androidDevelopment: "Android开发",
    microservices: "微服务",
    advancedProgramming: "高级编程",
  },
};

export default translation;
