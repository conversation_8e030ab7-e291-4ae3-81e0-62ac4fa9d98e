const translation = {
  "title": "学习路径",
  "description": "精心设计的系统化学习方案，助您从入门到精通",
  "hero": {
    "title": "学习路径",
    "subtitle": "所有学习路径都经过精心策划，帮助您实现特定的学习目标。在这里找到最适合您需求的学习路径。",
    "cta": {
      "primary": "开始学习",
      "secondary": "浏览路径"
    },
    "stats": {
      "totalPaths": "条学习路径",
      "completedPaths": "已完成路径",
      "activeStudents": "活跃学员",
      "averageCompletion": "平均完成率"
    }
  },
  "search": {
    "placeholder": "在这里搜索学习路径",
    "button": "搜索",
    "clear": "清除",
    "noResults": "未找到相关学习路径",
    "resultsCount": "找到 {{count}} 条学习路径"
  },
  "filters": {
    "title": "筛选条件",
    "clear": "清除筛选",
    "apply": "应用筛选",
    "skillLevel": {
      "title": "技能等级",
      "beginner": "初级",
      "intermediate": "中级",
      "advanced": "高级"
    },
    "duration": {
      "title": "学习时长",
      "short": "短期 (1-3个月)",
      "medium": "中期 (3-6个月)",
      "long": "长期 (6个月以上)"
    },
    "topics": {
      "title": "学习主题",
      "webDevelopment": "Web开发",
      "mobileDevelopment": "移动开发",
      "dataScience": "数据科学",
      "machineLearning": "机器学习",
      "artificialIntelligence": "人工智能",
      "cloudComputing": "云计算",
      "devOps": "DevOps",
      "cybersecurity": "网络安全",
      "blockchain": "区块链",
      "gamesDevelopment": "游戏开发",
      "uiUxDesign": "UI/UX设计",
      "digitalMarketing": "数字营销",
      "projectManagement": "项目管理",
      "businessAnalysis": "业务分析",
      "qualityAssurance": "质量保证"
    },
    "technologies": {
      "title": "技术栈",
      "javascript": "JavaScript",
      "typescript": "TypeScript",
      "python": "Python",
      "java": "Java",
      "csharp": "C#",
      "cpp": "C++",
      "go": "Go",
      "rust": "Rust",
      "php": "PHP",
      "ruby": "Ruby",
      "swift": "Swift",
      "kotlin": "Kotlin",
      "react": "React",
      "vue": "Vue.js",
      "angular": "Angular",
      "nodejs": "Node.js",
      "django": "Django",
      "flask": "Flask",
      "spring": "Spring",
      "dotnet": ".NET",
      "laravel": "Laravel",
      "rails": "Ruby on Rails",
      "mysql": "MySQL",
      "postgresql": "PostgreSQL",
      "mongodb": "MongoDB",
      "redis": "Redis",
      "aws": "AWS",
      "azure": "Azure",
      "gcp": "Google Cloud",
      "docker": "Docker",
      "kubernetes": "Kubernetes",
      "tensorflow": "TensorFlow",
      "pytorch": "PyTorch",
      "scikit": "Scikit-learn",
      "pandas": "Pandas",
      "numpy": "NumPy"
    },
    "careerGoal": {
      "title": "职业目标",
      "frontendDeveloper": "前端开发工程师",
      "backendDeveloper": "后端开发工程师",
      "fullstackDeveloper": "全栈开发工程师",
      "mobileAppDeveloper": "移动应用开发工程师",
      "dataScientist": "数据科学家",
      "dataAnalyst": "数据分析师",
      "mlEngineer": "机器学习工程师",
      "devOpsEngineer": "DevOps工程师",
      "cloudArchitect": "云架构师",
      "cybersecuritySpecialist": "网络安全专家",
      "blockchainDeveloper": "区块链开发工程师",
      "gameDeveloper": "游戏开发工程师",
      "uiUxDesigner": "UI/UX设计师",
      "productManager": "产品经理",
      "projectManager": "项目经理",
      "businessAnalyst": "业务分析师",
      "qaEngineer": "质量保证工程师",
      "systemAdministrator": "系统管理员",
      "databaseAdministrator": "数据库管理员",
      "softwareArchitect": "软件架构师"
    },
    "industry": {
      "title": "行业领域",
      "technology": "科技",
      "finance": "金融",
      "healthcare": "医疗",
      "education": "教育",
      "ecommerce": "电子商务",
      "entertainment": "娱乐",
      "gaming": "游戏",
      "automotive": "汽车",
      "manufacturing": "制造业",
      "retail": "零售",
      "consulting": "咨询",
      "government": "政府",
      "nonprofit": "非营利组织",
      "startup": "初创企业",
      "enterprise": "大型企业"
    }
  },
  "sort": {
    "title": "排序方式",
    "relevance": "相关性",
    "popularity": "热门程度",
    "rating": "评分",
    "duration": "学习时长",
    "difficulty": "难度等级",
    "newest": "最新发布",
    "completions": "完成人数",
    "enrollments": "报名人数"
  },
  "pathCard": {
    "new": "新路径",
    "popular": "热门",
    "featured": "精选",
    "updated": "已更新",
    "bestseller": "最受欢迎",
    "duration": "学习时长",
    "courses": "包含课程",
    "projects": "实战项目",
    "skills": "技能点",
    "level": "适合等级",
    "students": "学习人数",
    "rating": "评分",
    "reviews": "评价",
    "completion": "完成率",
    "certificate": "获得证书",
    "status": {
      "notStarted": "未开始",
      "inProgress": "学习中",
      "completed": "已完成",
      "paused": "已暂停"
    },
    "startPath": "开始学习",
    "continuePath": "继续学习",
    "viewPath": "查看路径",
    "preview": "预览",
    "bookmark": "收藏",
    "share": "分享",
    "enroll": "立即报名",
    "types": {
      "guided": "引导式",
      "selfPaced": "自主学习",
      "mentored": "导师指导",
      "bootcamp": "训练营",
      "specialization": "专业化",
      "certification": "认证课程"
    }
  },
  "pathDetails": {
    "tabs": {
      "overview": "路径概览",
      "curriculum": "课程大纲",
      "projects": "实战项目",
      "skills": "技能树",
      "instructors": "讲师团队",
      "reviews": "学员评价",
      "faq": "常见问题"
    },
    "overview": {
      "title": "学习路径概览",
      "description": "路径描述",
      "objectives": "学习目标",
      "outcomes": "学习成果",
      "prerequisites": "前置要求",
      "targetAudience": "适合人群",
      "careerOutcomes": "职业前景",
      "estimatedTime": "预计时间",
      "difficulty": "难度等级",
      "format": "学习形式",
      "support": "学习支持",
      "certificate": "证书认证"
    },
    "curriculum": {
      "title": "课程大纲",
      "totalCourses": "总课程数",
      "totalHours": "总学时",
      "modules": "学习模块",
      "expandAll": "展开全部",
      "collapseAll": "收起全部",
      "module": {
        "courses": "门课程",
        "hours": "学时",
        "projects": "个项目",
        "quizzes": "个测验",
        "assignments": "个作业",
        "optional": "选修",
        "required": "必修",
        "prerequisite": "前置课程"
      }
    },
    "projects": {
      "title": "实战项目",
      "totalProjects": "项目总数",
      "capstoneProject": "毕业项目",
      "portfolioProjects": "作品集项目",
      "project": {
        "duration": "项目时长",
        "difficulty": "难度等级",
        "technologies": "使用技术",
        "skills": "技能应用",
        "deliverables": "交付成果",
        "mentorship": "导师指导"
      }
    },
    "skills": {
      "title": "技能树",
      "coreSkills": "核心技能",
      "technicalSkills": "技术技能",
      "softSkills": "软技能",
      "industrySkills": "行业技能",
      "levels": {
        "beginner": "入门",
        "intermediate": "中级",
        "advanced": "高级",
        "expert": "专家"
      },
      "status": {
        "notStarted": "未开始",
        "learning": "学习中",
        "practiced": "已练习",
        "mastered": "已掌握"
      }
    },
    "instructors": {
      "title": "讲师团队",
      "leadInstructor": "首席讲师",
      "instructorTeam": "讲师团队",
      "guestExperts": "客座专家",
      "instructor": {
        "experience": "教学经验",
        "students": "教授学员",
        "courses": "开设课程",
        "rating": "讲师评分",
        "expertise": "专业领域",
        "background": "教育背景",
        "achievements": "主要成就"
      }
    },
    "reviews": {
      "title": "学员评价",
      "averageRating": "平均评分",
      "totalReviews": "总评价数",
      "ratingBreakdown": "评分分布",
      "mostHelpful": "最有帮助",
      "recent": "最新评价",
      "verified": "已验证学员",
      "dimensions": {
        "content": "内容质量",
        "instruction": "教学质量",
        "support": "学习支持",
        "value": "性价比",
        "career": "职业帮助"
      }
    },
    "faq": {
      "title": "常见问题",
      "general": "一般问题",
      "enrollment": "报名相关",
      "learning": "学习相关",
      "technical": "技术问题",
      "certificate": "证书相关",
      "career": "职业发展"
    }
  },
  "progress": {
    "title": "学习进度",
    "overall": "总体进度",
    "currentModule": "当前模块",
    "nextModule": "下一模块",
    "completed": "已完成",
    "inProgress": "学习中",
    "upcoming": "即将开始",
    "stats": {
      "coursesCompleted": "完成课程",
      "projectsSubmitted": "提交项目",
      "skillsLearned": "掌握技能",
      "hoursSpent": "学习时长",
      "streak": "连续学习天数",
      "achievements": "获得成就"
    },
    "milestones": {
      "title": "学习里程碑",
      "completed": "已完成里程碑",
      "current": "当前里程碑",
      "upcoming": "即将到来的里程碑"
    }
  },
  "certification": {
    "title": "证书认证",
    "pathCertificate": "路径完成证书",
    "skillBadges": "技能徽章",
    "industryRecognition": "行业认可",
    "types": {
      "completion": "完成证书",
      "proficiency": "能力证书",
      "specialization": "专业证书",
      "professional": "职业证书"
    },
    "requirements": {
      "courseCompletion": "完成所有课程",
      "projectSubmission": "提交所有项目",
      "skillAssessment": "通过技能评估",
      "finalExam": "通过期末考试",
      "peerReview": "完成同行评议"
    }
  },
  "messages": {
    "pathStarted": "学习路径已开始，祝您学习愉快！",
    "progressSaved": "学习进度已保存",
    "moduleCompleted": "模块学习完成",
    "pathCompleted": "恭喜！您已完成整个学习路径",
    "certificateEarned": "恭喜获得证书！",
    "bookmarkAdded": "已添加到收藏",
    "bookmarkRemoved": "已从收藏移除",
    "shareSuccess": "分享链接已复制",
    "enrollmentSuccess": "报名成功",
    "enrollmentError": "报名失败，请重试"
  },
  "empty": {
    "noPaths": "暂无学习路径",
    "noResults": "没有找到匹配的学习路径",
    "noProgress": "尚未开始任何学习路径",
    "noBookmarks": "暂无收藏的路径",
    "suggestion": "建议您：",
    "suggestions": {
      "adjustFilters": "调整筛选条件",
      "browseCategories": "浏览不同分类",
      "searchDifferent": "尝试不同的搜索词",
      "startBeginner": "从初级路径开始",
      "explorePopular": "查看热门路径"
    }
  }
}

export default translation
