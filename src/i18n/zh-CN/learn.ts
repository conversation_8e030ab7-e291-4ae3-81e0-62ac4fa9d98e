/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-08-11 21:54:03
 */
const translation = {
  myCourses: {
    title: "我的课程",
    empty: {
      title: "还没有注册任何课程",
      description: "通过注册课程开始您的学习之旅",
      button: "浏览课程",
    },
  },
  inProgress: {
    title: "进行中",
    empty: {
      title: "没有正在学习的课程",
      description: "开始一门课程来查看您的学习进度",
      button: "开始学习",
    },
  },
  favorites: {
    title: "收藏",
    empty: {
      title: "还没有收藏任何内容",
      description: "收藏您喜欢的课程和项目",
      button: "发现内容",
    },
  },
  completed: {
    title: "已完成",
    empty: {
      title: "还没有完成任何课程",
      description: "完成您的第一门课程以获得证书和成就",
      button: "开始学习",
    },
  },
  recentlyViewed: {
    title: "最近查看",
    empty: {
      title: "还没有查看任何内容",
      description: "您最近浏览的课程将在这里显示",
      button: "开始浏览",
    },
  },
  search: {
    placeholder: "搜索课程",
  },
  common: {
    continueReading: "继续阅读",
    startCourse: "开始课程",
    resume: "继续",
    completed: "已完成",
    inProgress: "进行中",
    notStarted: "未开始",
    timeLeft: "剩余时间",
    progress: "进度",
    lesson: "课时",
    lessons: "课时",
    hour: "小时",
    hours: "小时",
    minute: "分钟",
    minutes: "分钟",
  },
  header: {
    welcome: "欢迎回来，",
    subtitle: "继续您的学习之旅",
    preferences: "学习偏好",
  },
  tabs: {
    myCourses: "我的课程",
    inProgress: "进行中",
    favorites: "收藏",
    completed: "已完成",
    recentlyViewed: "最近查看",
  },
  actions: {
    viewAll: "查看全部",
    startLearning: "开始学习",
    continueLearning: "继续学习",
    browseContent: "浏览内容",
    discoverContent: "发现内容",
  },
  features: {
    mockInterview: {
      title: "模拟面试",
      description: "通过真实的编程面试和系统设计挑战测试您的技能。",
    },
    aiCodeReview: {
      title: "AI代码审查",
      description: "获得AI驱动的代码反馈和改进建议。",
    },
    personalizedPath: {
      title: "个性化路径",
      description: "根据您的技能和目标定制的学习路径。",
    },
    cloudLabs: {
      title: "云实验室",
      description:
        "通过 AWS 云计算技能获得实践经验 — 无需设置，无需清理，无需麻烦。",
    },
    projects: {
      title: "项目",
      description:
        "通过构建 300+ 全功能项目来练习您的技能，从 AI 和 ML 到 Web 开发。",
    },
  },
  sampleCourses: {
    javascriptFundamentals: {
      title: "JavaScript 基础",
      description:
        "掌握 JavaScript 编程语言的基础知识，包括变量、函数、对象和现代 ES6+ 特性。",
    },
    htmlCssEssentials: {
      title: "HTML & CSS 基础",
      description:
        "学习 Web 开发基础知识，包括 HTML 和 CSS，以及响应式设计和现代布局技术。",
    },
    gitVersionControl: {
      title: "Git 版本控制",
      description:
        "掌握 Git 版本控制和协作，包括分支、合并和高级 Git 工作流程。",
    },
  },
  kickstart: {
    title: "开启您的技术职业生涯",
    description:
      "以训练营或大学学习技能的一小部分成本进入技术领域。从您的第一行代码到您的第一份工作，我们将指导您完成整个过程。",
    button: "开始学习",
  },
  sections: {
    mostPopularCourses: "最受欢迎的课程",
    newAdditions: "新增课程",
    showMore: "显示更多",
    showLess: "显示更少",
    discoverMoreFeatures: "发现更多功能",
    yourActivity: "您的活动",
    learningStreak: "学习连续天数",
    viewAll: "查看全部",
    exploreAll: "探索全部",
  },
  goalCategories: [
    "Web开发",
    "移动开发",
    "数据科学",
    "机器学习",
    "DevOps",
    "云计算",
    "网络安全",
    "UI/UX设计",
    "后端开发",
    "前端开发",
    "数据库管理",
    "软件工程",
  ],
};

export default translation;
