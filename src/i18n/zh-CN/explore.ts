const translation = {
  title: "探索学习",
  description: "发现新的学习机会，探索各种技术领域和课程",
  searchBar: {
    placeholder: "搜索课程、技能、项目或学习路径...",
    button: "搜索",
    suggestions: "搜索建议",
    recentSearches: "最近搜索",
    popularSearches: "热门搜索",
    clearHistory: "清除历史",
  },
  featureCards: {
    title: "特色学习内容",
    subtitle: "精选的高质量学习资源",
    courses: {
      title: "精品课程",
      description: "由行业专家精心设计的课程",
      count: "门课程",
      viewAll: "查看全部课程",
    },
    projects: {
      title: "实战项目",
      description: "通过真实项目提升实践能力",
      count: "个项目",
      viewAll: "查看全部项目",
    },
    paths: {
      title: "学习路径",
      description: "系统化的技能提升方案",
      count: "条路径",
      viewAll: "查看全部路径",
    },
    labs: {
      title: "云实验室",
      description: "在线实验环境，即开即用",
      count: "个实验",
      viewAll: "查看全部实验",
    },
  },
  mostPopular: {
    title: "最受欢迎",
    description: "学员最喜爱的热门内容",
  },
  mostPopularCourses: {
    title: "最受欢迎的课程",
    subtitle: "学员最喜爱的热门课程",
    viewAll: "查看更多",
    course: {
      students: "名学员",
      rating: "评分",
      reviews: "评价",
      duration: "时长",
      level: "难度",
      free: "免费",
      premium: "高级",
      bestseller: "畅销",
      new: "新课程",
      updated: "已更新",
    },
  },
  newAdditions: {
    title: "最新添加",
    description: "最近发布的新课程和内容",
    subtitle: "最近发布的新课程和内容",
    viewAll: "查看全部新内容",
    timeLabels: {
      today: "今天",
      yesterday: "昨天",
      thisWeek: "本周",
      lastWeek: "上周",
      thisMonth: "本月",
    },
  },
  categories: {
    title: "按分类浏览",
    subtitle: "选择您感兴趣的技术领域",
    viewAll: "查看全部分类",
    webDevelopment: {
      title: "Web开发",
      description: "前端、后端和全栈开发技术",
      courses: "门课程",
      icon: "web",
    },
    mobileDevelopment: {
      title: "移动开发",
      description: "iOS、Android和跨平台开发",
      courses: "门课程",
      icon: "mobile",
    },
    dataScience: {
      title: "数据科学",
      description: "数据分析、机器学习和AI",
      courses: "门课程",
      icon: "data",
    },
    cloudComputing: {
      title: "云计算",
      description: "AWS、Azure、GCP等云服务",
      courses: "门课程",
      icon: "cloud",
    },
    devOps: {
      title: "DevOps",
      description: "自动化部署和运维管理",
      courses: "门课程",
      icon: "devops",
    },
    cybersecurity: {
      title: "网络安全",
      description: "信息安全和渗透测试",
      courses: "门课程",
      icon: "security",
    },
    artificialIntelligence: {
      title: "人工智能",
      description: "机器学习、深度学习和AI应用",
      courses: "门课程",
      icon: "ai",
    },
    blockchain: {
      title: "区块链",
      description: "区块链技术和加密货币",
      courses: "门课程",
      icon: "blockchain",
    },
    gamesDevelopment: {
      title: "游戏开发",
      description: "游戏引擎和游戏编程",
      courses: "门课程",
      icon: "games",
    },
    uiUxDesign: {
      title: "UI/UX设计",
      description: "用户界面和用户体验设计",
      courses: "门课程",
      icon: "design",
    },
  },
  recommendedPaths: {
    title: "推荐学习路径",
    subtitle: "根据您的兴趣为您推荐的学习路径",
    viewAll: "查看全部路径",
    path: {
      duration: "预计时长",
      courses: "包含课程",
      projects: "实战项目",
      skills: "技能点",
      level: "适合等级",
      students: "学习人数",
      completion: "完成率",
      certificate: "获得证书",
    },
  },
  skillAssessment: {
    title: "技能评估",
    subtitle: "测试您的技能水平，获得个性化学习建议",
    types: {
      programming: {
        title: "编程基础",
        description: "测试您的编程基础知识",
        duration: "15分钟",
        questions: "20题",
      },
      webDev: {
        title: "Web开发",
        description: "评估您的Web开发技能",
        duration: "20分钟",
        questions: "25题",
      },
      dataScience: {
        title: "数据科学",
        description: "测试您的数据分析能力",
        duration: "25分钟",
        questions: "30题",
      },
    },
    startAssessment: "开始评估",
    viewResults: "查看结果",
    retakeAssessment: "重新评估",
  },
  learningStats: {
    title: "学习统计",
    global: {
      totalLearners: "总学习者",
      coursesCompleted: "完成课程数",
      hoursLearned: "学习时长",
      certificatesEarned: "获得证书",
    },
    personal: {
      yourProgress: "您的进度",
      coursesEnrolled: "已报名课程",
      coursesCompleted: "已完成课程",
      hoursSpent: "学习时长",
      skillsLearned: "掌握技能",
      certificatesEarned: "获得证书",
      currentStreak: "连续学习天数",
      longestStreak: "最长连续天数",
    },
  },
  communityActivity: {
    title: "社区动态",
    subtitle: "看看其他学习者在做什么",
    activities: {
      courseCompleted: "完成了课程",
      projectSubmitted: "提交了项目",
      certificateEarned: "获得了证书",
      pathStarted: "开始了学习路径",
      discussionPosted: "发布了讨论",
      questionAnswered: "回答了问题",
    },
    timeAgo: {
      justNow: "刚刚",
      minutesAgo: "分钟前",
      hoursAgo: "小时前",
      daysAgo: "天前",
      weeksAgo: "周前",
    },
  },
  actions: {
    startLearning: "开始学习",
    continueReading: "继续阅读",
    viewDetails: "查看详情",
    enroll: "立即报名",
    preview: "预览",
    bookmark: "收藏",
    share: "分享",
    download: "下载",
    print: "打印",
    export: "导出",
    filter: "筛选",
    sort: "排序",
    refresh: "刷新",
    loadMore: "加载更多",
    showLess: "显示更少",
    expand: "展开",
    collapse: "收起",
  },
  empty: {
    noContent: "暂无内容",
    noResults: "没有找到相关结果",
    noBookmarks: "暂无收藏内容",
    noHistory: "暂无浏览历史",
    suggestion: "建议您：",
    suggestions: {
      browseCategories: "浏览不同分类",
      adjustFilters: "调整筛选条件",
      tryDifferentKeywords: "尝试不同关键词",
      checkSpelling: "检查拼写是否正确",
      explorePopular: "查看热门内容",
    },
  },
  loading: {
    content: "正在加载内容...",
    courses: "正在加载课程...",
    projects: "正在加载项目...",
    paths: "正在加载学习路径...",
    search: "正在搜索...",
    more: "正在加载更多...",
  },
  suggestions: {
    trendingNowTitle: "热门趋势",
    trendingNow: ["数据科学", "机器学习", "生成式AI"],
    popularCourses: [
      {
        title: "Go语言之路",
        level: "中级",
        duration: "25小时",
      },
      {
        title: "使用面向对象设计原则掌握低级设计面试",
        level: "中级",
        duration: "50小时",
      },
      {
        title: "掌握现代系统设计面试",
        level: "中级",
        duration: "26小时",
      },
      {
        title: "成为高效的软件工程经理",
        level: "初级",
        duration: "10小时45分钟",
      },
    ],
  },
  categoryDescription: "探索{{title}}课程和学习资源",
};

export default translation;
