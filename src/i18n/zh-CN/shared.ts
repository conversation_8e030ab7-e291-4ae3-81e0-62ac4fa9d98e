const translation = {
  faq: {
    title: "常见问题",
    subtitle: "查找您需要的答案",
    items: [
      {
        question: "什么是 Pageflux AI？",
        answer:
          "Pageflux AI 是一个AI驱动的在线学习平台，提供编程课程、项目实践和技能评估。",
      },
      {
        question: "如何开始学习？",
        answer:
          "您可以免费注册账户，然后浏览我们的课程目录，选择适合您水平的课程开始学习。",
      },
      {
        question: "课程是否免费？",
        answer:
          "我们提供免费和付费课程。免费用户可以访问基础内容，付费用户可以获得完整的学习体验。",
      },
      {
        question: "是否提供证书？",
        answer:
          "是的，完成课程后您将获得结业证书，可以添加到您的简历或LinkedIn档案中。",
      },
      {
        question: "如何联系客服？",
        answer:
          "您可以通过邮件 <EMAIL> 或在线聊天联系我们的客服团队。",
      },
    ],
  },
  testimonials: {
    title: "学员评价",
    subtitle: "看看其他学员怎么说",
    items: [
      {
        name: "张三",
        role: "前端开发工程师",
        company: "腾讯",
        content: "Pageflux AI 的课程质量很高，项目实践让我快速提升了编程技能。",
        rating: 5,
      },
      {
        name: "李四",
        role: "全栈开发工程师",
        company: "阿里巴巴",
        content: "AI辅助学习功能很棒，能够根据我的学习进度调整内容难度。",
        rating: 5,
      },
      {
        name: "王五",
        role: "数据科学家",
        company: "字节跳动",
        content: "云实验室环境很方便，不需要本地配置就能直接开始编程练习。",
        rating: 5,
      },
    ],
  },
  aiLearning: {
    title: "AI驱动的实践学习",
    subtitle: "看看 Pageflux AI 如何使用 AI 让您的学习比以往更加身临其境。",
    ctaButton: "立即免费试用",
    features: [
      {
        title: "即时代码反馈",
        description:
          "只需点击一个按钮即可评估和调试您的代码。获得测试用例的实时反馈，包括解决方案的时间和空间复杂度。",
        buttonText: "立即试用",
        buttonVariant: "outline",
      },
      {
        title: "AI模拟面试",
        description:
          "在模拟面试环境中测试您的技能。根据您的表现获得个性化反馈。适用于编程面试、系统设计等。",
        buttonText: "免费试用",
        buttonVariant: "primary",
      },
    ],
  },
};

export default translation;
