/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-08-11 21:07:07
 */
const translation = {
  validation: {
    fullName: "用户至少需要2个字符",
    email: "请输入有效的邮箱地址",
    password: "密码至少需要6个字符",
    confirmPassword: "请确认您的密码",
    passwordMatch: "密码不匹配",
  },
  fields: {
    fullName: "用户名",
    email: "邮箱",
    password: "密码",
    confirmPassword: "确认密码",
  },
  signup: {
    title: "创建您的免费账户",
    submit: "免费开始",
    creating: "正在创建账户...",
    hasAccount: "已有账户？",
    signInLink: "立即登录",
  },
  login: {
    title: "登录您的账户",
    submit: "登录",
    loggingIn: "正在登录...",
    forgotPassword: "忘记密码？",
    noAccount: "还没有账户？",
    signUpLink: "立即注册",
  },
  social: {
    continueWith: "使用 {{provider}} 继续",
    continueWithGoogle: "使用 Google 继续",
    continueWithEmail: "使用邮箱继续",
    or: "或",
  },
  errors: {
    invalidCredentials: "邮箱或密码错误",
    accountNotFound: "账户不存在",
    emailAlreadyExists: "该邮箱已被注册",
    weakPassword: "密码强度不够",
    networkError: "网络连接失败，请重试",
    serverError: "服务器错误，请稍后重试",
    unknownError: "未知错误，请联系客服",
  },
  success: {
    accountCreated: "账户创建成功！",
    loginSuccess: "登录成功！",
    passwordReset: "密码重置邮件已发送",
    emailVerified: "邮箱验证成功",
  },
  terms: {
    agreement: "继续，即表示您同意我们的",
    privacyPolicy: "隐私政策",
    and: "和",
    termsOfService: "服务条款",
    recaptcha: "此站点受到 reCAPTCHA 保护",
  },
};

export default translation;
