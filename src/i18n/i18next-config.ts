/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-24 20:06:21
 */
"use client";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import Cookies from "js-cookie";

import { LanguagesSupported } from "./language";
import { LOCALE_COOKIE_NAME } from "./config";

const loadLangResources = (lang: string) => ({
  translation: {
    // 共用文件
    common: require(`./${lang}/common`).default,
    layout: require(`./${lang}/layout`).default,
    login: require(`./${lang}/login`).default,
    auth: require(`./${lang}/auth`).default,
    shared: require(`./${lang}/shared`).default,
    footer: require(`./${lang}/footer`).default,

    // 页面文件
    home: require(`./${lang}/home`).default,
    courses: require(`./${lang}/courses`).default,
    explore: require(`./${lang}/explore`).default,
    projects: require(`./${lang}/projects`).default,
    paths: require(`./${lang}/paths`).default,
    pricing: require(`./${lang}/pricing`).default,
    profile: require(`./${lang}/profile`).default,
    business: require(`./${lang}/business`).default,
    careers: require(`./${lang}/careers`).default,
    interview: require(`./${lang}/interview`).default,
    cloudLabs: require(`./${lang}/cloudLabs`).default,
    catalog: require(`./${lang}/catalog`).default,
    cheatsheets: require(`./${lang}/cheatsheets`).default,
    mockInterviews: require(`./${lang}/mockInterviews`).default,
    assessments: require(`./${lang}/assessments`).default,
    learningPlans: require(`./${lang}/learningPlans`).default,
    learn: require(`./${lang}/learn`).default,
    aiFeedback: require(`./${lang}/aiFeedback`).default,
    preferences: require(`./${lang}/preferences`).default,
    errors: require(`./${lang}/errors`).default,
    playground: require(`./${lang}/playground`).default,
  },
});

// Automatically generate the resources object
const resources = LanguagesSupported.reduce((acc: any, lang: string) => {
  acc[lang] = loadLangResources(lang);
  return acc;
}, {});

i18n.use(initReactI18next).init({
  fallbackLng: "zh-CN",
  resources,
  interpolation: {
    escapeValue: false, // React already escapes values
  },
  react: {
    useSuspense: false,
  },
});

export const changeLanguage = i18n.changeLanguage;
export default i18n;
