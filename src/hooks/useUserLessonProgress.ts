/**
 * 用户课程学习进度相关的 React Hook
 */

import { useState, useCallback } from "react";
import { userLessonProgressService } from "@/service/user-lesson-progress.service";
import type {
  UserLessonProgress,
  UserLessonProgressMap,
  UpsertCompletedContentFlowRequest,
  RemoveCompletedContentFlowRequest,
  CompletedContentFlowsResponse,
} from "@/types/openapi";
import { handleApiResponse } from "@/lib/api-error-handler";
import { showToast } from "@/lib/toast-utils";

export const useUserLessonProgress = () => {
  const [loading, setLoading] = useState(false);
  const [allProgress, setAllProgress] = useState<UserLessonProgressMap>({});

  // 获取所有进度
  const fetchAllProgress =
    useCallback(async (): Promise<UserLessonProgressMap> => {
      try {
        setLoading(true);
        const response = await userLessonProgressService.getAllProgress();
        const data = handleApiResponse(response);
        setAllProgress(data || {});
        return data || {};
      } catch (error) {
        console.error("Failed to fetch all progress:", error);
        showToast.error("获取学习进度失败");
        return {};
      } finally {
        setLoading(false);
      }
    }, []);

  // 获取指定路径或节点的进度
  const fetchProgressByNodeOrPath = useCallback(
    async (nodePathId: string): Promise<UserLessonProgressMap> => {
      try {
        setLoading(true);
        const response =
          await userLessonProgressService.getProgressByNodeOrPath(nodePathId);
        const data = handleApiResponse(response);
        return data || {};
      } catch (error) {
        console.error("Failed to fetch progress by node/path:", error);
        showToast.error("获取学习进度失败");
        return {};
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 获取单个课程进度
  const fetchLessonProgress = useCallback(
    async (
      nodePathId: string,
      lessonId: string
    ): Promise<UserLessonProgress | null> => {
      try {
        setLoading(true);
        const response = await userLessonProgressService.getLessonProgress(
          nodePathId,
          lessonId
        );
        const data = handleApiResponse(response);
        return data || null;
      } catch (error) {
        console.error("Failed to fetch lesson progress:", error);
        showToast.error("获取课程进度失败");
        return null;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 获取已完成的内容流
  const fetchCompletedContentFlows = useCallback(
    async (
      nodePathId: string,
      lessonId: string
    ): Promise<CompletedContentFlowsResponse | null> => {
      try {
        setLoading(true);
        const response =
          await userLessonProgressService.getCompletedContentFlows(
            nodePathId,
            lessonId
          );
        const data = handleApiResponse(response);
        return data || null;
      } catch (error) {
        console.error("Failed to fetch completed content flows:", error);
        showToast.error("获取已完成内容流失败");
        return null;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 标记内容流为已完成
  const markContentFlowCompleted = useCallback(
    async (request: UpsertCompletedContentFlowRequest) => {
      try {
        setLoading(true);
        const response =
          await userLessonProgressService.upsertCompletedContentFlow(request);
        const data = handleApiResponse(response);

        // 可以选择刷新相关数据
        // await fetchAllProgress();

        return data;
      } catch (error) {
        console.error("Failed to mark content flow as completed:", error);
        showToast.error("标记内容流完成失败");
        return null;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 移除已完成的内容流
  const removeCompletedContentFlow = useCallback(
    async (request: RemoveCompletedContentFlowRequest) => {
      try {
        setLoading(true);
        const response =
          await userLessonProgressService.removeCompletedContentFlow(request);
        const data = handleApiResponse(response);
        showToast.success("内容流已标记为未完成");

        // 可以选择刷新相关数据
        // await fetchAllProgress();

        return data;
      } catch (error) {
        console.error("Failed to remove completed content flow:", error);
        showToast.error("移除内容流完成状态失败");
        return null;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  return {
    loading,
    allProgress,
    fetchAllProgress,
    fetchProgressByNodeOrPath,
    fetchLessonProgress,
    fetchCompletedContentFlows,
    markContentFlowCompleted,
    removeCompletedContentFlow,
  };
};
