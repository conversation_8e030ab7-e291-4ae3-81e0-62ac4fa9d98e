"use client";

import { useState, useCallback } from "react";
import { profileService } from "@/service/profile.service";
import { toast } from "sonner";

interface PreferencesFormData {
  // Basic Information
  age: string;
  gender: string;
  preferredLanguage: string;

  // Educational Background
  educationExperience: string;
  major: string;
  graduationYear: string;

  // Professional Background
  currentRole: string;
  industry: string;
  workExperience: string;

  // Learning Preferences
  learningStyle: string;
  studyTimePerWeek: string;
  preferredStudyTime: string;
  learningPace: string;
}

interface UsePreferencesReturn {
  formData: PreferencesFormData;
  setFormData: React.Dispatch<React.SetStateAction<PreferencesFormData>>;
  isLoading: boolean;
  isSaving: boolean;
  loadPreferences: () => Promise<void>;
  savePreferences: () => Promise<boolean>;
  resetForm: () => void;
  isFormValid: boolean;
  hasPreferences: boolean;
}

const initialFormData: PreferencesFormData = {
  // Basic Information
  age: "",
  gender: "",
  preferredLanguage: "zh-CN",

  // Educational Background
  educationExperience: "",
  major: "",
  graduationYear: "",

  // Professional Background
  currentRole: "",
  industry: "",
  workExperience: "",

  // Learning Preferences
  learningStyle: "",
  studyTimePerWeek: "",
  preferredStudyTime: "",
  learningPace: "",
};

export function usePreferences(): UsePreferencesReturn {
  const [formData, setFormData] =
    useState<PreferencesFormData>(initialFormData);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasPreferences, setHasPreferences] = useState(false);

  const loadPreferences = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await profileService.getStaticProfile();
      if (response.data) {
        const profile = response.data;
        const formDataFromProfile = {
          // Basic Information
          age: profile.age?.toString() || "",
          gender: profile.gender || "",
          preferredLanguage: profile.preferred_language || "zh-CN",

          // Educational Background
          educationExperience: profile.education_experience || "",
          major: profile.major || "",
          graduationYear: profile.graduation_year?.toString() || "",

          // Professional Background
          currentRole: profile.current_role || "",
          industry: profile.industry || "",
          workExperience: profile.work_experience?.toString() || "",

          // Learning Preferences
          learningStyle: profile.learning_style || "",
          studyTimePerWeek: profile.study_time_per_week?.toString() || "",
          preferredStudyTime: profile.preferred_study_time || "",
          learningPace: profile.learning_pace || "",
        };

        setFormData(formDataFromProfile);

        // Check if user has all required preferences filled
        const checks = {
          age: profile.age && profile.age > 0,
          gender: profile.gender && profile.gender.trim() !== "",
          preferred_language:
            profile.preferred_language &&
            profile.preferred_language.trim() !== "",
          education_experience:
            profile.education_experience &&
            profile.education_experience.trim() !== "",
          major: profile.major && profile.major.trim() !== "",
          graduation_year:
            profile.graduation_year && profile.graduation_year > 0,
          current_role:
            profile.current_role && profile.current_role.trim() !== "",
          industry: profile.industry && profile.industry.trim() !== "",
          work_experience:
            profile.work_experience !== undefined &&
            profile.work_experience !== null,
          learning_style:
            profile.learning_style && profile.learning_style.trim() !== "",
          study_time_per_week:
            profile.study_time_per_week && profile.study_time_per_week > 0,
          preferred_study_time:
            profile.preferred_study_time &&
            profile.preferred_study_time.trim() !== "",
          learning_pace:
            profile.learning_pace && profile.learning_pace.trim() !== "",
        };

        const hasAllRequiredFields = Object.values(checks).every(Boolean);

        setHasPreferences(!!hasAllRequiredFields);
      } else {
        // No profile data found - new user
        setHasPreferences(false);
      }
    } catch (error: any) {
      console.error("Failed to load preferences:", error);

      // Check if it's an authentication error
      if (error?.response?.status === 401) {
        // User is not authenticated, don't show onboarding
        setHasPreferences(true); // Prevent onboarding from showing
      } else {
        // Other errors (like 404 for no profile) should show onboarding
        setHasPreferences(false);
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  const savePreferences = useCallback(async (): Promise<boolean> => {
    try {
      setIsSaving(true);

      const profileData = {
        // Basic Information
        age: formData.age ? parseInt(formData.age) : undefined,
        gender: formData.gender || undefined,
        preferred_language: formData.preferredLanguage || undefined,

        // Educational Background
        education_experience: formData.educationExperience || undefined,
        major: formData.major || undefined,
        graduation_year: formData.graduationYear
          ? parseInt(formData.graduationYear)
          : undefined,

        // Professional Background
        current_role: formData.currentRole || undefined,
        industry: formData.industry || undefined,
        work_experience: formData.workExperience
          ? parseInt(formData.workExperience)
          : undefined,

        // Learning Preferences
        learning_style: formData.learningStyle || undefined,
        study_time_per_week: formData.studyTimePerWeek
          ? parseInt(formData.studyTimePerWeek)
          : undefined,
        preferred_study_time: formData.preferredStudyTime || undefined,
        learning_pace: formData.learningPace || undefined,
      };

      // Try to update first, if it fails, create new profile
      try {
        await profileService.updateStaticProfile(profileData);
      } catch (updateError) {
        // If update fails, try to create
        await profileService.createStaticProfile(profileData);
      }

      toast.success("Preferences updated successfully!");
      return true;
    } catch (error) {
      console.error("Failed to save preferences:", error);
      toast.error("Failed to save preferences. Please try again.");
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [formData]);

  const resetForm = useCallback(() => {
    setFormData(initialFormData);
  }, []);

  const isFormValid = Boolean(
    formData.age &&
      formData.gender &&
      formData.preferredLanguage &&
      formData.educationExperience &&
      formData.major &&
      formData.graduationYear &&
      formData.currentRole &&
      formData.industry &&
      formData.workExperience &&
      formData.learningStyle &&
      formData.studyTimePerWeek &&
      formData.preferredStudyTime &&
      formData.learningPace
  );

  return {
    formData,
    setFormData,
    isLoading,
    isSaving,
    loadPreferences,
    savePreferences,
    resetForm,
    isFormValid,
    hasPreferences,
  };
}
