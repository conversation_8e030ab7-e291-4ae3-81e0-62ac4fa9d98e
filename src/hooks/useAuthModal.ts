/*
 * @Description: 认证模态框Hook - 提供便捷的认证模态框操作方法
 * @Author: Devin
 * @Date: 2025-08-11
 */
import { useUIStore } from "@/store";

/**
 * 认证模态框Hook
 * 提供便捷的方法来控制全局认证模态框
 */
export const useAuthModal = () => {
  const {
    authModalOpen,
    authModalMode,
    openAuthModal,
    closeAuthModal,
    setAuthModalMode,
    openLoginModal,
    openSignupModal,
  } = useUIStore();

  return {
    // 状态
    isOpen: authModalOpen,
    mode: authModalMode,
    
    // 操作方法
    open: openAuthModal,
    close: closeAuthModal,
    setMode: setAuthModalMode,
    
    // 便捷方法
    openLogin: openLoginModal,
    openSignup: openSignupModal,
  };
};
