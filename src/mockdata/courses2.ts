import { PointerData } from "./maindata";

export const mockCourses2: PointerData[] = [
  {
    id: "c-programming-mastery",
    title: "C Programming Complete Mastery",
    description:
      "Master C programming from fundamentals to advanced concepts. Build system-level applications and understand memory management.",
    whatYouWillLearn: [
      "C programming fundamentals and syntax",
      "Memory management and pointers",
      "Data structures in C",
      "System programming concepts",
      "Building efficient C applications",
    ],
    skills: [
      "C Programming",
      "Memory Management",
      "Pointers",
      "System Programming",
    ],
    authors: [
      {
        id: "david-kim",
        name: "<PERSON>",
        title: "Systems Engineer",
        bio: "Systems programming expert with 12+ years at Intel and NVIDIA",
        avatar: "/avatars/david-kim.jpg",
      },
    ],
    developedByMAANG: false,
    category: "c",
    tags: ["C", "Programming", "Systems", "Memory Management"],
    duration: 64800, // 18 hours
    difficulty: "Intermediate",
    language: "c",
    instructor: "<PERSON>",
    price: "$59",
    level: "intermediate",
    type: "course",
    rating: 4.6,
    students: "95k+",
    createAt: 1712016000, // 2024-04-02
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 78,
    quizzes: 30,
    content: {
      period: "week",
      sections: [
        {
          id: "c-fundamentals",
          title: "C Fundamentals",
          description: "Basic C programming concepts",
          lessons: [
            {
              id: "pointers-memory",
              title: "Pointers and Memory Management",
              description: "Understanding C pointers and memory allocation",
              href: "/courses/c-programming-mastery/pointers-memory",
              duration: 3600,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  {
    id: "react-hooks-context",
    title: "React Hooks & Context API Masterclass",
    description:
      "Master modern React development with Hooks, Context API, and state management. Build scalable React applications.",
    whatYouWillLearn: [
      "React Hooks (useState, useEffect, useContext)",
      "Context API for state management",
      "Custom hooks development",
      "Performance optimization techniques",
      "Building complex React applications",
    ],
    skills: ["React", "Hooks", "Context API", "State Management", "Frontend"],
    authors: [
      {
        id: "emma-wilson",
        name: "Emma Wilson",
        title: "React Specialist",
        bio: "React expert with 6+ years at Meta and Shopify",
        avatar: "/avatars/emma-wilson.jpg",
      },
    ],
    developedByMAANG: true,
    category: "react",
    tags: ["React", "Hooks", "Frontend", "JavaScript"],
    duration: 75600, // 21 hours
    difficulty: "Advanced",
    language: "javascript",
    instructor: "Emma Wilson",
    price: "$79",
    level: "advanced",
    type: "course",
    rating: 4.9,
    students: "210k+",
    createAt: 1714694400, // 2024-05-03
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 105,
    quizzes: 40,
    content: {
      period: "week",
      sections: [
        {
          id: "react-hooks",
          title: "React Hooks Deep Dive",
          description: "Advanced React Hooks patterns",
          lessons: [
            {
              id: "custom-hooks",
              title: "Building Custom Hooks",
              description: "Creating reusable custom React hooks",
              href: "/courses/react-hooks-context/custom-hooks",
              duration: 2700,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  {
    id: "docker-kubernetes-devops",
    title: "Docker & Kubernetes for DevOps",
    description:
      "Master containerization and orchestration with Docker and Kubernetes. Deploy scalable applications in production.",
    whatYouWillLearn: [
      "Docker fundamentals and containerization",
      "Kubernetes cluster management",
      "CI/CD pipeline integration",
      "Container orchestration strategies",
      "Production deployment best practices",
    ],
    skills: ["Docker", "Kubernetes", "DevOps", "Containerization", "CI/CD"],
    authors: [
      {
        id: "carlos-martinez",
        name: "Carlos Martinez",
        title: "DevOps Engineer",
        bio: "DevOps specialist with 9+ years at Google Cloud and AWS",
        avatar: "/avatars/carlos-martinez.jpg",
      },
    ],
    developedByMAANG: true,
    category: "docker",
    tags: ["Docker", "Kubernetes", "DevOps", "Containers"],
    duration: 93600, // 26 hours
    difficulty: "Advanced",
    language: "general",
    instructor: "Carlos Martinez",
    price: "$99",
    level: "advanced",
    type: "course",
    rating: 4.8,
    students: "150k+",
    createAt: 1717372800, // 2024-06-03
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 130,
    quizzes: 48,
    content: {
      period: "week",
      sections: [
        {
          id: "docker-basics",
          title: "Docker Fundamentals",
          description: "Introduction to containerization",
          lessons: [
            {
              id: "docker-compose",
              title: "Docker Compose and Multi-container Apps",
              description: "Managing multi-container applications",
              href: "/courses/docker-kubernetes-devops/docker-compose",
              duration: 3000,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  // Web Development Course
  {
    id: "full-stack-web-dev",
    title: "Full Stack Web Development Bootcamp",
    description:
      "Complete web development course covering HTML, CSS, JavaScript, Node.js, and databases. Build real-world projects.",
    whatYouWillLearn: [
      "HTML5, CSS3, and responsive design",
      "JavaScript ES6+ and DOM manipulation",
      "Node.js and Express.js backend development",
      "Database design and integration",
      "Full-stack project development",
    ],
    skills: ["HTML", "CSS", "JavaScript", "Node.js", "Full Stack"],
    authors: [
      {
        id: "john-doe",
        name: "John Doe",
        title: "Full Stack Developer",
        bio: "Full-stack developer with 10+ years experience building web applications",
        avatar: "/avatars/john-doe.jpg",
      },
    ],
    developedByMAANG: false,
    category: "web-dev",
    tags: ["Web Development", "Full Stack", "JavaScript", "HTML", "CSS"],
    duration: 144000, // 40 hours
    difficulty: "Beginner",
    language: "javascript",
    instructor: "John Doe",
    price: "$99",
    level: "beginner",
    type: "course",
    rating: 4.5,
    students: "300k+",
    createAt: 1725408000, // 2024-09-04
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 115,
    quizzes: 45,
    content: {
      period: "week",
      sections: [
        {
          id: "web-fundamentals",
          title: "Web Development Fundamentals",
          description: "HTML, CSS, and JavaScript basics",
          lessons: [
            {
              id: "responsive-design",
              title: "Responsive Web Design",
              description: "Creating responsive layouts with CSS",
              href: "/courses/full-stack-web-dev/responsive-design",
              duration: 3600,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  // DevOps Course
  {
    id: "devops-fundamentals",
    title: "DevOps Fundamentals and Best Practices",
    description:
      "Master DevOps practices including CI/CD, infrastructure as code, monitoring, and automation.",
    whatYouWillLearn: [
      "DevOps culture and practices",
      "CI/CD pipeline implementation",
      "Infrastructure as Code (IaC)",
      "Monitoring and logging strategies",
      "Automation and deployment strategies",
    ],
    skills: ["DevOps", "CI/CD", "Infrastructure", "Automation", "Monitoring"],
    authors: [
      {
        id: "jane-smith",
        name: "Jane Smith",
        title: "DevOps Engineer",
        bio: "DevOps engineer with 8+ years experience at major tech companies",
        avatar: "/avatars/jane-smith.jpg",
      },
    ],
    developedByMAANG: true,
    category: "devops",
    tags: ["DevOps", "CI/CD", "Infrastructure", "Automation"],
    duration: 108000, // 30 hours
    difficulty: "Intermediate",
    language: "general",
    instructor: "Jane Smith",
    price: "$89",
    level: "intermediate",
    type: "course",
    rating: 4.7,
    students: "120k+",
    createAt: 1728086400, // 2024-10-05
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 98,
    quizzes: 36,
    content: {
      period: "week",
      sections: [
        {
          id: "devops-basics",
          title: "DevOps Fundamentals",
          description: "Introduction to DevOps practices",
          lessons: [
            {
              id: "cicd-pipelines",
              title: "Building CI/CD Pipelines",
              description: "Creating automated deployment pipelines",
              href: "/courses/devops-fundamentals/cicd-pipelines",
              duration: 3600,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
];
