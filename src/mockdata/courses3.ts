import { PointerData } from "./maindata";

export const mockCourses3: PointerData[] = [
  // AWS Course
  {
    id: "aws-cloud-practitioner",
    title: "AWS Cloud Practitioner Certification",
    description:
      "Complete guide to AWS Cloud fundamentals. Prepare for the AWS Cloud Practitioner certification exam.",
    whatYouWillLearn: [
      "AWS Cloud concepts and services",
      "AWS pricing and billing models",
      "Security and compliance in AWS",
      "AWS architectural best practices",
      "Hands-on experience with core AWS services",
    ],
    skills: ["AWS", "Cloud Computing", "Cloud Architecture", "DevOps"],
    authors: [
      {
        id: "michael-brown",
        name: "<PERSON>",
        title: "AWS Solutions Architect",
        bio: "AWS certified solutions architect with 12+ years cloud experience",
        avatar: "/avatars/michael-brown.jpg",
      },
    ],
    developedByMAANG: true,
    category: "aws",
    tags: ["AWS", "Cloud", "Certification", "DevOps"],
    duration: 72000, // 20 hours
    difficulty: "Beginner",
    language: "general",
    instructor: "<PERSON>",
    price: "$79",
    level: "beginner",
    type: "course",
    rating: 4.8,
    students: "200k+",
    createAt: 1730764800, // 2024-11-05
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 85,
    quizzes: 32,
    content: {
      period: "week",
      sections: [
        {
          id: "aws-fundamentals",
          title: "AWS Cloud Fundamentals",
          description: "Introduction to AWS services",
          lessons: [
            {
              id: "ec2-s3-basics",
              title: "EC2 and S3 Fundamentals",
              description:
                "Understanding core AWS compute and storage services",
              href: "/courses/aws-cloud-practitioner/ec2-s3-basics",
              duration: 3000,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  // C# Course
  {
    id: "csharp-dotnet-core",
    title: "C# and .NET Core Complete Guide",
    description:
      "Master C# programming and .NET Core framework. Build modern applications with Microsoft technologies.",
    whatYouWillLearn: [
      "C# programming fundamentals",
      ".NET Core framework and libraries",
      "Object-oriented programming in C#",
      "Building web APIs with ASP.NET Core",
      "Database integration with Entity Framework",
    ],
    skills: ["C#", ".NET Core", "ASP.NET", "Entity Framework", "Web API"],
    authors: [
      {
        id: "anna-johnson",
        name: "Anna Johnson",
        title: "Senior .NET Developer",
        bio: "Microsoft MVP with 10+ years .NET development experience",
        avatar: "/avatars/anna-johnson.jpg",
      },
    ],
    developedByMAANG: true,
    category: "csharp",
    tags: ["C#", ".NET", "Microsoft", "Backend"],
    duration: 90000, // 25 hours
    difficulty: "Intermediate",
    language: "csharp",
    instructor: "Anna Johnson",
    price: "$89",
    level: "intermediate",
    type: "course",
    rating: 4.7,
    students: "160k+",
    createAt: 1733443200, // 2024-12-06
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 102,
    quizzes: 39,
    content: {
      period: "week",
      sections: [
        {
          id: "csharp-fundamentals",
          title: "C# Programming Fundamentals",
          description: "Core C# concepts and syntax",
          lessons: [
            {
              id: "oop-principles",
              title: "Object-Oriented Programming in C#",
              description: "Classes, inheritance, and polymorphism",
              href: "/courses/csharp-dotnet-core/oop-principles",
              duration: 3600,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  // Generative AI Course
  {
    id: "generative-ai-fundamentals",
    title: "Generative AI and Large Language Models",
    description:
      "Explore the world of Generative AI, LLMs, and prompt engineering. Build AI-powered applications.",
    whatYouWillLearn: [
      "Understanding Generative AI concepts",
      "Working with Large Language Models",
      "Prompt engineering techniques",
      "Building AI-powered applications",
      "Ethics and responsible AI development",
    ],
    skills: ["AI", "Machine Learning", "LLM", "Prompt Engineering", "Python"],
    authors: [
      {
        id: "dr-sarah-kim",
        name: "Dr. Sarah Kim",
        title: "AI Research Scientist",
        bio: "PhD in AI, former researcher at OpenAI and Google DeepMind",
        avatar: "/avatars/sarah-kim.jpg",
      },
    ],
    developedByMAANG: true,
    category: "generative-ai",
    tags: ["AI", "Machine Learning", "LLM", "GPT", "Generative AI"],
    duration: 64800, // 18 hours
    difficulty: "Advanced",
    language: "python",
    instructor: "Dr. Sarah Kim",
    price: "$129",
    level: "advanced",
    type: "course",
    rating: 4.9,
    students: "75k+",
    createAt: 1736035200, // 2025-01-05
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 68,
    quizzes: 26,
    content: {
      period: "week",
      sections: [
        {
          id: "ai-fundamentals",
          title: "Generative AI Fundamentals",
          description: "Introduction to Generative AI",
          lessons: [
            {
              id: "llm-basics",
              title: "Large Language Models Explained",
              description: "Understanding how LLMs work",
              href: "/courses/generative-ai-fundamentals/llm-basics",
              duration: 2700,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  // Learn to Code Course
  {
    id: "learn-to-code-basics",
    title: "Learn to Code: Programming Fundamentals",
    description:
      "Perfect for absolute beginners. Learn programming concepts using multiple languages and build your first applications.",
    whatYouWillLearn: [
      "Programming fundamentals and logic",
      "Variables, functions, and control structures",
      "Problem-solving with code",
      "Introduction to multiple programming languages",
      "Building your first applications",
    ],
    skills: ["Programming", "Logic", "Problem Solving", "Multiple Languages"],
    authors: [
      {
        id: "tom-wilson",
        name: "Tom Wilson",
        title: "Programming Instructor",
        bio: "Computer Science educator with 15+ years teaching experience",
        avatar: "/avatars/tom-wilson.jpg",
      },
    ],
    developedByMAANG: false,
    category: "learn-to-code",
    tags: ["Programming", "Beginner", "Fundamentals", "Logic"],
    duration: 54000, // 15 hours
    difficulty: "Beginner",
    language: "general",
    instructor: "Tom Wilson",
    price: "$39",
    level: "beginner",
    type: "course",
    rating: 4.6,
    students: "400k+",
    createAt: 1738713600, // 2025-02-05
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 55,
    quizzes: 22,
    content: {
      period: "week",
      sections: [
        {
          id: "programming-basics",
          title: "Programming Fundamentals",
          description: "Core programming concepts",
          lessons: [
            {
              id: "variables-functions",
              title: "Variables and Functions",
              description: "Understanding basic programming building blocks",
              href: "/courses/learn-to-code-basics/variables-functions",
              duration: 2400,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
];
