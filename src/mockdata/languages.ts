// 编程语言配置
export const languageConfigs = {
  python: {
    name: "Python",
    icon: "🐍",
    description:
      "Python is one of the most popular programming languages in the world, known for its simplicity and readability, making it perfect for beginners and data science enthusiasts.",
    color: "from-blue-500 to-purple-600",
    slug: "python",
  },
  javascript: {
    name: "JavaScript",
    icon: "⚡",
    description:
      "JavaScript is the programming language of the web, enabling interactive and dynamic web applications. Master modern JavaScript development from frontend to backend.",
    color: "from-yellow-500 to-orange-600",
    slug: "javascript",
  },
  java: {
    name: "Java",
    icon: "☕",
    description:
      "Java is a powerful object-oriented programming language used for enterprise applications and Android development. Build scalable, secure, and high-performance applications.",
    color: "from-red-500 to-orange-600",
    slug: "java",
  },
  typescript: {
    name: "TypeScript",
    icon: "🔷",
    description:
      "TypeScript 为 JavaScript 添加了静态类型检查，提高代码质量和开发效率。现代前端开发的首选语言。",
    color: "from-blue-600 to-indigo-600",
    slug: "typescript",
  },
  go: {
    name: "Go",
    icon: "🐹",
    description:
      "Go 是 Google 开发的现代编程语言，专为构建简单、可靠和高效的软件而设计。云原生开发的理想选择。",
    color: "from-cyan-500 to-blue-600",
    slug: "go",
  },
  rust: {
    name: "Rust",
    icon: "🦀",
    description:
      "Rust 是一种系统编程语言，专注于安全性、速度和并发性。无需垃圾回收器即可保证内存安全。",
    color: "from-orange-600 to-red-600",
    slug: "rust",
  },
  cpp: {
    name: "C++",
    icon: "⚙️",
    description:
      "C++ 是一种通用编程语言，广泛用于系统软件、游戏开发、嵌入式系统和高性能应用程序。",
    color: "from-blue-700 to-purple-700",
    slug: "cpp",
  },
  swift: {
    name: "Swift",
    icon: "🦉",
    description:
      "Swift 是 Apple 开发的现代编程语言，用于 iOS、macOS、watchOS 和 tvOS 应用程序开发。",
    color: "from-orange-500 to-red-500",
    slug: "swift",
  },
  kotlin: {
    name: "Kotlin",
    icon: "🎯",
    description:
      "Kotlin 是一种现代编程语言，与 Java 100% 互操作，是 Android 开发的首选语言。",
    color: "from-purple-600 to-pink-600",
    slug: "kotlin",
  },
};

// 课程统计数据
export const languageStats = {
  python: {
    courses: 248,
    paths: 49,
    cloudLabs: 19,
    assessments: 6,
    projects: 159,
  },
  javascript: {
    courses: 180,
    paths: 35,
    cloudLabs: 15,
    assessments: 4,
    projects: 120,
  },
  java: {
    courses: 150,
    paths: 28,
    cloudLabs: 12,
    assessments: 3,
    projects: 95,
  },
  typescript: {
    courses: 85,
    paths: 18,
    cloudLabs: 8,
    assessments: 2,
    projects: 65,
  },
  go: {
    courses: 45,
    paths: 12,
    cloudLabs: 5,
    assessments: 1,
    projects: 35,
  },
  rust: {
    courses: 35,
    paths: 8,
    cloudLabs: 4,
    assessments: 1,
    projects: 25,
  },
  cpp: {
    courses: 120,
    paths: 22,
    cloudLabs: 10,
    assessments: 3,
    projects: 80,
  },
  swift: {
    courses: 65,
    paths: 15,
    cloudLabs: 6,
    assessments: 2,
    projects: 45,
  },
  kotlin: {
    courses: 55,
    paths: 12,
    cloudLabs: 5,
    assessments: 1,
    projects: 40,
  },
};

// 课程分类
export const languageCategories = {
  python: [
    "All Categories",
    "Programming Fundamentals",
    "Interview Prep",
    "System Design",
    "Machine Learning",
    "AI Tools",
    "Web Development",
    "Data Science",
  ],
  javascript: [
    "All Categories",
    "Programming Fundamentals",
    "Web Development",
    "Frontend Frameworks",
    "Backend Development",
    "Node.js",
    "React",
    "Vue.js",
  ],
  java: [
    "All Categories",
    "Programming Fundamentals",
    "Object-Oriented Programming",
    "Spring Framework",
    "Enterprise Development",
    "Android Development",
    "Microservices",
    "Advanced Programming",
  ],
  typescript: [
    "所有分类",
    "编程基础",
    "前端开发",
    "后端开发",
    "框架集成",
    "类型系统",
  ],
  go: ["所有分类", "编程基础", "并发编程", "微服务", "云原生", "系统编程"],
  rust: [
    "所有分类",
    "编程基础",
    "系统编程",
    "内存管理",
    "并发编程",
    "Web 开发",
  ],
  cpp: [
    "所有分类",
    "编程基础",
    "系统编程",
    "游戏开发",
    "嵌入式系统",
    "高性能计算",
  ],
  swift: [
    "所有分类",
    "编程基础",
    "iOS 开发",
    "macOS 开发",
    "UI 设计",
    "应用发布",
  ],
  kotlin: ["所有分类", "编程基础", "Android 开发", "多平台开发", "函数式编程"],
};

// 语言学习收益
export const languageBenefits = {
  python: [
    {
      icon: "🌐",
      title: "Web 开发",
      description: "构建强大的 Web 应用程序",
    },
    {
      icon: "📊",
      title: "数据科学",
      description: "分析和可视化数据",
    },
    {
      icon: "🤖",
      title: "人工智能",
      description: "创建智能系统",
    },
    {
      icon: "⚡",
      title: "自动化",
      description: "自动化重复性任务",
    },
  ],
  javascript: [
    {
      icon: "🌐",
      title: "前端开发",
      description: "构建交互式用户界面",
    },
    {
      icon: "⚙️",
      title: "Node.js 后端",
      description: "服务器端 JavaScript 开发",
    },
    {
      icon: "📱",
      title: "移动应用",
      description: "React Native 和混合应用",
    },
    {
      icon: "🚀",
      title: "现代框架",
      description: "React、Vue、Angular 等",
    },
  ],
  java: [
    {
      icon: "🏢",
      title: "企业应用",
      description: "构建可扩展的商业解决方案",
    },
    {
      icon: "📱",
      title: "Android 开发",
      description: "创建移动应用程序",
    },
    {
      icon: "☁️",
      title: "云和微服务",
      description: "分布式系统架构",
    },
    {
      icon: "🔧",
      title: "Spring 框架",
      description: "专业 Java 开发",
    },
  ],
};

// 获取语言配置
export const getLanguageConfig = (language: string) => {
  return languageConfigs[language as keyof typeof languageConfigs];
};

// 获取语言统计
export const getLanguageStats = (language: string) => {
  return languageStats[language as keyof typeof languageStats];
};

// 获取语言分类
export const getLanguageCategories = (language: string) => {
  return (
    languageCategories[language as keyof typeof languageCategories] || [
      "所有分类",
    ]
  );
};

// 获取语言收益
export const getLanguageBenefits = (language: string) => {
  return languageBenefits[language as keyof typeof languageBenefits] || [];
};

// 获取所有支持的语言
export const getSupportedLanguages = () => {
  return Object.keys(languageConfigs);
};

// 获取热门语言（前3个）
export const getPopularLanguages = () => {
  return ["python", "javascript", "java"];
};
