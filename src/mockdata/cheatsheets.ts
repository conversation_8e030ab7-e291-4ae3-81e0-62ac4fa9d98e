/*
 * @Description: Mock data for cheatsheets
 * @Author: <PERSON>
 * @Date: 2025-07-31
 */

import { Cheatsheet, CheatsheetStats } from "@/types/cheatsheet";

export const mockCheatsheets: Cheatsheet[] = [
  {
    id: "1",
    title: "Google System Design Interview Survival Guide",
    description: "Complete guide to acing Google's system design interviews with real examples and proven strategies.",
    category: "System Design",
    difficulty: "Advanced",
    downloadCount: 45230,
    rating: 4.8,
    ratingCount: 1250,
    tags: ["Google", "System Design", "Interview", "Scalability"],
    fileSize: "2.5 MB",
    pages: 24,
    lastUpdated: new Date("2025-07-25"),
    downloadUrl: "/downloads/google-system-design.pdf",
    previewUrl: "/previews/google-system-design",
    isFree: true,
    isPopular: true,
    isNew: false,
  },
  {
    id: "2",
    title: "JavaScript ES6+ Quick Reference",
    description: "Essential JavaScript ES6+ features, syntax, and best practices for modern web development.",
    category: "Programming Language",
    difficulty: "Intermediate",
    downloadCount: 38920,
    rating: 4.7,
    ratingCount: 890,
    tags: ["JavaScript", "ES6", "Web Development", "Syntax"],
    fileSize: "1.8 MB",
    pages: 16,
    lastUpdated: new Date("2025-07-20"),
    downloadUrl: "/downloads/javascript-es6.pdf",
    previewUrl: "/previews/javascript-es6",
    isFree: true,
    isPopular: true,
    isNew: false,
  },
  {
    id: "3",
    title: "Machine Learning Algorithms Cheatsheet",
    description: "Comprehensive overview of ML algorithms, when to use them, and implementation tips.",
    category: "Machine Learning",
    difficulty: "Intermediate",
    downloadCount: 32150,
    rating: 4.9,
    ratingCount: 1120,
    tags: ["Machine Learning", "Algorithms", "Data Science", "AI"],
    fileSize: "3.2 MB",
    pages: 32,
    lastUpdated: new Date("2025-07-28"),
    downloadUrl: "/downloads/ml-algorithms.pdf",
    previewUrl: "/previews/ml-algorithms",
    isFree: true,
    isPopular: true,
    isNew: true,
  },
  {
    id: "4",
    title: "AWS Services Quick Reference",
    description: "Essential AWS services, use cases, and pricing models for cloud architects and developers.",
    category: "Cloud",
    difficulty: "Intermediate",
    downloadCount: 28750,
    rating: 4.6,
    ratingCount: 750,
    tags: ["AWS", "Cloud", "DevOps", "Architecture"],
    fileSize: "2.1 MB",
    pages: 20,
    lastUpdated: new Date("2025-07-22"),
    downloadUrl: "/downloads/aws-services.pdf",
    previewUrl: "/previews/aws-services",
    isFree: true,
    isPopular: false,
    isNew: false,
  },
  {
    id: "5",
    title: "React Hooks Complete Guide",
    description: "Master React Hooks with practical examples, best practices, and common patterns.",
    category: "Frontend",
    difficulty: "Intermediate",
    downloadCount: 41200,
    rating: 4.8,
    ratingCount: 1340,
    tags: ["React", "Hooks", "Frontend", "JavaScript"],
    fileSize: "2.8 MB",
    pages: 28,
    lastUpdated: new Date("2025-07-26"),
    downloadUrl: "/downloads/react-hooks.pdf",
    previewUrl: "/previews/react-hooks",
    isFree: true,
    isPopular: true,
    isNew: false,
  },
  {
    id: "6",
    title: "Python Data Science Toolkit",
    description: "Essential Python libraries and functions for data analysis, visualization, and machine learning.",
    category: "Data Science",
    difficulty: "Beginner",
    downloadCount: 35680,
    rating: 4.7,
    ratingCount: 980,
    tags: ["Python", "Data Science", "Pandas", "NumPy", "Matplotlib"],
    fileSize: "2.4 MB",
    pages: 22,
    lastUpdated: new Date("2025-07-24"),
    downloadUrl: "/downloads/python-data-science.pdf",
    previewUrl: "/previews/python-data-science",
    isFree: true,
    isPopular: true,
    isNew: false,
  },
  {
    id: "7",
    title: "Docker & Kubernetes Essentials",
    description: "Container orchestration fundamentals with Docker and Kubernetes commands and best practices.",
    category: "DevOps",
    difficulty: "Intermediate",
    downloadCount: 22340,
    rating: 4.5,
    ratingCount: 560,
    tags: ["Docker", "Kubernetes", "DevOps", "Containers"],
    fileSize: "1.9 MB",
    pages: 18,
    lastUpdated: new Date("2025-07-21"),
    downloadUrl: "/downloads/docker-kubernetes.pdf",
    previewUrl: "/previews/docker-kubernetes",
    isFree: true,
    isPopular: false,
    isNew: false,
  },
  {
    id: "8",
    title: "Coding Interview Patterns",
    description: "Master the most common coding interview patterns with examples and practice problems.",
    category: "Interview Prep",
    difficulty: "Intermediate",
    downloadCount: 52100,
    rating: 4.9,
    ratingCount: 1890,
    tags: ["Interview", "Algorithms", "Data Structures", "Coding"],
    fileSize: "3.5 MB",
    pages: 36,
    lastUpdated: new Date("2025-07-29"),
    downloadUrl: "/downloads/coding-patterns.pdf",
    previewUrl: "/previews/coding-patterns",
    isFree: true,
    isPopular: true,
    isNew: true,
  },
  {
    id: "9",
    title: "REST API Design Best Practices",
    description: "Design scalable and maintainable REST APIs with industry best practices and examples.",
    category: "API Design",
    difficulty: "Intermediate",
    downloadCount: 19850,
    rating: 4.6,
    ratingCount: 420,
    tags: ["API", "REST", "Backend", "Design"],
    fileSize: "1.7 MB",
    pages: 14,
    lastUpdated: new Date("2025-07-23"),
    downloadUrl: "/downloads/rest-api-design.pdf",
    previewUrl: "/previews/rest-api-design",
    isFree: true,
    isPopular: false,
    isNew: false,
  },
  {
    id: "10",
    title: "SQL Query Optimization Guide",
    description: "Optimize database performance with advanced SQL techniques and query optimization strategies.",
    category: "Database",
    difficulty: "Advanced",
    downloadCount: 26780,
    rating: 4.7,
    ratingCount: 680,
    tags: ["SQL", "Database", "Performance", "Optimization"],
    fileSize: "2.2 MB",
    pages: 26,
    lastUpdated: new Date("2025-07-27"),
    downloadUrl: "/downloads/sql-optimization.pdf",
    previewUrl: "/previews/sql-optimization",
    isFree: true,
    isPopular: false,
    isNew: true,
  },
];

export const cheatsheetStats: CheatsheetStats = {
  totalCheatsheets: 150,
  totalDownloads: 2500000,
  averageRating: 4.7,
  popularCategories: [
    { category: "System Design", count: 25, percentage: 16.7 },
    { category: "Programming Language", count: 22, percentage: 14.7 },
    { category: "Interview Prep", count: 18, percentage: 12.0 },
    { category: "Machine Learning", count: 15, percentage: 10.0 },
    { category: "Cloud", count: 12, percentage: 8.0 },
    { category: "Frontend", count: 12, percentage: 8.0 },
    { category: "Data Science", count: 10, percentage: 6.7 },
    { category: "DevOps", count: 8, percentage: 5.3 },
  ],
};

// Helper functions
export const getPopularCheatsheets = (limit: number = 6): Cheatsheet[] => {
  return mockCheatsheets
    .filter(sheet => sheet.isPopular)
    .sort((a, b) => b.downloadCount - a.downloadCount)
    .slice(0, limit);
};

export const getNewCheatsheets = (limit: number = 6): Cheatsheet[] => {
  return mockCheatsheets
    .filter(sheet => sheet.isNew)
    .sort((a, b) => b.lastUpdated.getTime() - a.lastUpdated.getTime())
    .slice(0, limit);
};

export const getCheatsheetsByCategory = (category: string): Cheatsheet[] => {
  return mockCheatsheets.filter(sheet => sheet.category === category);
};

export const searchCheatsheets = (query: string): Cheatsheet[] => {
  const lowercaseQuery = query.toLowerCase();
  return mockCheatsheets.filter(sheet =>
    sheet.title.toLowerCase().includes(lowercaseQuery) ||
    sheet.description.toLowerCase().includes(lowercaseQuery) ||
    sheet.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};
