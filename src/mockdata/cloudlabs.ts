/*
 * @Description: Mock data for Cloud Labs
 * @Author: Devin
 * @Date: 2025-07-21
 */

export interface CloudLab {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  category: string;
  technologies: string[];
  isChallenge?: boolean;
  isFavorite?: boolean;
}

export const cloudLabsData: CloudLab[] = [
  {
    id: "1",
    title: "Speech Synthesis Using Amazon Polly",
    description:
      "Learn to use Amazon Polly for text-to-speech synthesis, integrating SSML for natural speech effects and enhancing applications.",
    duration: "1 h",
    difficulty: "Beginner",
    category: "AI & ML",
    technologies: ["Amazon Polly", "SSML", "AWS"],
    isChallenge: false,
  },
  {
    id: "2",
    title: "Build a Conversational Agent Using Amazon Bedrock Agents",
    description:
      "Challenge yourself to design a conversational agent using Amazon Bedrock Agent. Build the complete system hands-on—no step-by-step guidance provided.",
    duration: "1 h 30 m",
    difficulty: "Intermediate",
    category: "AI & ML",
    technologies: ["Amazon Bedrock", "Agent", "AWS"],
    isChallenge: true,
  },
  {
    id: "3",
    title: "Getting Started with Amazon Transcribe",
    description:
      "Learn to use Amazon Transcribe for speech recognition, batch and streaming transcription, and integrate it into a React app.",
    duration: "1 h 30 m",
    difficulty: "Beginner",
    category: "AI & ML",
    technologies: ["Amazon Transcribe", "React", "JavaScript"],
    isChallenge: false,
  },
  {
    id: "4",
    title: "Getting Started with Amazon Managed Streaming for Apache Kafka",
    description:
      "Learn to create an Amazon MSK cluster and a client machine using EC2 and to set up producers and consumers for a topic",
    duration: "1 h 30 m",
    difficulty: "Beginner",
    category: "Analytics",
    technologies: ["Amazon MSK", "Apache Kafka", "EC2"],
    isChallenge: false,
  },
  {
    id: "5",
    title: "Build a Real-time Analytics Pipeline Challenge",
    description:
      "Challenge yourself to build a complete real-time analytics pipeline using EMR and Spark. Design the architecture and implement the solution independently.",
    duration: "2 h",
    difficulty: "Advanced",
    category: "Analytics",
    technologies: ["Amazon EMR", "Apache Spark", "Big Data"],
    isChallenge: true,
  },
  {
    id: "6",
    title: "Getting to Know AWS CloudFormation",
    description:
      "Learn to manage AWS infrastructures using CloudFormation templates for efficient resource creation and maintenance.",
    duration: "1 h",
    difficulty: "Beginner",
    category: "Application Integration",
    technologies: ["AWS CloudFormation", "Infrastructure as Code", "AWS"],
    isChallenge: false,
  },
  {
    id: "7",
    title: "Infrastructure as Code Challenge",
    description:
      "Challenge yourself to design and implement a complete infrastructure solution using IAC tools. Build a scalable architecture from scratch.",
    duration: "2 h 30 m",
    difficulty: "Advanced",
    category: "Compute",
    technologies: ["AWS SAM", "CloudFormation", "Terraform"],
    isChallenge: true,
  },
  {
    id: "8",
    title: "Building Custom AWS Lambda Runtimes",
    description:
      "Explore creating custom AWS Lambda runtimes to enable non-native language support for your application.",
    duration: "2 h",
    difficulty: "Intermediate",
    category: "Compute",
    technologies: ["AWS Lambda", "Custom Runtimes", "Serverless"],
    isChallenge: false,
  },
  {
    id: "9",
    title: "Deploying a CI/CD Pipeline on EKS Using Terraform",
    description:
      "Using Terraform and EKS to automate CI/CD pipelines, manage Kubernetes clusters, and deploy applications seamlessly.",
    duration: "2 h 30 m",
    difficulty: "Advanced",
    category: "Containers",
    technologies: ["Amazon EKS", "Terraform", "Kubernetes", "CI/CD"],
    isChallenge: false,
  },
  {
    id: "10",
    title: "Understanding AWS Security and Management—From Zero to Hero",
    description:
      "Learn AWS security and identity management services through AWS IAM and KMS, focusing on policies, roles, and encryption.",
    duration: "2 h 30 m",
    difficulty: "Beginner",
    category: "Cryptography & PKI",
    technologies: ["AWS IAM", "AWS KMS", "Security"],
    isChallenge: false,
  },
  {
    id: "11",
    title: "Advanced ML Pipeline Challenge",
    description:
      "Challenge yourself to build an end-to-end ML pipeline with SageMaker. Design, train, and deploy a production-ready model independently.",
    duration: "3 h",
    difficulty: "Advanced",
    category: "AI & ML",
    technologies: ["Amazon SageMaker", "Machine Learning", "Python"],
    isChallenge: true,
  },
  {
    id: "12",
    title: "Computer Vision with Amazon Rekognition",
    description:
      "Explore advanced computer vision capabilities including object detection, facial analysis, and content moderation.",
    duration: "2 h",
    difficulty: "Intermediate",
    category: "AI & ML",
    technologies: ["Amazon Rekognition", "Computer Vision", "Python"],
    isChallenge: false,
  },
  {
    id: "13",
    title: "Natural Language Processing with Amazon Comprehend",
    description:
      "Learn to analyze text using Amazon Comprehend for sentiment analysis, entity recognition, and language detection.",
    duration: "1 h 45 m",
    difficulty: "Intermediate",
    category: "AI & ML",
    technologies: ["Amazon Comprehend", "NLP", "Text Analytics"],
    isChallenge: false,
  },
  {
    id: "14",
    title: "Real-time Analytics with Amazon Kinesis",
    description:
      "Build real-time data streaming and analytics solutions using Amazon Kinesis Data Streams and Analytics.",
    duration: "2 h 15 m",
    difficulty: "Advanced",
    category: "Analytics",
    technologies: ["Amazon Kinesis", "Real-time Analytics", "Streaming"],
    isChallenge: false,
  },
  {
    id: "15",
    title: "Data Warehousing with Amazon Redshift",
    description:
      "Learn to set up and optimize Amazon Redshift clusters for large-scale data warehousing and analytics.",
    duration: "2 h 30 m",
    difficulty: "Advanced",
    category: "Analytics",
    technologies: ["Amazon Redshift", "Data Warehousing", "SQL"],
    isChallenge: false,
  },
  {
    id: "16",
    title: "Business Intelligence with Amazon QuickSight",
    description:
      "Create interactive dashboards and visualizations using Amazon QuickSight for business intelligence.",
    duration: "1 h 30 m",
    difficulty: "Beginner",
    category: "Analytics",
    technologies: ["Amazon QuickSight", "Business Intelligence", "Dashboards"],
    isChallenge: false,
  },
];

export const cloudLabCategories = [
  "AI & ML",
  "Analytics",
  "Application Integration",
  "Compute",
  "Containers",
  "Cryptography & PKI",
  "Database",
  "Developer Tools",
  "Front-End Web & Mobile",
  "Internet of Things (IoT)",
  "Management & Governance",
  "Migration & Transfer",
  "Networking & Content Delivery",
  "Security, Identity, & Compliance",
  "Serverless",
  "Storage",
];

export const cloudLabTechnologies = [
  "Access Analyzer",
  "ALB",
  "Amplify",
  "API Gateway",
  "App Runner",
  "AppConfig",
  "Application Composer",
  "AppSync",
  "Athena",
  "Aurora",
  "AWS Lambda",
  "CloudFormation",
  "DynamoDB",
  "EC2",
  "EKS",
  "S3",
  "Terraform",
];
