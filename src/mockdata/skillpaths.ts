/*
 * @Description: Mock data for Skill Paths
 * @Author: Devin
 * @Date: 2025-07-21
 */

export interface SkillPath {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  topics: string[];
  technologies: string[];
  isFavorite?: boolean;
}

export const skillPathsData: SkillPath[] = [
  {
    id: "1",
    title: "Ace the JavaScript Coding Interview",
    description:
      "Catch up on everything you'll need to ace your JavaScript interviews.",
    duration: "185 h",
    difficulty: "Intermediate",
    topics: ["Coding Interview Patterns", "Algorithms", "Data Structures"],
    technologies: ["JavaScript", "Node.js", "React"],
  },
  {
    id: "2",
    title: "Speedrun the Meta Coding Interview: Top Problems in Python",
    description:
      "Speedrun the Meta/Facebook Coding Interview: Top Problems in Python. Solve <PERSON>tCode-style problems effectively and enhance your performance in tech interviews.",
    duration: "27 h",
    difficulty: "Beginner",
    topics: ["Coding Interview Patterns", "Algorithms"],
    technologies: ["Python", "Data Structures"],
  },
  {
    id: "3",
    title: "Speedrun the Apple Coding Interview: Top Problems in Python",
    description:
      "Speedrun the Apple Coding Interview: Top Problems in Python. Solve <PERSON>ode-style problems effectively and enhance your performance in tech interviews.",
    duration: "27 h",
    difficulty: "Beginner",
    topics: ["Coding Interview Patterns", "Algorithms"],
    technologies: ["Python", "Data Structures"],
  },
  {
    id: "4",
    title: "Speedrun the Apple Coding Interview: Top Problems in C++",
    description:
      "Speedrun the Apple Coding Interview: Top Problems in C++. Solve LeetCode-style problems effectively and enhance your performance in tech interviews.",
    duration: "27 h",
    difficulty: "Beginner",
    topics: ["Coding Interview Patterns", "Algorithms"],
    technologies: ["C++", "Data Structures"],
  },
  {
    id: "5",
    title: "Full-Stack Web Development with React",
    description:
      "Master modern web development with React, Node.js, and MongoDB. Build complete applications from frontend to backend.",
    duration: "120 h",
    difficulty: "Intermediate",
    topics: ["Front-end Development", "Back-end Development", "Database"],
    technologies: ["React", "Node.js", "MongoDB", "Express"],
  },
  {
    id: "6",
    title: "Machine Learning Engineer Path",
    description:
      "Comprehensive path covering machine learning fundamentals, deep learning, and MLOps for production systems.",
    duration: "150 h",
    difficulty: "Advanced",
    topics: ["Artificial Intelligence", "Machine Learning", "Data Science"],
    technologies: ["Python", "TensorFlow", "PyTorch", "Scikit-learn"],
  },
  {
    id: "7",
    title: "Cloud Architecture with AWS",
    description:
      "Learn to design and implement scalable cloud solutions using Amazon Web Services.",
    duration: "80 h",
    difficulty: "Intermediate",
    topics: ["Cloud", "DevOps", "Infrastructure"],
    technologies: ["AWS", "Docker", "Kubernetes", "Terraform"],
  },
  {
    id: "8",
    title: "Cybersecurity Fundamentals",
    description:
      "Essential cybersecurity concepts, threat analysis, and security implementation strategies.",
    duration: "60 h",
    difficulty: "Beginner",
    topics: ["Cyber Security", "Network Security"],
    technologies: ["Linux", "Python", "Wireshark", "Metasploit"],
  },
  {
    id: "9",
    title: "Mobile App Development with Flutter",
    description:
      "Build cross-platform mobile applications using Flutter and Dart programming language.",
    duration: "90 h",
    difficulty: "Intermediate",
    topics: ["Mobile Development", "Cross-platform"],
    technologies: ["Flutter", "Dart", "Firebase", "SQLite"],
  },
  {
    id: "10",
    title: "Data Engineering Pipeline",
    description:
      "Learn to build robust data pipelines, ETL processes, and data warehousing solutions.",
    duration: "110 h",
    difficulty: "Advanced",
    topics: ["Big Data", "Data Engineering", "Analytics"],
    technologies: ["Apache Spark", "Kafka", "Airflow", "Snowflake"],
  },
  {
    id: "11",
    title: "Advanced React Development",
    description:
      "Master advanced React concepts including hooks, context, performance optimization, and testing.",
    duration: "85 h",
    difficulty: "Advanced",
    topics: ["Frontend Development", "JavaScript"],
    technologies: ["React", "Redux", "Jest", "TypeScript"],
  },
  {
    id: "12",
    title: "Backend Development with Node.js",
    description:
      "Build scalable backend applications using Node.js, Express, and modern development practices.",
    duration: "95 h",
    difficulty: "Intermediate",
    topics: ["Backend Development", "JavaScript"],
    technologies: ["Node.js", "Express", "MongoDB", "JWT"],
  },
  {
    id: "13",
    title: "Machine Learning Fundamentals",
    description:
      "Learn the fundamentals of machine learning, from basic algorithms to model deployment.",
    duration: "120 h",
    difficulty: "Intermediate",
    topics: ["Machine Learning", "Data Science"],
    technologies: ["Python", "Scikit-learn", "TensorFlow", "Pandas"],
  },
  {
    id: "14",
    title: "DevOps Engineering",
    description:
      "Master DevOps practices including CI/CD, containerization, and infrastructure as code.",
    duration: "100 h",
    difficulty: "Advanced",
    topics: ["DevOps", "Infrastructure"],
    technologies: ["Docker", "Kubernetes", "Jenkins", "Terraform"],
  },
  {
    id: "15",
    title: "Frontend Development Bootcamp",
    description:
      "Complete frontend development path covering HTML, CSS, JavaScript, and modern frameworks.",
    duration: "150 h",
    difficulty: "Beginner",
    topics: ["Frontend Development", "Web Development"],
    technologies: ["HTML", "CSS", "JavaScript", "React"],
  },
  {
    id: "16",
    title: "Database Design and Management",
    description:
      "Learn database design principles, SQL optimization, and database administration.",
    duration: "80 h",
    difficulty: "Intermediate",
    topics: ["Database", "Backend Development"],
    technologies: ["PostgreSQL", "MySQL", "MongoDB", "Redis"],
  },
];

export const skillPathTopics = [
  "Algorithms",
  "Animation",
  "API",
  "Artificial Intelligence",
  "Back-end Development",
  "Big Data",
  "Blockchain",
  "Cloud",
  "Coding Interview Patterns",
  "Cyber Security",
  "Data Engineering",
  "Data Science",
  "Database",
  "DevOps",
  "Front-end Development",
  "Machine Learning",
  "Mobile Development",
  "Network Security",
  "Web Development",
];

export const skillPathTechnologies = [
  "AdonisJS",
  "Agile",
  "Algolia API",
  "Angular",
  "Ansible",
  "API-Football",
  "Appium",
  "ASP.NET Core",
  "Auth0",
  "Azure",
  "C++",
  "Docker",
  "Express",
  "Firebase",
  "Flutter",
  "JavaScript",
  "Kubernetes",
  "MongoDB",
  "Node.js",
  "Python",
  "React",
  "TensorFlow",
  "TypeScript",
];
