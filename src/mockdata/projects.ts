/*
 * @Description: Mock data for Projects
 * @Author: Devin
 * @Date: 2025-07-21
 */

export interface Project {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  technologies: string[];
  topics: string[];
  isFavorite?: boolean;
}

export const projectsData: Project[] = [
  {
    id: "1",
    title: "Build a Social Media Dashboard",
    description:
      "Create a comprehensive social media analytics dashboard with real-time data visualization and user engagement metrics.",
    duration: "15 h",
    difficulty: "Intermediate",
    technologies: ["React", "Node.js", "MongoDB", "Chart.js"],
    topics: ["Web Development", "Data Visualization", "API Integration"],
  },
  {
    id: "2",
    title: "E-commerce Platform with Payment Integration",
    description:
      "Develop a full-featured e-commerce platform with shopping cart, payment processing, and order management.",
    duration: "25 h",
    difficulty: "Advanced",
    technologies: ["Next.js", "Stripe", "PostgreSQL", "Tailwind CSS"],
    topics: ["Full-stack Development", "Payment Processing", "Database Design"],
  },
  {
    id: "3",
    title: "Real-time Chat Application",
    description:
      "Build a real-time messaging application with WebSocket connections, user authentication, and message history.",
    duration: "12 h",
    difficulty: "Intermediate",
    technologies: ["Socket.io", "Express", "React", "Redis"],
    topics: ["Real-time Communication", "WebSockets", "Authentication"],
  },
  {
    id: "4",
    title: "Machine Learning Image Classifier",
    description:
      "Create an image classification system using deep learning to identify objects in uploaded images.",
    duration: "20 h",
    difficulty: "Advanced",
    technologies: ["Python", "TensorFlow", "Flask", "OpenCV"],
    topics: ["Machine Learning", "Computer Vision", "Deep Learning"],
  },
  {
    id: "5",
    title: "Task Management Mobile App",
    description:
      "Develop a cross-platform mobile application for task management with offline sync and push notifications.",
    duration: "18 h",
    difficulty: "Intermediate",
    technologies: ["Flutter", "Firebase", "Dart", "SQLite"],
    topics: ["Mobile Development", "Offline Storage", "Push Notifications"],
  },
  {
    id: "6",
    title: "Weather Forecast API",
    description:
      "Build a RESTful API that aggregates weather data from multiple sources and provides forecasting capabilities.",
    duration: "10 h",
    difficulty: "Beginner",
    technologies: ["Node.js", "Express", "MongoDB", "External APIs"],
    topics: ["API Development", "Data Aggregation", "REST Architecture"],
  },
  {
    id: "7",
    title: "Blockchain Voting System",
    description:
      "Create a secure voting system using blockchain technology to ensure transparency and immutability.",
    duration: "30 h",
    difficulty: "Advanced",
    technologies: ["Solidity", "Web3.js", "Ethereum", "React"],
    topics: ["Blockchain", "Smart Contracts", "Decentralized Applications"],
  },
  {
    id: "8",
    title: "Personal Finance Tracker",
    description:
      "Develop a personal finance management application with expense tracking, budgeting, and financial insights.",
    duration: "16 h",
    difficulty: "Intermediate",
    technologies: ["Vue.js", "Python", "Django", "Chart.js"],
    topics: ["Financial Technology", "Data Analysis", "User Interface Design"],
  },
  {
    id: "9",
    title: "IoT Home Automation System",
    description:
      "Build an IoT-based home automation system to control lights, temperature, and security devices remotely.",
    duration: "22 h",
    difficulty: "Advanced",
    technologies: ["Raspberry Pi", "Python", "MQTT", "React Native"],
    topics: ["Internet of Things", "Hardware Integration", "Mobile Control"],
  },
  {
    id: "10",
    title: "Code Review Assistant",
    description:
      "Create an AI-powered code review tool that analyzes code quality, suggests improvements, and detects potential bugs.",
    duration: "14 h",
    difficulty: "Intermediate",
    technologies: ["Python", "Natural Language Processing", "Git API", "Flask"],
    topics: ["Artificial Intelligence", "Code Analysis", "Developer Tools"],
  },
  {
    id: "11",
    title: "Real-time Chat Application",
    description:
      "Build a full-stack real-time chat application with user authentication, rooms, and message history.",
    duration: "18 h",
    difficulty: "Intermediate",
    technologies: ["React", "Node.js", "Socket.io", "MongoDB"],
    topics: [
      "Full Stack Development",
      "Real-time Communication",
      "Web Sockets",
    ],
  },
  {
    id: "12",
    title: "Personal Finance Tracker",
    description:
      "Create a comprehensive personal finance management app with budgeting, expense tracking, and financial insights.",
    duration: "20 h",
    difficulty: "Intermediate",
    technologies: ["React Native", "Firebase", "Chart.js", "Plaid API"],
    topics: [
      "Mobile Development",
      "Financial Technology",
      "Data Visualization",
    ],
  },
  {
    id: "13",
    title: "Machine Learning Model Deployment",
    description:
      "Deploy a machine learning model as a REST API with monitoring, scaling, and A/B testing capabilities.",
    duration: "16 h",
    difficulty: "Advanced",
    technologies: ["Python", "FastAPI", "Docker", "Kubernetes", "MLflow"],
    topics: ["Machine Learning", "DevOps", "API Development"],
  },
  {
    id: "14",
    title: "Social Media Analytics Dashboard",
    description:
      "Build a dashboard to analyze social media metrics, engagement rates, and audience insights across platforms.",
    duration: "15 h",
    difficulty: "Intermediate",
    technologies: ["Python", "Streamlit", "Twitter API", "Pandas", "Plotly"],
    topics: ["Data Analytics", "Social Media", "Dashboard Development"],
  },
  {
    id: "15",
    title: "Blockchain Voting System",
    description:
      "Develop a secure, transparent voting system using blockchain technology with smart contracts.",
    duration: "25 h",
    difficulty: "Advanced",
    technologies: ["Solidity", "Web3.js", "React", "Ethereum", "MetaMask"],
    topics: ["Blockchain", "Smart Contracts", "Decentralized Applications"],
  },
  {
    id: "16",
    title: "Content Management System",
    description:
      "Create a full-featured CMS with user roles, content editing, media management, and SEO optimization.",
    duration: "22 h",
    difficulty: "Advanced",
    technologies: ["Next.js", "Prisma", "PostgreSQL", "AWS S3", "Stripe"],
    topics: ["Full Stack Development", "Content Management", "E-commerce"],
  },
];

export const projectTopics = [
  "API Development",
  "Artificial Intelligence",
  "Authentication",
  "Blockchain",
  "Code Analysis",
  "Computer Vision",
  "Data Analysis",
  "Data Visualization",
  "Database Design",
  "Decentralized Applications",
  "Deep Learning",
  "Developer Tools",
  "Financial Technology",
  "Full-stack Development",
  "Hardware Integration",
  "Internet of Things",
  "Machine Learning",
  "Mobile Development",
  "Payment Processing",
  "Push Notifications",
  "Real-time Communication",
  "REST Architecture",
  "Smart Contracts",
  "User Interface Design",
  "Web Development",
  "WebSockets",
];

export const projectTechnologies = [
  "Chart.js",
  "Django",
  "Express",
  "Firebase",
  "Flask",
  "Flutter",
  "MongoDB",
  "Next.js",
  "Node.js",
  "OpenCV",
  "PostgreSQL",
  "Python",
  "Raspberry Pi",
  "React",
  "React Native",
  "Redis",
  "Socket.io",
  "Solidity",
  "Stripe",
  "Tailwind CSS",
  "TensorFlow",
  "Vue.js",
  "Web3.js",
];
