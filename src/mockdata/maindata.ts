// 导入现有的类型定义
import {
  CourseData as BaseCourseData,
  Author,
  RelatedCourse,
  FreeResource,
  FAQ,
  CourseLesson,
  CourseSection,
  CourseContent,
} from "@/types/course";

// 导入额外的mock数据
import { mockCourses1 } from "./courses1";
import { mockCourses2 } from "./courses2";
import { mockCourses3 } from "./courses3";

// 定义类型信息接口
export interface TypeInfo {
  id: string;
  title: string;
  description: string;
}

// 定义分类信息接口
export interface CategoryInfo {
  id: string;
  title: string;
  description: string;
}

// 扩展学习资源数据接口
export interface PointerData
  extends Omit<BaseCourseData, "duration" | "faq" | "category"> {
  // 基础属性
  duration: number; // 秒 (覆盖原来的 string 类型)
  level: string; // "beginner" | "intermediate" | "advanced"
  type: string; // 类型ID，通过POINTER_TYPES查询详细信息
  category: string; // 分类ID，通过CATEGORY_TYPES查询详细信息
  createAt: number;
  // 评分和学生数
  rating: number;
  students: string;
  price: string;
  // 图片和统计
  image: string; // 课程图片URL
  playgrounds: number; // Playground数量
  quizzes: number; // Quiz数量

  // 修正 FAQ 字段名
  faqs?: FAQ[];
}

// 重新导出需要的类型
export type Lesson = CourseLesson;
export type Section = CourseSection;
export type { Author, RelatedCourse, FreeResource, FAQ, CourseContent };

// 定义所有可用的类型
export const POINTER_TYPES: { [key: string]: TypeInfo } = {
  course: {
    id: "course",
    title: "Courses",
    description: "Level up your skills",
  },
  cloudlab: {
    id: "cloudlab",
    title: "Cloud Labs",
    description: "Setup-free practice with Cloud Services",
  },
  path: {
    id: "path",
    title: "Skill Paths",
    description: "Achieve learning goals",
  },
  project: {
    id: "project",
    title: "Projects",
    description: "Build real-world applications",
  },
  mockinterview: {
    id: "mockinterview",
    title: "Mock Interviews",
    description: "AI-Powered interviews",
  },
  interview_prep: {
    id: "interview_prep",
    title: "Personalized Interview Prep",
    description: "Get a custom roadmap tailored to your goals",
  },
  assessment: {
    id: "assessment",
    title: "Assessments",
    description: "Benchmark your skills",
  },
  personalized_path: {
    id: "personalized_path",
    title: "Personalized Paths",
    description: "Get the right resources for your goals",
  },
};

// 定义所有可用的分类
export const CATEGORY_TYPES: { [key: string]: CategoryInfo } = {
  python: {
    id: "python",
    title: "Python",
    description: "Master Python programming from basics to advanced concepts",
  },
  java: {
    id: "java",
    title: "Java",
    description: "Build enterprise applications with Java and Spring",
  },
  javascript: {
    id: "javascript",
    title: "JavaScript",
    description: "Modern JavaScript development and frameworks",
  },
  c: {
    id: "c",
    title: "C",
    description: "System programming and memory management with C",
  },
  react: {
    id: "react",
    title: "React",
    description: "Build modern user interfaces with React",
  },
  docker: {
    id: "docker",
    title: "Docker",
    description: "Containerization and orchestration with Docker & Kubernetes",
  },
  "vue-js": {
    id: "vue-js",
    title: "Vue JS",
    description: "Progressive framework for building user interfaces",
  },
  r: {
    id: "r",
    title: "R",
    description: "Statistical computing and data analysis with R",
  },
  "web-dev": {
    id: "web-dev",
    title: "Web Dev",
    description: "Full-stack web development from frontend to backend",
  },
  devops: {
    id: "devops",
    title: "DevOps",
    description: "Automation, CI/CD, and infrastructure management",
  },
  aws: {
    id: "aws",
    title: "AWS",
    description: "Amazon Web Services cloud computing platform",
  },
  csharp: {
    id: "csharp",
    title: "C#",
    description: "Microsoft .NET development with C#",
  },
  "system-design": {
    id: "system-design",
    title: "System Design",
    description: "Design scalable and distributed systems",
  },
  "interview-prep": {
    id: "interview-prep",
    title: "Interview Prep",
    description: "Prepare for technical interviews at top companies",
  },
  "generative-ai": {
    id: "generative-ai",
    title: "Generative AI",
    description: "Large language models and AI-powered applications",
  },
  "learn-to-code": {
    id: "learn-to-code",
    title: "Learn to Code",
    description: "Programming fundamentals for absolute beginners",
  },
  other: {
    id: "other",
    title: "Learn Other Languages",
    description: "Specialized topics and emerging technologies",
  },
};

// 所有学习资源数据
export const allPointerData: PointerData[] = [
  // System Design Course - 完整示例
  {
    id: "system-design-interview",
    title: "Grokking the Modern System Design Interview",
    description:
      "The ultimate guide to the System Design Interview – developed by FAANG engineers. Master distributed system fundamentals, and practice with real-world interview questions & mock interviews.",

    whatYouWillLearn: [
      "Master system design fundamentals and best practices",
      "Learn to design scalable distributed systems",
      "Understand load balancing and caching strategies",
      "Practice with real FAANG interview questions",
      "Build confidence for system design interviews",
      "Design popular systems like Twitter, Instagram, and Uber",
    ],

    skills: [
      "System Design",
      "Distributed Systems",
      "Scalability",
      "Load Balancing",
      "Database Design",
      "Caching",
      "Microservices",
      "API Design",
    ],

    authors: [
      {
        id: "design-gurus-team",
        name: "Design Gurus Team",
        title: "FAANG Engineers",
        bio: "A team of experienced engineers from top tech companies including Google, Amazon, Facebook, Apple, and Netflix with 10+ years of experience in system design.",
        avatar: "/avatars/design-gurus.jpg",
      },
    ],

    developedByMAANG: true,

    category: "system-design",
    tags: [
      "System Design",
      "Interview",
      "FAANG",
      "Distributed Systems",
      "Scalability",
    ],
    duration: 93600, // 26 hours in seconds (26 * 60 * 60)
    difficulty: "Intermediate" as "Beginner" | "Intermediate" | "Advanced",
    language: "general",
    instructor: "Design Gurus",
    price: "$79",
    level: "intermediate",
    type: "course",
    rating: 4.8,
    students: "180k+",
    createAt: 1735689600, // 2025-01-01
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/********/****************/image/****************",
    playgrounds: 120,
    quizzes: 45,
    content: {
      period: "week",
      sections: [
        {
          id: "fundamentals",
          title: "System Design Fundamentals",
          description:
            "Master the fundamental concepts of system design including scalability, reliability, and performance.",
          lessons: [
            {
              id: "scalability-basics",
              title: "Scalability Basics",
              description:
                "Learn the basics of system scalability and design principles for building scalable systems.",
              href: "/courses/system-design-interview/scalability-basics",
              duration: 1800, // 30 minutes
              type: "lesson",
              isCompleted: false,
              htmlContent: `<div class="course-content">
                <h2>Understanding Scalability</h2>
                <p>Scalability is the capability of a system to handle a growing amount of work by adding resources to the system.</p>
                <h3>Types of Scalability</h3>
                <ul>
                  <li><strong>Vertical Scaling (Scale Up):</strong> Adding more power to existing machines</li>
                  <li><strong>Horizontal Scaling (Scale Out):</strong> Adding more machines to the pool of resources</li>
                </ul>
                <h3>Key Principles</h3>
                <ol>
                  <li>Design for failure</li>
                  <li>Decouple components</li>
                  <li>Think in terms of services</li>
                  <li>Implement redundancy</li>
                </ol>
              </div>`,
            },
            {
              id: "load-balancing",
              title: "Load Balancing Strategies",
              description:
                "Explore different load balancing techniques and algorithms used in distributed systems.",
              href: "/courses/system-design-interview/load-balancing",
              duration: 2400, // 40 minutes
              type: "lesson",
              isCompleted: false,
              htmlContent: `<div class="course-content">
                <h2>Load Balancing</h2>
                <p>Load balancing distributes incoming requests across multiple servers to ensure no single server becomes overwhelmed.</p>
                <h3>Load Balancing Algorithms</h3>
                <ul>
                  <li>Round Robin</li>
                  <li>Weighted Round Robin</li>
                  <li>Least Connections</li>
                  <li>IP Hash</li>
                </ul>
              </div>`,
            },
            {
              id: "caching-quiz",
              title: "Caching Strategies Quiz",
              description:
                "Test your understanding of caching mechanisms and strategies.",
              href: "/courses/system-design-interview/caching-quiz",
              duration: 1200, // 20 minutes
              type: "quiz",
              isCompleted: false,
            },
          ],
        },
      ],
    },

    currentProgress: {
      completedLessons: [],
      totalLessons: 3,
      totalQuizzes: 1,
      totalChallenges: 0,
      percentage: 0,
    },

    relatedCourses: [
      {
        id: "advanced-system-design",
        title: "Advanced System Design Patterns",
        description:
          "Deep dive into advanced system design concepts and patterns",
        href: "/courses/advanced-system-design",
        difficulty: "Advanced",
        duration: "35 hours",
      },
    ],

    freeResources: [
      {
        id: "system-design-primer",
        title: "System Design Primer",
        description: "A comprehensive guide to system design on GitHub",
        type: "article",
        url: "https://github.com/donnemartin/system-design-primer",
      },
    ],

    faqs: [
      {
        id: "faq-1",
        question: "Is this course suitable for beginners?",
        answer:
          "This course is designed for intermediate level developers who have some experience with software development and want to learn system design for interviews.",
      },
      {
        id: "faq-2",
        question: "How long does it take to complete?",
        answer:
          "The course contains 26 hours of content. Most students complete it in 4-6 weeks studying 1-2 hours per day.",
      },
    ],
  },
  // 添加更多课程数据
  ...mockCourses1,
  ...mockCourses2,
  ...mockCourses3,
  // Vue.js Course
  {
    id: "vue-js-complete",
    title: "Vue.js 3 Complete Guide",
    description:
      "Master Vue.js 3 with Composition API, Vuex, Vue Router, and build modern web applications.",
    whatYouWillLearn: [
      "Vue.js 3 fundamentals and Composition API",
      "State management with Vuex",
      "Routing with Vue Router",
      "Component communication patterns",
      "Building production-ready Vue applications",
    ],
    skills: ["Vue.js", "JavaScript", "Frontend", "SPA", "Web Development"],
    authors: [
      {
        id: "lisa-zhang",
        name: "Lisa Zhang",
        title: "Vue.js Core Team Member",
        bio: "Vue.js core team member with 7+ years frontend experience",
        avatar: "/avatars/lisa-zhang.jpg",
      },
    ],
    developedByMAANG: false,
    category: "vue-js",
    tags: ["Vue.js", "JavaScript", "Frontend", "SPA"],
    duration: 79200, // 22 hours
    difficulty: "Intermediate",
    language: "javascript",
    instructor: "Lisa Zhang",
    price: "$69",
    level: "intermediate",
    type: "course",
    rating: 4.7,
    students: "140k+",
    createAt: **********, // 2024-07-04
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/********/****************/image/****************",
    playgrounds: 85,
    quizzes: 32,
    content: {
      period: "week",
      sections: [
        {
          id: "vue-fundamentals",
          title: "Vue.js Fundamentals",
          description: "Core Vue.js concepts",
          lessons: [
            {
              id: "composition-api",
              title: "Composition API Deep Dive",
              description: "Understanding Vue 3 Composition API",
              href: "/courses/vue-js-complete/composition-api",
              duration: 2400,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  // R Programming Course
  {
    id: "r-data-science",
    title: "R Programming for Data Science",
    description:
      "Master R programming for data analysis, statistics, and machine learning. Perfect for data scientists and analysts.",
    whatYouWillLearn: [
      "R programming fundamentals",
      "Data manipulation with dplyr and tidyr",
      "Data visualization with ggplot2",
      "Statistical analysis and modeling",
      "Machine learning with R",
    ],
    skills: [
      "R Programming",
      "Data Science",
      "Statistics",
      "Machine Learning",
      "Data Visualization",
    ],
    authors: [
      {
        id: "dr-robert-smith",
        name: "Dr. Robert Smith",
        title: "Data Science Professor",
        bio: "PhD in Statistics, 15+ years in data science and R programming",
        avatar: "/avatars/robert-smith.jpg",
      },
    ],
    developedByMAANG: false,
    category: "r",
    tags: ["R", "Data Science", "Statistics", "Analytics"],
    duration: 86400, // 24 hours
    difficulty: "Intermediate",
    language: "r",
    instructor: "Dr. Robert Smith",
    price: "$79",
    level: "intermediate",
    type: "course",
    rating: 4.6,
    students: "85k+",
    createAt: 1722729600, // 2024-08-04
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/********/****************/image/****************",
    playgrounds: 95,
    quizzes: 38,
    content: {
      period: "week",
      sections: [
        {
          id: "r-basics",
          title: "R Programming Basics",
          description: "Introduction to R programming",
          lessons: [
            {
              id: "data-structures",
              title: "R Data Structures",
              description: "Understanding vectors, lists, and data frames",
              href: "/courses/r-data-science/data-structures",
              duration: 2700,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  // Other category courses
  {
    id: "blockchain-fundamentals",
    title: "Blockchain and Cryptocurrency Fundamentals",
    description:
      "Understand blockchain technology, cryptocurrencies, and decentralized applications. Explore the future of finance.",
    whatYouWillLearn: [
      "Blockchain technology fundamentals",
      "Cryptocurrency concepts and trading",
      "Smart contracts and DApps",
      "Blockchain development basics",
      "Future of decentralized finance",
    ],
    skills: ["Blockchain", "Cryptocurrency", "Smart Contracts", "DeFi"],
    authors: [
      {
        id: "alex-crypto",
        name: "Alex Crypto",
        title: "Blockchain Developer",
        bio: "Blockchain expert with 6+ years in cryptocurrency and DeFi",
        avatar: "/avatars/alex-crypto.jpg",
      },
    ],
    developedByMAANG: false,
    category: "other",
    tags: ["Blockchain", "Cryptocurrency", "DeFi", "Web3"],
    duration: 61200, // 17 hours
    difficulty: "Intermediate",
    language: "general",
    instructor: "Alex Crypto",
    price: "$89",
    level: "intermediate",
    type: "course",
    rating: 4.4,
    students: "90k+",
    createAt: 1741392000, // 2025-03-06
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/********/****************/image/****************",
    playgrounds: 75,
    quizzes: 28,
    content: {
      period: "week",
      sections: [
        {
          id: "blockchain-basics",
          title: "Blockchain Fundamentals",
          description: "Understanding blockchain technology",
          lessons: [
            {
              id: "bitcoin-ethereum",
              title: "Bitcoin vs Ethereum",
              description: "Comparing major blockchain platforms",
              href: "/courses/blockchain-fundamentals/bitcoin-ethereum",
              duration: 2400,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  {
    id: "cybersecurity-basics",
    title: "Cybersecurity Fundamentals",
    description:
      "Learn essential cybersecurity concepts, threat detection, and protection strategies for modern digital environments.",
    whatYouWillLearn: [
      "Cybersecurity fundamentals and threats",
      "Network security and firewalls",
      "Incident response and forensics",
      "Security best practices",
      "Ethical hacking basics",
    ],
    skills: [
      "Cybersecurity",
      "Network Security",
      "Ethical Hacking",
      "Risk Management",
    ],
    authors: [
      {
        id: "security-expert",
        name: "Security Expert",
        title: "Cybersecurity Specialist",
        bio: "Cybersecurity professional with 12+ years in enterprise security",
        avatar: "/avatars/security-expert.jpg",
      },
    ],
    developedByMAANG: false,
    category: "other",
    tags: ["Cybersecurity", "Security", "Hacking", "Network"],
    duration: 68400, // 19 hours
    difficulty: "Intermediate",
    language: "general",
    instructor: "Security Expert",
    price: "$79",
    level: "intermediate",
    type: "course",
    rating: 4.5,
    students: "110k+",
    createAt: 1744070400, // 2025-04-06
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/********/****************/image/****************",
    playgrounds: 88,
    quizzes: 35,
    content: {
      period: "week",
      sections: [
        {
          id: "security-fundamentals",
          title: "Security Fundamentals",
          description: "Core cybersecurity concepts",
          lessons: [
            {
              id: "threat-landscape",
              title: "Understanding the Threat Landscape",
              description: "Modern cybersecurity threats and vulnerabilities",
              href: "/courses/cybersecurity-basics/threat-landscape",
              duration: 2700,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
];

// 工具函数
import { generateCourseUrl, generateLessonUrl } from "@/lib/utils";

// 根据类型获取学习资源
export function getPointersByType(typeId: string): PointerData[] {
  return allPointerData.filter((pointer) => pointer.type === typeId);
}

// 为学习资源生成URL
export function getPointerUrl(pointer: PointerData): string {
  return generateCourseUrl(pointer.type, pointer.id);
}

// 为课程生成URL
export function getLessonUrl(pointer: PointerData, lessonId: string): string {
  return generateLessonUrl(pointer.type, pointer.id, lessonId);
}

// 根据 tab 名称获取学习资源
export function getPointersByTab(tabName: string): PointerData[] {
  switch (tabName) {
    case "all":
      return allPointerData;
    case "courses":
      return getPointersByType("course");
    case "cloud-labs":
      return getPointersByType("cloudlab");
    case "projects":
      return getPointersByType("project");
    case "paths":
      return getPointersByType("path");
    case "assessments":
      return getPointersByType("assessment");
    case "mock-interviews":
      return getPointersByType("mockinterview");
    default:
      return allPointerData;
  }
}

// 根据级别获取学习资源
export function getPointersByLevel(level: string): PointerData[] {
  return allPointerData.filter((pointer) => pointer.level === level);
}

// 根据ID获取单个学习资源
export function getPointerById(id: string): PointerData | undefined {
  return allPointerData.find((pointer) => pointer.id === id);
}

// 根据标签搜索学习资源
export function getPointersByTags(tags: string[]): PointerData[] {
  return allPointerData.filter((pointer) =>
    tags.some((tag) =>
      pointer.tags.some((pointerTag) =>
        pointerTag.toLowerCase().includes(tag.toLowerCase())
      )
    )
  );
}

// 根据学习资源ID和课程ID获取特定lesson
export function getLessonById(
  pointerId: string,
  lessonId: string
): Lesson | undefined {
  const pointer = getPointerById(pointerId);
  if (!pointer) return undefined;

  for (const section of pointer.content.sections) {
    const lesson = section.lessons.find((l) => l.id === lessonId);
    if (lesson) return lesson;
  }
  return undefined;
}

// 计算学习资源统计信息
export function calculatePointerStats(pointer: PointerData) {
  let totalLessons = 0;
  let totalQuizzes = 0;
  let totalChallenges = 0;
  let totalDuration = 0;

  pointer.content.sections.forEach((section) => {
    section.lessons.forEach((lesson) => {
      totalDuration += lesson.duration;
      switch (lesson.type) {
        case "lesson":
          totalLessons++;
          break;
        case "quiz":
          totalQuizzes++;
          break;
        case "challenge":
          totalChallenges++;
          break;
      }
    });
  });

  return {
    totalLessons,
    totalQuizzes,
    totalChallenges,
    totalDuration,
    totalItems: totalLessons + totalQuizzes + totalChallenges,
  };
}

// 计算section进度
export function calculateSectionProgress(
  section: Section,
  completedLessons: string[]
) {
  const totalLessons = section.lessons.filter(
    (l) => l.type === "lesson"
  ).length;
  const totalQuizzes = section.lessons.filter((l) => l.type === "quiz").length;
  const totalChallenges = section.lessons.filter(
    (l) => l.type === "challenge"
  ).length;

  const completedInSection = section.lessons.filter((l) =>
    completedLessons.includes(l.id)
  );
  const completedLessonsCount = completedInSection.filter(
    (l) => l.type === "lesson"
  ).length;
  const completedQuizzesCount = completedInSection.filter(
    (l) => l.type === "quiz"
  ).length;
  const completedChallengesCount = completedInSection.filter(
    (l) => l.type === "challenge"
  ).length;

  const totalItems = section.lessons.length;
  const completedItems = completedInSection.length;
  const percentage =
    totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

  return {
    completedLessons: completedLessonsCount,
    totalLessons,
    completedQuizzes: completedQuizzesCount,
    totalQuizzes,
    completedChallenges: completedChallengesCount,
    totalChallenges,
    percentage,
  };
}

// 根据语言分类获取学习资源
export function getPointersByLanguage(language: string): PointerData[] {
  // 直接使用category id进行匹配
  const categoryId = language.toLowerCase().replace(/\s+/g, "-");
  return allPointerData.filter((pointer) => pointer.category === categoryId);
}

// 获取最受欢迎的学习资源（根据rating排序，取前8个）
export function getMostPopularPointers(limit: number = 8): PointerData[] {
  return allPointerData.sort((a, b) => b.rating - a.rating).slice(0, limit);
}

// 获取最新添加的学习资源（根据createAt排序，取前12个）
export function getNewAdditions(limit: number = 12): PointerData[] {
  return allPointerData.sort((a, b) => b.createAt - a.createAt).slice(0, limit);
}

// 根据分类获取学习资源
export function getPointersByCategory(categoryId: string): PointerData[] {
  return allPointerData.filter((pointer) => pointer.category === categoryId);
}

// 获取所有可用的分类ID
export function getAllCategories(): string[] {
  const categories = new Set(allPointerData.map((pointer) => pointer.category));
  return Array.from(categories).sort();
}

// 获取所有分类信息（包含title和description）
export function getAllCategoryInfos(): CategoryInfo[] {
  const categoryIds = getAllCategories();
  return categoryIds
    .map((id) => getCategoryInfo(id))
    .filter((info): info is CategoryInfo => info !== undefined);
}

// 获取学习资源统计信息
export function getPointersStats() {
  const totalPointers = allPointerData.length;
  const categories = getAllCategories();
  const averageRating =
    allPointerData.reduce((sum, pointer) => sum + pointer.rating, 0) /
    totalPointers;

  return {
    totalPointers,
    totalCategories: categories.length,
    averageRating: Math.round(averageRating * 10) / 10,
    categories,
  };
}

// 根据type id获取类型信息的辅助函数
export function getTypeInfo(typeId: string): TypeInfo | undefined {
  return POINTER_TYPES[typeId];
}

// 根据category id获取分类信息的辅助函数
export function getCategoryInfo(categoryId: string): CategoryInfo | undefined {
  return CATEGORY_TYPES[categoryId];
}

// ===== 兼容性函数 (为了向后兼容) =====
// 这些函数保持原有的命名，但内部使用新的PointerData结构

export const allCourseData = allPointerData; // 别名，向后兼容
export type CourseData = PointerData; // 类型别名，向后兼容

export function getCoursesByType(type: string): PointerData[] {
  return getPointersByType(type);
}

export function getCourseUrl(course: PointerData): string {
  return getPointerUrl(course);
}

export function getCoursesByTab(tabName: string): PointerData[] {
  return getPointersByTab(tabName);
}

export function getCoursesByLevel(level: string): PointerData[] {
  return getPointersByLevel(level);
}

export function getCourseById(id: string): PointerData | undefined {
  return getPointerById(id);
}

export function getCoursesByTags(tags: string[]): PointerData[] {
  return getPointersByTags(tags);
}

export function calculateCourseStats(course: PointerData) {
  return calculatePointerStats(course);
}

export function getCoursesByLanguage(language: string): PointerData[] {
  return getPointersByLanguage(language);
}

export function getMostPopularCourses(limit: number = 8): PointerData[] {
  return getMostPopularPointers(limit);
}

export function getCoursesByCategory(category: string): PointerData[] {
  return getPointersByCategory(category);
}

export function getCoursesStats() {
  return getPointersStats();
}

// ===== 首页数据 =====

// 公司Logo数据
export interface CompanyLogo {
  name: string;
  logoPath: string;
  hasLogo: boolean;
}

export const COMPANY_LOGOS: CompanyLogo[] = [
  {
    name: "Netflix",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Apple",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Meta",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Google",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Amazon",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Coinbase",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
];

// 用户评价数据
export interface Testimonial {
  id: string;
  name: string;
  role: string;
  company: string;
  content: string;
  avatar?: string;
  rating?: number;
}

export const TESTIMONIALS: Testimonial[] = [
  {
    id: "testimonial-1",
    name: "Yichen Wang",
    role: "Software Engineer",
    company: "Microsoft",
    content:
      "I found the course incredibly helpful in breaking down complex System Design concepts into digestible steps. The visual format — starting from requirements, moving through high-level architecture, and diving into trade-offs — made it much easier to internalize key design principles.",
    rating: 5,
  },
  {
    id: "testimonial-2",
    name: "Mike Raheim",
    role: "Learner",
    company: "Pageflux AI",
    content:
      "You've provided a very good, well-structured overview on modern system design which helped reinforce existing knowledge and add more. The AI-driven questions allowed me to think through various scenarios and made it an interactive experience. I'm looking forward to more Pageflux AI content.",
    rating: 5,
  },
  {
    id: "testimonial-3",
    name: "Kshitij Tiwari",
    role: "Architectural Technologies",
    company: "Expert",
    content:
      "I was looking for a course on System Design which will cover everything and help me clear interviews at the best companies, and this Pageflux AI course was by far the best and the most comprehensive. Additionally, features like mock-interviews and cloud labs are a great boost for learning.",
    rating: 5,
  },
  {
    id: "testimonial-4",
    name: "Sarah Wilson",
    role: "Senior Developer",
    company: "Google",
    content:
      "Perfect for beginners and advanced developers alike. The course covers everything from basic concepts to advanced patterns. Highly recommended!",
    rating: 5,
  },
  {
    id: "testimonial-5",
    name: "Alex Chen",
    role: "Tech Lead",
    company: "Meta",
    content:
      "Great course with practical examples. The Redux section was particularly helpful for my current project. Would love to see more advanced topics covered.",
    rating: 4,
  },
];

// 学习路径数据
export interface LearningPath {
  id: string;
  title: string;
  description: string;
  duration: string;
  level: string;
  courses: number;
  image: string;
  skills: string[];
}

export const LEARNING_PATHS: LearningPath[] = [
  {
    id: "full-stack-web-dev",
    title: "Full-Stack Web Development",
    description:
      "Master both frontend and backend development with modern technologies",
    duration: "6 months",
    level: "Beginner to Advanced",
    courses: 12,
    image: "/images/paths/fullstack.svg",
    skills: ["React", "Node.js", "MongoDB", "Express", "TypeScript"],
  },
  {
    id: "data-science-ml",
    title: "Data Science & Machine Learning",
    description:
      "Learn data analysis, visualization, and machine learning algorithms",
    duration: "8 months",
    level: "Intermediate",
    courses: 15,
    image: "/images/paths/datascience.svg",
    skills: ["Python", "Pandas", "Scikit-learn", "TensorFlow", "SQL"],
  },
  {
    id: "cloud-devops",
    title: "Cloud & DevOps Engineering",
    description:
      "Master cloud platforms and DevOps practices for scalable applications",
    duration: "5 months",
    level: "Intermediate to Advanced",
    courses: 10,
    image: "/images/paths/devops.svg",
    skills: ["AWS", "Docker", "Kubernetes", "CI/CD", "Terraform"],
  },
  {
    id: "mobile-development",
    title: "Mobile App Development",
    description: "Build native and cross-platform mobile applications",
    duration: "4 months",
    level: "Beginner to Intermediate",
    courses: 8,
    image: "/images/paths/mobile.svg",
    skills: ["React Native", "Flutter", "iOS", "Android", "Firebase"],
  },
];

// 获取函数
export function getCompanyLogos(): CompanyLogo[] {
  return COMPANY_LOGOS;
}

export function getTestimonials(limit?: number): Testimonial[] {
  return limit ? TESTIMONIALS.slice(0, limit) : TESTIMONIALS;
}

export function getLearningPaths(limit?: number): LearningPath[] {
  return limit ? LEARNING_PATHS.slice(0, limit) : LEARNING_PATHS;
}
