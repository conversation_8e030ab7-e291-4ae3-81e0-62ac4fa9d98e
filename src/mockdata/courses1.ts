import { PointerData } from "./maindata";

export const mockCourses1: PointerData[] = [
  {
    id: "python-fundamentals",
    title: "Python Programming Fundamentals",
    description:
      "Master Python programming from basics to advanced concepts. Perfect for beginners and those looking to strengthen their Python skills.",
    whatYouWillLearn: [
      "Python syntax and basic programming concepts",
      "Object-oriented programming in Python",
      "Working with data structures and algorithms",
      "File handling and exception management",
      "Building real-world Python applications",
    ],
    skills: [
      "Python",
      "Programming",
      "Data Structures",
      "OOP",
      "Problem Solving",
    ],
    authors: [
      {
        id: "sarah-chen",
        name: "<PERSON>",
        title: "Senior Python Developer",
        bio: "10+ years experience in Python development at Google and Microsoft",
        avatar: "/avatars/sarah-chen.jpg",
      },
    ],
    developedByMAANG: true,
    category: "python",
    tags: ["Python", "Programming", "Beginner", "Fundamentals"],
    duration: 72000, // 20 hours
    difficulty: "Beginner",
    language: "python",
    instructor: "<PERSON>",
    price: "$49",
    level: "beginner",
    type: "course",
    rating: 4.9,
    students: "250k+",
    createAt: 1704067200, // 2024-01-01
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 65,
    quizzes: 25,
    content: {
      period: "week",
      sections: [
        {
          id: "python-basics",
          title: "Python Basics",
          description: "Learn Python fundamentals",
          lessons: [
            {
              id: "variables-data-types",
              title: "Variables and Data Types",
              description: "Understanding Python variables and data types",
              href: "/courses/python-fundamentals/variables-data-types",
              duration: 1800,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  {
    id: "java-spring-boot",
    title: "Java Spring Boot Masterclass",
    description:
      "Build enterprise-grade applications with Java Spring Boot. Learn microservices, REST APIs, and database integration.",
    whatYouWillLearn: [
      "Spring Boot framework fundamentals",
      "Building REST APIs with Spring",
      "Database integration with JPA/Hibernate",
      "Microservices architecture",
      "Testing Spring Boot applications",
    ],
    skills: ["Java", "Spring Boot", "REST API", "Microservices", "Database"],
    authors: [
      {
        id: "mike-johnson",
        name: "Mike Johnson",
        title: "Java Architect",
        bio: "15+ years Java development experience at Amazon and Netflix",
        avatar: "/avatars/mike-johnson.jpg",
      },
    ],
    developedByMAANG: true,
    category: "java",
    tags: ["Java", "Spring Boot", "Backend", "Enterprise"],
    duration: 108000, // 30 hours
    difficulty: "Intermediate",
    language: "java",
    instructor: "Mike Johnson",
    price: "$89",
    level: "intermediate",
    type: "course",
    rating: 4.7,
    students: "180k+",
    createAt: 1706745600, // 2024-02-01
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 110,
    quizzes: 42,
    content: {
      period: "week",
      sections: [
        {
          id: "spring-basics",
          title: "Spring Boot Basics",
          description: "Introduction to Spring Boot",
          lessons: [
            {
              id: "spring-setup",
              title: "Setting up Spring Boot",
              description: "Creating your first Spring Boot application",
              href: "/courses/java-spring-boot/spring-setup",
              duration: 2400,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
  {
    id: "javascript-es6-plus",
    title: "Modern JavaScript ES6+ Complete Guide",
    description:
      "Master modern JavaScript features including ES6, ES7, ES8 and beyond. Build dynamic web applications with confidence.",
    whatYouWillLearn: [
      "ES6+ features and syntax",
      "Async/await and Promises",
      "Modern JavaScript patterns",
      "DOM manipulation and events",
      "Building interactive web applications",
    ],
    skills: [
      "JavaScript",
      "ES6+",
      "Web Development",
      "DOM",
      "Async Programming",
    ],
    authors: [
      {
        id: "alex-rodriguez",
        name: "Alex Rodriguez",
        title: "Frontend Engineer",
        bio: "Frontend specialist with 8+ years at Facebook and Airbnb",
        avatar: "/avatars/alex-rodriguez.jpg",
      },
    ],
    developedByMAANG: true,
    category: "javascript",
    tags: ["JavaScript", "ES6", "Frontend", "Web Development"],
    duration: 86400, // 24 hours
    difficulty: "Intermediate",
    language: "javascript",
    instructor: "Alex Rodriguez",
    price: "$69",
    level: "intermediate",
    type: "course",
    rating: 4.8,
    students: "320k+",
    createAt: 1709424000, // 2024-03-03
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=256,quality=85/v2api/collection/10370001/4595802214629376/image/5776842396401664",
    playgrounds: 95,
    quizzes: 38,
    content: {
      period: "week",
      sections: [
        {
          id: "es6-features",
          title: "ES6 Features",
          description: "Modern JavaScript features",
          lessons: [
            {
              id: "arrow-functions",
              title: "Arrow Functions and Template Literals",
              description: "Understanding modern JavaScript syntax",
              href: "/courses/javascript-es6-plus/arrow-functions",
              duration: 2100,
              type: "lesson",
              isCompleted: false,
            },
          ],
        },
      ],
    },
    currentProgress: {
      completedLessons: [],
      totalLessons: 1,
      totalQuizzes: 0,
      totalChallenges: 0,
      percentage: 0,
    },
    relatedCourses: [],
    freeResources: [],
    faqs: [],
  },
];
