/*
 * @Description: <PERSON><PERSON> Interview Data
 * @Author: AI Assistant
 * @Date: 2025-07-30
 */

import { MockInterview, Testimonial, InterviewAnalytics, InterviewCategory, CompanyType } from '@/types/mock-interview';

// Mock testimonials data
export const mockTestimonials: Testimonial[] = [
  {
    id: "1",
    name: "<PERSON>",
    content: "This was the first time I had done an interview with a system. It's really helpful.",
    rating: 5,
    featured: true
  },
  {
    id: "2",
    name: "<PERSON><PERSON>",
    content: "I like the way AI is able to understand my answers and responding like a human.",
    rating: 5,
    featured: true
  },
  {
    id: "3",
    name: "<PERSON><PERSON>",
    content: "I am always hesitating solving coding problems. I thought how people can crack Amazon, Google interviews. After following educative I think now it's possible.",
    rating: 5,
    featured: true
  },
  {
    id: "4",
    name: "<PERSON><PERSON>",
    content: "This is wonderful! It's helpful for those who don't necessarily have much access to real people in the industry to practice mock interviews. Thanks so much.",
    rating: 5,
    featured: true
  },
  {
    id: "5",
    name: "<PERSON><PERSON>",
    content: "Awesome! This AI mock interviewer was far more effective than the $200 human coaches I used for practice.",
    rating: 5,
    featured: true
  },
  {
    id: "6",
    name: "<PERSON><PERSON><PERSON><PERSON>n <PERSON>o",
    content: "I really liked the experience with the AI interviewer. It cuts to the chase and gives me insightful feedback along the way. Great job.",
    rating: 5,
    featured: true
  },
  {
    id: "7",
    name: "Dhananjaya",
    content: "I started using this AI mock interview, and it's great. It pushed my limits to go deeper into the solution.",
    rating: 5,
    featured: false
  },
  {
    id: "8",
    name: "<PERSON>",
    content: "It was helpful to go through the interview with a friendly bot and not feel rushed. The bot does feel like a friendly interviewer.",
    rating: 5,
    featured: false
  }
];

// Mock interviews data - System Design
export const systemDesignInterviews: MockInterview[] = [
  {
    id: "sd-youtube-free",
    title: "YouTube",
    description: "Learn to design a video streaming platform like YouTube by tackling functional and non-functional requirements, core components, and high-level to detailed design challenges.",
    category: "System Design",
    difficulty: "Medium",
    duration: 15,
    isFree: true,
    isPopular: true,
    tags: ["Video Streaming", "Scalability", "CDN", "Database Design"],
    questions: [],
    learningObjectives: [
      "Design scalable video streaming architecture",
      "Handle millions of concurrent users",
      "Implement efficient content delivery",
      "Design robust database schema"
    ],
    completionCount: 15420,
    averageRating: 4.8,
    ratingCount: 2341
  },
  {
    id: "sd-youtube-full",
    title: "YouTube",
    description: "Learn to design a video streaming platform like YouTube by tackling functional and non-functional requirements, core components, and high-level to detailed design challenges.",
    category: "System Design",
    difficulty: "Hard",
    duration: 45,
    isFree: false,
    isPopular: true,
    tags: ["Video Streaming", "Scalability", "CDN", "Database Design", "Microservices"],
    questions: [],
    learningObjectives: [
      "Design complete video streaming platform",
      "Handle petabyte-scale data storage",
      "Implement real-time analytics",
      "Design fault-tolerant architecture"
    ],
    completionCount: 8932,
    averageRating: 4.9,
    ratingCount: 1876
  },
  {
    id: "sd-twitter",
    title: "X (Twitter)",
    description: "Practice designing a system to handle millions of concurrent users posting tweets, retweets, and replies while maintaining low latency and high availability, like X (formerly Twitter).",
    category: "System Design",
    difficulty: "Hard",
    duration: 45,
    isFree: false,
    isPopular: true,
    tags: ["Social Media", "Real-time", "Timeline", "Notifications"],
    questions: [],
    learningObjectives: [
      "Design real-time social media platform",
      "Handle high-frequency writes",
      "Implement timeline generation",
      "Design notification system"
    ],
    completionCount: 7654,
    averageRating: 4.7,
    ratingCount: 1432
  },
  {
    id: "sd-whatsapp",
    title: "WhatsApp",
    description: "Gain insights into designing a robust messaging system like WhatsApp, focusing on scalability, low latency, and ensuring security through real-time scenario-based questions.",
    category: "System Design",
    difficulty: "Hard",
    duration: 45,
    isFree: false,
    tags: ["Messaging", "Real-time", "Security", "Mobile"],
    questions: [],
    learningObjectives: [
      "Design end-to-end encrypted messaging",
      "Handle billions of messages daily",
      "Implement real-time delivery",
      "Design mobile-first architecture"
    ],
    completionCount: 6789,
    averageRating: 4.8,
    ratingCount: 1234
  }
];

// Mock interviews data - Coding Interview
export const codingInterviews: MockInterview[] = [
  {
    id: "ci-patterns-free",
    title: "Coding Patterns",
    description: "Get a glimpse of the AI-powered mock interview with a coding challenge and feedback, designed to mirror real-world interviews, before upgrading to the full experience.",
    category: "Coding Interview",
    difficulty: "Medium",
    duration: 12,
    isFree: true,
    isPopular: true,
    tags: ["Algorithms", "Data Structures", "Problem Solving"],
    questions: [],
    learningObjectives: [
      "Master common coding patterns",
      "Improve problem-solving speed",
      "Learn optimal solutions",
      "Practice interview communication"
    ],
    completionCount: 23456,
    averageRating: 4.6,
    ratingCount: 4567
  },
  {
    id: "ci-sliding-window",
    title: "Sliding Window",
    description: "Gain hands-on experience with Sliding Window through real-world interview-style challenges and AI feedback to sharpen your problem-solving, efficiency, and pattern mastery.",
    category: "Coding Interview",
    difficulty: "Medium",
    duration: 45,
    isFree: false,
    tags: ["Sliding Window", "Arrays", "Two Pointers"],
    questions: [],
    learningObjectives: [
      "Master sliding window technique",
      "Solve array problems efficiently",
      "Optimize time complexity",
      "Handle edge cases"
    ],
    completionCount: 12345,
    averageRating: 4.7,
    ratingCount: 2345
  }
];

// Mock interviews data - MAANG+
export const maangInterviews: MockInterview[] = [
  {
    id: "maang-google-coding",
    title: "Google Coding Interview",
    description: "Prepare for a Google coding interview with MCQs and challenges focusing on problem-solving skills, optimal code patterns, and time-space complexity tailored to your experience level.",
    category: "MAANG+",
    company: "Google",
    difficulty: "Hard",
    duration: 45,
    isFree: false,
    isPopular: true,
    tags: ["Google", "Algorithms", "System Design", "Coding"],
    questions: [],
    learningObjectives: [
      "Master Google's interview style",
      "Solve complex algorithmic problems",
      "Optimize for time and space",
      "Practice system design thinking"
    ],
    completionCount: 9876,
    averageRating: 4.9,
    ratingCount: 1987
  },
  {
    id: "maang-meta-coding",
    title: "Meta Coding Interview",
    description: "Practice your Facebook coding interview with targeted MCQs, coding challenges, and coding optimization exercises guided by an expert AI interviewer, tailored to your experience level.",
    category: "MAANG+",
    company: "Meta",
    difficulty: "Hard",
    duration: 45,
    isFree: false,
    isPopular: true,
    tags: ["Meta", "Facebook", "Algorithms", "Optimization"],
    questions: [],
    learningObjectives: [
      "Understand Meta's coding standards",
      "Master graph algorithms",
      "Optimize for scale",
      "Practice behavioral questions"
    ],
    completionCount: 8765,
    averageRating: 4.8,
    ratingCount: 1765
  }
];

// Combined mock interviews data
export const mockInterviews: MockInterview[] = [
  ...systemDesignInterviews,
  ...codingInterviews,
  ...maangInterviews
];

// Mock analytics data
export const mockAnalytics: InterviewAnalytics = {
  totalConducted: 10000,
  averageCompletionRate: 78.5,
  popularCategories: [
    { category: "Coding Interview", count: 4500, percentage: 45 },
    { category: "System Design", count: 2800, percentage: 28 },
    { category: "MAANG+", count: 1800, percentage: 18 },
    { category: "Behavioral Interview", count: 900, percentage: 9 }
  ],
  userSatisfactionScore: 4.7,
  averageSessionDuration: 42
};

// Helper function to get interviews by category
export const getInterviewsByCategory = (category: InterviewCategory): MockInterview[] => {
  return mockInterviews.filter(interview => interview.category === category);
};

// Helper function to get free interviews
export const getFreeInterviews = (): MockInterview[] => {
  return mockInterviews.filter(interview => interview.isFree);
};

// Helper function to get popular interviews
export const getPopularInterviews = (): MockInterview[] => {
  return mockInterviews.filter(interview => interview.isPopular);
};

// Helper function to get interviews by company
export const getInterviewsByCompany = (company: CompanyType): MockInterview[] => {
  return mockInterviews.filter(interview => interview.company === company);
};
