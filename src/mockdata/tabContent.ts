// Tab内容数据 - 包含所有类型的学习资源

export interface TabItem {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: string;
  challenges: string;
  quizzes: string;
  image?: string;
}

export interface FAQItem {
  question: string;
  answer: string;
}

export interface LanguageContent {
  introduction: string; // HTML content
  faqs: FAQItem[];
}

// Python 数据
export const pythonTabData = {
  courses: [
    {
      id: "1",
      title: "Grokking the Coding Interview Patterns in Python",
      description:
        "The ultimate guide to coding interviews, developed by FAANG engineers. Learn patterns to tackle problems from top companies and get...",
      duration: "85 hrs",
      difficulty: "Intermediate",
      challenges: "267 Challenges",
      quizzes: "209 Quizzes",
    },
    {
      id: "2",
      title: "Grokking the Low Level Design Interview Using OOD Principles",
      description:
        "A battle-tested guide to Object Oriented Design Interviews, developed by FAANG engineers. Master OOD fundamentals & practice real...",
      duration: "50 hrs",
      difficulty: "Intermediate",
      challenges: "8 Playgrounds",
      quizzes: "4 Quizzes",
    },
    {
      id: "3",
      title: "Python for Data Science and Machine Learning",
      description:
        "Master Python programming for data analysis, visualization, and machine learning. Learn pandas, numpy, matplotlib, and scikit-learn...",
      duration: "42 hrs",
      difficulty: "Beginner",
      challenges: "156 Challenges",
      quizzes: "89 Quizzes",
    },
    {
      id: "4",
      title: "Advanced Python Programming",
      description:
        "Deep dive into advanced Python concepts including decorators, metaclasses, async programming, and performance optimization...",
      duration: "38 hrs",
      difficulty: "Advanced",
      challenges: "124 Challenges",
      quizzes: "67 Quizzes",
    },
    {
      id: "5",
      title: "Python Web Development with Django",
      description:
        "Build modern web applications using Django framework. Learn models, views, templates, authentication, and deployment...",
      duration: "55 hrs",
      difficulty: "Intermediate",
      challenges: "189 Challenges",
      quizzes: "98 Quizzes",
    },
    {
      id: "6",
      title: "Python API Development with FastAPI",
      description:
        "Create high-performance APIs using FastAPI. Learn async programming, database integration, authentication, and testing...",
      duration: "32 hrs",
      difficulty: "Intermediate",
      challenges: "145 Challenges",
      quizzes: "76 Quizzes",
    },
    {
      id: "7",
      title: "Python Testing and Quality Assurance",
      description:
        "Master testing in Python with pytest, unittest, and test-driven development. Learn mocking, fixtures, and CI/CD integration...",
      duration: "28 hrs",
      difficulty: "Intermediate",
      challenges: "112 Challenges",
      quizzes: "58 Quizzes",
    },
    {
      id: "8",
      title: "Python DevOps and Automation",
      description:
        "Automate infrastructure and deployments using Python. Learn Docker, Kubernetes, CI/CD pipelines, and monitoring...",
      duration: "45 hrs",
      difficulty: "Advanced",
      challenges: "167 Challenges",
      quizzes: "89 Quizzes",
    },
    {
      id: "9",
      title: "Python GUI Development with Tkinter",
      description:
        "Build desktop applications using Python's Tkinter library. Learn widgets, event handling, and creating professional GUIs...",
      duration: "25 hrs",
      difficulty: "Beginner",
      challenges: "89 Challenges",
      quizzes: "45 Quizzes",
    },
    {
      id: "10",
      title: "Python Game Development with Pygame",
      description:
        "Create 2D games using Pygame library. Learn game loops, sprites, collision detection, and sound integration...",
      duration: "35 hrs",
      difficulty: "Intermediate",
      challenges: "134 Challenges",
      quizzes: "67 Quizzes",
    },
    {
      id: "11",
      title: "Python Network Programming",
      description:
        "Master network programming with Python. Learn sockets, HTTP clients, servers, and network protocols implementation...",
      duration: "30 hrs",
      difficulty: "Advanced",
      challenges: "98 Challenges",
      quizzes: "52 Quizzes",
    },
    {
      id: "12",
      title: "Python Cybersecurity Fundamentals",
      description:
        "Learn cybersecurity concepts using Python. Covers encryption, penetration testing, vulnerability assessment, and security tools...",
      duration: "40 hrs",
      difficulty: "Advanced",
      challenges: "156 Challenges",
      quizzes: "78 Quizzes",
    },
    {
      id: "13",
      title: "Python Blockchain Development",
      description:
        "Build blockchain applications with Python. Learn cryptocurrency, smart contracts, and decentralized application development...",
      duration: "48 hrs",
      difficulty: "Advanced",
      challenges: "178 Challenges",
      quizzes: "92 Quizzes",
    },
    {
      id: "14",
      title: "Python Computer Vision with OpenCV",
      description:
        "Process images and videos using OpenCV and Python. Learn object detection, face recognition, and image manipulation...",
      duration: "36 hrs",
      difficulty: "Intermediate",
      challenges: "142 Challenges",
      quizzes: "71 Quizzes",
    },
    {
      id: "15",
      title: "Python Natural Language Processing",
      description:
        "Analyze text data using NLTK and spaCy. Learn sentiment analysis, text classification, and language model development...",
      duration: "44 hrs",
      difficulty: "Advanced",
      challenges: "165 Challenges",
      quizzes: "83 Quizzes",
    },
  ],
  paths: [
    {
      id: "1",
      title: "Python Developer Path",
      description:
        "Complete learning path to become a professional Python developer. Covers fundamentals, web development, databases, and deployment...",
      duration: "120 hrs",
      difficulty: "Beginner to Advanced",
      challenges: "450 Challenges",
      quizzes: "320 Quizzes",
    },
    {
      id: "2",
      title: "Data Science with Python Path",
      description:
        "Comprehensive path covering data analysis, machine learning, and AI using Python. Perfect for aspiring data scientists...",
      duration: "95 hrs",
      difficulty: "Intermediate",
      challenges: "280 Challenges",
      quizzes: "180 Quizzes",
    },
  ],
  cloudlabs: [
    {
      id: "1",
      title: "Python Web Development Lab",
      description:
        "Hands-on lab environment for building web applications with Django and Flask. Practice in a real cloud environment...",
      duration: "8 hrs",
      difficulty: "Intermediate",
      challenges: "15 Labs",
      quizzes: "5 Assessments",
    },
    {
      id: "2",
      title: "Python Data Analysis Lab",
      description:
        "Interactive lab for data analysis and visualization using pandas, matplotlib, and seaborn in a cloud environment...",
      duration: "6 hrs",
      difficulty: "Beginner",
      challenges: "12 Labs",
      quizzes: "8 Assessments",
    },
  ],
  assessments: [
    {
      id: "1",
      title: "Python Fundamentals Assessment",
      description:
        "Test your knowledge of Python basics including data types, control structures, functions, and object-oriented programming...",
      duration: "45 mins",
      difficulty: "Beginner",
      challenges: "25 Questions",
      quizzes: "Multiple Choice",
    },
    {
      id: "2",
      title: "Advanced Python Assessment",
      description:
        "Comprehensive assessment covering advanced Python topics like decorators, generators, context managers, and metaclasses...",
      duration: "60 mins",
      difficulty: "Advanced",
      challenges: "30 Questions",
      quizzes: "Mixed Format",
    },
  ],
  projects: [
    {
      id: "1",
      title: "Build a REST API with Django",
      description:
        "Create a full-featured REST API using Django REST Framework. Includes authentication, database integration, and deployment...",
      duration: "25 hrs",
      difficulty: "Intermediate",
      challenges: "10 Milestones",
      quizzes: "Code Reviews",
    },
    {
      id: "2",
      title: "Machine Learning Prediction Model",
      description:
        "Build and deploy a machine learning model for real-world predictions. Use scikit-learn, pandas, and cloud deployment...",
      duration: "18 hrs",
      difficulty: "Advanced",
      challenges: "8 Milestones",
      quizzes: "Performance Tests",
    },
    {
      id: "3",
      title: "Python Web Scraper",
      description:
        "Develop a sophisticated web scraping application using BeautifulSoup and Scrapy. Handle dynamic content and data storage...",
      duration: "12 hrs",
      difficulty: "Intermediate",
      challenges: "6 Milestones",
      quizzes: "Code Reviews",
    },
  ],
  content: {
    introduction: `
      <h2 class="text-xl font-semibold text-gray-900 mb-4">Delve into any sector with Python</h2>
      <ul class="list-disc list-inside space-y-2 text-gray-700 mb-4">
        <li>Web development</li>
        <li>Data science</li>
        <li>Artificial intelligence</li>
        <li>Automation</li>
      </ul>
      <p class="text-gray-700 mb-4">
        Its extensive ecosystem of libraries and frameworks allows rapid development and problem-solving, making it a top choice for developers and organizations worldwide.
      </p>
      <p class="text-gray-700">
        Explore Pageflux AI's catalog for a treasure trove of resources, including courses, skill paths, projects, and more tailored to empower your Python coding journey.
      </p>

      <h2 class="text-xl font-semibold text-gray-900 mt-8 mb-4">Get started with Python</h2>
      <p class="text-gray-700 mb-4">
        Whether you seek to automate tasks, build web applications, or dive into machine learning, learning Python will equip you with all the tools and flexibility to materialize your ideas into something tangible. Find our hand-picked courses and skill paths curated to cover everything theoretical to real-world projects you can practice with and build your coding up to the next level.
      </p>

      <h2 class="text-xl font-semibold text-gray-900 mb-4">Explore everything: Python</h2>
      <p class="text-gray-700 mb-4">
        Discover the power of Python with online courses that spark creativity and boost your career—just like those on Pageflux AI!
      </p>
      <ul class="list-disc list-inside space-y-2 text-gray-700 mb-4">
        <li><strong>Code in real-time:</strong> Dive into interactive, hands-on lessons that let you write code as you learn.</li>
        <li><strong>Learn at your pace:</strong> No deadlines—just pure, self-paced fun that fits your schedule.</li>
        <li><strong>Real-world projects:</strong> Build projects that showcase your skills and prepare you for the industry.</li>
        <li><strong>Expert guidance:</strong> Get insights and tips from industry pros, ensuring you learn the best practices.</li>
        <li><strong>Community vibes:</strong> Join a vibrant community of fellow learners ready to support and inspire you.</li>
      </ul>
      <p class="text-gray-700">
        Experience a learning journey where every lesson is interactive, every project is exciting, and every challenge is a chance to level up your skills. Get ready to code, create, and conquer the world of Python—because your future in tech starts now!
      </p>
    `,
    faqs: [
      {
        question: "How do I become a Python developer?",
        answer:
          "To become a Python developer, start with learning the fundamentals through structured courses, practice with real projects, and build a portfolio. Join our comprehensive learning paths designed specifically for Python development.",
      },
      {
        question: "What is Python used for?",
        answer:
          "Python is used for web development, data science, artificial intelligence, automation, and much more. Its versatility makes it suitable for beginners and experienced developers across various industries.",
      },
      {
        question: "How many hours a day does it take to learn Python?",
        answer:
          "Learning Python effectively requires consistent practice. We recommend dedicating 1-2 hours daily for steady progress. Our self-paced courses allow you to learn at your own speed and schedule.",
      },
    ],
  },
};

// JavaScript 数据
export const javascriptTabData = {
  courses: [
    {
      id: "1",
      title: "JavaScript Fundamentals for Beginners",
      description:
        "Master the basics of JavaScript programming with hands-on exercises and real-world examples. Perfect starting point for web development.",
      duration: "12 hrs",
      difficulty: "Beginner",
      challenges: "85 Challenges",
      quizzes: "15 Quizzes",
    },
    {
      id: "2",
      title: "Modern JavaScript: ES6 and Beyond",
      description:
        "Learn modern JavaScript features including ES6, ES7, and ES8 with practical examples and projects. Stay current with the latest standards.",
      duration: "15 hrs",
      difficulty: "Intermediate",
      challenges: "120 Challenges",
      quizzes: "20 Quizzes",
    },
    {
      id: "3",
      title: "The Complete React Developer Course",
      description:
        "Master React from basics to advanced concepts. Build real-world applications with hooks, context, and modern patterns.",
      duration: "28 hrs",
      difficulty: "Intermediate",
      challenges: "150 Challenges",
      quizzes: "35 Quizzes",
    },
  ],
  paths: [
    {
      id: "1",
      title: "Frontend Developer Path",
      description:
        "Complete path to become a frontend developer. Learn HTML, CSS, JavaScript, React, and modern development tools...",
      duration: "100 hrs",
      difficulty: "Beginner to Advanced",
      challenges: "380 Challenges",
      quizzes: "250 Quizzes",
    },
    {
      id: "2",
      title: "Full Stack JavaScript Path",
      description:
        "Master both frontend and backend JavaScript development. Build complete web applications from scratch...",
      duration: "140 hrs",
      difficulty: "Intermediate",
      challenges: "420 Challenges",
      quizzes: "300 Quizzes",
    },
  ],
  cloudlabs: [
    {
      id: "1",
      title: "React Development Lab",
      description:
        "Build interactive React applications in a cloud environment. Practice with real-world scenarios and deployment...",
      duration: "10 hrs",
      difficulty: "Intermediate",
      challenges: "20 Labs",
      quizzes: "8 Assessments",
    },
    {
      id: "2",
      title: "Node.js Backend Lab",
      description:
        "Create scalable backend services with Node.js and Express. Learn API development and database integration...",
      duration: "12 hrs",
      difficulty: "Intermediate",
      challenges: "18 Labs",
      quizzes: "10 Assessments",
    },
  ],
  assessments: [
    {
      id: "1",
      title: "JavaScript Fundamentals Test",
      description:
        "Evaluate your understanding of JavaScript basics including variables, functions, objects, and DOM manipulation...",
      duration: "40 mins",
      difficulty: "Beginner",
      challenges: "30 Questions",
      quizzes: "Multiple Choice",
    },
    {
      id: "2",
      title: "React Skills Assessment",
      description:
        "Test your React knowledge including components, hooks, state management, and modern patterns...",
      duration: "50 mins",
      difficulty: "Intermediate",
      challenges: "25 Questions",
      quizzes: "Practical Tasks",
    },
  ],
  projects: [
    {
      id: "1",
      title: "E-commerce Website with React",
      description:
        "Build a complete e-commerce platform using React, Redux, and modern JavaScript. Includes payment integration...",
      duration: "30 hrs",
      difficulty: "Advanced",
      challenges: "12 Milestones",
      quizzes: "Code Reviews",
    },
    {
      id: "2",
      title: "Real-time Chat Application",
      description:
        "Create a real-time chat app using Node.js, Socket.io, and React. Learn websockets and real-time communication...",
      duration: "20 hrs",
      difficulty: "Intermediate",
      challenges: "8 Milestones",
      quizzes: "Feature Tests",
    },
  ],
  content: {
    introduction: `
      <h2 class="text-xl font-semibold text-gray-900 mb-4">Delve into any sector with JavaScript</h2>
      <ul class="list-disc list-inside space-y-2 text-gray-700 mb-4">
        <li>Frontend web development</li>
        <li>Backend development with Node.js</li>
        <li>Mobile app development</li>
        <li>Desktop applications</li>
      </ul>
      <p class="text-gray-700 mb-4">
        JavaScript's versatility and ubiquity make it the backbone of modern web development. From interactive user interfaces to server-side applications, JavaScript powers the digital world.
      </p>
      <p class="text-gray-700">
        Explore Pageflux AI's comprehensive JavaScript catalog featuring courses, skill paths, and hands-on projects designed to master both frontend and backend development.
      </p>

      <h2 class="text-xl font-semibold text-gray-900 mt-8 mb-4">Get started with JavaScript</h2>
      <p class="text-gray-700 mb-4">
        Whether you're building dynamic websites, creating mobile apps, or developing server-side applications, JavaScript provides the foundation for modern development. Our structured learning paths cover everything from ES6+ features to popular frameworks like React, Vue, and Node.js.
      </p>

      <h2 class="text-xl font-semibold text-gray-900 mb-4">Explore everything: JavaScript</h2>
      <p class="text-gray-700 mb-4">
        Master JavaScript with our comprehensive learning platform designed for developers at every level!
      </p>
      <ul class="list-disc list-inside space-y-2 text-gray-700 mb-4">
        <li><strong>Interactive coding:</strong> Practice JavaScript concepts with live code editors and instant feedback.</li>
        <li><strong>Modern frameworks:</strong> Learn React, Vue, Angular, and Node.js with real-world projects.</li>
        <li><strong>Industry projects:</strong> Build portfolio-worthy applications that showcase your skills.</li>
        <li><strong>Expert mentorship:</strong> Learn from experienced developers and industry professionals.</li>
        <li><strong>Community support:</strong> Connect with fellow JavaScript developers and share knowledge.</li>
      </ul>
      <p class="text-gray-700">
        Join thousands of developers who have mastered JavaScript through our hands-on approach. Start your journey today and become a full-stack JavaScript developer!
      </p>
    `,
    faqs: [
      {
        question: "How long does it take to learn JavaScript?",
        answer:
          "With consistent practice, you can learn JavaScript basics in 2-3 months. Our structured courses help you progress from beginner to advanced level efficiently.",
      },
      {
        question: "Should I learn vanilla JavaScript or a framework first?",
        answer:
          "We recommend starting with vanilla JavaScript to understand the fundamentals, then moving to frameworks like React or Vue. This approach builds a solid foundation.",
      },
      {
        question: "Can I use JavaScript for backend development?",
        answer:
          "Yes! Node.js allows you to use JavaScript for server-side development. You can build full-stack applications using JavaScript for both frontend and backend.",
      },
    ],
  },
};

// Java 数据
export const javaTabData = {
  courses: [
    {
      id: "1",
      title: "Java Programming for Beginners",
      description:
        "Start your Java journey with comprehensive coverage of object-oriented programming concepts. Perfect for absolute beginners.",
      duration: "20 hrs",
      difficulty: "Beginner",
      challenges: "150 Challenges",
      quizzes: "25 Quizzes",
    },
    {
      id: "2",
      title: "Advanced Java: Concurrency and Performance",
      description:
        "Master advanced Java concepts including multithreading, concurrency, and performance optimization for enterprise applications.",
      duration: "25 hrs",
      difficulty: "Advanced",
      challenges: "80 Challenges",
      quizzes: "18 Quizzes",
    },
    {
      id: "3",
      title: "Spring Boot Microservices Development",
      description:
        "Build scalable microservices with Spring Boot. Learn cloud-native development, API design, and deployment strategies.",
      duration: "30 hrs",
      difficulty: "Intermediate",
      challenges: "120 Challenges",
      quizzes: "30 Quizzes",
    },
  ],
  paths: [
    {
      id: "1",
      title: "Java Enterprise Developer Path",
      description:
        "Complete path for enterprise Java development. Master Spring, Hibernate, microservices, and cloud deployment...",
      duration: "150 hrs",
      difficulty: "Intermediate to Advanced",
      challenges: "500 Challenges",
      quizzes: "400 Quizzes",
    },
    {
      id: "2",
      title: "Android Developer Path",
      description:
        "Learn Android development with Java. Build mobile apps from basics to advanced features and Play Store deployment...",
      duration: "120 hrs",
      difficulty: "Beginner to Advanced",
      challenges: "350 Challenges",
      quizzes: "280 Quizzes",
    },
  ],
  cloudlabs: [
    {
      id: "1",
      title: "Spring Boot Development Lab",
      description:
        "Hands-on experience building enterprise applications with Spring Boot. Practice with real cloud environments...",
      duration: "15 hrs",
      difficulty: "Intermediate",
      challenges: "25 Labs",
      quizzes: "12 Assessments",
    },
    {
      id: "2",
      title: "Java Microservices Lab",
      description:
        "Build and deploy microservices architecture using Java and Spring Cloud. Learn containerization and orchestration...",
      duration: "18 hrs",
      difficulty: "Advanced",
      challenges: "20 Labs",
      quizzes: "15 Assessments",
    },
  ],
  assessments: [
    {
      id: "1",
      title: "Java OOP Fundamentals Test",
      description:
        "Test your understanding of Java object-oriented programming including classes, inheritance, polymorphism, and encapsulation...",
      duration: "50 mins",
      difficulty: "Beginner",
      challenges: "35 Questions",
      quizzes: "Multiple Choice",
    },
    {
      id: "2",
      title: "Spring Framework Assessment",
      description:
        "Comprehensive test of Spring framework knowledge including dependency injection, AOP, and Spring Boot...",
      duration: "60 mins",
      difficulty: "Advanced",
      challenges: "40 Questions",
      quizzes: "Practical Tasks",
    },
  ],
  projects: [
    {
      id: "1",
      title: "Enterprise Banking System",
      description:
        "Build a complete banking system using Java, Spring Boot, and microservices architecture. Includes security and transactions...",
      duration: "40 hrs",
      difficulty: "Advanced",
      challenges: "15 Milestones",
      quizzes: "System Reviews",
    },
    {
      id: "2",
      title: "Android Social Media App",
      description:
        "Create a full-featured social media application for Android. Learn UI design, data storage, and real-time features...",
      duration: "35 hrs",
      difficulty: "Intermediate",
      challenges: "12 Milestones",
      quizzes: "App Reviews",
    },
  ],
  content: {
    introduction: `
      <h2 class="text-xl font-semibold text-gray-900 mb-4">Delve into any sector with Java</h2>
      <ul class="list-disc list-inside space-y-2 text-gray-700 mb-4">
        <li>Enterprise application development</li>
        <li>Android mobile development</li>
        <li>Web development with Spring</li>
        <li>Big data processing</li>
      </ul>
      <p class="text-gray-700 mb-4">
        Java's platform independence, robustness, and extensive ecosystem make it a cornerstone of enterprise development. From large-scale systems to mobile applications, Java powers critical business solutions worldwide.
      </p>
      <p class="text-gray-700">
        Discover Pageflux AI's Java learning resources, including comprehensive courses, hands-on projects, and skill paths designed to master enterprise-grade development.
      </p>

      <h2 class="text-xl font-semibold text-gray-900 mt-8 mb-4">Get started with Java</h2>
      <p class="text-gray-700 mb-4">
        Whether you're building enterprise applications, developing Android apps, or working with big data, Java provides the reliability and performance needed for mission-critical systems. Our curriculum covers core Java, Spring Framework, microservices, and modern development practices.
      </p>

      <h2 class="text-xl font-semibold text-gray-900 mb-4">Explore everything: Java</h2>
      <p class="text-gray-700 mb-4">
        Master Java development with our comprehensive platform tailored for professional growth!
      </p>
      <ul class="list-disc list-inside space-y-2 text-gray-700 mb-4">
        <li><strong>Object-oriented mastery:</strong> Deep dive into OOP principles with practical examples and exercises.</li>
        <li><strong>Enterprise frameworks:</strong> Learn Spring Boot, Hibernate, and other industry-standard frameworks.</li>
        <li><strong>Real-world projects:</strong> Build scalable applications that demonstrate enterprise development skills.</li>
        <li><strong>Industry expertise:</strong> Learn from senior Java developers with years of enterprise experience.</li>
        <li><strong>Career advancement:</strong> Join a community focused on professional Java development careers.</li>
      </ul>
      <p class="text-gray-700">
        Transform your career with Java expertise that's in high demand. Start building enterprise-grade applications and join the ranks of professional Java developers!
      </p>
    `,
    faqs: [
      {
        question: "Is Java still relevant in 2024?",
        answer:
          "Absolutely! Java remains one of the most popular programming languages, especially in enterprise environments. It's constantly evolving with new features and improvements.",
      },
      {
        question: "What's the difference between Java and JavaScript?",
        answer:
          "Despite similar names, Java and JavaScript are completely different languages. Java is used for enterprise applications and Android development, while JavaScript is primarily for web development.",
      },
      {
        question: "How long does it take to become proficient in Java?",
        answer:
          "With dedicated study, you can learn Java fundamentals in 3-4 months. Becoming proficient in enterprise Java development typically takes 6-12 months of consistent practice.",
      },
    ],
  },
};

// 所有语言的tab数据
export const allTabData = {
  python: pythonTabData,
  javascript: javascriptTabData,
  java: javaTabData,
};

// 获取指定语言和tab的数据
export const getTabData = (language: string, tab: string): TabItem[] => {
  const languageData = allTabData[language as keyof typeof allTabData];
  if (!languageData) return [];

  // 排除content字段，只返回TabItem数组
  const tabData = languageData[tab as keyof typeof languageData];
  return Array.isArray(tabData) ? tabData : [];
};

// 获取指定语言的content数据
export const getLanguageContent = (
  language: string
): LanguageContent | null => {
  const languageData = allTabData[language as keyof typeof allTabData];
  if (!languageData) return null;

  return languageData.content || null;
};
