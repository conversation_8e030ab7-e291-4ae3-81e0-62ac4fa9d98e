import { CategoryInfo } from "@/types/course";

// 定义所有可用的分类
export const CATEGORY_TYPES: { [key: string]: CategoryInfo } = {
  python: {
    id: "python",
    title: "Python",
    description:
      "Python is one of the most popular programming languages in the world, and for a good reason—known for its simplicity and readable syntax, it is often the first choice of many, accessible to beginners while still being powerful for experienced developers.",
    icon: "/images/common/python.png",
    faqs: [
      {
        id: "python-faq-1",
        question: "How do I become a Python developer?",
        answer:
          "To become a Python developer, start with learning the fundamentals through structured courses, practice with real projects, and build a portfolio. Join our comprehensive learning paths designed specifically for Python development.",
      },
      {
        id: "python-faq-2",
        question: "What is Python used for?",
        answer:
          "Python is used for web development, data science, artificial intelligence, automation, and much more. Its versatility makes it suitable for beginners and experienced developers across various industries.",
      },
      {
        id: "python-faq-3",
        question: "How many hours a day does it take to learn Python?",
        answer:
          "Learning Python effectively requires consistent practice. We recommend dedicating 1-2 hours daily for steady progress. Our self-paced courses allow you to learn at your own speed and schedule.",
      },
    ],
    infoSections: [
      {
        title: "Delve into any sector with Python",
        description:
          "Its extensive ecosystem of libraries and frameworks allows rapid development and problem-solving, making it a top choice for developers and organizations worldwide. Explore Pageflux AI's catalog for a treasure trove of resources, including courses, skill paths, projects, and more tailored to empower your Python coding journey.",
        features: [
          "Web development",
          "Data science",
          "Artificial intelligence",
          "Automation",
        ],
      },
      {
        title: "Get started with Python",
        description:
          "Whether you seek to automate tasks, build web applications, or dive into machine learning, learning Python will equip you with all the tools and flexibility to materialize your ideas into something tangible. Find our hand-picked courses and skill paths curated to cover everything theoretical to real-world projects you can practice with and build your coding up to the next level.",
      },
      {
        title: "Explore everything: Python",
        description:
          "Discover the power of Python with online courses that spark creativity and boost your career—just like those on Pageflux AI!",
        features: [
          "Code in real-time: Dive into interactive, hands-on lessons that let you write code as you learn.",
          "Learn at your pace: No deadlines—just pure, self-paced fun that fits your schedule.",
          "Real-world projects: Build projects that showcase your skills and prepare you for the tech industry.",
          "Expert guidance: Get insights and tips from industry pros, ensuring you learn the best practices.",
          "Community vibes: Join a vibrant community of fellow learners ready to support and inspire you.",
        ],
      },
    ],
  },
  java: {
    id: "java",
    title: "Java",
    description:
      "Java is a robust, object-oriented programming language designed for enterprise applications, Android development, and large-scale systems. Known for its 'write once, run anywhere' philosophy, Java provides strong memory management, security features, and extensive libraries that make it ideal for building scalable, maintainable applications.",
    icon: "/images/common/java.png",
    faqs: [
      {
        id: "java-faq-1",
        question: "What makes Java different from other programming languages?",
        answer:
          "Java is platform-independent, meaning code written once can run on any device with a Java Virtual Machine (JVM). It features automatic memory management, strong type checking, and built-in security features, making it ideal for enterprise applications and large-scale systems.",
      },
      {
        id: "java-faq-2",
        question: "Is Java good for beginners?",
        answer:
          "Yes, Java is excellent for beginners due to its clear syntax, strong documentation, and extensive community support. Its object-oriented nature helps new programmers understand fundamental programming concepts, and there are abundant learning resources available.",
      },
      {
        id: "java-faq-3",
        question: "What can I build with Java?",
        answer:
          "Java is versatile and used for enterprise applications, Android mobile apps, web applications with Spring framework, desktop applications, big data processing with Apache Spark, and microservices architecture. Major companies like Netflix, Amazon, and Google use Java extensively.",
      },
    ],
  },
  javascript: {
    id: "javascript",
    title: "JavaScript",
    description:
      "JavaScript is the programming language of the web, enabling interactive and dynamic web applications. From frontend user interfaces to backend servers with Node.js, JavaScript powers modern web development with frameworks like React, Vue, and Angular, making it essential for full-stack development.",
    icon: "/images/common/javascript.png",
    faqs: [
      {
        id: "javascript-faq-1",
        question: "Why is JavaScript so popular?",
        answer:
          "JavaScript is the only programming language that runs natively in web browsers, making it essential for web development. It's versatile, running on both frontend and backend (Node.js), has a huge ecosystem of libraries and frameworks, and offers excellent job opportunities in web development.",
      },
      {
        id: "javascript-faq-2",
        question: "Should I learn JavaScript as my first programming language?",
        answer:
          "JavaScript is an excellent first language because you can see immediate visual results in web browsers, it has a relatively gentle learning curve, and there's a massive community and learning resources. You can start building interactive websites right away without complex setup.",
      },
      {
        id: "javascript-faq-3",
        question: "What's the difference between JavaScript and Java?",
        answer:
          "Despite similar names, JavaScript and Java are completely different languages. JavaScript is primarily for web development and runs in browsers, while Java is for enterprise applications and runs on the Java Virtual Machine. JavaScript is dynamically typed and interpreted, while Java is statically typed and compiled.",
      },
    ],
    infoSections: [
      {
        title: "Delve into any sector with JavaScript",
        description:
          "JavaScript's versatility extends far beyond web browsers. Its extensive ecosystem enables rapid development across multiple platforms and domains, making it the most widely-used programming language in the world.",
        features: [
          "Frontend web development",
          "Backend server development (Node.js)",
          "Mobile app development (React Native)",
          "Desktop applications (Electron)",
        ],
      },
      {
        title: "Get started with JavaScript",
        description:
          "Whether you want to create interactive websites, build mobile apps, or develop server-side applications, JavaScript provides the foundation for modern web development. Start with the basics and gradually explore frameworks like React, Vue, and Node.js to build full-stack applications.",
      },
      {
        title: "Explore everything: JavaScript",
        description:
          "Master JavaScript with comprehensive courses that cover everything from fundamentals to advanced frameworks!",
        features: [
          "Interactive coding: Write and test JavaScript code directly in your browser with instant feedback.",
          "Framework mastery: Learn popular frameworks like React, Vue, Angular, and Node.js through hands-on projects.",
          "Real-world applications: Build portfolio-worthy projects including web apps, APIs, and mobile applications.",
          "Industry best practices: Learn modern JavaScript (ES6+), testing, debugging, and performance optimization.",
          "Career preparation: Develop skills that are in high demand across the tech industry.",
        ],
      },
    ],
  },
  c: {
    id: "c",
    title: "C",
    description:
      "C is a foundational systems programming language that provides low-level access to memory and hardware. Known for its efficiency and performance, C is used in operating systems, embedded systems, and performance-critical applications. Learning C provides deep understanding of how computers work at the hardware level.",
    icon: "/images/common/c.png",
    faqs: [
      {
        id: "c-faq-1",
        question: "Why should I learn C programming?",
        answer:
          "C provides fundamental understanding of how computers work, memory management, and system-level programming. It's the foundation for many other languages, used in operating systems, embedded systems, and performance-critical applications. Learning C makes you a better programmer in any language.",
      },
      {
        id: "c-faq-2",
        question: "Is C difficult to learn?",
        answer:
          "C has a steeper learning curve than higher-level languages because you must manage memory manually and understand low-level concepts. However, this makes you a stronger programmer. With dedication and practice, C is definitely learnable and provides excellent programming fundamentals.",
      },
      {
        id: "c-faq-3",
        question: "What is C used for today?",
        answer:
          "C is used in operating systems (Linux, Windows), embedded systems (IoT devices, automotive), game engines, databases, compilers, and high-performance applications. It's essential for system programming, device drivers, and anywhere maximum performance and hardware control are needed.",
      },
    ],
  },
  react: {
    id: "react",
    title: "React",
    description:
      "React is a powerful JavaScript library for building user interfaces, developed by Facebook. It uses a component-based architecture and virtual DOM for efficient rendering, making it the most popular choice for modern web applications. React's ecosystem includes tools like Next.js, React Native for mobile development, and extensive community support.",
    icon: "/images/common/react.png",
    faqs: [
      {
        id: "react-faq-1",
        question: "What makes React different from other frontend frameworks?",
        answer:
          "React uses a virtual DOM for efficient updates, component-based architecture for reusability, and unidirectional data flow for predictable state management. Its large ecosystem, strong community support, and backing by Facebook make it a reliable choice for modern web development.",
      },
      {
        id: "react-faq-2",
        question: "Do I need to know JavaScript before learning React?",
        answer:
          "Yes, solid JavaScript knowledge is essential before learning React. You should understand ES6+ features like arrow functions, destructuring, modules, and promises. React builds upon JavaScript concepts, so a strong foundation will make learning React much easier and more effective.",
      },
      {
        id: "react-faq-3",
        question: "What can I build with React?",
        answer:
          "React is versatile for building single-page applications (SPAs), progressive web apps (PWAs), mobile apps with React Native, desktop applications with Electron, and even static sites with Gatsby. Companies like Netflix, Airbnb, and Instagram use React for their user interfaces.",
      },
    ],
    infoSections: [
      {
        title: "Delve into any sector with React",
        description:
          "React's component-based architecture and virtual DOM make it the go-to choice for building scalable, maintainable user interfaces. From startups to Fortune 500 companies, React powers some of the world's most popular applications.",
        features: [
          "Single-page applications (SPAs)",
          "Progressive web apps (PWAs)",
          "Mobile development (React Native)",
          "Server-side rendering (Next.js)",
        ],
      },
      {
        title: "Get started with React",
        description:
          "Begin your React journey by mastering components, state management, and the React ecosystem. Learn to build interactive user interfaces that scale from simple websites to complex enterprise applications. Our courses cover everything from React fundamentals to advanced patterns and best practices.",
      },
      {
        title: "Explore everything: React",
        description:
          "Become a React expert with hands-on courses designed for real-world development!",
        features: [
          "Component mastery: Learn to build reusable, maintainable components with modern React patterns.",
          "State management: Master React hooks, Context API, and popular libraries like Redux and Zustand.",
          "Performance optimization: Understand React's rendering process and optimization techniques.",
          "Ecosystem exploration: Dive into Next.js, React Router, testing libraries, and development tools.",
          "Industry projects: Build production-ready applications that showcase your React expertise.",
        ],
      },
    ],
  },
  docker: {
    id: "docker",
    title: "Docker",
    description:
      "Docker revolutionizes application deployment through containerization technology. It packages applications with their dependencies into lightweight, portable containers that run consistently across different environments. Essential for modern DevOps, microservices architecture, and cloud deployment strategies.",
    icon: "/images/common/docker.svg",
    faqs: [
      {
        id: "docker-faq-1",
        question: "What problems does Docker solve?",
        answer:
          "Docker solves the 'it works on my machine' problem by packaging applications with all dependencies into containers. It ensures consistent environments across development, testing, and production, simplifies deployment, enables microservices architecture, and improves resource utilization compared to traditional virtual machines.",
      },
      {
        id: "docker-faq-2",
        question: "Is Docker difficult to learn?",
        answer:
          "Docker has a moderate learning curve. Basic concepts like containers, images, and Dockerfiles are straightforward, but mastering orchestration, networking, and production deployment takes practice. With hands-on experience and good tutorials, most developers can become productive with Docker within a few weeks.",
      },
      {
        id: "docker-faq-3",
        question: "How is Docker used in real projects?",
        answer:
          "Docker is used for application packaging, microservices deployment, CI/CD pipelines, development environment standardization, and cloud migration. It's essential for Kubernetes orchestration, serverless computing, and modern DevOps practices. Most tech companies use Docker for scalable, reliable application deployment.",
      },
    ],
  },
  "vue-js": {
    id: "vue-js",
    title: "Vue.js",
    description:
      "Vue.js is a progressive JavaScript framework for building user interfaces. Known for its gentle learning curve and excellent documentation, Vue combines the best features of React and Angular while remaining approachable for beginners. It's perfect for both simple projects and complex single-page applications.",
    icon: "/images/common/javascript.png",
    faqs: [
      {
        id: "vue-faq-1",
        question: "Why choose Vue.js over React or Angular?",
        answer:
          "Vue.js offers a gentler learning curve than Angular and simpler syntax than React. It provides excellent documentation, progressive adoption (you can use it in parts of existing projects), and combines the best features of both frameworks. Vue is particularly great for developers transitioning from jQuery or starting with frontend frameworks.",
      },
      {
        id: "vue-faq-2",
        question: "Is Vue.js good for large applications?",
        answer:
          "Yes, Vue.js scales well for large applications with features like Vuex for state management, Vue Router for routing, and Vue CLI for project scaffolding. Companies like GitLab, Adobe, and Nintendo use Vue.js for production applications. Vue 3's Composition API makes it even more suitable for complex projects.",
      },
      {
        id: "vue-faq-3",
        question: "How long does it take to learn Vue.js?",
        answer:
          "With basic JavaScript knowledge, you can start building with Vue.js in a few days. Becoming proficient typically takes 2-4 weeks of consistent practice. Vue's excellent documentation and gentle learning curve make it one of the fastest frontend frameworks to learn and become productive with.",
      },
    ],
  },
  r: {
    id: "r",
    title: "R",
    description:
      "R is a powerful programming language specifically designed for statistical computing, data analysis, and data visualization. Widely used in academia, research, and data science, R provides extensive statistical libraries, advanced plotting capabilities, and seamless integration with databases and big data tools.",
    icon: "/images/common/python.png",
    faqs: [
      {
        id: "r-faq-1",
        question: "What makes R special for data analysis?",
        answer:
          "R was specifically designed for statistics and data analysis, offering thousands of specialized packages for every statistical method imaginable. It excels at data visualization with ggplot2, handles complex statistical modeling, and integrates well with databases and big data tools like Hadoop and Spark.",
      },
      {
        id: "r-faq-2",
        question: "Should I learn R or Python for data science?",
        answer:
          "Both are excellent choices. R is superior for statistical analysis, academic research, and data visualization, while Python is better for general programming, machine learning, and production systems. Many data scientists use both languages depending on the project requirements.",
      },
      {
        id: "r-faq-3",
        question: "Is R difficult to learn for beginners?",
        answer:
          "R has a moderate learning curve, especially for those without programming experience. However, its syntax is designed to be close to statistical notation, making it intuitive for statisticians and researchers. With dedicated practice and good resources, beginners can become productive in R within a few months.",
      },
    ],
  },
  "web-dev": {
    id: "web-dev",
    title: "Web Development",
    description:
      "Web development encompasses the complete process of building websites and web applications, from frontend user interfaces to backend servers and databases. Master HTML, CSS, JavaScript, and modern frameworks to create responsive, interactive, and scalable web solutions that power the digital world.",
    icon: "/images/common/web_dev.png",
    faqs: [
      {
        id: "web-dev-faq-1",
        question: "What skills do I need to become a web developer?",
        answer:
          "Start with HTML for structure, CSS for styling, and JavaScript for interactivity. Then learn a frontend framework (React, Vue, or Angular), backend technologies (Node.js, Python, or PHP), databases (SQL and NoSQL), and version control with Git. Understanding responsive design and web performance is also crucial.",
      },
      {
        id: "web-dev-faq-2",
        question: "How long does it take to become a web developer?",
        answer:
          "With consistent daily practice, you can build basic websites in 3-6 months. Becoming job-ready typically takes 6-12 months of dedicated learning and building projects. Full-stack proficiency usually requires 1-2 years, but you can start freelancing or find entry-level positions much sooner.",
      },
      {
        id: "web-dev-faq-3",
        question:
          "What's the difference between frontend and backend development?",
        answer:
          "Frontend development focuses on what users see and interact with (HTML, CSS, JavaScript, React). Backend development handles server-side logic, databases, and APIs (Node.js, Python, Java). Full-stack developers work on both sides, creating complete web applications from user interface to server infrastructure.",
      },
    ],
  },
  devops: {
    id: "devops",
    title: "DevOps",
    description:
      "DevOps bridges the gap between development and operations, emphasizing automation, continuous integration/deployment (CI/CD), and infrastructure as code. Learn containerization with Docker, orchestration with Kubernetes, cloud platforms, and monitoring tools to streamline software delivery and improve system reliability.",
    icon: "/images/common/docker.svg",
    faqs: [
      {
        id: "devops-faq-1",
        question: "What exactly does a DevOps engineer do?",
        answer:
          "DevOps engineers automate software deployment pipelines, manage cloud infrastructure, implement monitoring and logging systems, ensure security compliance, and facilitate collaboration between development and operations teams. They focus on making software delivery faster, more reliable, and more secure.",
      },
      {
        id: "devops-faq-2",
        question: "What skills are essential for DevOps?",
        answer:
          "Key skills include Linux/Unix systems, cloud platforms (AWS, Azure, GCP), containerization (Docker, Kubernetes), CI/CD tools (Jenkins, GitLab), infrastructure as code (Terraform, Ansible), scripting (Python, Bash), and monitoring tools (Prometheus, Grafana). Understanding networking and security is also crucial.",
      },
      {
        id: "devops-faq-3",
        question: "How do I transition into DevOps from development?",
        answer:
          "Start by learning Linux fundamentals, cloud platforms, and containerization. Practice with CI/CD pipelines, infrastructure automation, and monitoring tools. Build projects that demonstrate these skills, contribute to open-source DevOps tools, and consider getting cloud certifications to validate your knowledge.",
      },
    ],
  },
  aws: {
    id: "aws",
    title: "AWS (Amazon Web Services)",
    description:
      "Amazon Web Services is the world's most comprehensive cloud computing platform, offering over 200 services including computing power, storage, databases, machine learning, and analytics. Master AWS to build scalable, secure, and cost-effective cloud solutions that power businesses worldwide.",
    icon: "/images/common/aws.svg",
    faqs: [
      {
        id: "aws-faq-1",
        question: "Why should I learn AWS?",
        answer:
          "AWS dominates the cloud market with over 30% market share, making AWS skills highly valuable for career growth. It offers unmatched scalability, global infrastructure, and comprehensive services. AWS certification can significantly boost your salary and open doors to cloud architect, DevOps, and solutions architect roles.",
      },
      {
        id: "aws-faq-2",
        question: "What AWS services should I learn first?",
        answer:
          "Start with core services: EC2 (virtual servers), S3 (storage), RDS (databases), VPC (networking), and IAM (security). Then progress to Lambda (serverless), CloudFormation (infrastructure as code), and CloudWatch (monitoring). This foundation covers most real-world AWS use cases.",
      },
      {
        id: "aws-faq-3",
        question: "How do I get started with AWS without breaking the bank?",
        answer:
          "AWS offers a generous free tier with 12 months of free access to many services, including 750 hours of EC2, 5GB of S3 storage, and more. Use the free tier to practice, set up billing alerts to avoid unexpected charges, and leverage AWS training resources and hands-on labs.",
      },
    ],
  },
  csharp: {
    id: "csharp",
    title: "C#",
    description:
      "C# is a modern, object-oriented programming language developed by Microsoft for the .NET ecosystem. Known for its strong typing, memory management, and versatility, C# is used for web applications with ASP.NET, desktop applications with WPF, mobile apps with Xamarin, and game development with Unity.",
    icon: "/images/common/c_sharp.png",
    faqs: [
      {
        id: "csharp-faq-1",
        question: "What makes C# a good programming language to learn?",
        answer:
          "C# combines the power of C++ with the simplicity of Visual Basic. It offers strong typing for fewer runtime errors, automatic memory management, excellent tooling with Visual Studio, and versatility across web, desktop, mobile, and game development. The .NET ecosystem provides extensive libraries and frameworks.",
      },
      {
        id: "csharp-faq-2",
        question: "Can I use C# for cross-platform development?",
        answer:
          "Yes! With .NET Core/.NET 5+, C# applications run on Windows, macOS, and Linux. You can build web APIs, console applications, and even desktop apps that work across platforms. Xamarin enables mobile development for iOS and Android, while Unity supports game development for multiple platforms.",
      },
      {
        id: "csharp-faq-3",
        question: "What career opportunities are available with C#?",
        answer:
          "C# developers can pursue roles as web developers (ASP.NET), desktop application developers, mobile developers (Xamarin), game developers (Unity), enterprise software developers, and cloud developers (Azure). Microsoft's ecosystem offers excellent career prospects with competitive salaries.",
      },
    ],
  },
  "system-design": {
    id: "system-design",
    title: "System Design",
    description:
      "System design is the art and science of architecting large-scale distributed systems that are scalable, reliable, and efficient. Learn to design systems like Netflix, Twitter, and Uber by understanding load balancing, databases, caching, microservices, and distributed system principles essential for senior engineering roles.",
    icon: "/images/common/overview.svg",
    faqs: [
      {
        id: "system-design-faq-1",
        question: "Why is system design important for software engineers?",
        answer:
          "System design skills are crucial for senior engineering roles and technical interviews at top companies. They demonstrate your ability to think at scale, make architectural decisions, and solve complex real-world problems. System design knowledge helps you build better software and advance to architect-level positions.",
      },
      {
        id: "system-design-faq-2",
        question: "What topics should I focus on when learning system design?",
        answer:
          "Focus on scalability principles, load balancing, database design (SQL vs NoSQL), caching strategies, microservices architecture, message queues, CDNs, and distributed system concepts like CAP theorem. Practice designing real systems like social media platforms, chat applications, and video streaming services.",
      },
      {
        id: "system-design-faq-3",
        question: "How do I prepare for system design interviews?",
        answer:
          "Practice designing popular systems (Twitter, Netflix, Uber), understand trade-offs between different approaches, learn to estimate scale and capacity, and practice explaining your designs clearly. Focus on asking clarifying questions, starting with high-level architecture, then diving into specific components and their interactions.",
      },
    ],
  },
  "interview-prep": {
    id: "interview-prep",
    title: "Interview Prep",
    description:
      "Master technical interviews at top tech companies with comprehensive preparation covering algorithms, data structures, system design, and behavioral questions. Practice with real interview questions from Google, Amazon, Facebook, and other leading companies to land your dream job.",
    icon: "/images/common/certification.svg",
    faqs: [
      {
        id: "interview-prep-faq-1",
        question: "How long should I prepare for technical interviews?",
        answer:
          "Preparation time varies by experience level. New graduates typically need 3-6 months of consistent practice, while experienced developers might need 1-3 months. Focus on daily practice with algorithms, data structures, and system design. Quality of preparation matters more than duration.",
      },
      {
        id: "interview-prep-faq-2",
        question: "What topics are most important for technical interviews?",
        answer:
          "Core topics include arrays, strings, linked lists, trees, graphs, dynamic programming, sorting algorithms, and hash tables. For senior roles, add system design, scalability concepts, and leadership principles. Practice coding problems on platforms like LeetCode and HackerRank.",
      },
      {
        id: "interview-prep-faq-3",
        question: "How do I handle the pressure during actual interviews?",
        answer:
          "Practice mock interviews with friends or platforms like Pramp. Think out loud during problem-solving, ask clarifying questions, and don't panic if you get stuck. Remember that interviewers want to see your thought process, not just the final solution. Stay calm and communicate clearly.",
      },
    ],
  },
  "generative-ai": {
    id: "generative-ai",
    title: "Generative AI",
    description:
      "Explore the cutting-edge world of generative artificial intelligence, including large language models like GPT, image generation with DALL-E and Stable Diffusion, and AI-powered applications. Learn to build, fine-tune, and deploy AI models that create content, solve problems, and transform industries.",
    icon: "/images/common/activity.svg",
    faqs: [
      {
        id: "generative-ai-faq-1",
        question: "What is generative AI and why is it important?",
        answer:
          "Generative AI creates new content (text, images, code, audio) based on training data. It's revolutionizing industries by automating creative tasks, enhancing productivity, and enabling new applications. Understanding generative AI is crucial for staying relevant in the rapidly evolving tech landscape.",
      },
      {
        id: "generative-ai-faq-2",
        question: "Do I need a PhD to work with generative AI?",
        answer:
          "No! While deep research requires advanced degrees, many practical AI applications can be built with basic programming skills and understanding of APIs. You can start by using pre-trained models, learning prompt engineering, and building applications with tools like OpenAI's API or Hugging Face.",
      },
      {
        id: "generative-ai-faq-3",
        question:
          "What programming languages should I learn for AI development?",
        answer:
          "Python is the dominant language for AI development, with libraries like TensorFlow, PyTorch, and Transformers. JavaScript is useful for web-based AI applications, while R is valuable for data analysis. Start with Python and focus on understanding AI concepts rather than just coding syntax.",
      },
    ],
  },
  "learn-to-code": {
    id: "learn-to-code",
    title: "Learn to Code",
    description:
      "Begin your programming journey with comprehensive fundamentals covering programming concepts, problem-solving skills, and multiple programming languages. Perfect for absolute beginners, this path provides a solid foundation in computational thinking and prepares you for any programming specialization.",
    icon: "/images/common/content.svg",
    faqs: [
      {
        id: "learn-to-code-faq-1",
        question: "Which programming language should I learn first?",
        answer:
          "Python is often recommended for beginners due to its readable syntax and versatility. JavaScript is great if you want to see immediate visual results in web browsers. The specific language matters less than learning fundamental programming concepts like variables, loops, functions, and problem-solving.",
      },
      {
        id: "learn-to-code-faq-2",
        question: "How long does it take to learn programming?",
        answer:
          "Basic programming concepts can be learned in 3-6 months with consistent daily practice. Becoming job-ready typically takes 6-18 months depending on your goals and dedication. Remember, programming is a lifelong learning journey - even experienced developers continuously learn new technologies and techniques.",
      },
      {
        id: "learn-to-code-faq-3",
        question: "Do I need a computer science degree to become a programmer?",
        answer:
          "No, many successful programmers are self-taught or come from bootcamps. What matters most is your ability to solve problems, write clean code, and continuously learn. Build a strong portfolio of projects, contribute to open source, and demonstrate your skills through practical applications.",
      },
    ],
  },
  other: {
    id: "other",
    title: "Other Technologies",
    description:
      "Explore specialized programming languages and emerging technologies beyond mainstream options. Discover niche languages like Rust, Go, Kotlin, Swift, and cutting-edge technologies in blockchain, IoT, quantum computing, and more. Perfect for expanding your technical horizons and staying ahead of industry trends.",
    icon: "/images/common/discover.svg",
    faqs: [
      {
        id: "other-faq-1",
        question: "Should I learn niche programming languages?",
        answer:
          "Learning specialized languages can be valuable for specific domains. Rust is excellent for systems programming, Go for backend services, Swift for iOS development, and Kotlin for Android. Choose based on your career goals and the problems you want to solve, but ensure you have solid fundamentals first.",
      },
      {
        id: "other-faq-2",
        question: "How do I stay updated with emerging technologies?",
        answer:
          "Follow tech blogs, join developer communities, attend conferences and meetups, and experiment with new technologies through side projects. Platforms like GitHub, Stack Overflow, and tech Twitter are great for discovering trends. Focus on understanding underlying principles rather than chasing every new framework.",
      },
      {
        id: "other-faq-3",
        question:
          "When should I explore technologies outside my main expertise?",
        answer:
          "Once you're comfortable with your primary technology stack, exploring adjacent areas can make you more versatile. Cross-pollination of ideas from different domains often leads to innovative solutions. Allocate 10-20% of your learning time to exploring new technologies while maintaining depth in your core skills.",
      },
    ],
  },
};

// 根据category id获取分类信息的辅助函数
export function getCategoryInfo(categoryId: string): CategoryInfo | undefined {
  return CATEGORY_TYPES[categoryId];
}
