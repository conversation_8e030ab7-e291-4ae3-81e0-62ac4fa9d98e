/*
 * @Description: UI状态管理 - 管理全局UI状态，如模态框、侧边栏等
 * @Author: Devin
 * @Date: 2025-08-11
 */
import { create } from "zustand";

// UI Store 的类型
interface UIState {
  // Auth Modal 状态
  authModalOpen: boolean;
  authModalMode: "login" | "signup";

  // Auth Modal 操作方法
  openAuthModal: (mode?: "login" | "signup") => void;
  closeAuthModal: () => void;
  setAuthModalMode: (mode: "login" | "signup") => void;

  // 便捷方法
  openLoginModal: () => void;
  openSignupModal: () => void;
}

export const useUIStore = create<UIState>((set) => ({
  // 初始状态
  authModalOpen: false,
  authModalMode: "login",

  // Auth Modal 操作方法
  openAuthModal: (mode = "login") =>
    set({
      authModalOpen: true,
      authModalMode: mode,
    }),

  closeAuthModal: () => set({ authModalOpen: false }),

  setAuthModalMode: (mode) => set({ authModalMode: mode }),

  // 便捷方法
  openLoginModal: () =>
    set({
      authModalOpen: true,
      authModalMode: "login",
    }),

  openSignupModal: () =>
    set({
      authModalOpen: true,
      authModalMode: "signup",
    }),
}));
