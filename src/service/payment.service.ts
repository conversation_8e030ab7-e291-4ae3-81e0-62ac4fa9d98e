/**
 * 支付相关服务
 * 包括支付宝和微信支付的API调用
 */

import { api } from "./api";
import type {
  // 支付宝相关
  AlipayQRCodeRequest,
  AlipayQRCodeResponse,
  AlipayWebRequest,
  AlipayWebResponse,
  AlipayStatusRequest,
  AlipayStatusResponse,
  AlipayNotifyData,

  // 微信支付相关
  WechatJSAPIRequest,
  WechatJSAPIResponse,
  WechatQRCodeRequest,
  WechatQRCodeResponse,
  WechatStatusRequest,
  WechatStatusResponse,
  WechatNotifyData,

  // 通用支付相关
  PaymentOrder,
  PaymentStatus,
  PaymentCallbackResult,
  PaymentVerificationResult,
  PaymentStats,
} from "@/types/payment";
import type { ApiResponse } from "@/types/openapi";

export const paymentService = {
  // ===== 支付宝相关 =====

  // 创建支付宝二维码支付
  async createAlipayQRCode(
    data: AlipayQRCodeRequest
  ): Promise<AlipayQRCodeResponse> {
    const resp = await api.post<AlipayQRCodeResponse>("/alipay/qrcode", data);
    return resp.data;
  },

  // 创建支付宝网页支付
  async createAlipayWeb(data: AlipayWebRequest): Promise<AlipayWebResponse> {
    const resp = await api.post<AlipayWebResponse>("/alipay/web", data);
    return resp.data;
  },

  // 查询支付宝支付状态
  async getAlipayStatus(outTradeNo: string): Promise<AlipayStatusResponse> {
    const searchParams = new URLSearchParams({ out_trade_no: outTradeNo });
    const resp = await api.get<AlipayStatusResponse>(
      `/alipay/status?${searchParams.toString()}`
    );
    return resp.data;
  },

  // 处理支付宝同步回调（GET）
  async handleAlipayCallback(
    params: Record<string, string>
  ): Promise<ApiResponse<PaymentCallbackResult>> {
    const searchParams = new URLSearchParams(params);
    const resp = await api.get<ApiResponse<PaymentCallbackResult>>(
      `/alipay/callback?${searchParams.toString()}`
    );
    return resp.data;
  },

  // 处理支付宝同步回调（POST）
  async handleAlipayCallbackPost(
    data: Record<string, string>
  ): Promise<ApiResponse<PaymentCallbackResult>> {
    const formData = new URLSearchParams();
    Object.entries(data).forEach(([key, value]) => {
      formData.append(key, value);
    });

    const resp = await api.post<ApiResponse<PaymentCallbackResult>>(
      "/alipay/callback",
      formData,
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );
    return resp.data;
  },

  // 验证支付宝异步通知
  async verifyAlipayNotify(
    notifyData: AlipayNotifyData
  ): Promise<PaymentVerificationResult> {
    try {
      const resp = await api.post<ApiResponse<PaymentVerificationResult>>(
        "/alipay/verify-notify",
        notifyData
      );
      return resp.data.data || { valid: false, error_message: "验证失败" };
    } catch (error) {
      return { valid: false, error_message: "验证请求失败" };
    }
  },

  // ===== 微信支付相关 =====

  // 创建微信JSAPI支付
  async createWechatJSAPI(
    data: WechatJSAPIRequest
  ): Promise<WechatJSAPIResponse> {
    const resp = await api.post<WechatJSAPIResponse>("/wechat/jsapi", data);
    return resp.data;
  },

  // 创建微信二维码支付
  async createWechatQRCode(
    data: WechatQRCodeRequest
  ): Promise<WechatQRCodeResponse> {
    const resp = await api.post<WechatQRCodeResponse>("/wechat/qrcode", data);
    return resp.data;
  },

  // 查询微信支付状态
  async getWechatStatus(outTradeNo: string): Promise<WechatStatusResponse> {
    const searchParams = new URLSearchParams({ out_trade_no: outTradeNo });
    const resp = await api.get<WechatStatusResponse>(
      `/wechat/status?${searchParams.toString()}`
    );
    return resp.data;
  },

  // 处理微信同步回调（GET）
  async handleWechatCallback(
    params: Record<string, string>
  ): Promise<ApiResponse<PaymentCallbackResult>> {
    const searchParams = new URLSearchParams(params);
    const resp = await api.get<ApiResponse<PaymentCallbackResult>>(
      `/wechat/callback?${searchParams.toString()}`
    );
    return resp.data;
  },

  // 处理微信同步回调（POST）
  async handleWechatCallbackPost(
    data: Record<string, string>
  ): Promise<ApiResponse<PaymentCallbackResult>> {
    const formData = new URLSearchParams();
    Object.entries(data).forEach(([key, value]) => {
      formData.append(key, value);
    });

    const resp = await api.post<ApiResponse<PaymentCallbackResult>>(
      "/wechat/callback",
      formData,
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );
    return resp.data;
  },

  // 验证微信异步通知
  async verifyWechatNotify(
    notifyData: WechatNotifyData
  ): Promise<PaymentVerificationResult> {
    try {
      const resp = await api.post<ApiResponse<PaymentVerificationResult>>(
        "/wechat/verify-notify",
        notifyData
      );
      return resp.data.data || { valid: false, error_message: "验证失败" };
    } catch (error) {
      return { valid: false, error_message: "验证请求失败" };
    }
  },

  // ===== 通用支付相关 =====

  // 创建统一支付订单
  async createPaymentOrder(
    data: PaymentOrder & {
      payment_method: "alipay_qr" | "alipay_web" | "wechat_jsapi" | "wechat_qr";
      extra_params?: Record<string, any>;
    }
  ): Promise<
    ApiResponse<{
      out_trade_no: string;
      payment_url?: string;
      qr_code?: string;
      form_data?: string;
      jsapi_params?: Record<string, any>;
    }>
  > {
    const resp = await api.post<
      ApiResponse<{
        out_trade_no: string;
        payment_url?: string;
        qr_code?: string;
        form_data?: string;
        jsapi_params?: Record<string, any>;
      }>
    >("/payment/create-order", data);
    return resp.data;
  },

  // 查询支付订单状态
  async getPaymentOrderStatus(
    outTradeNo: string
  ): Promise<ApiResponse<PaymentStatus>> {
    const searchParams = new URLSearchParams({ out_trade_no: outTradeNo });
    const resp = await api.get<ApiResponse<PaymentStatus>>(
      `/payment/order-status?${searchParams.toString()}`
    );
    return resp.data;
  },

  // 取消支付订单
  async cancelPaymentOrder(
    outTradeNo: string,
    reason?: string
  ): Promise<ApiResponse<{ message: string }>> {
    const resp = await api.post<ApiResponse<{ message: string }>>(
      "/payment/cancel-order",
      {
        out_trade_no: outTradeNo,
        cancel_reason: reason,
      }
    );
    return resp.data;
  },

  // 申请退款
  async requestRefund(data: {
    out_trade_no: string;
    refund_amount: number;
    refund_reason?: string;
    out_refund_no?: string;
  }): Promise<
    ApiResponse<{
      refund_id: string;
      refund_status: "processing" | "success" | "failed";
      refund_amount: number;
    }>
  > {
    const resp = await api.post<
      ApiResponse<{
        refund_id: string;
        refund_status: "processing" | "success" | "failed";
        refund_amount: number;
      }>
    >("/payment/refund", data);
    return resp.data;
  },

  // 查询退款状态
  async getRefundStatus(refundId: string): Promise<
    ApiResponse<{
      refund_id: string;
      out_trade_no: string;
      refund_status: "processing" | "success" | "failed";
      refund_amount: number;
      refund_time?: string;
      refund_reason?: string;
    }>
  > {
    const resp = await api.get<
      ApiResponse<{
        refund_id: string;
        out_trade_no: string;
        refund_status: "processing" | "success" | "failed";
        refund_amount: number;
        refund_time?: string;
        refund_reason?: string;
      }>
    >(`/payment/refund-status/${refundId}`);
    return resp.data;
  },

  // ===== 支付统计和报表相关 =====

  // 获取支付统计数据
  async getPaymentStats(params?: {
    start_date?: string;
    end_date?: string;
    payment_method?: "alipay" | "wechat" | "all";
    group_by?: "day" | "week" | "month";
  }): Promise<ApiResponse<PaymentStats>> {
    const searchParams = new URLSearchParams();
    if (params?.start_date)
      searchParams.append("start_date", params.start_date);
    if (params?.end_date) searchParams.append("end_date", params.end_date);
    if (params?.payment_method)
      searchParams.append("payment_method", params.payment_method);
    if (params?.group_by) searchParams.append("group_by", params.group_by);

    const resp = await api.get<ApiResponse<PaymentStats>>(
      `/payment/stats?${searchParams.toString()}`
    );
    return resp.data;
  },

  // 获取支付订单列表
  async getPaymentOrders(params?: {
    page?: number;
    page_size?: number;
    status?: "pending" | "paid" | "failed" | "cancelled" | "refunded";
    payment_method?: "alipay" | "wechat";
    start_date?: string;
    end_date?: string;
    search?: string;
  }): Promise<
    ApiResponse<{
      orders: Array<
        PaymentStatus & {
          payment_method: string;
          created_at: string;
          updated_at: string;
        }
      >;
      pagination: {
        page: number;
        page_size: number;
        total: number;
        total_pages: number;
      };
    }>
  > {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.page_size)
      searchParams.append("page_size", params.page_size.toString());
    if (params?.status) searchParams.append("status", params.status);
    if (params?.payment_method)
      searchParams.append("payment_method", params.payment_method);
    if (params?.start_date)
      searchParams.append("start_date", params.start_date);
    if (params?.end_date) searchParams.append("end_date", params.end_date);
    if (params?.search) searchParams.append("search", params.search);

    const resp = await api.get<
      ApiResponse<{
        orders: Array<
          PaymentStatus & {
            payment_method: string;
            created_at: string;
            updated_at: string;
          }
        >;
        pagination: {
          page: number;
          page_size: number;
          total: number;
          total_pages: number;
        };
      }>
    >(`/payment/orders?${searchParams.toString()}`);
    return resp.data;
  },

  // 导出支付数据
  async exportPaymentData(params?: {
    format?: "csv" | "excel";
    start_date?: string;
    end_date?: string;
    payment_method?: "alipay" | "wechat" | "all";
  }): Promise<ApiResponse<{ download_url: string }>> {
    const resp = await api.post<ApiResponse<{ download_url: string }>>(
      "/payment/export",
      params || {}
    );
    return resp.data;
  },
};
