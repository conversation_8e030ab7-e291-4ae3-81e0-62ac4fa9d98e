/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-08-16 13:19:41
 */
/**
 * 学习节点服务（依据 docs/swagger.json）
 */

import { courseCache, withCache } from "@/lib/api-cache";
import type {
  BaseResponse,
  CreateLearningNodeRequest,
  GenerateCourseResult,
  GenerateLearningNodeRequest,
  GenerateNodeAndLessonsRequest,
  GenerateNodeAndLessonsResponse,
  LearningNode,
  NodeLesson,
  PaginatedResponse,
  UpdateLearningNodeRequest,
  UpdateNodeStatusRequest,
} from "@/types/openapi";

import { api } from "./api";

export const learningNodeService = {
  async list(params?: {
    page?: number;
    page_size?: number;
    status?: "draft" | "active" | "archived" | "deprecated";
    difficulty?: number; // 1-10
    tags?: string[];
    skills?: string[];
    learner_tags?: string[];
    query?: string;
    search_mode?: "exact" | "fuzzy";
    search_fields?: string; // Search across tags, skills, learner_tags, title, and description
  }): Promise<PaginatedResponse<LearningNode>> {
    const search = new URLSearchParams();
    if (params?.page) search.set("page", String(params.page));
    if (params?.page_size) search.set("page_size", String(params.page_size));
    if (params?.status) search.set("status", params.status);
    if (typeof params?.difficulty === "number")
      search.set("difficulty", String(params.difficulty));
    if (params?.tags?.length)
      params.tags.forEach((v) => search.append("tags", v));
    if (params?.skills?.length)
      params.skills.forEach((v) => search.append("skills", v));
    if (params?.learner_tags?.length)
      params.learner_tags.forEach((v) => search.append("learner_tags", v));
    if (params?.query) search.set("query", params.query);
    if (params?.search_mode) search.set("search_mode", params.search_mode);
    if (params?.search_fields)
      search.set("search_fields", params.search_fields);

    const resp = await api.get<PaginatedResponse<LearningNode>>(
      `/learning-nodes?${search.toString()}`
    );
    return resp.data;
  },

  async create(
    body: CreateLearningNodeRequest
  ): Promise<BaseResponse<LearningNode>> {
    const resp = await api.post<BaseResponse<LearningNode>>(
      "/learning-nodes",
      body
    );
    return resp.data;
  },

  async generate(
    body: GenerateLearningNodeRequest
  ): Promise<BaseResponse<LearningNode>> {
    const resp = await api.post<BaseResponse<LearningNode>>(
      "/learning-nodes/generate",
      body
    );
    return resp.data;
  },

  async generateNodeAndLessons(
    body: GenerateNodeAndLessonsRequest
  ): Promise<BaseResponse<GenerateNodeAndLessonsResponse>> {
    const resp = await api.post<BaseResponse<GenerateNodeAndLessonsResponse>>(
      "/learning-nodes/generate-node-and-lessons",
      body
    );
    return resp.data;
  },

  async get(id: string): Promise<BaseResponse<LearningNode>> {
    const resp = await api.get<BaseResponse<LearningNode>>(
      `/learning-nodes/${id}`
    );
    return resp.data;
  },

  async update(
    id: string,
    body: UpdateLearningNodeRequest
  ): Promise<BaseResponse<LearningNode>> {
    const resp = await api.put<BaseResponse<LearningNode>>(
      `/learning-nodes/${id}`,
      body
    );
    return resp.data;
  },

  async updateStatus(
    id: string,
    body: UpdateNodeStatusRequest
  ): Promise<BaseResponse<unknown>> {
    const resp = await api.put<BaseResponse<unknown>>(
      `/learning-nodes/${id}/status`,
      body
    );
    return resp.data;
  },

  async remove(id: string): Promise<BaseResponse<unknown>> {
    const resp = await api.delete<BaseResponse<unknown>>(
      `/learning-nodes/${id}`
    );
    return resp.data;
  },

  async listByPath(path_id: string): Promise<BaseResponse<LearningNode[]>> {
    const search = new URLSearchParams({ path_id });
    const resp = await api.get<BaseResponse<LearningNode[]>>(
      `/learning-nodes/path-nodes?${search.toString()}`
    );
    return resp.data;
  },

  async generateCourse(
    node_id: string
  ): Promise<BaseResponse<GenerateCourseResult>> {
    const resp = await api.post<BaseResponse<GenerateCourseResult>>(
      `/learning-nodes/${node_id}/generate-course`,
      { node_id },
      { timeout: 300000 } // 5 minutes
    );
    return resp.data;
  },

  // 使用缓存的课程列表获取
  listLessons: withCache(
    async (node_id: string): Promise<BaseResponse<NodeLesson[]>> => {
      const resp = await api.get<BaseResponse<NodeLesson[]>>(
        `/learning-nodes/${node_id}/lessons`
      );
      return resp.data;
    },
    courseCache,
    {
      keyGenerator: (node_id: string) => `node-lessons:${node_id}`,
      ttl: 8 * 60 * 1000, // 8 分钟缓存
    }
  ),
};
