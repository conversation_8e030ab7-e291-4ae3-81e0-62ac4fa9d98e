/**
 * 用户课程学习进度服务（依据 docs/swagger_progress.json）
 */

import { api } from "./api";
import { progressCache, withCache } from "@/lib/api-cache";
import type {
  BaseResponse,
  UserLessonProgress,
  UserLessonProgressMap,
  UpsertCompletedContentFlowRequest,
  RemoveCompletedContentFlowRequest,
  CompletedContentFlowsResponse,
} from "@/types/openapi";

export const userLessonProgressService = {
  /**
   * 添加/更新已完成的内容流
   * POST /api/v1/user-lesson-progress/content-flow/upsert
   */
  async upsertCompletedContentFlow(
    request: UpsertCompletedContentFlowRequest
  ): Promise<BaseResponse<{ lesson_id: string; content_flow_id: string }>> {
    const resp = await api.post<
      BaseResponse<{ lesson_id: string; content_flow_id: string }>
    >("/user-lesson-progress/content-flow/upsert", request);
    return resp.data;
  },

  /**
   * 移除已完成的内容流
   * POST /api/v1/user-lesson-progress/content-flow/remove
   */
  async removeCompletedContentFlow(
    request: RemoveCompletedContentFlowRequest
  ): Promise<BaseResponse<{ lesson_id: string; content_flow_id: string }>> {
    const resp = await api.post<
      BaseResponse<{ lesson_id: string; content_flow_id: string }>
    >("/user-lesson-progress/content-flow/remove", request);
    return resp.data;
  },

  /**
   * 获取所有进度 - 使用缓存
   * GET /api/v1/user-lesson-progress/all
   */
  getAllProgress: withCache(
    async (): Promise<BaseResponse<UserLessonProgressMap>> => {
      const resp = await api.get<BaseResponse<UserLessonProgressMap>>(
        "/user-lesson-progress/all"
      );
      return resp.data;
    },
    progressCache,
    {
      keyGenerator: () => "all-progress",
      ttl: 1 * 60 * 1000, // 1 分钟缓存
    }
  ),

  /**
   * 获取指定路径或节点的所有进度 - 使用缓存
   * GET /api/v1/user-lesson-progress/{node_path_id}/all
   */
  getProgressByNodeOrPath: withCache(
    async (
      nodePathId: string
    ): Promise<BaseResponse<UserLessonProgressMap>> => {
      const resp = await api.get<BaseResponse<UserLessonProgressMap>>(
        `/user-lesson-progress/${nodePathId}/all`
      );
      return resp.data;
    },
    progressCache,
    {
      keyGenerator: (nodePathId: string) => `progress:${nodePathId}`,
      ttl: 1 * 60 * 1000, // 1 分钟缓存
    }
  ),

  /**
   * 获取指定路径或节点中单个课程的进度
   * GET /api/v1/user-lesson-progress/{node_path_id}/lesson/{lesson_id}
   */
  async getLessonProgress(
    nodePathId: string,
    lessonId: string
  ): Promise<BaseResponse<UserLessonProgress>> {
    const resp = await api.get<BaseResponse<UserLessonProgress>>(
      `/user-lesson-progress/${nodePathId}/lesson/${lessonId}`
    );
    return resp.data;
  },

  /**
   * 获取指定路径或节点课程的已完成内容流
   * GET /api/v1/user-lesson-progress/{node_path_id}/lesson/{lesson_id}/content-flows
   */
  async getCompletedContentFlows(
    nodePathId: string,
    lessonId: string
  ): Promise<BaseResponse<CompletedContentFlowsResponse>> {
    const resp = await api.get<BaseResponse<CompletedContentFlowsResponse>>(
      `/user-lesson-progress/${nodePathId}/lesson/${lessonId}/content-flows`
    );
    return resp.data;
  },
};
