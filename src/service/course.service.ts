/*
 * @Description: Course Service - 替换 maindata，使用 mockapi 数据
 * @Author: Devin
 * @Date: 2025-08-05
 */

import { mockApiCourseService } from "./mockapi.service";
import { MockApiCourseData } from "@/types/mockapi";

/**
 * 根据类型过滤课程
 */
function filterByType(
  courses: MockApiCourseData[],
  type: string
): MockApiCourseData[] {
  if (type === "course" || type === "all") {
    return courses; // 所有 mockapi 数据都是课程
  }
  return []; // 其他类型暂时返回空数组
}

/**
 * 课程服务类
 */
export class CourseService {
  private static instance: CourseService;
  private cachedCourses: MockApiCourseData[] | null = null;
  private lastFetchTime: number = 0;
  private cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

  private constructor() {}

  public static getInstance(): CourseService {
    if (!CourseService.instance) {
      CourseService.instance = new CourseService();
    }
    return CourseService.instance;
  }

  /**
   * 获取所有课程数据（带缓存）
   */
  async getAllCourses(): Promise<MockApiCourseData[]> {
    const now = Date.now();

    // 检查缓存是否有效
    if (this.cachedCourses && now - this.lastFetchTime < this.cacheTimeout) {
      return this.cachedCourses;
    }

    try {
      const mockApiCourses = await mockApiCourseService.getAllCourses();

      // 更新缓存
      this.cachedCourses = mockApiCourses;
      this.lastFetchTime = now;

      return mockApiCourses;
    } catch (error) {
      console.error("Failed to fetch courses:", error);
      // 如果有缓存数据，返回缓存数据
      if (this.cachedCourses) {
        return this.cachedCourses;
      }
      return [];
    }
  }

  /**
   * 根据 tab 名称获取课程
   */
  async getCoursesByTab(tabName: string): Promise<MockApiCourseData[]> {
    const allCourses = await this.getAllCourses();

    switch (tabName) {
      case "all":
        return allCourses;
      case "courses":
        return filterByType(allCourses, "course");
      case "cloud-labs":
        return filterByType(allCourses, "cloudlab");
      case "projects":
        return filterByType(allCourses, "project");
      case "paths":
        return filterByType(allCourses, "path");
      case "assessments":
        return filterByType(allCourses, "assessment");
      case "mock-interviews":
        return filterByType(allCourses, "mockinterview");
      default:
        return allCourses;
    }
  }

  /**
   * 根据标签获取课程
   */
  async getCoursesByLevel(level: string): Promise<MockApiCourseData[]> {
    const allCourses = await this.getAllCourses();
    return allCourses.filter(
      (course) =>
        course.tags.some((tag) =>
          tag.toLowerCase().includes(level.toLowerCase())
        ) ||
        course.learner_tags.some((tag) =>
          tag.toLowerCase().includes(level.toLowerCase())
        )
    );
  }

  /**
   * 根据技能获取课程
   */
  async getCoursesByCategory(category: string): Promise<MockApiCourseData[]> {
    const allCourses = await this.getAllCourses();
    return allCourses.filter((course) =>
      course.skills.some((skill) =>
        skill.toLowerCase().includes(category.toLowerCase())
      )
    );
  }

  /**
   * 根据ID获取单个课程
   */
  async getCourseById(courseId: string): Promise<MockApiCourseData | null> {
    try {
      const mockApiCourse = await mockApiCourseService.getCourseById(courseId);
      return mockApiCourse;
    } catch (error) {
      console.error("Failed to fetch course by ID:", error);
      return null;
    }
  }

  /**
   * 搜索课程
   */
  async searchCourses(query: string): Promise<MockApiCourseData[]> {
    const allCourses = await this.getAllCourses();
    const searchTerm = query.toLowerCase();

    return allCourses.filter(
      (course) =>
        course.courses[0]?.title.toLowerCase().includes(searchTerm) ||
        course.courses[0]?.summary.toLowerCase().includes(searchTerm) ||
        course.courses[0]?.brief_summary.toLowerCase().includes(searchTerm) ||
        course.tags.some((tag) => tag.toLowerCase().includes(searchTerm)) ||
        course.skills.some((skill) => skill.toLowerCase().includes(searchTerm))
    );
  }

  /**
   * 获取最受欢迎的课程
   */
  async getMostPopularCourses(limit: number = 8): Promise<MockApiCourseData[]> {
    const allCourses = await this.getAllCourses();
    // 按发布时间排序（这里简化处理）
    return allCourses
      .sort(
        (a, b) =>
          new Date(b.last_published_time).getTime() -
          new Date(a.last_published_time).getTime()
      )
      .slice(0, limit);
  }

  /**
   * 获取最新课程
   */
  async getNewCourses(limit: number = 12): Promise<MockApiCourseData[]> {
    const allCourses = await this.getAllCourses();
    return allCourses
      .sort(
        (a, b) =>
          new Date(b.first_published_time).getTime() -
          new Date(a.first_published_time).getTime()
      )
      .slice(0, limit);
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cachedCourses = null;
    this.lastFetchTime = 0;
  }
}

// 导出单例实例
export const courseService = CourseService.getInstance();

// 导出兼容函数，替换 maindata 中的函数
export async function getCoursesByTab(
  tabName: string
): Promise<MockApiCourseData[]> {
  return courseService.getCoursesByTab(tabName);
}

export async function getCoursesByLevel(
  level: string
): Promise<MockApiCourseData[]> {
  return courseService.getCoursesByLevel(level);
}

export async function getCourseById(
  courseId: string
): Promise<MockApiCourseData | null> {
  return courseService.getCourseById(courseId);
}

export async function getMostPopularPointers(
  limit: number = 8
): Promise<MockApiCourseData[]> {
  return courseService.getMostPopularCourses(limit);
}

export async function getNewAdditions(
  limit: number = 12
): Promise<MockApiCourseData[]> {
  return courseService.getNewCourses(limit);
}
