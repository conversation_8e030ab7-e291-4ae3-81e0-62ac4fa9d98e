/*
 * @Description: MockAPI 服务 - 处理课程数据的获取和转换
 * @Author: Devin
 * @Date: 2025-08-05
 */

import {CourseConverter, CourseDifficulty, CourseQueryParams, CourseSearchResult, CourseStatus, MockApiCourseData, MockApiCourseStats, MockApiResponse,} from '@/types/mockapi';

import {api} from './api';

/**
 * MockAPI 课程服务类
 */
export class MockApiCourseService {
  private static instance: MockApiCourseService;
  private baseUrl = '/api/course';

  private constructor() {}

  public static getInstance(): MockApiCourseService {
    if (!MockApiCourseService.instance) {
      MockApiCourseService.instance = new MockApiCourseService();
    }
    return MockApiCourseService.instance;
  }

  /**
   * 根据ID获取课程数据
   * @param courseId 课程ID或url_slug
   * @returns 课程数据
   */
  async getCourseById(courseId: string): Promise<MockApiCourseData|null> {
    try {
      const response = await api.get<MockApiResponse<MockApiCourseData>>(
          `${this.baseUrl}/${courseId}`, {baseURL: ''}
          // override /api/v1 to hit Next local route /api/course
      );

      if (response.data.success && response.data.data) {
        return response.data.data;
      }

      return null;
    } catch (error) {
      console.error('Error fetching course by ID:', error);
      return null;
    }
  }

  /**
   * 获取所有课程列表
   * @returns 课程列表
   */
  async getAllCourses(): Promise<MockApiCourseData[]> {
    try {
      const response = await api.get<MockApiResponse<CourseSearchResult>>(
          `${this.baseUrl}`, {baseURL: ''});

      if (response.data.success && response.data.data) {
        return response.data.data.courses;
      }

      return [];
    } catch (error) {
      console.error('Error fetching all courses:', error);
      return [];
    }
  }

  /**
   * 搜索课程
   * @param params 查询参数
   * @returns 搜索结果
   */
  async searchCourses(params: CourseQueryParams): Promise<CourseSearchResult> {
    try {
      const queryString = new URLSearchParams();

      if (params.tags?.length) {
        queryString.append('tags', params.tags.join(','));
      }
      if (params.skills?.length) {
        queryString.append('skills', params.skills.join(','));
      }
      if (params.is_published !== undefined) {
        queryString.append('is_published', params.is_published.toString());
      }

      const response = await api.get<MockApiResponse<CourseSearchResult>>(
          `${this.baseUrl}?${queryString.toString()}`, {baseURL: ''});

      if (response.data.success && response.data.data) {
        return response.data.data;
      }

      return {
        courses: [],
        total: 0,
        page: 1,
        limit: 10,
        hasMore: false,
      };
    } catch (error) {
      console.error('Error searching courses:', error);
      return {
        courses: [],
        total: 0,
        page: 1,
        limit: 10,
        hasMore: false,
      };
    }
  }

  /**
   * 获取课程统计信息
   * @param courseData 课程数据
   * @returns 统计信息
   */
  calculateCourseStats(courseData: MockApiCourseData): MockApiCourseStats {
    const totalPages = courseData.courses.reduce(
        (total, course) => total +
            course.categories.reduce(
                (catTotal, category) => catTotal + category.pages.length, 0),
        0);

    const totalCategories = courseData.courses.reduce(
        (total, course) => total + course.categories.length, 0);

    const previewPages = courseData.courses.reduce(
        (total, course) => total +
            course.categories.reduce(
                (catTotal, category) => catTotal +
                    category.pages.filter((page) => page.is_preview).length,
                0),
        0);

    // 将秒转换为分钟
    const estimatedDuration = Math.round(courseData.read_time / 60);

    return {
      totalPages,
      totalCategories,
      previewPages,
      estimatedDuration,
      widgetCounts: courseData.aggregated_widget_stats,
    };
  }

  /**
   * 根据标签过滤课程
   * @param courses 课程列表
   * @param tags 标签列表
   * @returns 过滤后的课程列表
   */
  filterCoursesByTags(courses: MockApiCourseData[], tags: string[]):
      MockApiCourseData[] {
    if (!tags.length) return courses;

    return courses.filter(
        (course) => tags.some(
            (tag) => course.tags.includes(tag) ||
                course.learner_tags.includes(tag) ||
                course.skills.includes(tag)));
  }

  /**
   * 根据技能过滤课程
   * @param courses 课程列表
   * @param skills 技能列表
   * @returns 过滤后的课程列表
   */
  filterCoursesBySkills(courses: MockApiCourseData[], skills: string[]):
      MockApiCourseData[] {
    if (!skills.length) return courses;

    return courses.filter(
        (course) => skills.some((skill) => course.skills.includes(skill)));
  }

  /**
   * 获取课程的学习路径
   * @param courseData 课程数据
   * @returns 学习路径数组
   */
  extractLearningPath(courseData: MockApiCourseData): string[] {
    const path: string[] = [];

    courseData.courses.forEach((course) => {
      course.categories.forEach((category) => {
        category.pages.forEach((page) => {
          path.push(`${category.title} > ${page.title}`);
        });
      });
    });

    return path;
  }

  /**
   * 判断课程难度
   * @param courseData 课程数据
   * @returns 课程难度
   */
  determineDifficulty(courseData: MockApiCourseData): CourseDifficulty {
    // 优先使用 target_audience 字段
    if (courseData.courses.length > 0 &&
        courseData.courses[0].target_audience) {
      const targetAudience =
          courseData.courses[0].target_audience.toLowerCase();
      if (targetAudience === 'beginner') return CourseDifficulty.BEGINNER;
      if (targetAudience === 'intermediate')
        return CourseDifficulty.INTERMEDIATE;
      if (targetAudience === 'advanced') return CourseDifficulty.ADVANCED;
    }

    // 回退到基于标签的判断
    const tags = courseData.tags.map((tag) => tag.toLowerCase());
    const learnerTags = courseData.learner_tags.map((tag) => tag.toLowerCase());

    if (tags.includes('beginner') || learnerTags.includes('beginner')) {
      return CourseDifficulty.BEGINNER;
    }

    if (tags.includes('advanced') || learnerTags.includes('advanced')) {
      return CourseDifficulty.ADVANCED;
    }

    // 如果没有 target_audience 字段且标签中也没有难度信息，默认为 beginner
    return CourseDifficulty.BEGINNER;
  }

  /**
   * 获取课程状态
   * @param courseData 课程数据
   * @returns 课程状态
   */
  getCourseStatus(courseData: MockApiCourseData): CourseStatus {
    if (courseData.is_published) {
      return CourseStatus.PUBLISHED;
    }

    if (courseData.deletion_time) {
      return CourseStatus.ARCHIVED;
    }

    return CourseStatus.DRAFT;
  }
}

// 导出单例实例
export const mockApiCourseService = MockApiCourseService.getInstance();

// 导出课程转换器
export const courseConverter: CourseConverter = {
  toInternalFormat: (mockData: MockApiCourseData) => {
    const service = MockApiCourseService.getInstance();
    const stats = service.calculateCourseStats(mockData);

    return {
      id: mockData.id,
      title: mockData.courses[0]?.title || 'Untitled Course',
      description: mockData.courses[0]?.summary || '',
      difficulty: service.determineDifficulty(mockData),
      duration: `${stats.estimatedDuration} min`,
      tags: mockData.tags,
      skills: mockData.skills,
      price: mockData.price,
      status: service.getCourseStatus(mockData),
      stats,
      learningPath: service.extractLearningPath(mockData),
    };
  },

  calculateStats: (mockData: MockApiCourseData) => {
    const service = MockApiCourseService.getInstance();
    return service.calculateCourseStats(mockData);
  },

  extractLearningPath: (mockData: MockApiCourseData) => {
    const service = MockApiCourseService.getInstance();
    return service.extractLearningPath(mockData);
  },
};
