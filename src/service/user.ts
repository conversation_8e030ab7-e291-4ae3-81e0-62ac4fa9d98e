/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-07-24 20:06:21
 */
import { api } from "./api";
import type {
  BaseResponse,
  User,
  RegisterRequest,
  LoginRequest,
  LoginResponseData,
  RefreshTokenRequest,
  RefreshResponseData,
} from "@/types/openapi";

// 去重：缓存进行中的 /auth/me 请求，避免开发模式 StrictMode 导致的重复调用
let meRequestPromise: Promise<BaseResponse<User>> | null = null;

export const userService = {
  // 注册
  async register(body: RegisterRequest): Promise<BaseResponse<User>> {
    const resp = await api.post<BaseResponse<User>>("/auth/register", body);
    return resp.data;
  },

  // 登录（邮箱/密码 或 Google OAuth / OAuth 服务端）
  async login(body: LoginRequest): Promise<BaseResponse<LoginResponseData>> {
    const resp = await api.post<BaseResponse<LoginResponseData>>(
      "/auth/login",
      body
    );
    return resp.data;
  },

  // 获取当前用户（去重处理）
  async me(): Promise<BaseResponse<User>> {
    if (!meRequestPromise) {
      meRequestPromise = api
        .get<BaseResponse<User>>("/auth/me")
        .then((r) => r.data)
        .finally(() => {
          // 短暂延迟后释放，允许下一次显式触发
          setTimeout(() => {
            meRequestPromise = null;
          }, 0);
        });
    }
    return meRequestPromise;
  },

  // 退出登录
  async logout(): Promise<BaseResponse<unknown>> {
    const resp = await api.post<BaseResponse<unknown>>("/auth/logout");
    return resp.data;
  },

  // 刷新令牌
  async refresh(
    body: RefreshTokenRequest
  ): Promise<BaseResponse<RefreshResponseData>> {
    const resp = await api.post<BaseResponse<RefreshResponseData>>(
      "/auth/refresh",
      body
    );
    return resp.data;
  },

  // 修改密码
  async changePassword(body: {
    current_password: string;
    new_password: string;
  }): Promise<BaseResponse<{ message: string }>> {
    const resp = await api.put<BaseResponse<{ message: string }>>(
      "/auth/password",
      body
    );
    return resp.data;
  },

  // 更新个人资料
  async updateProfile(body: {
    username?: string;
    bio?: string;
    github?: string;
    website?: string;
  }): Promise<BaseResponse<User>> {
    const resp = await api.put<BaseResponse<User>>("/auth/profile", body);
    return resp.data;
  },
};
