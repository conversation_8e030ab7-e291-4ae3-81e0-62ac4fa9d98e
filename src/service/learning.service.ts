/**
 * 学习相关服务（旧聚合）
 * 为兼容保留：导出新的按路由分组的服务
 */

import { learningPathService } from "./learning-paths.service";
import { learningNodeService } from "./learning-nodes.service";
import { learningLessonService } from "./learning-lessons.service";

export { learningPathService, learningNodeService, learningLessonService };

// 兼容旧命名的聚合服务
export const learningService = {
  // 学习路径
  getLearningPaths: (params?: Parameters<typeof learningPathService.list>[0]) =>
    learningPathService.list(params),
  createLearningPath: learningPathService.create,
  generateLearningPath: learningPathService.generate,
  getLearningPath: learningPathService.get,
  updateLearningPath: learningPathService.update,
  deleteLearningPath: learningPathService.remove,

  // 学习节点
  getLearningNodes: (params?: Parameters<typeof learningNodeService.list>[0]) =>
    learningNodeService.list(params),
  createLearningNode: learningNodeService.create,
  generateLearningNode: learningNodeService.generate,
  getLearningNode: learningNodeService.get,
  updateLearningNode: learningNodeService.update,
  updateNodeStatus: learningNodeService.updateStatus,
  deleteLearningNode: learningNodeService.remove,

  // 学习课程（新增）
  getLearningLessons: (
    params?: Parameters<typeof learningLessonService.list>[0]
  ) => learningLessonService.list(params),
  createLearningLesson: learningLessonService.create,
  generateLearningLesson: learningLessonService.generate,
  getLearningLesson: learningLessonService.get,
  updateLearningLesson: learningLessonService.update,
  deleteLearningLesson: learningLessonService.remove,
  listLessonsByType: learningLessonService.listByType,
  searchLessons: learningLessonService.search,
  updateLessonStatus: learningLessonService.updateStatus,
  duplicateLesson: learningLessonService.duplicate,
  archiveLesson: learningLessonService.archive,
  publishLesson: learningLessonService.publish,
};
