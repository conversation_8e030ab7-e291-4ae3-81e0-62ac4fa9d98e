/*
 * Personalize (adjust) service: start, reply, status
 */
import type {ApiResponse, PersonalizeReplyRequest, PersonalizeStartRequest, PersonalizeStartResponse, PersonalizeStatusResponse,} from '@/types/openapi';

import {api} from './api';

export const personalizeService = {
  async start(body: PersonalizeStartRequest) {
    const resp =
        await api.post<any>('/personalize/start', body, {timeout: 60000});
    const raw = resp.data || {};
    if (raw && raw.conversation_id && raw.success !== undefined) {
      const wrapped: ApiResponse<PersonalizeStartResponse> = {
        success: !!raw.success,
        message: raw.message || '',
        data: {conversation_id: raw.conversation_id},
        code: resp.status,
      };
      return wrapped;
    }
    return raw as ApiResponse<PersonalizeStartResponse>;
  },

  async status(conversation_id: string) {
    const resp = await api.get<any>(
        `/personalize/status/${encodeURIComponent(conversation_id)}`,
        {timeout: 30000});
    const raw = resp.data || {};
    if (raw && raw.status) {
      const wrapped: ApiResponse<PersonalizeStatusResponse> = {
        success: true,
        message: '',
        data: {
          status: raw.status,
          last_thought: raw.last_thought,
          ask_question: raw.ask_question,
          error: raw.error,
        },
        code: resp.status,
      };
      return wrapped;
    }
    return raw as ApiResponse<PersonalizeStatusResponse>;
  },

  async reply(body: PersonalizeReplyRequest) {
    const resp = await api.post<any>('/personalize/reply', body);
    const raw = resp.data || {};
    if (typeof raw.success === 'boolean' && !('data' in raw)) {
      const wrapped: ApiResponse<unknown> = {
        success: !!raw.success,
        message: raw.message || '',
        data: raw.data,
        code: resp.status,
      };
      return wrapped;
    }
    return raw as ApiResponse<unknown>;
  },
};

export default personalizeService;
