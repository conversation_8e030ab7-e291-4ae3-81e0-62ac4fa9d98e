/*
 * @Description: AI Goals service (goal planning and learning path generation)
 * @Author: Devin
 * @Date: 2025-08-14
 */
import type {BaseResponse, GoalPlan, SuggestedGoal} from '@/types/openapi';

import {api} from './api';

export interface CreateGoalPlanRequest {
  origin_goal: string;
}

// New interactive goal planning types
export interface StartChatRequest {
  origin_goal?: string;
  conversation_id?: string;
  message?: string;
}

export interface ChatResponse {
  conversation_id: string;
  message: string;
  suggested_goals?: SuggestedGoal[];
  status: string;  // e.g., 'clarifying' | 'ready' | ...
}

export interface ChooseGoalRequest {
  conversation_id: string;
  index: number;
}

export interface ChooseGoalResponse {
  path_id: string;
}

export const goalsService = {
  /**
   * Some backends may return raw GoalPlan without envelope, others
   * BaseResponse<GoalPlan>. Return the raw JSON so caller can handle both.
   */
  async plan(body: CreateGoalPlanRequest):
      Promise<BaseResponse<GoalPlan>|GoalPlan> {
        const resp = await api.post<BaseResponse<GoalPlan>|GoalPlan>(
            '/goals/plan', body, {timeout: 60_000}  // 延长等待时间：60s
        );
        return resp.data as any;
      },
  /**
   * Interactive chat for AI goal planning
   */
  async chat(body: StartChatRequest):
      Promise<ChatResponse|BaseResponse<ChatResponse>> {
        const resp = await api.post<ChatResponse|BaseResponse<ChatResponse>>(
            '/goals/chat', body, {timeout: 60_000});
        return resp.data as any;
      },

  /**
   * Choose one of suggested goals to trigger path generation
   */
  async chooseGoal(conversationId: string, index: number):
      Promise<ChooseGoalResponse|BaseResponse<ChooseGoalResponse>> {
        const body:
            ChooseGoalRequest = {conversation_id: conversationId, index};
        const resp =
            await api.post<ChooseGoalResponse|BaseResponse<ChooseGoalResponse>>(
                `/goals/tasks/${conversationId}/choose`, body,
                {timeout: 30_000});
        return resp.data as any;
      },
};
