/**
 * 健康检查服务（依据 docs/swagger.json）
 */

import { api } from "./api";

export interface HealthResponse {
  status: string; // healthy
  timestamp?: string;
  service?: string;
  version?: string;
}

export interface ReadyResponse {
  status: string; // ready
  checks: { database: string };
}

export interface LiveResponse {
  status: string; // alive
}

export const healthService = {
  async health(): Promise<HealthResponse> {
    const resp = await api.get<HealthResponse>("/health");
    return resp.data;
  },
  async ready(): Promise<ReadyResponse> {
    const resp = await api.get<ReadyResponse>("/ready");
    return resp.data;
  },
  async live(): Promise<LiveResponse> {
    const resp = await api.get<LiveResponse>("/live");
    return resp.data;
  },
};
