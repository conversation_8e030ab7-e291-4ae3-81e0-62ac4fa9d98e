/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-08-11 14:37:31
 */
/**
 * 学习路径服务（依据 docs/swagger.json）
 */

import type {
  BaseResponse,
  CreateLearningPathRequest,
  GenerateLearningPathRequest,
  LearningPath,
  Paginated,
  UpdateLearningPathRequest,
} from "@/types/openapi";
import { time } from "console";
import { TIMEOUT } from "dns";

import { api } from "./api";

export const learningPathService = {
  /**
   * 获取公开的学习路径（原始接口）
   */
  async list(params?: {
    page?: number;
    page_size?: number;
    path_type?: "personalized" | "standard" | "custom" | "adaptive";
    difficulty?: number; // 1-10
    tags?: string[];
    skills?: string[];
    learner_tags?: string[];
    query?: string;
    search_mode?: "exact" | "fuzzy";
    search_fields?: string; // Search across tags, skills, learner_tags, title, and description
  }): Promise<BaseResponse<Paginated<LearningPath>>> {
    const search = new URLSearchParams();
    if (params?.page) search.set("page", String(params.page));
    if (params?.page_size) search.set("page_size", String(params.page_size));
    if (params?.path_type) search.set("path_type", params.path_type);
    if (typeof params?.difficulty === "number")
      search.set("difficulty", String(params.difficulty));
    if (params?.tags?.length)
      params.tags.forEach((v) => search.append("tags", v));
    if (params?.skills?.length)
      params.skills.forEach((v) => search.append("skills", v));
    if (params?.learner_tags?.length)
      params.learner_tags.forEach((v) => search.append("learner_tags", v));
    if (params?.query) search.set("query", params.query);
    if (params?.search_mode) search.set("search_mode", params.search_mode);
    if (params?.search_fields)
      search.set("search_fields", params.search_fields);

    const resp = await api.get<BaseResponse<Paginated<LearningPath>>>(
      `/learning-paths?${search.toString()}`
    );
    return resp.data;
  },

  /**
   * 获取当前用户的学习路径（新接口）
   * 路径：/user/learning-paths
   * 查询参数同 list
   */
  async listMine(params?: {
    page?: number;
    page_size?: number;
    path_type?: "personalized" | "standard" | "custom" | "adaptive";
    difficulty?: number; // 1-10
    tags?: string[];
    skills?: string[];
    learner_tags?: string[];
    query?: string;
    search_mode?: "exact" | "fuzzy";
    search_fields?: string; // Search across tags, skills, learner_tags, title, and description
  }): Promise<BaseResponse<Paginated<LearningPath>>> {
    const search = new URLSearchParams();
    if (params?.page) search.set("page", String(params.page));
    if (params?.page_size) search.set("page_size", String(params.page_size));
    if (params?.path_type) search.set("path_type", params.path_type);
    if (typeof params?.difficulty === "number")
      search.set("difficulty", String(params.difficulty));
    if (params?.tags?.length)
      params.tags.forEach((v) => search.append("tags", v));
    if (params?.skills?.length)
      params.skills.forEach((v) => search.append("skills", v));
    if (params?.learner_tags?.length)
      params.learner_tags.forEach((v) => search.append("learner_tags", v));
    if (params?.query) search.set("query", params.query);
    if (params?.search_mode) search.set("search_mode", params.search_mode);
    if (params?.search_fields)
      search.set("search_fields", params.search_fields);

    const resp = await api.get<BaseResponse<Paginated<LearningPath>>>(
      `/user/learning-paths?${search.toString()}`
    );
    return resp.data;
  },

  async create(
    body: CreateLearningPathRequest
  ): Promise<BaseResponse<LearningPath>> {
    const resp = await api.post<BaseResponse<LearningPath>>(
      "/learning-paths",
      body
    );
    return resp.data;
  },

  async generate(
    body: GenerateLearningPathRequest
  ): Promise<BaseResponse<LearningPath>> {
    const resp = await api.post<BaseResponse<LearningPath>>(
      "/learning-paths/generate",
      body,
      {
        timeout: 60000,
      }
    );
    return resp.data;
  },

  async get(id: string): Promise<BaseResponse<LearningPath>> {
    const resp = await api.get<BaseResponse<LearningPath>>(
      `/learning-paths/${id}`
    );
    return resp.data;
  },

  async update(
    id: string,
    body: UpdateLearningPathRequest
  ): Promise<BaseResponse<LearningPath>> {
    const resp = await api.put<BaseResponse<LearningPath>>(
      `/learning-paths/${id}`,
      body
    );
    return resp.data;
  },

  async remove(id: string): Promise<BaseResponse<unknown>> {
    const resp = await api.delete<BaseResponse<unknown>>(
      `/learning-paths/${id}`
    );
    return resp.data;
  },
};
