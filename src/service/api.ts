/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-07-24 20:06:21
 */
import axios from "axios";
import { useUserStore } from "@/store";

import { showToast } from "@/lib/toast-utils";
import { extractErrorMessage } from "@/lib/api-error-handler";

const API_BASE_URL = "/api/v1";

// 创建自定义事件
export const AUTH_ERROR_EVENT = "auth:error";

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true, // 重要：允许发送 httpOnly cookies
});

// 请求拦截器 - 不再需要手动添加 token，使用 httpOnly cookie
api.interceptors.request.use((config) => {
  // httpOnly cookie 会自动包含在请求中
  return config;
});

// 响应拦截器：401 统一处理，其它交由调用方根据 swagger 的结构处理
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const url: string = error.config?.url || "";
      const method: string = (error.config?.method || "").toLowerCase();
      const isSilent401 = method === "get" && url.startsWith("/auth/me");

      console.warn("Unauthorized or session expired, clearing login info...", {
        url,
        method,
      });

      // 清空本地登录信息
      useUserStore.getState().clearLoginInfo();

      if (!isSilent401) {
        const msg =
          (error.response?.data && (error.response.data.message as string)) ||
          extractErrorMessage(error, "登录未授权");
        showToast.error(msg);
        if (typeof window !== "undefined") {
          window.dispatchEvent(new Event(AUTH_ERROR_EVENT));
        }
      }
    }
    return Promise.reject(error);
  }
);

export { api };
