/**
 * 学习课程服务（Learning Lessons，依据 docs/swagger.json）
 */

import { api } from "./api";
import { courseCache, withCache } from "@/lib/api-cache";
import type {
  BaseResponse,
  LearningLesson,
  CreateLearningLessonRequest,
  UpdateLearningLessonRequest,
  GenerateLessonRequest,
  UpdateLessonStatusRequest,
  DuplicateLessonRequest,
} from "@/types/openapi";

export const learningLessonService = {
  async list(params?: { page?: number; page_size?: number }): Promise<
    BaseResponse<{
      data: LearningLesson[];
      total: number;
      page: number;
      page_size: number;
    }>
  > {
    const search = new URLSearchParams();
    if (params?.page) search.set("page", String(params.page));
    if (params?.page_size) search.set("page_size", String(params.page_size));
    const resp = await api.get<
      BaseResponse<{
        data: LearningLesson[];
        total: number;
        page: number;
        page_size: number;
      }>
    >(`/learning-lessons?${search.toString()}`);
    return resp.data;
  },

  async create(
    body: CreateLearningLessonRequest
  ): Promise<BaseResponse<LearningLesson>> {
    const resp = await api.post<BaseResponse<LearningLesson>>(
      "/learning-lessons",
      body
    );
    return resp.data;
  },

  // 使用缓存的单个课程获取
  get: withCache(
    async (id: string): Promise<BaseResponse<LearningLesson>> => {
      const resp = await api.get<BaseResponse<LearningLesson>>(
        `/learning-lessons/${id}`
      );
      return resp.data;
    },
    courseCache,
    {
      keyGenerator: (id: string) => `lesson:${id}`,
      ttl: 10 * 60 * 1000, // 10 分钟缓存
    }
  ),

  async update(
    id: string,
    body: UpdateLearningLessonRequest
  ): Promise<BaseResponse<LearningLesson>> {
    const resp = await api.put<BaseResponse<LearningLesson>>(
      `/learning-lessons/${id}`,
      body
    );
    return resp.data;
  },

  async remove(id: string): Promise<BaseResponse<unknown>> {
    const resp = await api.delete<BaseResponse<unknown>>(
      `/learning-lessons/${id}`
    );
    return resp.data;
  },

  async generate(
    body: GenerateLessonRequest
  ): Promise<BaseResponse<LearningLesson>> {
    const resp = await api.post<BaseResponse<LearningLesson>>(
      "/learning-lessons/generate",
      body
    );
    return resp.data;
  },

  async listByType(params: {
    type: string;
    page?: number;
    page_size?: number;
  }): Promise<
    BaseResponse<{
      data: LearningLesson[];
      total: number;
      page: number;
      page_size: number;
    }>
  > {
    const search = new URLSearchParams();
    search.set("type", params.type);
    if (params.page) search.set("page", String(params.page));
    if (params.page_size) search.set("page_size", String(params.page_size));
    const resp = await api.get<
      BaseResponse<{
        data: LearningLesson[];
        total: number;
        page: number;
        page_size: number;
      }>
    >(`/learning-lessons/type?${search.toString()}`);
    return resp.data;
  },

  async search(params: {
    q: string;
    page?: number;
    page_size?: number;
  }): Promise<
    BaseResponse<{
      data: LearningLesson[];
      total: number;
      page: number;
      page_size: number;
    }>
  > {
    const search = new URLSearchParams();
    search.set("q", params.q);
    if (params.page) search.set("page", String(params.page));
    if (params.page_size) search.set("page_size", String(params.page_size));
    const resp = await api.get<
      BaseResponse<{
        data: LearningLesson[];
        total: number;
        page: number;
        page_size: number;
      }>
    >(`/learning-lessons/search?${search.toString()}`);
    return resp.data;
  },

  async updateStatus(
    id: string,
    body: UpdateLessonStatusRequest
  ): Promise<BaseResponse<unknown>> {
    const resp = await api.put<BaseResponse<unknown>>(
      `/learning-lessons/${id}/status`,
      body
    );
    return resp.data;
  },

  async duplicate(
    id: string,
    body: DuplicateLessonRequest
  ): Promise<BaseResponse<LearningLesson>> {
    const resp = await api.post<BaseResponse<LearningLesson>>(
      `/learning-lessons/${id}/duplicate`,
      body
    );
    return resp.data;
  },

  async archive(id: string): Promise<BaseResponse<unknown>> {
    const resp = await api.post<BaseResponse<unknown>>(
      `/learning-lessons/${id}/archive`
    );
    return resp.data;
  },

  async publish(id: string): Promise<BaseResponse<unknown>> {
    const resp = await api.post<BaseResponse<unknown>>(
      `/learning-lessons/${id}/publish`
    );
    return resp.data;
  },
};
