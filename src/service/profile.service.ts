/**
 * 用户画像相关服务
 * 包括静态画像和技术能力画像的API调用
 */

import { api } from "./api";
import type {
  // 静态画像相关
  StaticProfile,
  StaticProfileResponse,
  StaticProfileListResponse,
  CreateStaticProfileRequest,
  UpdateStaticProfileRequest,
  StaticProfileListParams,

  // 技术能力画像相关
  TechCompetencyProfile,
  TechCompetencyProfileResponse,
  CompetencyNodeListResponse,
  CompetencyStatsResponse,
  UpdateTechCompetencyRequest,

  // 画像分析相关
  ProfileAnalysis,
  ProfileAnalysisResponse,
  ProfileMatch,
  ProfileMatchResponse,
  ProfileComparison,
  ProfileComparisonResponse,
} from "@/types/profile";
import type { ApiResponse } from "@/types/openapi";

export const profileService = {
  // ===== 静态画像相关 =====

  // 获取当前用户的静态画像
  async getStaticProfile(): Promise<StaticProfileResponse> {
    const resp = await api.get<StaticProfileResponse>("/profiles/static");
    return resp.data;
  },

  // 创建静态画像
  async createStaticProfile(
    data: CreateStaticProfileRequest
  ): Promise<StaticProfileResponse> {
    const resp = await api.post<StaticProfileResponse>(
      "/profiles/static",
      data
    );
    return resp.data;
  },

  // 更新静态画像
  async updateStaticProfile(
    data: UpdateStaticProfileRequest
  ): Promise<StaticProfileResponse> {
    const resp = await api.put<StaticProfileResponse>("/profiles/static", data);
    return resp.data;
  },

  // 获取静态画像列表
  async getStaticProfileList(
    params?: StaticProfileListParams
  ): Promise<StaticProfileListResponse> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.page_size)
      searchParams.append("page_size", params.page_size.toString());
    if (params?.industry) searchParams.append("industry", params.industry);
    if (params?.learning_style)
      searchParams.append("learning_style", params.learning_style);
    if (params?.min_age)
      searchParams.append("min_age", params.min_age.toString());
    if (params?.max_age)
      searchParams.append("max_age", params.max_age.toString());
    if (params?.experience_level)
      searchParams.append("experience_level", params.experience_level);
    if (params?.search) searchParams.append("search", params.search);

    const resp = await api.get<StaticProfileListResponse>(
      `/profiles/static/list?${searchParams.toString()}`
    );
    return resp.data;
  },

  // ===== 技术能力画像相关 =====

  // 获取技术能力画像
  async getTechCompetencyProfile(): Promise<TechCompetencyProfileResponse> {
    const resp = await api.get<TechCompetencyProfileResponse>(
      "/profiles/tech-competency"
    );
    return resp.data;
  },

  // 基于行为更新技术能力画像
  async updateTechCompetencyProfile(
    data: UpdateTechCompetencyRequest
  ): Promise<ApiResponse<{ message: string }>> {
    const resp = await api.post<ApiResponse<{ message: string }>>(
      "/profiles/tech-competency",
      data
    );
    return resp.data;
  },

  // 按等级获取能力节点
  async getCompetencyNodesByLevel(
    level: "beginner" | "intermediate" | "advanced"
  ): Promise<CompetencyNodeListResponse> {
    const resp = await api.get<CompetencyNodeListResponse>(
      `/profiles/tech-competency/nodes/${level}`
    );
    return resp.data;
  },

  // 获取能力统计
  async getCompetencyStats(): Promise<CompetencyStatsResponse> {
    const resp = await api.get<CompetencyStatsResponse>(
      "/profiles/tech-competency/stats"
    );
    return resp.data;
  },

  // 刷新陈旧画像
  async refreshStaleProfile(): Promise<ApiResponse<{ message: string }>> {
    const resp = await api.post<ApiResponse<{ message: string }>>(
      "/profiles/tech-competency/refresh-stale"
    );
    return resp.data;
  },

  // ===== 画像分析和推荐相关 =====

  // 生成用户画像分析
  async generateProfileAnalysis(
    analysisType?:
      | "learning_style"
      | "skill_gap"
      | "career_path"
      | "learning_efficiency"
  ): Promise<ProfileAnalysisResponse> {
    const params = analysisType ? `?type=${analysisType}` : "";
    const resp = await api.post<ProfileAnalysisResponse>(
      `/profiles/analysis${params}`
    );
    return resp.data;
  },

  // 获取画像分析历史
  async getProfileAnalysisHistory(params?: {
    page?: number;
    page_size?: number;
    analysis_type?: string;
  }): Promise<ApiResponse<ProfileAnalysis[]>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.page_size)
      searchParams.append("page_size", params.page_size.toString());
    if (params?.analysis_type)
      searchParams.append("analysis_type", params.analysis_type);

    const resp = await api.get<ApiResponse<ProfileAnalysis[]>>(
      `/profiles/analysis/history?${searchParams.toString()}`
    );
    return resp.data;
  },

  // 匹配相似用户画像
  async matchSimilarProfiles(
    targetProfileId?: string
  ): Promise<ProfileMatchResponse> {
    const params = targetProfileId
      ? `?target_profile_id=${targetProfileId}`
      : "";
    const resp = await api.post<ProfileMatchResponse>(
      `/profiles/match${params}`
    );
    return resp.data;
  },

  // 对比用户画像
  async compareProfiles(
    benchmarkProfileId: string
  ): Promise<ProfileComparisonResponse> {
    const resp = await api.post<ProfileComparisonResponse>(
      "/profiles/compare",
      {
        benchmark_profile_id: benchmarkProfileId,
      }
    );
    return resp.data;
  },

  // ===== 画像导出和导入相关 =====

  // 导出用户画像数据
  async exportProfileData(
    format: "json" | "csv" = "json"
  ): Promise<ApiResponse<{ download_url: string }>> {
    const resp = await api.post<ApiResponse<{ download_url: string }>>(
      "/profiles/export",
      {
        format,
      }
    );
    return resp.data;
  },

  // 导入用户画像数据
  async importProfileData(
    file: File
  ): Promise<ApiResponse<{ message: string; imported_count: number }>> {
    const formData = new FormData();
    formData.append("file", file);

    const resp = await api.post<
      ApiResponse<{ message: string; imported_count: number }>
    >("/profiles/import", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return resp.data;
  },

  // ===== 画像推荐相关 =====

  // 获取个性化学习路径推荐
  async getPersonalizedPathRecommendations(params?: {
    max_recommendations?: number;
    difficulty_preference?: "beginner" | "intermediate" | "advanced";
    time_constraint?: number; // 小时
    focus_areas?: string[];
  }): Promise<
    ApiResponse<
      Array<{
        path_id: string;
        path_title: string;
        match_score: number;
        reasons: string[];
        estimated_duration: number;
      }>
    >
  > {
    const searchParams = new URLSearchParams();
    if (params?.max_recommendations)
      searchParams.append(
        "max_recommendations",
        params.max_recommendations.toString()
      );
    if (params?.difficulty_preference)
      searchParams.append(
        "difficulty_preference",
        params.difficulty_preference
      );
    if (params?.time_constraint)
      searchParams.append("time_constraint", params.time_constraint.toString());
    if (params?.focus_areas) {
      params.focus_areas.forEach((area) =>
        searchParams.append("focus_areas", area)
      );
    }

    const resp = await api.get<
      ApiResponse<
        Array<{
          path_id: string;
          path_title: string;
          match_score: number;
          reasons: string[];
          estimated_duration: number;
        }>
      >
    >(`/profiles/recommendations/paths?${searchParams.toString()}`);
    return resp.data;
  },

  // 获取个性化课程推荐
  async getPersonalizedLessonRecommendations(params?: {
    max_recommendations?: number;
    lesson_type?: "video" | "text" | "interactive" | "quiz" | "coding";
    difficulty_preference?: "beginner" | "intermediate" | "advanced";
    focus_skills?: string[];
  }): Promise<
    ApiResponse<
      Array<{
        lesson_id: string;
        lesson_title: string;
        match_score: number;
        reasons: string[];
        estimated_duration: number;
      }>
    >
  > {
    const searchParams = new URLSearchParams();
    if (params?.max_recommendations)
      searchParams.append(
        "max_recommendations",
        params.max_recommendations.toString()
      );
    if (params?.lesson_type)
      searchParams.append("lesson_type", params.lesson_type);
    if (params?.difficulty_preference)
      searchParams.append(
        "difficulty_preference",
        params.difficulty_preference
      );
    if (params?.focus_skills) {
      params.focus_skills.forEach((skill) =>
        searchParams.append("focus_skills", skill)
      );
    }

    const resp = await api.get<
      ApiResponse<
        Array<{
          lesson_id: string;
          lesson_title: string;
          match_score: number;
          reasons: string[];
          estimated_duration: number;
        }>
      >
    >(`/profiles/recommendations/lessons?${searchParams.toString()}`);
    return resp.data;
  },

  // ===== 画像同步和备份相关 =====

  // 同步画像数据
  async syncProfileData(): Promise<
    ApiResponse<{ message: string; sync_timestamp: string }>
  > {
    const resp = await api.post<
      ApiResponse<{ message: string; sync_timestamp: string }>
    >("/profiles/sync");
    return resp.data;
  },

  // 备份画像数据
  async backupProfileData(): Promise<
    ApiResponse<{ backup_id: string; backup_url: string }>
  > {
    const resp = await api.post<
      ApiResponse<{ backup_id: string; backup_url: string }>
    >("/profiles/backup");
    return resp.data;
  },

  // 恢复画像数据
  async restoreProfileData(
    backupId: string
  ): Promise<ApiResponse<{ message: string; restored_at: string }>> {
    const resp = await api.post<
      ApiResponse<{ message: string; restored_at: string }>
    >("/profiles/restore", {
      backup_id: backupId,
    });
    return resp.data;
  },
};
