import { Course, CatalogData } from "@/types/catalog";

// Function to get language configurations with translations
export const getLanguageConfigs = (t: any) => ({
  python: {
    name: t("catalog.languages.python.name"),
    icon: "/images/common/python.png",
    description: t("catalog.languages.python.description"),
    color: "from-blue-500 to-purple-600",
  },
  javascript: {
    name: t("catalog.languages.javascript.name"),
    icon: "/images/common/javascript.png",
    description: t("catalog.languages.javascript.description"),
    color: "from-yellow-500 to-orange-600",
  },
  java: {
    name: t("catalog.languages.java.name"),
    icon: "/images/common/java.png",
    description: t("catalog.languages.java.description"),
    color: "from-red-500 to-orange-600",
  },
});

// Legacy export for backward compatibility
export const languageConfigs = getLanguageConfigs((key: string) => key);

// Mock data based on the Pageflux AI Python catalog
export const pythonCoursesData: Course[] = [
  {
    id: "grokking-coding-interview-python",
    title: "Grokking the Coding Interview Patterns in Python",
    description:
      "The ultimate guide to coding interviews, developed by FAANG engineers. Learn patterns to tackle problems from top companies and get interview-ready in just a few hours.",
    difficulty: "Intermediate",
    duration: "85 hrs",
    url: "https://www.pointer-ai.com/courses/grokking-coding-interview-in-python",
    tags: ["Coding Interview", "FAANG", "Patterns", "Python"],
    challenges: 397,
    quizzes: 398,
    category: "Interview Prep",
    instructor: "FAANG Engineers",
    students: "250k+",
    rating: "4.9",
    price: "Premium",
    language: "python",
  },
  {
    id: "grokking-low-level-design-ood",
    title: "Grokking the Low Level Design Interview Using OOD Principles",
    description:
      "A battle-tested guide to Object Oriented Design Interviews – developed by FAANG engineers. Master OOD fundamentals & practice real-world interview questions.",
    difficulty: "Intermediate",
    duration: "50 hrs",
    url: "https://www.pointer-ai.com/courses/grokking-the-low-level-design-interview-using-ood-principles",
    tags: ["Object Oriented Design", "System Design", "Interview"],
    playgrounds: 8,
    quizzes: 4,
    category: "System Design",
    instructor: "FAANG Engineers",
    students: "180k+",
    rating: "4.8",
    price: "Premium",
    language: "python",
  },
  {
    id: "master-github-copilot",
    title: "Master GitHub Copilot",
    description:
      "Learn GitHub Copilot foundations through hands-on lessons: explore AI-assisted coding, Copilot chat, prompt engineering, code reviews, testing, and debugging to incorporate AI into your workflows.",
    difficulty: "Beginner",
    duration: "4 hrs",
    url: "https://www.pointer-ai.com/courses/github-copilot",
    tags: ["GitHub Copilot", "AI", "Coding Assistant", "Productivity"],
    playgrounds: 11,
    quizzes: 7,
    category: "AI Tools",
    instructor: "AI Experts",
    students: "45k+",
    rating: "4.7",
    price: "Free",
    language: "python",
  },
  {
    id: "llama-stack-fundamentals",
    title: "Llama Stack: From Fundamentals to Deployment",
    description:
      "This course guides you through Llama Stack and its key components, including agentic workflows, RAG systems, safety mechanisms, monitoring, and deployment.",
    difficulty: "Intermediate",
    duration: "5 hrs",
    url: "https://www.pointer-ai.com/courses/llamastack",
    tags: ["Llama Stack", "LLM", "RAG", "AI Deployment"],
    playgrounds: 39,
    illustrations: 31,
    category: "Machine Learning",
    instructor: "ML Engineers",
    students: "25k+",
    rating: "4.6",
    price: "Premium",
    language: "python",
  },
  {
    id: "learn-to-code-python-beginners",
    title: "Learn to Code: Python for Absolute Beginners",
    description:
      "Want to learn how to code? Get hands-on with Python for beginners. Master the basics, solve real-world problems, and confidently build in-demand Python skills for diverse industries.",
    difficulty: "Beginner",
    duration: "8 hrs",
    url: "https://www.pointer-ai.com/courses/learn-to-code-python-for-absolute-beginners",
    tags: ["Python Basics", "Programming Fundamentals", "Beginner"],
    challenges: 4,
    quizzes: 6,
    category: "Programming Fundamentals",
    instructor: "Python Experts",
    students: "120k+",
    rating: "4.8",
    price: "Free",
    language: "python",
  },
  {
    id: "learn-python",
    title: "Learn Python",
    description:
      "This hands-on Python course helps absolute beginners write their first lines of code, build interactive programs, and understand core programming concepts through playful examples and real-life logic.",
    difficulty: "Beginner",
    duration: "10 hrs",
    url: "https://www.pointer-ai.com/courses/learn-python",
    tags: ["Python", "Programming", "Interactive Learning"],
    playgrounds: 103,
    quizzes: 17,
    category: "Programming Fundamentals",
    instructor: "Python Experts",
    students: "200k+",
    rating: "4.7",
    price: "Free",
    language: "python",
  },
  {
    id: "learn-intermediate-python-3",
    title: "Learn Intermediate Python 3",
    description:
      "Gain insights into Python 3 by exploring fundamental and advanced concepts. Learn about functions, data structures, and OOP while reinforcing knowledge with quizzes and coding challenges.",
    difficulty: "Intermediate",
    duration: "10 hrs",
    url: "https://www.pointer-ai.com/courses/learn-intermediate-python-3",
    tags: ["Python 3", "OOP", "Data Structures", "Functions"],
    playgrounds: 254,
    quizzes: 26,
    category: "Programming Fundamentals",
    instructor: "Python Experts",
    students: "150k+",
    rating: "4.6",
    price: "Premium",
    language: "python",
  },
  {
    id: "building-blocks-coding-python",
    title: "Building Blocks of Coding: Learning Python",
    description:
      "This course demystifies Python programming for beginners, offering a path to learn Python from scratch, build foundational skills in Python programming, and begin your journey as a Python developer.",
    difficulty: "Beginner",
    duration: "10 hrs",
    url: "https://www.pointer-ai.com/courses/building-blocks-of-coding-learning-python",
    tags: ["Python Fundamentals", "Coding Basics", "Developer Journey"],
    playgrounds: 80,
    quizzes: 2,
    category: "Programming Fundamentals",
    instructor: "Python Experts",
    students: "90k+",
    rating: "4.5",
    price: "Free",
    language: "python",
  },
];

// JavaScript courses data
export const javascriptCoursesData: Course[] = [
  {
    id: "javascript-fundamentals",
    title: "JavaScript Fundamentals for Beginners",
    description:
      "Master the basics of JavaScript programming with hands-on exercises and real-world examples.",
    difficulty: "Beginner",
    duration: "12 hrs",
    url: "https://www.pointer-ai.com/courses/javascript-fundamentals",
    tags: ["JavaScript", "Programming", "Web Development"],
    playgrounds: 85,
    quizzes: 15,
    category: "Programming Fundamentals",
    instructor: "JavaScript Experts",
    students: "180k+",
    rating: "4.7",
    price: "Free",
    language: "javascript",
  },
  {
    id: "modern-javascript-es6",
    title: "Modern JavaScript: ES6 and Beyond",
    description:
      "Learn modern JavaScript features including ES6, ES7, and ES8 with practical examples and projects.",
    difficulty: "Intermediate",
    duration: "15 hrs",
    url: "https://www.pointer-ai.com/courses/modern-javascript",
    tags: ["ES6", "Modern JavaScript", "Advanced Features"],
    playgrounds: 120,
    quizzes: 20,
    category: "Advanced JavaScript",
    instructor: "JavaScript Experts",
    students: "95k+",
    rating: "4.8",
    price: "Premium",
    language: "javascript",
  },
];

// Java courses data
export const javaCoursesData: Course[] = [
  {
    id: "java-programming-basics",
    title: "Java Programming for Beginners",
    description:
      "Start your Java journey with comprehensive coverage of object-oriented programming concepts.",
    difficulty: "Beginner",
    duration: "20 hrs",
    url: "https://www.pointer-ai.com/courses/java-programming",
    tags: ["Java", "OOP", "Programming Fundamentals"],
    playgrounds: 150,
    quizzes: 25,
    category: "Programming Fundamentals",
    instructor: "Java Experts",
    students: "220k+",
    rating: "4.6",
    price: "Free",
    language: "java",
  },
  {
    id: "advanced-java-concepts",
    title: "Advanced Java: Concurrency and Performance",
    description:
      "Master advanced Java concepts including multithreading, concurrency, and performance optimization.",
    difficulty: "Advanced",
    duration: "25 hrs",
    url: "https://www.pointer-ai.com/courses/advanced-java",
    tags: ["Java", "Concurrency", "Performance", "Advanced"],
    playgrounds: 80,
    quizzes: 18,
    category: "Advanced Programming",
    instructor: "Java Experts",
    students: "75k+",
    rating: "4.9",
    price: "Premium",
    language: "java",
  },
];

export const catalogStats = {
  python: {
    courses: 248,
    paths: 49,
    cloudLabs: 19,
    assessments: 6,
    projects: 159,
  },
  javascript: {
    courses: 180,
    paths: 35,
    cloudLabs: 15,
    assessments: 4,
    projects: 120,
  },
  java: {
    courses: 150,
    paths: 28,
    cloudLabs: 12,
    assessments: 3,
    projects: 95,
  },
};

export const catalogCategories = {
  python: [
    "All Categories",
    "Programming Fundamentals",
    "Interview Prep",
    "System Design",
    "Machine Learning",
    "AI Tools",
    "Web Development",
    "Data Science",
  ],
  javascript: [
    "All Categories",
    "Programming Fundamentals",
    "Web Development",
    "Frontend Frameworks",
    "Backend Development",
    "Node.js",
    "React",
    "Vue.js",
  ],
  java: [
    "All Categories",
    "Programming Fundamentals",
    "Object-Oriented Programming",
    "Spring Framework",
    "Enterprise Development",
    "Android Development",
    "Microservices",
    "Advanced Programming",
  ],
};

// Backward compatibility
export const pythonCatalogStats = catalogStats.python;
export const pythonCategories = catalogCategories.python;

export class CatalogService {
  static getCatalogData(language: string): CatalogData {
    const languageInfo =
      languageConfigs[language as keyof typeof languageConfigs];

    const languageKey = language as keyof typeof catalogStats;
    const stats = catalogStats[languageKey];
    const categories = catalogCategories[languageKey];

    let courses: Course[] = [];

    switch (language) {
      case "python":
        courses = pythonCoursesData;
        break;
      case "javascript":
        courses = javascriptCoursesData;
        break;
      case "java":
        courses = javaCoursesData;
        break;
      default:
        courses = [];
    }

    if (stats && categories && courses.length > 0) {
      return {
        courses,
        categories,
        totalCourses: courses.length,
        language,
        languageInfo,
        stats,
      };
    }

    // Default empty data for unsupported languages
    return {
      courses: [],
      categories: ["All Categories"],
      totalCourses: 0,
      language,
      languageInfo: languageInfo || {
        name: language,
        icon: "💻",
        description: "",
        color: "from-gray-500 to-gray-600",
      },
      stats: {
        courses: 0,
        paths: 0,
        cloudLabs: 0,
        assessments: 0,
        projects: 0,
      },
    };
  }

  static filterCourses(
    courses: Course[],
    filters: {
      searchTerm?: string;
      category?: string;
      difficulty?: string;
      price?: string;
    }
  ): Course[] {
    return courses.filter((course) => {
      const matchesSearch =
        !filters.searchTerm ||
        course.title.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        course.description
          .toLowerCase()
          .includes(filters.searchTerm.toLowerCase()) ||
        course.tags.some((tag) =>
          tag.toLowerCase().includes(filters.searchTerm?.toLowerCase() || "")
        );

      const matchesCategory =
        !filters.category ||
        filters.category === "All Categories" ||
        course.category === filters.category;

      const matchesDifficulty =
        !filters.difficulty ||
        filters.difficulty === "All Levels" ||
        course.difficulty === filters.difficulty;

      const matchesPrice =
        !filters.price ||
        filters.price === "All Prices" ||
        course.price === filters.price;

      return (
        matchesSearch && matchesCategory && matchesDifficulty && matchesPrice
      );
    });
  }

  static getCoursesByCategory(language: string, category: string): Course[] {
    const catalogData = this.getCatalogData(language);
    if (category === "All Categories") {
      return catalogData.courses;
    }
    return catalogData.courses.filter((course) => course.category === category);
  }
}

// Backward compatibility
export const PythonCourseService = {
  getCatalogData: () => CatalogService.getCatalogData("python"),
  filterCourses: CatalogService.filterCourses,
  getCoursesByCategory: (category: string) =>
    CatalogService.getCoursesByCategory("python", category),
};
