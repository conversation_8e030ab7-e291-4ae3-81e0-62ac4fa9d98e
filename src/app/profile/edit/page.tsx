"use client";

import React, { useState, useEffect } from "react";
import { Camera, Edit3 } from "lucide-react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { TextField } from "@/components/ui/text-field";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useAuth } from "@/lib/auth-context";
import { getUserDisplayName, getUserInitial } from "@/lib/user-utils";
import ChangePasswordDialog from "@/components/auth/change-password-dialog";
import { userService } from "@/service/user";
import { showToast } from "@/lib/toast-utils";
import { extractErrorMessage } from "@/lib/api-error-handler";

const UserSettingsPage: React.FC = () => {
  const { userInfo } = useAuth();
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    bio: "",
    github: "",
    website: "",
  });
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [originalFormData, setOriginalFormData] = useState({
    username: "",
    email: "",
    bio: "",
    github: "",
    website: "",
  });

  // Initialize form data with user info
  useEffect(() => {
    if (userInfo) {
      const initialData = {
        username: userInfo.username || "",
        email: userInfo.email || "",
        bio: userInfo.bio || "",
        github: userInfo.github || "",
        website: userInfo.website || "",
      };
      setFormData(initialData);
      setOriginalFormData(initialData);
    }
  }, [userInfo]);

  // Check if form has been modified
  const hasFormChanged = () => {
    return JSON.stringify(formData) !== JSON.stringify(originalFormData);
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePasswordChange = () => {
    setIsPasswordDialogOpen(true);
  };

  const handleSaveProfile = async () => {
    if (!hasFormChanged()) {
      return;
    }

    setIsSaving(true);

    try {
      const updateData = {
        username: formData.username,
        bio: formData.bio,
        github: formData.github,
        website: formData.website,
      };

      const response = await userService.updateProfile(updateData);

      if (response.success) {
        showToast.success("Profile updated successfully");
        // Update original form data to reflect the saved state
        setOriginalFormData({ ...formData });
      } else {
        const errorMessage = extractErrorMessage(
          null,
          response.message || "Failed to update profile"
        );
        showToast.error(errorMessage);
      }
    } catch (error) {
      console.error("Update profile error:", error);
      const errorMessage = extractErrorMessage(
        error,
        "Failed to update profile. Please try again."
      );
      showToast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const accountSettings = [
    {
      title: "Your Purchases",
      description: "Keep track of your purchases information.",
      buttonText: "View Purchases",
      onClick: () => {
        showToast.info("即将支持");
      },
    },
    {
      title: "Password",
      description: "Set a permanent password to login to your account.",
      buttonText: "Change Password",
      onClick: handlePasswordChange,
    },
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Profile Header */}
        <div className="mb-10">
          <div className="flex items-center mb-6">
            <div className="relative">
              {userInfo?.avatar ? (
                <Image
                  src={userInfo.avatar}
                  alt={getUserDisplayName(userInfo)}
                  width={120}
                  height={120}
                  className="rounded-full object-cover"
                />
              ) : (
                <Avatar className="w-[120px] h-[120px] text-3xl">
                  <AvatarFallback className="bg-indigo-500 text-white text-3xl font-semibold">
                    {getUserInitial(userInfo)}
                  </AvatarFallback>
                </Avatar>
              )}
              <Button
                variant="outline"
                size="icon"
                className="absolute -bottom-2 -right-2 h-9 w-9 rounded-full border-4 border-white dark:border-gray-800 bg-indigo-50 dark:bg-gray-700 hover:bg-indigo-100 dark:hover:bg-gray-600"
              >
                <Edit3 className="h-4 w-4 text-indigo-500 dark:text-indigo-400" />
              </Button>
            </div>
            <h1 className="ml-6 text-2xl font-bold text-gray-900 dark:text-gray-100">
              {getUserDisplayName(userInfo)}
            </h1>
          </div>
        </div>

        {/* Account Settings Section */}
        <div className="mb-10">
          <h2 className="text-xs font-bold tracking-widest text-gray-900 dark:text-gray-300 mb-3">
            ACCOUNT SETTINGS
          </h2>
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6" />

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {accountSettings.map((setting, index) => (
              <div
                key={index}
                className="border border-gray-200 dark:border-gray-600 rounded p-4"
              >
                <h3 className="font-bold text-gray-900 dark:text-gray-100 mb-1">
                  {setting.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                  {setting.description}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-gray-700 dark:text-gray-300"
                  onClick={setting.onClick}
                >
                  {setting.buttonText}
                </Button>
              </div>
            ))}
          </div>
        </div>

        {/* Profile Information Section */}
        <div>
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-xs font-bold tracking-widest text-gray-900 dark:text-gray-300">
              PROFILE INFORMATION
            </h2>
            <Button
              type="button"
              size="sm"
              disabled={!hasFormChanged() || isSaving}
              className="bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleSaveProfile}
            >
              {isSaving ? "Saving..." : "Save"}
            </Button>
          </div>
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6" />

          <form className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <TextField
              label="Username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              placeholder="Add your username"
              fullWidth
            />

            {/* Email Address */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                  Email Address
                </label>
              </div>
              <Input
                type="email"
                name="email"
                value={formData.email}
                readOnly
                className="bg-gray-200 dark:bg-gray-700 cursor-not-allowed"
              />
            </div>

            <TextField
              label="GitHub"
              name="github"
              value={formData.github}
              onChange={handleInputChange}
              placeholder="Add your github profile url"
              fullWidth
            />

            <TextField
              label="Personal Website"
              name="website"
              value={formData.website}
              onChange={handleInputChange}
              placeholder="Add your personal website url"
              fullWidth
            />

            {/* Bio - moved to last position */}
            <div className="sm:col-span-2">
              <TextField
                label="Bio"
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                placeholder="What defines you"
                multiline
                rows={4}
                helperText="256 max characters"
                fullWidth
                className="[&_label]:text-base [&_label]:font-bold"
              />
            </div>
          </form>
        </div>
      </div>

      {/* Password Change Dialog */}
      <ChangePasswordDialog
        open={isPasswordDialogOpen}
        onClose={() => setIsPasswordDialogOpen(false)}
      />
    </div>
  );
};

export default UserSettingsPage;
