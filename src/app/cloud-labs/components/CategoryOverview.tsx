/*
 * @Description: Category Overview Component for Cloud Labs with Carousel
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import Carousel from "@/components/ui/carousel";
import ItemCard from "@/components/common/ItemCard";
import { cloudLabsData, cloudLabCategories } from "@/mockdata/cloudlabs";

interface CategoryOverviewProps {
  onCategoryClick: (categoryName: string) => void;
}

const CategoryOverview: React.FC<CategoryOverviewProps> = ({}) => {
  // Get category description
  const getCategoryDescription = (categoryName: string) => {
    switch (categoryName) {
      case "AI & ML":
        return "Accelerate AI adoption in application development using cloud-based ML and AI services. Uncover the insights from data for personalized user experience.";
      case "Analytics":
        return "Transform raw data into actionable insights with cloud-based analytics services by seamlessly connecting data warehouses to analytics services.";
      case "Application Integration":
        return "Connect and coordinate distributed applications and services using messaging, APIs, and event-driven architectures.";
      case "Compute":
        return "Build and deploy applications using scalable compute services including virtual machines, containers, and serverless computing.";
      case "Containers":
        return "Deploy, manage, and scale containerized applications using orchestration platforms and container services.";
      case "Cryptography & PKI":
        return "Implement security through encryption, key management, and public key infrastructure for data protection.";
      case "Database":
        return "Store, manage, and analyze data using various database services from relational to NoSQL and data warehousing solutions.";
      case "Developer Tools":
        return "Streamline development workflows with CI/CD pipelines, code repositories, and development environment tools.";
      default:
        return "Explore cloud services and technologies in this category.";
    }
  };

  const handleItemClick = (id: string) => {
    console.log("Cloud lab clicked:", id);
    // TODO: Navigate to cloud lab detail page
  };

  const handleFavoriteToggle = (id: string) => {
    console.log("Favorite toggled for cloud lab:", id);
    // TODO: Implement favorite functionality
  };

  return (
    <section className="bg-gray-50 py-4">
      {/* Render each category with its labs */}
      {cloudLabCategories.map((categoryName) => {
        const categoryLabs = cloudLabsData.filter(
          (lab) => lab.category === categoryName
        );

        // Only render categories that have labs
        if (categoryLabs.length === 0) return null;

        return (
          <div key={categoryName} className="mb-16">
            <div className="mb-8">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Typography variant="h4" className="font-bold text-gray-900">
                    {categoryName}
                  </Typography>
                </div>
              </div>
              <Typography className="text-gray-600 text-sm">
                {getCategoryDescription(categoryName)}
              </Typography>
            </div>

            <Carousel itemsPerSlide={3} showArrows={true} showIndicators={true}>
              {categoryLabs.map((lab) => (
                <ItemCard
                  key={lab.id}
                  id={lab.id}
                  title={lab.title}
                  description={lab.description}
                  duration={lab.duration}
                  difficulty={lab.difficulty}
                  category={lab.category}
                  technologies={lab.technologies}
                  type="cloudlab"
                  onClick={handleItemClick}
                  onFavoriteToggle={handleFavoriteToggle}
                />
              ))}
            </Carousel>
          </div>
        );
      })}
    </section>
  );
};

export default CategoryOverview;
