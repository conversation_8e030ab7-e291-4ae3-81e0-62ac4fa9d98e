/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-21 15:19:46
 */
"use client";

import Image from "next/image";
import { FC } from "react";
import { CreditCardIcon, ServerIcon } from "@heroicons/react/24/outline";
import { Container } from "@/components/ui/container";
import LogoCarousel from "@/components/common/logo-carousel";

const companyLogos = [
  {
    name: "Coinbase",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Netflix",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "<PERSON>",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Meta",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Google",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Amazon",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
];

const CloudLabsHero: FC = () => {
  return (
    <div className="bg-[#F4F1FE] pt-16 pb-8 px-6">
      <Container size={"lg"} className="">
        <div className="mx-auto flex flex-col-reverse lg:flex-row items-center justify-between gap-12">
          {/* Left Content */}
          <div className="w-full lg:w-1/2">
            <h1 className="text-4xl font-bold text-gray-900 leading-tight">
              Cloud Labs Augments <br />
              Your Projects with Cloud <br />
              Services
            </h1>
            <p className="mt-4 text-sm leading-5">
              Cloud Labs provide hands-on access for learners to interact with
              cloud services, with zero pain from payments, setup, or cleanup,
              all right here in your Pageflux AI Account.
            </p>

            {/* Features */}
            <ul
              className="mt-4 flex flex-col gap-y-1 text-sm"
              style={{
                fontFamily:
                  '"Helvetica Neue", SF Pro Display, Arial, Roboto, system-ui',
              }}
            >
              <li className="flex items-start gap-3 text-gray-800">
                <CreditCardIcon className="w-4 h-4 mt-1 text-indigo-500" />
                <span>
                  Payment-Free Cloud Services with No Set-up or Clean-up
                </span>
              </li>
              <li className="flex items-start gap-3 text-gray-800">
                <ServerIcon className="w-4 h-4 mt-1 text-indigo-500" />
                <span>Instant, Pain-free Access to Server-less Computing</span>
              </li>
            </ul>

            {/* Logos */}
            <div className="mt-8 flex flex-col items-center w-full">
              <LogoCarousel
                logos={companyLogos}
                speed={20}
                className="max-w-full mx-auto"
              />
            </div>

            {/* CTA */}
            <div className="mt-12">
              <button className="rounded border border-indigo-500 px-4 py-2 text-indigo-600 hover:bg-indigo-100 transition text-sm">
                Unlock Full Access
              </button>
            </div>
          </div>

          {/* Right Image */}
          <div className="w-full lg:w-1/2 flex justify-center">
            <Image
              src="/images/common/cloudLabsHeroImage.svg"
              alt="Cloud Labs Hero"
              width={500}
              height={500}
              className="object-contain"
              priority
            />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default CloudLabsHero;
