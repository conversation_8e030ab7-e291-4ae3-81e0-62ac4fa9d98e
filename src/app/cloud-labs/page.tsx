/*
 * @Description: Cloud Labs Page
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import FilterableContentLayout from "@/components/common/FilterableContentLayout";
import CloudLabsHero from "./components/CloudLabsHero";
import {
  cloudLabsData,
  cloudLabCategories,
  cloudLabTechnologies,
} from "@/mockdata/cloudlabs";

export default function CloudLabsPage() {
  const { t } = useTranslation();

  const filterSections = [
    {
      title: t("cloudLabs.filters.skillLevel"),
      key: "skillLevel",
      options: [
        { id: "Beginner", label: t("common.difficulty.beginner") },
        { id: "Intermediate", label: t("common.difficulty.intermediate") },
        { id: "Advanced", label: t("common.difficulty.advanced") },
      ],
    },
    {
      title: t("cloudLabs.filters.categories"),
      key: "categories",
      options: cloudLabCategories.map((category) => ({
        id: category,
        label: category,
      })),
      showMore: true,
      initialShowCount: 10,
    },
    {
      title: t("cloudLabs.filters.technologies"),
      key: "technologies",
      options: cloudLabTechnologies.map((tech) => ({
        id: tech,
        label: tech,
      })),
      showMore: true,
      initialShowCount: 10,
    },
  ];

  const handleItemClick = (id: string) => {
    console.log("Cloud lab clicked:", id);
    // TODO: Navigate to cloud lab detail page
  };

  const handleFavoriteToggle = (id: string) => {
    console.log("Favorite toggled for cloud lab:", id);
    // TODO: Implement favorite functionality
  };

  return (
    <div>
      {/* Hero Section */}
      <CloudLabsHero />

      {/* Filterable Content with Category Overview */}
      <FilterableContentLayout
        searchPlaceholder={t("cloudLabs.search.placeholder")}
        filterSections={filterSections}
        items={cloudLabsData}
        type="cloudlab"
        onItemClick={handleItemClick}
        onFavoriteToggle={handleFavoriteToggle}
      />
    </div>
  );
}
