/*
 * @Description: Pageflux AI Homepage
 * @Author: <PERSON>
 * @Date: 2025-07-17 17:02:16
 */
"use client";

import React from "react";
import JoinDevelopersGlobally from "@/feature/home/<USER>";
import Introduce from "@/feature/home/<USER>";
import CourseCategories from "@/feature/home/<USER>";
import FreeCourses from "@/feature/home/<USER>";
import LearningPaths from "@/feature/home/<USER>";
import AILearning from "@/feature/home/<USER>";
import Testimonials from "@/feature/home/<USER>";
import TeamSkillsPromotion from "@/feature/home/<USER>";

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <Introduce />
      {/* Course Categories Section */}
      <CourseCategories />

      {/* AI Learning Section */}
      <AILearning />
      {/* Learning Paths Section */}
      <LearningPaths />
      {/* Testimonials Section */}
      <Testimonials />
      {/* Free Courses Section */}
      <FreeCourses />
      {/* Team Skills Promotion Section */}
      <TeamSkillsPromotion />

      {/* Join Developers Globally Section */}
      <JoinDevelopersGlobally />
    </div>
  );
}
