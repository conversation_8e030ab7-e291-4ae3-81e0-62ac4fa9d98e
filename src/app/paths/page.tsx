/*
 * @Description: Skill Paths Page
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import FilterableContentLayout from "@/components/common/FilterableContentLayout";
import SkillPathsHero from "./components/SkillPathsHero";
import {
  skillPathsData,
  skillPathTopics,
  skillPathTechnologies,
} from "@/mockdata/skillpaths";

export default function PathsPage() {
  const { t } = useTranslation();

  const filterSections = [
    {
      title: t("paths.filters.skillLevel"),
      key: "skillLevel",
      options: [
        { id: "Beginner", label: t("common.difficulty.beginner") },
        { id: "Intermediate", label: t("common.difficulty.intermediate") },
        { id: "Advanced", label: t("common.difficulty.advanced") },
      ],
    },
    {
      title: t("paths.filters.topics"),
      key: "topics",
      options: skillPathTopics.map((topic) => ({
        id: topic,
        label: topic,
      })),
      showMore: true,
      initialShowCount: 10,
    },
    {
      title: t("paths.filters.technologies"),
      key: "technologies",
      options: skillPathTechnologies.map((tech) => ({
        id: tech,
        label: tech,
      })),
      showMore: true,
      initialShowCount: 10,
    },
  ];

  const handleItemClick = (id: string) => {
    console.log("Skill path clicked:", id);
    // TODO: Navigate to skill path detail page
  };

  const handleFavoriteToggle = (id: string) => {
    console.log("Favorite toggled for skill path:", id);
    // TODO: Implement favorite functionality
  };

  return (
    <div>
      <SkillPathsHero
        title="Skill Paths"
        subtitle="All our Skill Paths are carefully curated to help you achieve a specific learning goal. Find the perfect Skill Path for your needs here."
      />
      <FilterableContentLayout
        searchPlaceholder={t("paths.search.placeholder")}
        filterSections={filterSections}
        items={skillPathsData}
        type="path"
        onItemClick={handleItemClick}
        onFavoriteToggle={handleFavoriteToggle}
      />
    </div>
  );
}
