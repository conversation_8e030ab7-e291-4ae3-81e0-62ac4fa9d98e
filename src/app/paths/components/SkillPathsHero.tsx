/*
 * @Description: Hero Section for Skill Paths Page
 * @Author: <PERSON>
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";

interface SkillPathsHeroProps {
  title?: string;
  subtitle?: string;
}

const SkillPathsHero: React.FC<SkillPathsHeroProps> = ({
  title = "Skill Paths",
  subtitle = "All our Skill Paths are carefully curated to help you achieve a specific learning goal. Find the perfect Skill Path for your needs here.",
}) => {
  return (
    <div
      className="w-full bg-white dark:bg-gray-D1500 dark:text-gray-D200"
      style={{ zIndex: 1 }}
    >
      <Container size="lg-plus" className="flex items-center pt-16 pb-8">
        <div className="flex flex-col">
          <Typography
            variant={"h3"}
            className="text-2xl md:text-3xl font-bold text-gray-900 mb-6"
          >
            {title}
          </Typography>
          <p className="m-0 text-sm" style={{ maxWidth: "662px" }}>
            {subtitle}
          </p>
        </div>
      </Container>
    </div>
  );
};

export default SkillPathsHero;
