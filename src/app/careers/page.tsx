"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search as SearchIcon,
  MapPin as LocationIcon,
  Briefcase as WorkIcon,
  Clock as ScheduleIcon,
  Check as CheckIcon,
  TrendingUp as GrowthIcon,
  Users as PeopleIcon,
  Award as AwardIcon,
} from "lucide-react";

const jobOpenings = [
  {
    id: 1,
    title: "Senior Frontend Engineer",
    department: "Engineering",
    location: "San Francisco, CA",
    type: "Full-time",
    experience: "5+ years",
    description:
      "Join our frontend team to build beautiful, responsive user interfaces using React, TypeScript, and modern web technologies.",
    requirements: [
      "5+ years of experience with React and TypeScript",
      "Strong understanding of modern CSS and responsive design",
      "Experience with state management libraries (Redux, Zustand)",
      "Familiarity with testing frameworks (Jest, React Testing Library)",
      "Bachelor's degree in Computer Science or equivalent experience",
    ],
    posted: "2 days ago",
  },
  {
    id: 2,
    title: "Product Manager",
    department: "Product",
    location: "Remote",
    type: "Full-time",
    experience: "3+ years",
    description:
      "Lead product strategy and execution for our educational platform, working closely with engineering and design teams.",
    requirements: [
      "3+ years of product management experience",
      "Experience with educational technology products",
      "Strong analytical and problem-solving skills",
      "Excellent communication and leadership abilities",
      "MBA or equivalent experience preferred",
    ],
    posted: "1 week ago",
  },
  {
    id: 3,
    title: "UX Designer",
    department: "Design",
    location: "New York, NY",
    type: "Full-time",
    experience: "2+ years",
    description:
      "Design intuitive and engaging user experiences for our learning platform, conducting user research and creating prototypes.",
    requirements: [
      "2+ years of UX design experience",
      "Proficiency in Figma, Sketch, or similar design tools",
      "Experience with user research and usability testing",
      "Strong portfolio demonstrating design thinking",
      "Bachelor's degree in Design, HCI, or related field",
    ],
    posted: "3 days ago",
  },
  {
    id: 4,
    title: "Backend Engineer",
    department: "Engineering",
    location: "Austin, TX",
    type: "Full-time",
    experience: "4+ years",
    description:
      "Build scalable backend systems and APIs to support our growing platform using Node.js, Python, and cloud technologies.",
    requirements: [
      "4+ years of backend development experience",
      "Proficiency in Node.js, Python, or similar languages",
      "Experience with databases (PostgreSQL, MongoDB)",
      "Knowledge of cloud platforms (AWS, GCP, Azure)",
      "Understanding of microservices architecture",
    ],
    posted: "5 days ago",
  },
  {
    id: 5,
    title: "Content Strategist",
    department: "Content",
    location: "Remote",
    type: "Contract",
    experience: "3+ years",
    description:
      "Develop and execute content strategy for our educational courses, working with subject matter experts and instructional designers.",
    requirements: [
      "3+ years of content strategy experience",
      "Background in educational content or e-learning",
      "Excellent writing and editing skills",
      "Experience with content management systems",
      "Understanding of learning design principles",
    ],
    posted: "1 day ago",
  },
  {
    id: 6,
    title: "DevOps Engineer",
    department: "Engineering",
    location: "Seattle, WA",
    type: "Full-time",
    experience: "3+ years",
    description:
      "Manage and optimize our cloud infrastructure, CI/CD pipelines, and deployment processes to ensure reliable and scalable systems.",
    requirements: [
      "3+ years of DevOps or infrastructure experience",
      "Experience with containerization (Docker, Kubernetes)",
      "Knowledge of CI/CD tools (Jenkins, GitHub Actions)",
      "Proficiency with cloud platforms and IaC tools",
      "Strong scripting skills (Bash, Python)",
    ],
    posted: "4 days ago",
  },
];

const benefits = [
  {
    icon: <GrowthIcon className="w-8 h-8 text-blue-500" />,
    title: "Professional Growth",
    description:
      "Continuous learning opportunities and career development programs",
  },
  {
    icon: <PeopleIcon className="w-8 h-8 text-emerald-500" />,
    title: "Great Team",
    description:
      "Work with passionate, talented people who care about education",
  },
  {
    icon: <AwardIcon className="w-8 h-8 text-amber-500" />,
    title: "Competitive Benefits",
    description: "Health insurance, equity, unlimited PTO, and more",
  },
];

export default function CareersPage() {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState("all");
  const [selectedLocation, setSelectedLocation] = useState("all");
  const [selectedType, setSelectedType] = useState("all");

  const departments = ["all", "Engineering", "Product", "Design", "Content"];
  const locations = [
    "all",
    "Remote",
    "San Francisco, CA",
    "New York, NY",
    "Austin, TX",
    "Seattle, WA",
  ];
  const types = ["all", "Full-time", "Contract"];

  const filteredJobs = jobOpenings.filter((job) => {
    const matchesSearch =
      job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDepartment =
      selectedDepartment === "all" || job.department === selectedDepartment;
    const matchesLocation =
      selectedLocation === "all" || job.location === selectedLocation;
    const matchesType = selectedType === "all" || job.type === selectedType;

    return matchesSearch && matchesDepartment && matchesLocation && matchesType;
  });

  return (
    <div className="bg-white min-h-screen pt-20">
      <Container size="lg">
        {/* Header */}
        <div className="text-center mb-16">
          <Typography
            variant="h1"
            className="font-bold text-gray-900 mb-6 text-4xl md:text-5xl"
          >
            {t("careers.hero.title")}
          </Typography>
          <Typography
            variant="p"
            className="text-gray-600 text-lg max-w-2xl mx-auto mb-8"
          >
            {t("careers.hero.description")}
          </Typography>
          <div className="flex justify-center gap-4">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
              {t("careers.hero.viewPositions")}
            </Button>
            <Button variant="outline" className="px-8 py-3">
              {t("careers.hero.learnCulture")}
            </Button>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="mb-16">
          <Typography
            variant="h2"
            className="font-bold text-gray-900 text-center mb-12 text-3xl"
          >
            Why Work With Us?
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center p-8">
                <CardContent className="space-y-4">
                  <div className="flex justify-center">{benefit.icon}</div>
                  <Typography
                    variant="h6"
                    className="font-semibold text-gray-900"
                  >
                    {benefit.title}
                  </Typography>
                  <Typography
                    variant="p"
                    className="text-gray-600 leading-relaxed"
                  >
                    {benefit.description}
                  </Typography>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Job Search and Filters */}
        <div className="mb-12">
          <Typography
            variant="h2"
            className="font-bold text-gray-900 mb-8 text-3xl"
          >
            Open Positions
          </Typography>

          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search positions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={selectedDepartment}
              onValueChange={setSelectedDepartment}
            >
              <SelectTrigger>
                <SelectValue placeholder="Department" />
              </SelectTrigger>
              <SelectContent>
                {departments.map((dept) => (
                  <SelectItem key={dept} value={dept}>
                    {dept === "all" ? "All Departments" : dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={selectedLocation}
              onValueChange={setSelectedLocation}
            >
              <SelectTrigger>
                <SelectValue placeholder="Location" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((loc) => (
                  <SelectItem key={loc} value={loc}>
                    {loc === "all" ? "All Locations" : loc}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                {types.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type === "all" ? "All Types" : type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Results Count */}
          <Typography variant="p" className="text-gray-600 mb-8">
            {filteredJobs.length} position{filteredJobs.length !== 1 ? "s" : ""}{" "}
            found
          </Typography>

          {/* Job Listings */}
          <div className="space-y-6">
            {filteredJobs.map((job) => (
              <Card key={job.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-8">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-4">
                        <Typography
                          variant="h5"
                          className="font-semibold text-gray-900"
                        >
                          {job.title}
                        </Typography>
                        <Badge variant="secondary">{job.department}</Badge>
                      </div>

                      <Typography
                        variant="p"
                        className="text-gray-600 mb-4 leading-relaxed"
                      >
                        {job.description}
                      </Typography>

                      <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <LocationIcon className="w-4 h-4" />
                          <span>{job.location}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <WorkIcon className="w-4 h-4" />
                          <span>{job.type}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <ScheduleIcon className="w-4 h-4" />
                          <span>{job.experience}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-3">
                      <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                        Apply Now
                      </Button>
                      <Typography
                        variant="small"
                        className="text-gray-500 text-center"
                      >
                        Posted {job.posted}
                      </Typography>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
}
