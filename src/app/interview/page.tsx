/*
 * @Description: Interview Prep Page
 * @Author: <PERSON>
 * @Date: 2025-07-30
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import InterviewHero from "@/feature/interview/interview-hero";
import InterviewTestimonials from "@/feature/interview/interview-testimonials";
import InterviewTopics from "@/feature/interview/interview-topics";
import InterviewPatterns from "@/feature/interview/interview-patterns";
import InterviewCatalog from "@/feature/interview/interview-catalog";
import FAQSection from "@/feature/shared/FAQSection";
import InterviewPricing from "@/feature/interview/interview-pricing";

export default function InterviewPage() {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <InterviewHero />

      {/* Testimonials Section */}
      <InterviewTestimonials />

      {/* Interview Topics Section */}
      <InterviewTopics />

      {/* Coding Patterns Section */}
      <InterviewPatterns />

      {/* Interview Catalog Section */}
      <InterviewCatalog />

      <InterviewPricing />

      {/* FAQ Section */}
      <FAQSection
        title={t("interview.faq.title")}
        containerSize="lg-plus"
        className="py-16"
      />
    </div>
  );
}
