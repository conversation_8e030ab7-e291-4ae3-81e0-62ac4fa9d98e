/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-21 11:06:06
 */
"use client";

import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { Container } from "@/components/ui/container";
import ExploreCategorySection from "@/components/sections/ExploreCategorySection";
import FeatureCards from "@/app/explore/components/FeatureCards";
import MostPopularCourses from "@/app/explore/components/MostPopularCourses";
import NewAdditions from "@/app/explore/components/NewAdditions";
import ExploreSearchBar from "./components/ExploreSearchBar";
import {
  getAllCategories,
  getPointersByCategory,
  getCategoryInfo,
} from "@/mockdata/maindata";
import { Typography } from "@/components/ui/typography";
import { learningNodeService } from "@/service/learning-nodes.service";
import { handleApiError, handlePaginatedApiResponse } from "@/lib/api-error-handler";
import type { LearningNode } from "@/types/openapi";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import { useRouter } from "next/navigation";
import { formatDuration } from "@/lib/utils";

// Main Explore Page Component
export default function ExplorePage() {
  const { t } = useTranslation();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchMode] = useState<"exact" | "fuzzy">("fuzzy");
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<LearningNode[]>([]);
  const [searchPage] = useState(1);
  const [searchPageSize] = useState(12);

  const handleSearch = async (q: string) => {
    setSearchQuery(q);
    if (!q || !q.trim()) {
      setSearchResults([]);
      return;
    }
    try {
      setSearchLoading(true);
      const resp = await learningNodeService.list({
        page: searchPage,
        page_size: searchPageSize,
        query: q.trim(),
        search_mode: searchMode,
      });
      const data = handlePaginatedApiResponse(resp, undefined, { showToast: false });
      setSearchResults(data?.data || []);
    } catch (err) {
      handleApiError(err, { showToast: true, defaultMessage: "搜索失败" });
    } finally {
      setSearchLoading(false);
    }
  };

  // 获取所有分类并为每个分类创建数据
  const categories = getAllCategories();
  const categoryData = categories
    .filter((categoryId) => categoryId !== "other") // 先过滤掉other分类
    .map((categoryId) => {
      const categoryInfo = getCategoryInfo(categoryId);
      const courses = getPointersByCategory(categoryId);
      return {
        id: categoryId,
        title: categoryInfo?.title || categoryId,
        description:
          categoryInfo?.description ||
          t("explore.categoryDescription", {
            title: categoryInfo?.title || categoryId,
          }),
        href: `/catalog/${categoryId}`,
        courses: courses.slice(0, 8), // 限制每个分类显示8门课程
      };
    });

  // 单独处理Other分类，放在New Additions前面
  const otherCategoryInfo = getCategoryInfo("other");
  const otherCourses = getPointersByCategory("other");
  const otherCategoryData =
    otherCourses.length > 0
      ? {
          id: "other",
          title: otherCategoryInfo?.title || "Other",
          description:
            otherCategoryInfo?.description ||
            "Specialized topics and emerging technologies",
          href: "/catalog/other",
          courses: otherCourses.slice(0, 8),
        }
      : null;

  return (
    <div className="bg-white">
      <ExploreSearchBar onSearch={handleSearch} />

      {/* 搜索结果展示 */}
      {searchQuery && (
        <Container size="lg-plus">
          <div className="py-8">
            <div className="mb-4 flex items-baseline justify-between">
              <Typography variant="h4" className="font-bold text-gray-900">
                {t("explore.searchResults.title", { defaultValue: "搜索结果" })}
              </Typography>
              <div className="text-xs text-gray-500">
                {searchLoading
                  ? t("common.loading", { defaultValue: "加载中..." })
                  : t("explore.searchResults.count", {
                      defaultValue: "共 {{count}} 条",
                      count: searchResults.length,
                    })}
              </div>
            </div>

            {searchLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : searchResults.length === 0 ? (
              <div className="text-sm text-gray-500 py-8">
                {t("explore.searchResults.empty", { defaultValue: "未找到匹配内容" })}
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {searchResults.map((course) => {
                  const getDifficultyString = (difficulty?: number): "beginner" | "intermediate" | "advanced" => {
                    if (!difficulty) return "intermediate";
                    if (difficulty <= 3) return "beginner";
                    if (difficulty >= 7) return "advanced";
                    return "intermediate";
                  };

                  return (
                    <SimpleCourseCard
                      key={course.id || "unknown"}
                      course={{
                        id: course.id || "unknown",
                        title: course.title || "Untitled Course",
                        description: course.description || "No description available",
                        duration: formatDuration(course.estimated_times),
                        difficulty: getDifficultyString(course.difficulty),
                        bookmarked: false,
                      }}
                      onClick={() => router.push(`/courses/${course.id}`)}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </Container>
      )}

      <Container size="lg-plus">
        <FeatureCards />
      </Container>
      <Container size="lg-plus">
        <MostPopularCourses />
      </Container>
      {/* <Container size="lg-plus">
        <Typography variant="h2" className="text-black border-0">
          {t("explore.title")}
        </Typography>
      </Container> */}

      {/* Category sections */}
      {/* <div className="bg-white">
        {categoryData.map((category) => (
          <ExploreCategorySection key={category.id} category={category} />
        ))}
      </div> */}

      {/* Other category before New Additions */}
      {/* {otherCategoryData && (
        <div className="bg-white">
          <ExploreCategorySection category={otherCategoryData} />
        </div>
      )} */}

      {/* New Additions at the end */}
      {/* <Container size="lg-plus">
        <NewAdditions />
      </Container> */}
    </div>
  );
}
