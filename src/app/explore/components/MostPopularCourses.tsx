/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-21 12:10:04
 */
import React from "react";
import { useTranslation } from "react-i18next";
import FluxIcon from "@/components/fluxIcon";
import { Typography } from "@/components/ui/typography";
import Carousel from "@/components/ui/carousel";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import { learningNodeService } from "@/service/learning-nodes.service";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { LearningNode, Paginated } from "@/types/openapi";
import {
  handleApiError,
  handlePaginatedApiResponse,
} from "@/lib/api-error-handler";
import { formatDuration } from "@/lib/utils";

const MostPopularCourses: React.FC = () => {
  const { t } = useTranslation();
  const [mostPopularCourses, setMostPopularCourses] = useState<LearningNode[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchPopularCourses = async () => {
      try {
        const response = await learningNodeService.list({
          page_size: 8,
          // status: "active",
        });
        const data = handlePaginatedApiResponse(response, undefined, {
          showToast: false,
        });
        setMostPopularCourses(data?.data || []);
      } catch (error) {
        handleApiError(error, {
          showToast: true,
          defaultMessage: "Failed to fetch popular courses",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPopularCourses();
  }, []);

  return (
    <div className="py-12">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-2">
          <FluxIcon name="most" width={24} height={24} className="w-5 h-5" />
          <Typography variant="h4" className="font-bold text-gray-900">
            {t("explore.mostPopular.title")}
          </Typography>
        </div>
        <Typography className="text-gray-600 text-xs">
          {t("explore.mostPopular.description")}
        </Typography>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <Carousel itemsPerSlide={4} showArrows={true} showIndicators={true}>
          {mostPopularCourses.map((course) => {
            // 根据数字难度转换为字符串
            const getDifficultyString = (difficulty?: number): string => {
              if (!difficulty) return "intermediate";
              if (difficulty <= 3) return "beginner";
              if (difficulty >= 7) return "advanced";
              return "intermediate";
            };

            return (
              <SimpleCourseCard
                key={course.id || "unknown"}
                course={{
                  id: course.id || "unknown",
                  title: course.title || "Untitled Course",
                  description: course.description || "No description available",
                  duration: formatDuration(course.estimated_times),
                  difficulty: getDifficultyString(course.difficulty),
                  bookmarked: false,
                }}
                onClick={() => {
                  // 导航到课程页面
                  router.push(`/courses/${course.id}`);
                }}
              />
            );
          })}
        </Carousel>
      )}
    </div>
  );
};

export default MostPopularCourses;
