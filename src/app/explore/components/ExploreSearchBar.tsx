/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-21 11:16:27
 */
"use client";

import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Search } from "lucide-react";
import { Container } from "@/components/ui/container";
import SearchSuggestions from "./SearchSuggestions";

interface ExploreSearchBarProps {
  onSearch?: (query: string) => void;
  placeholder?: string;
}

const ExploreSearchBar: React.FC<ExploreSearchBarProps> = ({
  onSearch,
  placeholder,
}) => {
  const { t } = useTranslation();
  const defaultPlaceholder = placeholder || t("explore.searchBar.placeholder");
  const [searchQuery, setSearchQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(searchQuery);
    setShowSuggestions(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setShowSuggestions(true);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion);
    onSearch?.(suggestion);
    setShowSuggestions(false);
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    setShowSuggestions(true);
  };

  const handleInputBlur = () => {
    setIsFocused(false);
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => setShowSuggestions(false), 150);
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="sticky z-10 w-full bg-[#f8f8ff] py-12 dark:bg-gray-900">
      <Container size="lg-plus">
        <div className="flex w-full justify-center">
          <div
            className="relative w-full max-w-xl px-4 sm:px-0"
            ref={searchRef}
          >
            <form onSubmit={handleSearch} className="w-full">
              <div className="relative">
                <div className="flex items-center w-full h-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm transition-shadow duration-200">
                  <div className="flex items-center justify-center pl-3">
                    <Search className="h-4 w-4 text-indigo-600 dark:text-gray-400" />
                  </div>
                  <input
                    type="search"
                    placeholder={defaultPlaceholder}
                    value={searchQuery}
                    onChange={handleInputChange}
                    onFocus={handleInputFocus}
                    className="flex-1 h-full bg-transparent border-none outline-none text-gray-700 dark:text-gray-200 px-3 text-xs placeholder:text-gray-400 dark:placeholder:text-gray-500 caret-indigo-500"
                    autoComplete="off"
                  />
                </div>
              </div>
            </form>

            {/* Search Suggestions */}
            <SearchSuggestions
              query={searchQuery}
              onSuggestionClick={handleSuggestionClick}
              isVisible={showSuggestions}
            />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default ExploreSearchBar;
