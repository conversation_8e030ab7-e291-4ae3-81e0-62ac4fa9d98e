/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-07-21 11:52:41
 */
import React from "react";
import { useTranslation } from "react-i18next";
import Link from "next/link";
import { Typography } from "@/components/ui/typography";
import { MessageSquare, Cloud, FolderOpen } from "lucide-react";
import { getTypeInfo } from "@/mockdata/maindata";

interface Feature {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
  badgeColor: string;
}

const FeatureCards: React.FC = () => {
  const { t } = useTranslation();
  const mockinterviewType = getTypeInfo("mockinterview");
  const cloudlabType = getTypeInfo("cloudlab");
  const projectType = getTypeInfo("project");

  const features: Feature[] = [
    {
      title:
        mockinterviewType?.title || t("explore.features.mockInterviews.title"),
      description:
        mockinterviewType?.description ||
        t("explore.features.mockInterviews.description"),
      href: "/mock-interview",
      icon: <MessageSquare className="h-4 w-4" />,
      badgeColor: "bg-orange-50 text-orange-700",
    },
    {
      title: cloudlabType?.title || t("explore.features.cloudLabs.title"),
      description:
        cloudlabType?.description ||
        t("explore.features.cloudLabs.description"),
      href: "/cloud-labs",
      icon: <Cloud className="h-4 w-4" />,
      badgeColor: "bg-purple-50 text-purple-700",
    },
    {
      title: projectType?.title || t("explore.features.projects.title"),
      description:
        projectType?.description || t("explore.features.projects.description"),
      href: "/projects",
      icon: <FolderOpen className="h-4 w-4" />,
      badgeColor: "bg-blue-50 text-blue-700",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
      {features.map((feature: Feature, index) => (
        <Link key={index} href={feature.href} className="no-underline">
          <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg hover:border-gray-300 transition-all duration-200 cursor-pointer group h-full">
            <div
              className={`flex flex-row justify-center items-center text-sm eading-6 rounded-full h-4 max-w-max px-3 py-4 mb-1 ${feature.badgeColor}`}
            >
              <div className="mr-2">{feature.icon}</div>
              <div className="text-xs font-medium">{feature.title}</div>
            </div>

            {/* Description */}
            <div className="text-gray-600 text-xs leading-relaxed mt-2">
              {feature.description}
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
};

export default FeatureCards;
