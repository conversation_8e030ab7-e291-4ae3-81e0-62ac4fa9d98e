/*
 * @Description: New Additions component for explore page
 * @Author: Devin
 * @Date: 2025-07-25
 */
import React from "react";
import { useTranslation } from "react-i18next";
import FluxIcon from "@/components/fluxIcon";
import { Typography } from "@/components/ui/typography";
import Carousel from "@/components/ui/carousel";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import { getNewAdditions } from "@/service/course.service";
import { getCourseUrl } from "@/mockdata/maindata";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { MockApiCourseData } from "@/types/mockapi";
import {
  getDifficulty,
  getCourseTitle,
  getCourseDescription,
  getCourseWhatYouWillLearn,
  getDifficultyStandard,
} from "@/utils/course.utils";

const NewAdditions: React.FC = () => {
  const { t } = useTranslation();
  const [newCourses, setNewCourses] = useState<MockApiCourseData[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchNewCourses = async () => {
      try {
        const courses = await getNewAdditions(12);
        setNewCourses(courses);
      } catch (error) {
        console.error("Failed to fetch new courses:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchNewCourses();
  }, []);

  return (
    <div className="py-12">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-2">
          <FluxIcon name="most" width={24} height={24} className="w-5 h-5" />
          <Typography variant="h4" className="font-bold text-gray-900">
            {t("explore.newAdditions.title")}
          </Typography>
        </div>
        <Typography className="text-gray-600 text-xs">
          {t("explore.newAdditions.description")}
        </Typography>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <Carousel itemsPerSlide={4} showArrows={true} showIndicators={true}>
          {newCourses.map((course) => {
            // 判断课程难度
            const getDifficulty = (course: MockApiCourseData) => {
              const tags = course.tags.map((tag) => tag.toLowerCase());
              const learnerTags = course.learner_tags.map((tag) =>
                tag.toLowerCase()
              );

              if (
                tags.includes("beginner") ||
                learnerTags.includes("beginner")
              ) {
                return "beginner";
              }
              if (
                tags.includes("advanced") ||
                learnerTags.includes("advanced")
              ) {
                return "advanced";
              }
              return "intermediate";
            };

            return (
              <SimpleCourseCard
                key={course.id || "unknown"}
                course={{
                  id: course.id || "unknown",
                  title: getCourseTitle(course),
                  description: getCourseDescription(course),
                  duration: `${Math.round(course.read_time / 3600)} h`, // 转换秒为小时
                  difficulty: getDifficulty(course) as
                    | "beginner"
                    | "intermediate"
                    | "advanced",
                  bookmarked: false,
                }}
                onClick={() => {
                  // 创建一个兼容的对象用于 getCourseUrl
                  const compatibleCourse = {
                    id: course.id || "unknown",
                    title: getCourseTitle(course),
                    description: getCourseDescription(course),
                    level: getDifficulty(course),
                    type: "course",
                    category: "programming",
                    tags: [...course.tags, ...course.skills],
                    duration: course.read_time,
                    price: `$${course.price}`,
                    rating: 4.5,
                    students: "1k+",
                    image: "/images/course-placeholder.jpg",
                    playgrounds: 0,
                    quizzes: course.aggregated_widget_stats.Quiz || 0,
                    createAt: new Date(course.first_published_time).getTime(),
                    authors: [],
                    skills: course.skills,
                    whatYouWillLearn: getCourseWhatYouWillLearn(course),
                    developedByMAANG: false,
                    instructor: "Expert Instructor",
                    difficulty: getDifficultyStandard(course),
                    language: "en",
                    content: {
                      period: "week" as const,
                      sections: [],
                    },
                    currentProgress: {
                      completedLessons: [],
                      totalLessons: 0,
                      totalQuizzes: 0,
                      totalChallenges: 0,
                      percentage: 0,
                    },
                  };
                  const courseUrl = getCourseUrl(compatibleCourse);
                  router.push(courseUrl);
                }}
              />
            );
          })}
        </Carousel>
      )}
    </div>
  );
};

export default NewAdditions;
