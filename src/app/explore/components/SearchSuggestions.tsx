"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Search } from "lucide-react";

interface SearchSuggestionsProps {
  query: string;
  onSuggestionClick: (suggestion: string) => void;
  isVisible: boolean;
}

interface CourseItem {
  title: string;
  level: string;
  duration: string;
}

// 获取趋势数据的函数
const getTrendingData = (t: any) => ({
  trendingNow: t("explore.suggestions.trendingNow", {
    returnObjects: true,
  }) as string[],
  popularCourses: t("explore.suggestions.popularCourses", {
    returnObjects: true,
  }) as CourseItem[],
});

// 统一图片地址
const placeholderImage =
  "https://www.pointer-ai.com/cdn-cgi/image/f=auto,fit=cover,w=200/v2api/collection/10370001/6289391964127232/image/5627886733099008";

const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({
  query,
  onSuggestionClick,
  isVisible,
}) => {
  const { t } = useTranslation();
  const [suggestions, setSuggestions] = useState<string[]>([]);

  // 获取翻译数据 - 使用 useMemo 避免无限重新渲染
  const { trendingNow, popularCourses } = useMemo(
    () => getTrendingData(t),
    [t]
  );

  useEffect(() => {
    if (query.trim()) {
      const filtered = popularCourses
        .map((c) => c.title)
        .filter((title) => title.toLowerCase().includes(query.toLowerCase()))
        .slice(0, 5);
      setSuggestions(filtered);
    } else {
      setSuggestions([]);
    }
  }, [query, popularCourses]);

  if (!isVisible) return null;

  return (
    <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-xl shadow-2xl z-50 mt-2 max-h-[32rem] overflow-y-auto animate-fadeIn transition-opacity duration-200 text-[13px]">
      <div className="p-4 space-y-5 text-gray-800">
        {/* Trending Now */}
        {!query && (
          <div>
            <p className="text-[10px] font-semibold  uppercase mb-2 tracking-wider text-indigo-500 dark:text-indigo-300">
              {t("explore.suggestions.trendingNowTitle").toUpperCase()}
            </p>
            <ul className="space-y-1">
              {trendingNow.map((item, index) => (
                <li
                  key={index}
                  onClick={() => onSuggestionClick(item)}
                  className="cursor-pointer px-3 py-2 rounded-md hover:bg-indigo-50 transition-colors flex items-center gap-2"
                >
                  <Search className="h-3 w-3 text-gray-400" />
                  {item}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Most Popular */}
        {!query && (
          <div>
            <p className="text-[10px] font-semibold  uppercase mb-2 tracking-wider text-indigo-500 dark:text-indigo-300">
              Most Popular
            </p>
            <div className="space-y-1.5">
              {popularCourses.map((course, index) => (
                <div
                  key={index}
                  onClick={() => onSuggestionClick(course.title)}
                  className="flex items-start gap-2 p-2 rounded-lg hover:bg-indigo-50 transition cursor-pointer"
                >
                  <img
                    src={placeholderImage}
                    alt={course.title}
                    className="w-16 h-8 rounded-md object-cover mt-0.5"
                  />
                  <div className="flex-1">
                    <p className="text-[13px] font-medium text-gray-900 leading-tight line-clamp-2">
                      {course.title}
                    </p>
                    <div className="text-[11px] text-gray-500 flex gap-2 mt-1">
                      <span>Course</span>
                      <span>• {course.level}</span>
                      <span>• {course.duration}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Suggestions */}
        {query && suggestions.length > 0 && (
          <div>
            <p className="text-[11px] font-semibold text-gray-500 uppercase mb-2 tracking-wider">
              Suggestions
            </p>
            <ul className="space-y-1">
              {suggestions.map((s, index) => (
                <li
                  key={index}
                  onClick={() => onSuggestionClick(s)}
                  className="cursor-pointer px-3 py-2 rounded-md hover:bg-indigo-50 transition-colors"
                >
                  {s}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* No results */}
        {query && suggestions.length === 0 && (
          <div className="text-center py-4 text-[13px] text-gray-500">
            No results for "{query}"
          </div>
        )}
      </div>

      {/* Bottom Hint */}
      <div className="border-t border-gray-100 px-4 py-3 text-[11px] text-center text-gray-400 uppercase">
        Press Enter to see all search results
      </div>

      {/* Custom Scrollbar */}
      <style jsx>{`
        div::-webkit-scrollbar {
          width: 6px;
        }
        div::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.1);
          border-radius: 4px;
        }
        div::-webkit-scrollbar-track {
          background: transparent;
        }
      `}</style>
    </div>
  );
};

export default SearchSuggestions;
