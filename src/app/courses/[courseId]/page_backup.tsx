/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-24 20:06:21
 */
"use client";

import React, { useEffect, useMemo, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { Container } from "@/components/ui/container";
import { Button } from "@/components/ui/button";
import { learningNodeService } from "@/service/learning-nodes.service";
import { handleApiError, handleApiResponse } from "@/lib/api-error-handler";
import type { NodeLesson, LearningNode } from "@/types/openapi";
import { formatDuration } from "@/lib/utils";
export default function CoursePage() {
  const params = useParams();
  const courseId = params.courseId as string; // 实际即 node_id
  const router = useRouter();

  const [loading, setLoading] = useState(true);
  const [nodeInfo, setNodeInfo] = useState<LearningNode | null>(null);
  const [nodeLessons, setNodeLessons] = useState<NodeLesson[]>([]);

  useEffect(() => {
    const fetchAll = async () => {
      try {
        setLoading(true);
        const [nodeResp, lessonsResp] = await Promise.all([
          learningNodeService.get(courseId),
          learningNodeService.listLessons(courseId),
        ]);
        const node = handleApiResponse(nodeResp, undefined, {
          showToast: false,
        });
        const lessons =
          handleApiResponse(lessonsResp, undefined, { showToast: false }) || [];
        setNodeInfo(node || null);
        setNodeLessons(lessons);
      } catch (err) {
        handleApiError(err, {
          showToast: true,
          defaultMessage: "加载课程失败",
        });
      } finally {
        setLoading(false);
      }
    };
    fetchAll();
  }, [courseId]);

  const sorted = useMemo(() => {
    // 使用 order_key 的字典序排序；缺省时回退到创建时间
    return [...nodeLessons].sort((a, b) => {
      if (a.order_key && b.order_key && a.order_key !== b.order_key)
        return a.order_key.localeCompare(b.order_key);
      const ta = a.created_at ? new Date(a.created_at).getTime() : 0;
      const tb = b.created_at ? new Date(b.created_at).getTime() : 0;
      return ta - tb;
    });
  }, [nodeLessons]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载课程中...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {
        <div className="bg-[#eef]">
          <Container size="lg-plus">
            <div className="py-8">
              <h1 className="text-2xl font-semibold">
                {nodeInfo?.title || "课程"}
              </h1>
              <div className="mt-2 text-gray-600 whitespace-pre-wrap">
                {nodeInfo?.description}
              </div>
              <div className="mt-3 flex gap-4 text-sm text-gray-700">
                <span>难度：{nodeInfo?.difficulty ?? 5}</span>
                <span>
                  预计时长：
                  {nodeInfo?.estimated_times &&
                    formatDuration(nodeInfo?.estimated_times)}
                </span>
                <span>课时数：{sorted.length}</span>
              </div>
            </div>
          </Container>
        </div>
      }

      <Container size="lg-plus">
        <div className="py-6">
          {sorted.length === 0 ? (
            <div className="text-gray-500">
              暂无课程，请稍后再试或生成课程内容。
            </div>
          ) : (
            <div className="space-y-3">
              {sorted.map((it, idx) => (
                <div
                  key={it.id || idx}
                  className="rounded-md border bg-white p-4 hover:shadow-sm transition"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-base font-medium">
                        {it.lesson_title || `课程 ${idx + 1}`}
                      </div>
                      {it.lesson_description && (
                        <div className="text-sm text-gray-600 line-clamp-2 mt-1">
                          {it.lesson_description}
                        </div>
                      )}
                    </div>
                    <Button
                      size="sm"
                      onClick={() =>
                        router.push(`/courses/${courseId}/${it.lesson_id}`)
                      }
                    >
                      开始学习
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Container>
    </div>
  );
}
