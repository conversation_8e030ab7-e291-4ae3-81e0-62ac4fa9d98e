/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-08-16 13:19:41
 */
"use client";

import { useParams, useRouter } from "next/navigation";
import LessonComponent from "@/feature/courses/lesson";

export default function CourseLessonPage() {
  const params = useParams();
  const router = useRouter();
  const courseId = params.courseId as string; // node_id
  const lessonId = params.lessonId as string; // lesson mongo id

  return (
    <LessonComponent courseId={courseId} lessonId={lessonId}></LessonComponent>
  );
}
