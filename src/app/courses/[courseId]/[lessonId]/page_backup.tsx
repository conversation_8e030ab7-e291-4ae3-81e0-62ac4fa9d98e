"use client";

import React, { useEffect, useMemo, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { Container } from "@/components/ui/container";
import { Button } from "@/components/ui/button";
import { handleApiError, handleApiResponse } from "@/lib/api-error-handler";
import { learningLessonService } from "@/service/learning-lessons.service";
import { learningNodeService } from "@/service/learning-nodes.service";
import type { LearningLesson, NodeLesson } from "@/types/openapi";
import AtomicContentRenderer from "@/components/lessons/AtomicContentRenderer";

export default function CourseLessonPage() {
  const params = useParams();
  const router = useRouter();
  const courseId = params.courseId as string; // node_id
  const lessonId = params.lessonId as string; // lesson mongo id

  const [lesson, setLesson] = useState<LearningLesson | null>(null);
  const [nodeLessons, setNodeLessons] = useState<NodeLesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAll, setShowAll] = useState(false); // 一屏展示所有内容
  const [cursor, setCursor] = useState(0); // 单段模式游标

  useEffect(() => {
    const fetchAll = async () => {
      try {
        setLoading(true);
        const [lessonResp, listResp] = await Promise.all([
          learningLessonService.get(lessonId),
          learningNodeService.listLessons(courseId),
        ]);
        const lessonData = handleApiResponse(lessonResp, undefined, { showToast: false });
        const listData = handleApiResponse(listResp, undefined, { showToast: false }) || [];
        setLesson(lessonData || null);
        setNodeLessons(listData);
      } catch (err) {
        handleApiError(err, { showToast: true, defaultMessage: "加载课时失败" });
      } finally {
        setLoading(false);
      }
    };
    fetchAll();
  }, [courseId, lessonId]);

  const sortedLessons = useMemo(() => {
    return [...nodeLessons].sort((a, b) => {
      if (a.order_key && b.order_key && a.order_key !== b.order_key) return a.order_key.localeCompare(b.order_key);
      const ta = a.created_at ? new Date(a.created_at).getTime() : 0;
      const tb = b.created_at ? new Date(b.created_at).getTime() : 0;
      return ta - tb;
    });
  }, [nodeLessons]);

  const flow = useMemo(() => {
    const arr = lesson?.content_flow || [];
    return [...arr].sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
  }, [lesson?.content_flow]);

  // 计算上一/下一课时（基于 node lessons 列表）
  const { prevLesson, nextLesson } = useMemo(() => {
    const idx = sortedLessons.findIndex((x) => x.lesson_id === lessonId);
    return {
      prevLesson: idx > 0 ? sortedLessons[idx - 1] : undefined,
      nextLesson: idx >= 0 && idx < sortedLessons.length - 1 ? sortedLessons[idx + 1] : undefined,
    };
  }, [sortedLessons, lessonId]);

  useEffect(() => {
    // lesson 切换时重置单段游标
    setCursor(0);
  }, [lessonId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载课时中...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-[#eef]">
        <Container size="lg-plus">
          <div className="py-8">
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm" onClick={() => router.push(`/courses/${courseId}`)}>返回课程</Button>
              <div className="ml-auto flex items-center gap-2">
                <Button variant={showAll ? "secondary" : "default"} size="sm" onClick={() => setShowAll((v) => !v)}>
                  {showAll ? "逐段模式" : "一屏展示所有内容"}
                </Button>
              </div>
            </div>
            <h1 className="text-2xl font-semibold mt-4">{lesson?.title || "课时"}</h1>
            {lesson?.description && (
              <div className="mt-2 text-gray-600 whitespace-pre-wrap">{lesson.description}</div>
            )}
            <div className="mt-3 flex gap-4 text-sm text-gray-700">
              <span>难度：{lesson?.difficulty ?? 5}</span>
              <span>预计时长：{lesson?.estimated_minutes ?? 10} 分钟</span>
            </div>
            <div className="mt-3 flex gap-2 text-sm text-gray-700">
              {prevLesson && (
                <Button variant="secondary" size="sm" onClick={() => router.push(`/courses/${courseId}/${prevLesson.lesson_id}`)}>
                  上一课时
                </Button>
              )}
              {nextLesson && (
                <Button size="sm" onClick={() => router.push(`/courses/${courseId}/${nextLesson.lesson_id}`)}>
                  下一课时
                </Button>
              )}
            </div>
          </div>
        </Container>
      </div>

      <Container size="lg-plus">
        {flow.length === 0 ? (
          <div className="py-6 text-gray-500">此课时暂无内容。</div>
        ) : showAll ? (
          <div className="py-6 space-y-6">
            {flow.map((block, i) => (
              <div key={i} className="bg-white rounded-md border p-4">
                <AtomicContentRenderer block={block} />
              </div>
            ))}
          </div>
        ) : (
          <div className="py-6 space-y-6">
            <div className="text-xs text-gray-500">进度：{cursor + 1} / {flow.length}</div>
            <div className="bg-white rounded-md border p-4">
              <AtomicContentRenderer block={flow[cursor]} />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="secondary" size="sm" onClick={() => setCursor((c) => Math.max(0, c - 1))} disabled={cursor <= 0}>上一段</Button>
              <Button size="sm" onClick={() => setCursor((c) => Math.min(flow.length - 1, c + 1))} disabled={cursor >= flow.length - 1}>下一段</Button>
            </div>
          </div>
        )}
      </Container>
    </div>
  );
}
