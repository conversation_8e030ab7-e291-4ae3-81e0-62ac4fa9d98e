"use client";

import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Container } from "@/components/ui/container";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { SingleSelect } from "@/components/ui/single-select";
import { SearchableMultiSelect } from "@/components/ui/searchable-multi-select";
import TabContentLayout from "@/feature/courses/components/TabContentLayout";
import { Search as SearchIcon, ChevronDown } from "lucide-react";
import { getCoursesByTab } from "@/service/course.service";
import { MockApiCourseData } from "@/types/mockapi";

const getLevelOptions = (t: any) => [
  { value: "beginner", label: t("courses.filters.level.beginner") },
  { value: "intermediate", label: t("courses.filters.level.intermediate") },
  { value: "advanced", label: t("courses.filters.level.advanced") },
];

const getTabItems = (t: any) => [
  { id: "all", label: t("courses.tabs.all") },
  { id: "courses", label: t("courses.tabs.courses") },
  { id: "cloud-labs", label: t("courses.tabs.cloudLabs") },
  { id: "projects", label: t("courses.tabs.projects") },
  { id: "paths", label: t("courses.tabs.paths") },
  { id: "assessments", label: t("courses.tabs.assessments") },
  { id: "mock-interviews", label: t("courses.tabs.mockInterviews") },
];

const topicsOptions = [
  { value: "system-design", label: "System Design" },
  { value: "coding-patterns", label: "Coding Patterns" },
  { value: "machine-learning", label: "Machine Learning" },
  { value: "aws", label: "AWS" },
  { value: "generative-ai", label: "Generative AI" },
  { value: "interview", label: "Interview Prep" },
  { value: "algorithms", label: "Algorithms" },
  { value: "architecture", label: "Architecture" },
  { value: "behavioral", label: "Behavioral" },
  { value: "advanced", label: "Advanced" },
  { value: "api-design", label: "API Design" },
  { value: "programming", label: "Programming" },
  { value: "kubernetes", label: "Kubernetes" },
  { value: "microservices", label: "Microservices" },
  { value: "database", label: "Database" },
  { value: "cloud-computing", label: "Cloud Computing" },
];

const mockInterviewOptions = [
  { value: "technical", label: "Technical" },
  { value: "behavioral", label: "Behavioral" },
  { value: "system-design", label: "System Design" },
  { value: "coding", label: "Coding" },
  { value: "leadership", label: "Leadership" },
  { value: "product-management", label: "Product Management" },
  { value: "data-science", label: "Data Science" },
  { value: "frontend", label: "Frontend" },
  { value: "backend", label: "Backend" },
  { value: "full-stack", label: "Full Stack" },
];

export default function CoursesPage() {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [filterBySelected, setFilterBySelected] = useState("");
  const [selectedLevel, setSelectedLevel] = useState("");
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [selectedMockInterviewTopics, setSelectedMockInterviewTopics] =
    useState<string[]>([]);

  // 获取翻译后的选项
  const levelOptions = getLevelOptions(t);
  const tabItems = getTabItems(t);

  // 课程数据状态
  const [courses, setCourses] = useState<MockApiCourseData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取课程数据
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        const courseData = await getCoursesByTab(activeTab);
        setCourses(courseData);
      } catch (err) {
        setError("Failed to fetch courses");
        console.error("Error fetching courses:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, [activeTab]); // 当 activeTab 改变时重新获取数据

  const filteredCourses = courses.filter((course) => {
    const matchesSearch =
      course.courses[0]?.title
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      course.courses[0]?.summary
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      course.courses[0]?.brief_summary
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

    const matchesLevel =
      !selectedLevel ||
      course.tags.some((tag: string) =>
        tag.toLowerCase().includes(selectedLevel.toLowerCase())
      ) ||
      course.learner_tags.some((tag: string) =>
        tag.toLowerCase().includes(selectedLevel.toLowerCase())
      );

    const matchesTopics =
      selectedTopics.length === 0 ||
      selectedTopics.some(
        (topic) =>
          course.tags.some((tag: string) =>
            tag.toLowerCase().includes(topic.toLowerCase())
          ) ||
          course.skills.some((skill: string) =>
            skill.toLowerCase().includes(topic.toLowerCase())
          )
      );

    const matchesMockInterviewTypes =
      selectedMockInterviewTopics.length === 0 ||
      selectedMockInterviewTopics.some((type) =>
        course.tags.some((tag: string) =>
          tag.toLowerCase().includes(type.toLowerCase())
        )
      );

    return (
      matchesSearch &&
      matchesLevel &&
      matchesTopics &&
      matchesMockInterviewTypes
    );
  });

  const clearAllFilters = () => {
    setFilterBySelected("");
    setSelectedLevel("");
    setSelectedTopics([]);
    setSelectedMockInterviewTopics([]);
    setSearchTerm("");
  };

  return (
    <div className="bg-[#f9fafb] min-h-screen">
      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">{t("common.status.loadingCourses")}</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 m-4">
          <div className="flex">
            <div className="text-red-600">
              <p className="font-medium">
                {t("common.status.errorLoadingCourses")}
              </p>
              <p className="text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {!loading && (
        <>
          {/* Full Width Header */}
          <div className="bg-white border-b border-gray-200">
            <Container size="xl">
              {/* Header with Search */}
              <div className="pt-8 pb-4">
                {/* Search Bar */}
                <div className="">
                  <div className="relative">
                    <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <Input
                      placeholder={t("courses.search.placeholder")}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 py-3 rounded-lg border-gray-200 bg-white hover:border-gray-300 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>
            </Container>

            {/* Tab Navigation - Full Width */}
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <div className="border-b border-gray-200 sticky top-[64px] bg-white pt-3 pb-[2px]">
                <Container size="xl">
                  <TabsList className="flex w-full justify-start p-1 py-2 bg-transparent border-b-0">
                    {tabItems.map((tab) => (
                      <TabsTrigger
                        key={tab.id}
                        value={tab.id}
                        className="px-4 py-3 text-sm font-medium border-b-2 border-transparent data-[state=active]:border-blue-600 data-[state=active]:text-blue-600 data-[state=active]:bg-transparent hover:text-blue-600 transition-colors rounded-none bg-transparent whitespace-nowrap"
                      >
                        {tab.label}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </Container>
              </div>

              {/* Filter Bar - Full Width */}
              <div className="pt-4 pb-2 border-b border-gray-200  sticky top-[119px] bg-white">
                <Container size="xl">
                  <div className="flex gap-4 flex-wrap items-center">
                    {/* Clear All Button */}
                    <Button
                      variant="ghost"
                      onClick={clearAllFilters}
                      className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 px-3 py-1 h-auto text-sm"
                    >
                      {t("courses.filters.clear")}
                    </Button>

                    {/* Filter By Dropdown */}
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={`w-auto min-w-[100px] h-8 text-sm border-gray-300 rounded-md justify-between ${
                            filterBySelected
                              ? "border-blue-500 bg-blue-50 text-blue-700"
                              : ""
                          }`}
                        >
                          {t("courses.filters.title")}
                          <ChevronDown className="ml-2 h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-48 p-2" align="start">
                        <div className="space-y-1">
                          {[t("courses.filters.newOnly")].map((option) => (
                            <div
                              key={option}
                              className="flex items-center space-x-2 p-2 hover:bg-gray-50 cursor-pointer rounded"
                              onClick={() =>
                                setFilterBySelected(
                                  option === filterBySelected ? "" : option
                                )
                              }
                            >
                              <div
                                className={`w-4 h-4 border border-gray-300 rounded-full flex items-center justify-center ${
                                  filterBySelected === option
                                    ? "border-blue-600"
                                    : ""
                                }`}
                              >
                                {filterBySelected === option && (
                                  <div className="w-2 h-2 bg-blue-600 rounded-full" />
                                )}
                              </div>
                              <span className="text-sm">{option}</span>
                            </div>
                          ))}
                        </div>
                      </PopoverContent>
                    </Popover>

                    {/* Skill Level Filter - Single Select with Radio */}
                    <SingleSelect
                      options={levelOptions}
                      selectedValue={selectedLevel}
                      onSelectionChange={setSelectedLevel}
                      placeholder={t("courses.filters.level.title")}
                      label={t("courses.filters.level.title")}
                      className="min-w-[120px]"
                    />

                    {/* Topics Filter - Multi Select with Search */}
                    <SearchableMultiSelect
                      options={topicsOptions}
                      selectedValues={selectedTopics}
                      onSelectionChange={setSelectedTopics}
                      placeholder={t("courses.filters.category.title")}
                      searchPlaceholder={t("courses.search.placeholder")}
                      className="min-w-[120px]"
                      label={t("courses.filters.category.title")}
                    />

                    {/* Mock Interviews Filter - Multi Select with Search */}
                    <SearchableMultiSelect
                      options={mockInterviewOptions}
                      selectedValues={selectedMockInterviewTopics}
                      onSelectionChange={setSelectedMockInterviewTopics}
                      placeholder="Mock Interviews"
                      searchPlaceholder="Search interview types"
                      className="min-w-[140px]"
                      label="Mock Interviews"
                    />
                  </div>
                </Container>
              </div>

              {/* Tab Content */}
              <TabsContent value="all" className="bg-[#f9fafb]">
                <TabContentLayout
                  activeTab="all"
                  filteredCourses={filteredCourses}
                  showSearchAI={true}
                  className="py-0"
                />
              </TabsContent>

              <TabsContent value="courses" className="bg-[#f9fafb]">
                <TabContentLayout
                  activeTab="courses"
                  filteredCourses={filteredCourses}
                  showSearchAI={true}
                  className="py-0"
                />
              </TabsContent>

              <TabsContent value="cloud-labs" className="bg-[#f9fafb]">
                <TabContentLayout
                  activeTab="cloud-labs"
                  filteredCourses={filteredCourses}
                  showSearchAI={true}
                  className="py-0"
                />
              </TabsContent>

              <TabsContent value="projects" className="bg-[#f9fafb]">
                <TabContentLayout
                  activeTab="projects"
                  filteredCourses={filteredCourses}
                  showSearchAI={true}
                  className="py-0"
                />
              </TabsContent>

              <TabsContent value="paths" className="bg-[#f9fafb]">
                <TabContentLayout
                  activeTab="paths"
                  filteredCourses={filteredCourses}
                  showSearchAI={true}
                  className="py-0"
                />
              </TabsContent>

              <TabsContent value="assessments" className="bg-[#f9fafb]">
                <TabContentLayout
                  activeTab="assessments"
                  filteredCourses={filteredCourses}
                  showSearchAI={true}
                  className="py-0"
                />
              </TabsContent>

              <TabsContent value="mock-interviews" className="bg-[#f9fafb]">
                <TabContentLayout
                  activeTab="mock-interviews"
                  filteredCourses={filteredCourses}
                  showSearchAI={true}
                  className="py-0"
                />
              </TabsContent>
            </Tabs>
          </div>
        </>
      )}
    </div>
  );
}
