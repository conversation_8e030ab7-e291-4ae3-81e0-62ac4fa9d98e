/*
 * @Description: In Progress Tab Content - Currently learning courses
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ITitleIcon } from "@/components/ui/i-title-icon";
import { Input } from "@/components/ui/input";
import FluxIcon from "@/components/fluxIcon";
import SearchNotFound from "@/components/common/search-not-found";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SearchIcon, ClockIcon, MoreHorizontalIcon } from "lucide-react";
import { useRouter } from "next/navigation";

const inProgressCourses = [
  {
    id: "1",
    title: "React Deep Dive: From Beginner to Advanced",
    progress: 3,
    timeLeft: "16 hours 10 minutes left",
    category: "COURSE",
  },
];

export default function InProgressTabContent() {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = React.useState("");
  const router = useRouter();

  const filteredCourses = inProgressCourses.filter((course) =>
    course.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header with Title and Search */}
      <div className="flex items-center justify-between">
        <ITitleIcon
          icon={<FluxIcon name="clipboard" width={20} height={20} />}
          title={t("learn.inProgress.title")}
          backgroundColor="#eef"
          iconColor="#5553ff"
          titleVariant="h4"
          titleClassName="font-semibold text-gray-900"
        />

        <div className="relative w-80">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder={t("learn.search.placeholder")}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Course Cards */}
      <div className="space-y-4">
        {filteredCourses.map((course) => (
          <Card
            key={course.id}
            className="rounded-lg border border-gray-200 hover:shadow-sm transition-shadow duration-200 relative"
          >
            <CardContent className="p-6">
              <div className="space-y-4 relative">
                {/* Top row - Course category badge and more options */}
                <div className="flex items-center justify-between min-h-[32px]">
                  <span className="inline-block px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full">
                    {course.category}
                  </span>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-2 h-8 w-8 flex-shrink-0"
                      >
                        <MoreHorizontalIcon className="h-4 w-4 text-gray-400" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      side="bottom"
                      sideOffset={4}
                      className="min-w-[120px]"
                      avoidCollisions={true}
                      collisionPadding={8}
                    >
                      <DropdownMenuItem className="text-red-600 cursor-pointer">
                        Remove
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                {/* Second row - Course info and Continue Learning button */}
                <div className="flex items-start justify-between">
                  {/* Left side - Title and progress */}
                  <div className="mr-6">
                    {/* Course title */}
                    <Typography
                      variant="h6"
                      className="font-semibold text-gray-900 mb-3"
                    >
                      {course.title}
                    </Typography>

                    {/* Progress bar with info */}
                    <div className="flex items-center gap-3 w-full">
                      <div className="relative h-2 w-[25rem] overflow-hidden rounded-full bg-gray-200">
                        <div
                          className="h-full transition-all duration-300 ease-in-out rounded-full"
                          style={{
                            width: `${course.progress}%`,
                            backgroundColor: "#1f6b39",
                          }}
                        />
                      </div>
                      <Typography
                        variant="small"
                        className="text-gray-600 font-medium"
                      >
                        {course.progress}%
                      </Typography>
                      <div className="flex items-center gap-1 text-gray-500">
                        <ClockIcon className="h-3 w-3" />
                        <Typography variant="small" className="text-xs">
                          {course.timeLeft}
                        </Typography>
                      </div>
                    </div>
                  </div>

                  {/* Right side - Continue Learning button */}
                  <Button
                    variant="outline"
                    className="px-6 py-2 text-blue-600 border-blue-600 hover:bg-blue-50"
                  >
                    Continue Learning
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredCourses.length === 0 && (
        <>
          {searchQuery ? (
            <SearchNotFound
              searchQuery={searchQuery}
              onClearSearch={() => setSearchQuery("")}
            />
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">📖</div>
              <Typography variant="h5" className="text-gray-600 mb-2">
                {t("learn.inProgress.empty.title")}
              </Typography>
              <Typography variant="p" className="text-gray-500 mb-6">
                {t("learn.inProgress.empty.description")}
              </Typography>
              <Button
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={() => router.push("/explore")}
              >
                {t("learn.inProgress.empty.button")}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
