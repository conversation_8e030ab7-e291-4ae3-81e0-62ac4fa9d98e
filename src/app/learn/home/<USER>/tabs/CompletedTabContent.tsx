/*
 * @Description: Completed Tab Content - Finished courses and achievements
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ITitleIcon } from "@/components/ui/i-title-icon";
import { Input } from "@/components/ui/input";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import SearchNotFound from "@/components/common/search-not-found";
import FluxIcon from "@/components/fluxIcon";
import { MagnifyingGlassIcon as SearchIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";

// 获取已完成课程数据的函数
const getCompletedCourses = (t: any) => [
  {
    id: "1",
    title: t("learn.sampleCourses.javascriptFundamentals.title"),
    description: t("learn.sampleCourses.javascriptFundamentals.description"),
    duration: "25 hrs",
    difficulty: "Beginner",
    bookmarked: false,
  },
  {
    id: "2",
    title: t("learn.sampleCourses.htmlCssEssentials.title"),
    description: t("learn.sampleCourses.htmlCssEssentials.description"),
    duration: "18 hrs",
    difficulty: "Beginner",
    bookmarked: true,
  },
  {
    id: "3",
    title: t("learn.sampleCourses.gitVersionControl.title"),
    description: t("learn.sampleCourses.gitVersionControl.description"),
    duration: "12 hrs",
    difficulty: "Intermediate",
    bookmarked: false,
  },
  {
    id: "4",
    title: "React.js Complete Guide",
    description:
      "Build modern web applications with React including hooks, context, state management, and performance optimization.",
    duration: "45 hrs",
    difficulty: "Intermediate",
    bookmarked: true,
  },
  {
    id: "5",
    title: "Node.js Backend Development",
    description:
      "Create scalable backend applications with Node.js, Express, and MongoDB including authentication and API design.",
    duration: "38 hrs",
    difficulty: "Advanced",
    bookmarked: false,
  },
  {
    id: "6",
    title: "Python for Data Science",
    description:
      "Learn Python programming for data analysis, visualization, and machine learning with pandas, numpy, and scikit-learn.",
    duration: "52 hrs",
    difficulty: "Intermediate",
    bookmarked: true,
  },
  {
    id: "7",
    title: "AWS Cloud Fundamentals",
    description:
      "Master Amazon Web Services cloud computing including EC2, S3, Lambda, and deployment strategies.",
    duration: "35 hrs",
    difficulty: "Advanced",
    bookmarked: false,
  },
  {
    id: "8",
    title: "Database Design & SQL",
    description:
      "Design efficient databases and write complex SQL queries including joins, subqueries, and performance optimization.",
    duration: "28 hrs",
    difficulty: "Intermediate",
    bookmarked: false,
  },
];

export default function CompletedTabContent() {
  const { t } = useTranslation();
  const [showAllCourses, setShowAllCourses] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const router = useRouter();

  // 获取已完成课程数据
  const completedCoursesData = getCompletedCourses(t);

  const filteredCourses = completedCoursesData.filter((course) =>
    course.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleShowMore = () => {
    setShowAllCourses(true);
  };

  return (
    <div className="space-y-6">
      {/* Header with Title and Search */}
      <div className="flex items-center justify-between">
        <ITitleIcon
          icon={<FluxIcon name="completed" width={20} height={20} />}
          title={t("learn.completed.title")}
          backgroundColor="#eef"
          iconColor="#5553ff"
          titleVariant="h4"
          titleClassName="font-semibold text-gray-900"
        />

        <div className="relative w-80">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder={t("learn.search.placeholder")}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Completed Courses Grid - 4 columns layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {(showAllCourses ? filteredCourses : filteredCourses.slice(0, 4)).map(
          (course) => (
            <SimpleCourseCard
              key={course.id}
              course={{
                id: course.id,
                title: course.title,
                description: course.description,
                duration: course.duration,
                difficulty: course.difficulty,
                bookmarked: course.bookmarked,
              }}
              onClick={() => console.log("Course clicked:", course.id)}
            />
          )
        )}
      </div>

      {/* Show More Button - Only show if not all courses are displayed */}
      {!showAllCourses && filteredCourses.length > 4 && (
        <div className="text-center">
          <Button
            variant="outline"
            className="px-8 py-2 text-blue-600 border-blue-600 hover:bg-blue-50"
            onClick={handleShowMore}
          >
            Show More
          </Button>
        </div>
      )}

      {/* Empty State */}
      {filteredCourses.length === 0 && (
        <>
          {searchQuery ? (
            <SearchNotFound
              searchQuery={searchQuery}
              onClearSearch={() => setSearchQuery("")}
            />
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🎓</div>
              <Typography variant="h5" className="text-gray-600 mb-2">
                {t("learn.completed.empty.title")}
              </Typography>
              <Typography variant="p" className="text-gray-500 mb-6">
                {t("learn.completed.empty.description")}
              </Typography>
              <Button
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={() => router.push("/explore")}
              >
                {t("learn.completed.empty.button")}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
