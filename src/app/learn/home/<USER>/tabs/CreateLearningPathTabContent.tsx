/*
 * @Description: Learning Path Tab Content - Display and manage learning paths
 * @Author: Devin
 * @Date: 2025-08-12
 */
"use client";

import React, { useState, useEffect } from "react";
import { Typography } from "@/components/ui/typography";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Plus, Clock, Users, BookOpen, X } from "lucide-react";
import { useTranslation } from "react-i18next";
import { learningPathService } from "@/service/learning-paths.service";
import { LearningPath, CreateLearningPathRequest } from "@/types/openapi";
import { toast } from "sonner";

// Goal categories function to get translated categories
const getGoalCategories = (t: any): string[] => {
  const categories = t("learn.goalCategories");
  // Ensure we always return an array, even if translation fails
  return Array.isArray(categories)
    ? categories
    : [
        "Web Development",
        "Mobile Development",
        "Data Science",
        "Machine Learning",
        "DevOps",
        "Cloud Computing",
        "Cybersecurity",
        "UI/UX Design",
        "Backend Development",
        "Frontend Development",
        "Database Management",
        "Software Engineering",
      ];
};

// Learning Path Card Component
import { useRouter } from "next/navigation";

const LearningPathCard = ({ path }: { path: LearningPath }) => {
  const router = useRouter();
  const getDifficultyColor = (difficulty?: number) => {
    if (!difficulty) return "bg-gray-100 text-gray-800";
    if (difficulty <= 3) return "bg-green-100 text-green-800";
    if (difficulty <= 7) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const getDifficultyLabel = (difficulty?: number) => {
    if (!difficulty) return "Unknown";
    if (difficulty <= 3) return "Beginner";
    if (difficulty <= 7) return "Intermediate";
    return "Advanced";
  };

  return (
    <Card
      className="hover:shadow-md transition-shadow cursor-pointer"
      onClick={async () => {
        if (!path.id) return;
        try {
          const resp = await learningPathService.get(path.id);
          const latest = resp?.data;
          const latestStatus = (latest as any)?.status as string | undefined;
          if (latestStatus === 'generating') {
            toast.info('该学习路径仍在生成中，请稍后再试');
            return;
          }
        } catch (_) {
          // 忽略错误，仍然尝试进入详情页
        }
        router.push(`/learn/paths/${path.id}`);
      }}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 line-clamp-2">
              {path.title || "Untitled Learning Path"}
            </CardTitle>
            <CardDescription className="text-sm text-gray-600 mt-1 line-clamp-2">
              {path.description || "No description available"}
            </CardDescription>
          </div>
          <Badge
            className={`ml-2 text-xs ${getDifficultyColor(path.difficulty)}`}
          >
            {getDifficultyLabel(path.difficulty)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex items-center gap-4 text-sm text-gray-500">
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            <span>{path.estimated_times || 0}h</span>
          </div>
          <div className="flex items-center gap-1">
            <BookOpen className="w-4 h-4" />
            <span>{path.total_nodes || 0} modules</span>
          </div>
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            <span>{path.usage_count || 0} learners</span>
          </div>
        </div>
        {path.goal && (
          <div className="mt-3 text-sm">
            <span className="font-medium text-gray-700">Goal:</span>
            <span className="text-gray-600 ml-1">{path.goal}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default function LearningPathsTabContent() {
  const { t } = useTranslation();
  const goalCategories = getGoalCategories(t);
  const [learningPaths, setLearningPaths] = useState<LearningPath[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Form state
  const [formData, setFormData] = useState<CreateLearningPathRequest>({
    title: "",
    description: "",
    goal: "",
    goal_category: "",
    difficulty: 3,
    estimated_times: 40,
    suitable_for: [],
    learning_outcomes: [],
    is_public: false,
  });

  // Temporary input states for arrays
  const [suitableForInput, setSuitableForInput] = useState("");
  const [learningOutcomeInput, setLearningOutcomeInput] = useState("");

  // Load learning paths
  useEffect(() => {
    loadLearningPaths();
  }, []);

  const loadLearningPaths = async () => {
    try {
      setIsLoading(true);
  const response = await learningPathService.listMine({
        page: 1,
        page_size: 20,
      });
      if (response.success && response.data?.data) {
        setLearningPaths(response.data.data);
      }
    } catch (error) {
      console.error("Error loading learning paths:", error);
      toast.error("Failed to load learning paths");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions for array management
  const addSuitableFor = () => {
    if (suitableForInput.trim()) {
      setFormData((prev) => ({
        ...prev,
        suitable_for: [...(prev.suitable_for ?? []), suitableForInput.trim()],
      }));
      setSuitableForInput("");
    }
  };

  const removeSuitableFor = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      suitable_for: (prev.suitable_for ?? []).filter((_, i) => i !== index),
    }));
  };

  const addLearningOutcome = () => {
    if (learningOutcomeInput.trim()) {
      setFormData((prev) => ({
        ...prev,
        learning_outcomes: [
          ...(prev.learning_outcomes ?? []),
          learningOutcomeInput.trim(),
        ],
      }));
      setLearningOutcomeInput("");
    }
  };

  const removeLearningOutcome = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      learning_outcomes: (prev.learning_outcomes ?? []).filter(
        (_, i) => i !== index
      ),
    }));
  };

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      goal: "",
      goal_category: "",
      difficulty: 3,
      estimated_times: 40,
      suitable_for: [],
      learning_outcomes: [],
      is_public: false,
    });
    setSuitableForInput("");
    setLearningOutcomeInput("");
  };

  const handleCreatePath = async () => {
    if (!formData.title.trim() || !formData.goal.trim()) {
      toast.error(
        "Please fill in the required fields (Title and Learning Goal)"
      );
      return;
    }

    setIsCreating(true);
    try {
      // TODO: Get actual user ID from auth context
      const requestData: CreateLearningPathRequest = {
        ...formData,
      };

      const response = await learningPathService.create(requestData);
      if (response.success) {
        toast.success("Learning path created successfully!");
        resetForm();
        setIsDialogOpen(false);
        // Reload the learning paths to show the new one
        loadLearningPaths();
      } else {
        toast.error("Failed to create learning path");
      }
    } catch (error) {
      console.error("Error creating learning path:", error);
      toast.error("An error occurred while creating the learning path");
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h4" className="font-semibold text-gray-900">
            Learning Paths
          </Typography>
          <Typography variant="small" className="text-gray-600 mt-1">
            Discover and create personalized learning journeys
          </Typography>
        </div>

        {/* Create Button */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-purple-600 hover:bg-purple-700">
              <Plus className="w-4 h-4 mr-2" />
              Create Path
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                Create Learning Path
              </DialogTitle>
              <DialogDescription className="text-xs text-gray-500">
                Create a custom learning path with detailed information.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="title"
                    className="text-sm font-medium"
                    required
                  >
                    Title
                  </Label>
                  <Input
                    id="title"
                    placeholder="e.g., Complete React Developer Path"
                    value={formData.title}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        title: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="goal"
                    className="text-sm font-medium"
                    required
                  >
                    Learning Goal
                  </Label>
                  <Input
                    id="goal"
                    placeholder="e.g., Master React development"
                    value={formData.goal}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        goal: e.target.value,
                      }))
                    }
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium">
                  Description
                </Label>
                <Textarea
                  id="description"
                  placeholder="Describe what learners will achieve..."
                  value={formData.description}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="goal_category"
                    className="text-sm font-medium"
                  >
                    Category
                  </Label>
                  <Select
                    value={formData.goal_category}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, goal_category: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {goalCategories.map((category: string) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="difficulty" className="text-sm font-medium">
                    Difficulty Level (1-10)
                  </Label>
                  <Input
                    id="difficulty"
                    type="number"
                    min="1"
                    max="10"
                    value={formData.difficulty}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        difficulty: parseInt(e.target.value) || 1,
                      }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="estimated_times"
                    className="text-sm font-medium"
                  >
                    Estimated Hours
                  </Label>
                  <Input
                    id="estimated_times"
                    type="number"
                    min="1"
                    value={formData.estimated_times}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        estimated_times: parseInt(e.target.value) || 1,
                      }))
                    }
                  />
                </div>
              </div>

              {/* Suitable For */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Suitable For</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="e.g., Beginners, Developers"
                    value={suitableForInput}
                    onChange={(e) => setSuitableForInput(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && addSuitableFor()}
                  />
                  <Button type="button" onClick={addSuitableFor} size="sm">
                    Add
                  </Button>
                </div>
                {(formData.suitable_for ?? []).length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {(formData.suitable_for ?? []).map((item, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {item}
                        <X
                          className="w-3 h-3 cursor-pointer"
                          onClick={() => removeSuitableFor(index)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              {/* Learning Outcomes */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Learning Outcomes</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="e.g., Build React applications"
                    value={learningOutcomeInput}
                    onChange={(e) => setLearningOutcomeInput(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && addLearningOutcome()}
                  />
                  <Button type="button" onClick={addLearningOutcome} size="sm">
                    Add
                  </Button>
                </div>
                {(formData.learning_outcomes ?? []).length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {(formData.learning_outcomes ?? []).map((item, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {item}
                        <X
                          className="w-3 h-3 cursor-pointer"
                          onClick={() => removeLearningOutcome(index)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              {/* Public Switch */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_public"
                  checked={formData.is_public}
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({ ...prev, is_public: checked }))
                  }
                />
                <Label htmlFor="is_public" className="text-sm font-medium">
                  Make this path public
                </Label>
              </div>

              <Button
                onClick={handleCreatePath}
                disabled={
                  isCreating || !formData.title.trim() || !formData.goal.trim()
                }
                className="w-full bg-purple-600 hover:bg-purple-700"
              >
                {isCreating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    Create Path
                  </>
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Learning Paths Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-full mt-2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : learningPaths.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {learningPaths.map((path) => (
            <LearningPathCard key={path.id} path={path} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <Typography variant="h6" className="text-gray-600 mb-2">
            No learning paths yet
          </Typography>
          <Typography variant="small" className="text-gray-500 mb-4">
            Create your first learning path to get started
          </Typography>
          <Button
            onClick={() => setIsDialogOpen(true)}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Your First Path
          </Button>
        </div>
      )}
    </div>
  );
}
