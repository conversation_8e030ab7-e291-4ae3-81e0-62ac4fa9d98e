/*
 * @Description: Saved Tab Content - Bookmarked courses and resources
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ITitleIcon } from "@/components/ui/i-title-icon";
import { Input } from "@/components/ui/input";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import SearchNotFound from "@/components/common/search-not-found";
import FluxIcon from "@/components/fluxIcon";
import { MagnifyingGlassIcon as SearchIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";

const savedCourses = [
  {
    id: "1",
    title: "Advanced React Patterns and Best Practices",
    description:
      "Master advanced React concepts including hooks, context, performance optimization, and modern patterns for building scalable applications.",
    duration: "35 hrs",
    difficulty: "Advanced",
    bookmarked: true,
  },
  {
    id: "2",
    title: "Kubernetes for Developers",
    description:
      "Learn container orchestration with Kubernetes. Deploy, scale, and manage containerized applications in production environments.",
    duration: "28 hrs",
    difficulty: "Intermediate",
    bookmarked: true,
  },
  {
    id: "3",
    title: "Data Structures and Algorithms in Python",
    description:
      "Comprehensive guide to data structures and algorithms with Python implementations. Perfect for coding interviews and competitive programming.",
    duration: "42 hrs",
    difficulty: "Intermediate",
    bookmarked: true,
  },
  {
    id: "4",
    title: "AWS Solutions Architect Path",
    description:
      "Complete learning path to become an AWS Solutions Architect. Covers core services, architecture patterns, and best practices.",
    duration: "65 hrs",
    difficulty: "Advanced",
    bookmarked: true,
  },
  {
    id: "5",
    title: "Machine Learning Fundamentals",
    description:
      "Learn the basics of machine learning including supervised and unsupervised learning, neural networks, and practical applications.",
    duration: "48 hrs",
    difficulty: "Intermediate",
    bookmarked: true,
  },
  {
    id: "6",
    title: "Full Stack Web Development",
    description:
      "Complete guide to full stack development covering frontend, backend, databases, and deployment strategies.",
    duration: "72 hrs",
    difficulty: "Beginner",
    bookmarked: true,
  },
  {
    id: "7",
    title: "System Design Interview Preparation",
    description:
      "Master system design concepts and patterns for technical interviews at top tech companies.",
    duration: "38 hrs",
    difficulty: "Advanced",
    bookmarked: true,
  },
  {
    id: "8",
    title: "DevOps and CI/CD Pipeline",
    description:
      "Learn DevOps practices, continuous integration, continuous deployment, and infrastructure as code.",
    duration: "45 hrs",
    difficulty: "Intermediate",
    bookmarked: true,
  },
];

export default function SavedTabContent() {
  const { t } = useTranslation();
  const [showAllCourses, setShowAllCourses] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const router = useRouter();

  const filteredCourses = savedCourses.filter((course) =>
    course.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleShowMore = () => {
    setShowAllCourses(true);
  };

  return (
    <div className="space-y-6">
      {/* Header with Title and Search */}
      <div className="flex items-center justify-between">
        <ITitleIcon
          icon={<FluxIcon name="save" width={20} height={20} />}
          title="Saved for Later"
          backgroundColor="#eef"
          iconColor="#5553ff"
          titleVariant="h4"
          titleClassName="font-semibold text-gray-900"
        />

        <div className="relative w-80">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search Saved Courses"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Saved Courses Grid - 4 columns layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {(showAllCourses ? filteredCourses : filteredCourses.slice(0, 4)).map(
          (course) => (
            <SimpleCourseCard
              key={course.id}
              course={{
                id: course.id,
                title: course.title,
                description: course.description,
                duration: course.duration,
                difficulty: course.difficulty,
                bookmarked: course.bookmarked,
              }}
              onClick={() => console.log("Course clicked:", course.id)}
            />
          )
        )}
      </div>

      {/* Show More Button - Only show if not all courses are displayed */}
      {!showAllCourses && filteredCourses.length > 4 && (
        <div className="text-center">
          <Button
            variant="outline"
            className="px-8 py-2 text-blue-600 border-blue-600 hover:bg-blue-50"
            onClick={handleShowMore}
          >
            Show More
          </Button>
        </div>
      )}

      {/* Empty State */}
      {filteredCourses.length === 0 && (
        <>
          {searchQuery ? (
            <SearchNotFound
              searchQuery={searchQuery}
              onClearSearch={() => setSearchQuery("")}
            />
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🔖</div>
              <Typography variant="h5" className="text-gray-600 mb-2">
                {t("learn.favorites.empty.title")}
              </Typography>
              <Typography variant="p" className="text-gray-500 mb-6">
                {t("learn.favorites.empty.description")}
              </Typography>
              <Button
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={() => router.push("/explore")}
              >
                {t("learn.favorites.empty.button")}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
