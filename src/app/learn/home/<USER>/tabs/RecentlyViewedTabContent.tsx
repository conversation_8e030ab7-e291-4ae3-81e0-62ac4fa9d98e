/*
 * @Description: Recently Viewed Tab Content - Recently browsed courses and resources
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ITitleIcon } from "@/components/ui/i-title-icon";
import { Input } from "@/components/ui/input";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import SearchNotFound from "@/components/common/search-not-found";
import FluxIcon from "@/components/fluxIcon";
import { MagnifyingGlassIcon as SearchIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";

const recentlyViewedCourses = [
  {
    id: "1",
    title: "Advanced React Patterns and Best Practices",
    description:
      "Master advanced React concepts including hooks, context, performance optimization, and modern patterns for building scalable applications.",
    duration: "35 hrs",
    difficulty: "Advanced",
    bookmarked: false,
  },
  {
    id: "2",
    title: "Kubernetes for Developers",
    description:
      "Learn container orchestration with Kubernetes. Deploy, scale, and manage containerized applications in production environments.",
    duration: "28 hrs",
    difficulty: "Intermediate",
    bookmarked: true,
  },
  {
    id: "3",
    title: "System Design Interview Preparation",
    description:
      "Comprehensive guide to system design interviews with real-world examples and practice problems.",
    duration: "42 hrs",
    difficulty: "Advanced",
    bookmarked: false,
  },
  {
    id: "4",
    title: "Full Stack Developer Path",
    description:
      "Complete learning path to become a full-stack developer. Covers frontend, backend, databases, and deployment.",
    duration: "120 hrs",
    difficulty: "Beginner",
    bookmarked: false,
  },
  {
    id: "5",
    title: "Data Structures and Algorithms Masterclass",
    description:
      "Deep dive into data structures and algorithms with implementations in multiple programming languages.",
    duration: "55 hrs",
    difficulty: "Intermediate",
    bookmarked: true,
  },
  {
    id: "6",
    title: "AWS Cloud Practitioner Certification",
    description:
      "Prepare for AWS Cloud Practitioner certification with hands-on labs and practice exams.",
    duration: "25 hrs",
    difficulty: "Beginner",
    bookmarked: false,
  },
  {
    id: "7",
    title: "Machine Learning with Python",
    description:
      "Learn machine learning fundamentals and build ML models using Python, scikit-learn, and TensorFlow.",
    duration: "48 hrs",
    difficulty: "Intermediate",
    bookmarked: true,
  },
  {
    id: "8",
    title: "DevOps Engineering Complete Course",
    description:
      "Master DevOps practices including CI/CD, containerization, monitoring, and infrastructure as code.",
    duration: "62 hrs",
    difficulty: "Advanced",
    bookmarked: false,
  },
];

export default function RecentlyViewedTabContent() {
  const { t } = useTranslation();
  const [showAllCourses, setShowAllCourses] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const router = useRouter();

  const filteredCourses = recentlyViewedCourses.filter((course) =>
    course.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleShowMore = () => {
    setShowAllCourses(true);
  };

  return (
    <div className="space-y-6">
      {/* Header with Title and Search */}
      <div className="flex items-center justify-between">
        <ITitleIcon
          icon={<FluxIcon name="recent" width={20} height={20} />}
          title={t("learn.recentlyViewed.title")}
          backgroundColor="#eef"
          iconColor="#5553ff"
          titleVariant="h4"
          titleClassName="font-semibold text-gray-900"
        />

        <div className="relative w-80">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder={t("learn.search.placeholder")}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Recently Viewed Courses Grid - 4 columns layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {(showAllCourses ? filteredCourses : filteredCourses.slice(0, 4)).map(
          (course) => (
            <SimpleCourseCard
              key={course.id}
              course={{
                id: course.id,
                title: course.title,
                description: course.description,
                duration: course.duration,
                difficulty: course.difficulty,
                bookmarked: course.bookmarked,
              }}
              onClick={() => console.log("Course clicked:", course.id)}
            />
          )
        )}
      </div>

      {/* Show More Button - Only show if not all courses are displayed */}
      {!showAllCourses && filteredCourses.length > 4 && (
        <div className="text-center">
          <Button
            variant="outline"
            className="px-8 py-2 text-blue-600 border-blue-600 hover:bg-blue-50"
            onClick={handleShowMore}
          >
            Show More
          </Button>
        </div>
      )}

      {/* Empty State */}
      {filteredCourses.length === 0 && (
        <>
          {searchQuery ? (
            <SearchNotFound
              searchQuery={searchQuery}
              onClearSearch={() => setSearchQuery("")}
            />
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">👁️</div>
              <Typography variant="h5" className="text-gray-600 mb-2">
                {t("learn.recentlyViewed.empty.title")}
              </Typography>
              <Typography variant="p" className="text-gray-500 mb-6">
                {t("learn.recentlyViewed.empty.description")}
              </Typography>
              <Button
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={() => router.push("/explore")}
              >
                {t("learn.recentlyViewed.empty.button")}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
