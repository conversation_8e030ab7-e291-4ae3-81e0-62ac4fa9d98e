/*
 * @Description: Home Tab Content - Recommended courses and learning paths
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";
import { Button } from "@/components/ui/button";
import Carousel from "@/components/ui/carousel";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import FluxIcon from "@/components/fluxIcon";
import { MessageSquare, Cloud, FolderOpen } from "lucide-react";
import {
  getCourseUrl,
  getMostPopularPointers,
  getNewAdditions,
} from "@/mockdata/maindata";
import { formatDuration } from "@/lib/utils";

// 获取推荐课程 - 使用最新添加的课程作为推荐
const recommendedCourses = getNewAdditions(3);

// 获取最受欢迎的课程
const mostPopularCourses = getMostPopularPointers(8);

// 获取功能列表的函数
const getFeatures = (t: any) => [
  {
    title: t("learn.features.mockInterview.title"),
    description: t("learn.features.mockInterview.description"),
    href: "/mock-interview",
    icon: <MessageSquare className="h-4 w-4" />,
    badgeColor: "bg-orange-50 text-orange-700",
  },
  {
    title: t("learn.features.cloudLabs.title"),
    description: t("learn.features.cloudLabs.description"),
    href: "/cloudlabs",
    icon: <Cloud className="h-4 w-4" />,
    badgeColor: "bg-purple-50 text-purple-700",
  },
  {
    title: t("learn.features.projects.title"),
    description: t("learn.features.projects.description"),
    href: "/projects",
    icon: <FolderOpen className="h-4 w-4" />,
    badgeColor: "bg-blue-50 text-blue-700",
  },
];

export default function HomeTabContent() {
  const { t } = useTranslation();
  const router = useRouter();
  const [showAllCourses, setShowAllCourses] = React.useState(false);

  // 获取功能列表
  const features = getFeatures(t);

  const handleExploreAll = () => {
    router.push("/explore");
  };

  const handleShowMore = () => {
    setShowAllCourses(true);
  };

  return (
    <div className="space-y-8">
      {/* Kickstart Section */}
      <div
        className="p-6 mb-8"
        style={{
          backgroundColor: "#f8f8ff",
          border: "0.0625rem solid #cccbff",
          borderRadius: "0.25rem",
        }}
      >
        <div className="flex flex-col lg:flex-row items-center lg:items-center justify-between gap-6">
          <div className="flex-1 my-6 mx-6">
            <Typography
              variant="h4"
              className="font-bold text-gray-900 mb-1 text-2xl"
            >
              {t("learn.kickstart.title")}
            </Typography>
            <Typography
              variant="small"
              className="text-gray-600 mb-3 max-w-2xl text-sm"
            >
              {t("learn.kickstart.description")}
            </Typography>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
              {t("learn.kickstart.button")}
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row gap-6 lg:gap-8">
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full mb-2 mx-auto">
                <span className="text-xl">💰</span>
              </div>
              <Typography variant="small" className="text-gray-500 mb-1">
                Earn on average
              </Typography>
              <Typography variant="h5" className="font-bold text-gray-900">
                $ 120K
              </Typography>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full mb-2 mx-auto">
                <span className="text-xl">⏱️</span>
              </div>
              <Typography variant="small" className="text-gray-500 mb-1">
                Complete in
              </Typography>
              <Typography variant="h5" className="font-bold text-gray-900">
                8 Weeks
              </Typography>
            </div>
          </div>
        </div>
      </div>

      {/* Section Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <div
              className="flex justify-center items-center rounded-full"
              style={{
                width: "2.5rem",
                height: "2.5rem",
                backgroundColor: "#eef",
              }}
            >
              <FluxIcon
                name="most"
                width={20}
                height={20}
                className="w-5 h-5"
                style={{ color: "#5553ff" }}
              />
            </div>
            <Typography variant="h3" className="font-bold text-gray-900">
              Recommended For You
            </Typography>
          </div>
        </div>
        <Button
          variant="outline"
          className="text-blue-600 border-blue-600 hover:bg-blue-50"
        >
          Learning Preferences
        </Button>
      </div>

      {/* Course Carousel */}
      <Carousel itemsPerSlide={4} showArrows={true} showIndicators={true}>
        {recommendedCourses.map((course) => (
          <SimpleCourseCard
            key={course.id}
            course={{
              id: course.id,
              title: course.title,
              description: course.description,
              duration: formatDuration(course.duration),
              difficulty:
                course.level.charAt(0).toUpperCase() + course.level.slice(1),
              bookmarked: false,
            }}
            onClick={() => {
              const courseUrl = getCourseUrl(course);
              router.push(courseUrl);
            }}
          />
        ))}
      </Carousel>

      {/* View More Button */}
      <div className="text-center">
        <Button
          className="px-8 bg-[#5553ff] text-white"
          onClick={handleExploreAll}
        >
          {t("learn.sections.exploreAll")}
        </Button>
      </div>

      {/* Most Popular Courses Section */}
      <div className="py-2">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <div
              className="flex justify-center items-center rounded-full"
              style={{
                width: "2.5rem",
                height: "2.5rem",
                backgroundColor: "#eef",
              }}
            >
              <FluxIcon
                name="most"
                width={20}
                height={20}
                className="w-5 h-5"
                style={{ color: "#5553ff" }}
              />
            </div>
            <Typography variant="h3" className="font-bold text-gray-900">
              {t("learn.sections.mostPopularCourses")}
            </Typography>
          </div>
        </div>

        {/* Grid Layout - Show courses based on state */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {(showAllCourses
            ? mostPopularCourses
            : mostPopularCourses.slice(0, 4)
          ).map((course) => (
            <SimpleCourseCard
              key={course.id}
              course={{
                id: course.id,
                title: course.title,
                description: course.description,
                duration: formatDuration(course.duration),
                difficulty:
                  course.level.charAt(0).toUpperCase() + course.level.slice(1),
                bookmarked: false,
              }}
              onClick={() => {
                const courseUrl = getCourseUrl(course);
                router.push(courseUrl);
              }}
            />
          ))}
        </div>

        {/* Show More Button - Only show if not all courses are displayed */}
        {!showAllCourses && mostPopularCourses.length > 4 && (
          <div className="text-center">
            <Button
              variant="outline"
              className="px-8 py-2 text-blue-600 border-blue-600 hover:bg-blue-50"
              onClick={handleShowMore}
            >
              {t("learn.sections.showMore")}
            </Button>
          </div>
        )}
      </div>
      {/* Discover More Features Section */}
      <div className="py-8">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <div
              className="flex justify-center items-center rounded-full"
              style={{
                width: "2.5rem",
                height: "2.5rem",
                backgroundColor: "#eef",
              }}
            >
              <FluxIcon
                name="discover"
                width={20}
                height={20}
                className="w-5 h-5"
                style={{ color: "#5553ff" }}
              />
            </div>
            <Typography variant="h3" className="font-bold text-gray-900">
              {t("learn.sections.discoverMoreFeatures")}
            </Typography>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Link key={index} href={feature.href} className="no-underline">
              <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg hover:border-gray-300 transition-all duration-200 cursor-pointer group h-full">
                <div
                  className={`flex flex-row justify-center items-center text-sm leading-6 rounded-full h-4 max-w-max px-3 py-4 mb-1 ${feature.badgeColor}`}
                >
                  <div className="mr-2">{feature.icon}</div>
                  <div className="text-xs font-medium">{feature.title}</div>
                </div>

                {/* Description */}
                <div className="text-gray-600 text-xs leading-relaxed mt-2">
                  {feature.description}
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
      {/* Your Activity Section */}
      <div>
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <div
              className="flex justify-center items-center rounded-full"
              style={{
                width: "2.5rem",
                height: "2.5rem",
                backgroundColor: "#eef",
              }}
            >
              <FluxIcon
                name="activity"
                width={20}
                height={20}
                className="w-5 h-5"
                style={{ color: "#5553ff" }}
              />
            </div>
            <Typography variant="h3" className="font-bold text-gray-900">
              {t("learn.sections.yourActivity")}
            </Typography>
          </div>
        </div>

        <div className="space-y-6 pb-12 ">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Learning Streak */}
            <div className="bg-[#f8f8ff] border border-gray-200 rounded-lg overflow-hidden px-8">
              <div className="flex items-center justify-between py-4 ">
                <Typography
                  variant="h6"
                  className="font-semibold text-gray-900"
                >
                  {t("learn.sections.learningStreak")}
                </Typography>
                <Button variant="ghost" className="text-blue-600 text-sm">
                  {t("learn.sections.viewAll")}
                </Button>
              </div>

              <div className="grid grid-cols-2 mb-6">
                {/* Current Streak */}
                <div className="flex items-center justify-center p-6 border border-gray-200">
                  <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full mr-3">
                    <span className="text-2xl">🔥</span>
                  </div>
                  <div>
                    <Typography
                      variant="h5"
                      className="font-bold text-gray-900 mb-1"
                    >
                      1 day
                    </Typography>
                    <Typography variant="small" className="text-gray-500">
                      Current Streak
                    </Typography>
                  </div>
                </div>

                {/* Longest Streak */}
                <div className="flex items-center justify-center p-6 border">
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mr-3">
                    <span className="text-2xl">🏆</span>
                  </div>
                  <div>
                    <Typography
                      variant="h5"
                      className="font-bold text-gray-900 mb-1"
                    >
                      1 day
                    </Typography>
                    <Typography variant="small" className="text-gray-500">
                      Longest Streak
                    </Typography>
                  </div>
                </div>
              </div>
            </div>

            {/* My Learning Calendar */}
            <div className="bg-[#f8f8ff] border border-gray-200 rounded-lg p-6">
              <Typography
                variant="h6"
                className="font-semibold text-gray-900 mb-4"
              >
                My Learning Calendar
              </Typography>
              <Typography variant="small" className="text-gray-600 mb-4">
                Goal-setters are 42% more likely to succeed. Schedule one now!
                🚀
              </Typography>
              <div className="flex items-center gap-4 mt-12">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                  Explore Now
                </Button>
                <Typography
                  variant="small"
                  className="text-gray-500 mt-2 text-xs"
                >
                  Note: Create a goal by clicking on 📅 icon on any content
                  title
                </Typography>
              </div>
            </div>
          </div>
        </div>

        {/* Certification Section */}
        <div>
          <div className="mb-8">
            <div className="flex items-center gap-3 mb-2">
              <div
                className="flex justify-center items-center rounded-full"
                style={{
                  width: "2.5rem",
                  height: "2.5rem",
                  backgroundColor: "#eef",
                }}
              >
                <FluxIcon
                  name="certification"
                  width={20}
                  height={20}
                  className="w-5 h-5 text-[#5553ff]"
                />
              </div>
              <Typography variant="h3" className="font-bold text-gray-900">
                Certification
              </Typography>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center gap-6">
              <div className="flex items-center justify-center w-36 h-20 bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg">
                <span className="text-3xl text-gray-400">🔒</span>
              </div>
              <div className="flex-1">
                <Typography variant="h6" className="font-semibold  mb-2">
                  Your certificate is waiting. Don't delay!
                </Typography>
                <Typography variant="small" className="text-gray-600 mb-4">
                  Accelerate your career with a Pageflux AI Certification
                </Typography>
              </div>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                Upgrade
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
