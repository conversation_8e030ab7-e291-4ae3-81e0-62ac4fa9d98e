/*
 * @Description: My Courses Tab Content - All enrolled courses overview
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ITitleIcon } from "@/components/ui/i-title-icon";
import { Input } from "@/components/ui/input";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import SearchNotFound from "@/components/common/search-not-found";
import FluxIcon from "@/components/fluxIcon";
import { MagnifyingGlassIcon as SearchIcon } from "@heroicons/react/24/outline";
import { getCourseUrl, getMostPopularPointers } from "@/mockdata/maindata";
import { formatDuration } from "@/lib/utils";
import { useRouter } from "next/navigation";

// 获取我的课程 - 使用最受欢迎的课程作为示例
const myCourses = getMostPopularPointers(8);

export default function MyCoursesTabContent() {
  const { t } = useTranslation();
  const [showAllCourses, setShowAllCourses] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const router = useRouter();

  const filteredCourses = myCourses.filter((course) =>
    course.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleShowMore = () => {
    setShowAllCourses(true);
  };

  return (
    <div className="space-y-6">
      {/* Header with Title and Search */}
      <div className="flex items-center justify-between">
        <ITitleIcon
          icon={<FluxIcon name="courses" width={20} height={20} />}
          title="My Courses"
          backgroundColor="#eef"
          iconColor="#5553ff"
          titleVariant="h4"
          titleClassName="font-semibold text-gray-900"
        />

        <div className="relative w-80">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search My Courses"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* My Courses Grid - 4 columns layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {(showAllCourses ? filteredCourses : filteredCourses.slice(0, 4)).map(
          (course) => (
            <SimpleCourseCard
              key={course.id}
              course={{
                id: course.id,
                title: course.title,
                description: course.description,
                duration: formatDuration(course.duration),
                difficulty:
                  course.level.charAt(0).toUpperCase() + course.level.slice(1),
                bookmarked: false, // Default to false since PointerData doesn't have bookmarked
              }}
              onClick={() => {
                const courseUrl = getCourseUrl(course);
                router.push(courseUrl);
              }}
            />
          )
        )}
      </div>

      {/* Show More Button - Only show if not all courses are displayed */}
      {!showAllCourses && filteredCourses.length > 4 && (
        <div className="text-center">
          <Button
            variant="outline"
            className="px-8 py-2 text-blue-600 border-blue-600 hover:bg-blue-50"
            onClick={handleShowMore}
          >
            Show More
          </Button>
        </div>
      )}

      {/* Empty State */}
      {filteredCourses.length === 0 && (
        <>
          {searchQuery ? (
            <SearchNotFound
              searchQuery={searchQuery}
              onClearSearch={() => setSearchQuery("")}
            />
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">📚</div>
              <Typography variant="h5" className="text-gray-600 mb-2">
                {t("learn.myCourses.empty.title")}
              </Typography>
              <Typography variant="p" className="text-gray-500 mb-6">
                {t("learn.myCourses.empty.description")}
              </Typography>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                {t("learn.myCourses.empty.button")}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
