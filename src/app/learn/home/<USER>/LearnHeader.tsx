/*
 * @Description: <PERSON><PERSON> Header Component - Welcome section with user greeting
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/lib/auth-context";
import { getUserDisplayName } from "@/lib/user-utils";
import { PreferencesDrawer } from "@/feature/profile/components";
import TechProfileAssessment from "./TechProfileAssessment";
import AIGoalPlannerDialog from "./AIGoalPlannerDialog";
import { TrendingUp } from "lucide-react";

export default function LearnHeader() {
  const { t } = useTranslation();
  const { userInfo } = useAuth();
  const [preferencesOpen, setPreferencesOpen] = useState(false);
  const [learningPreferencesOpen, setLearningPreferencesOpen] = useState(false);

  const userName = getUserDisplayName(userInfo);

  const [techAssessmentOpen, setTechAssessmentOpen] = useState(false);
  const [aiPlannerOpen, setAiPlannerOpen] = useState(false);

  return (
    <div className="mb-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          {/* Left: Icon */}
          <div className="rounded-full bg-[#d1d5db]">
            <img
              src="/images/common/c_sharp.png"
              alt="C# Programming"
              className="w-16 h-16"
            />
          </div>

          {/* Right: Name + Description */}
          <div>
            <Typography
              variant="h3"
              className="pt-3 text-gray-800 text-2xl md:text-3xl mb-1"
            >
              {t("learn.header.welcome")}
              {userName}！
            </Typography>
            <div className="my-2 text-sm">{t("learn.header.subtitle")}</div>
          </div>
        </div>

        <div className="hidden md:flex items-center gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-2 px-4 py-2 border-gray-300 text-gray-700 hover:bg-gray-50"
            onClick={() => setTechAssessmentOpen(true)}
          >
            能力评估
          </Button>

          <Button
            variant="outline"
            className="flex items-center gap-2 px-4 py-2 border-gray-300 text-gray-700 hover:bg-gray-50"
            onClick={() => setPreferencesOpen(true)}
          >
            {t("learn.header.preferences")}
          </Button>

          <Button
            className="flex items-center gap-2 px-4 py-2"
            onClick={() => setAiPlannerOpen(true)}
          >
            ✨ AI 个性化路径
          </Button>
        </div>
      </div>

      {/* Preferences Drawer */}
      <PreferencesDrawer
        open={preferencesOpen}
        onOpenChange={setPreferencesOpen}
      />

      {/* Tech Assessment Dialog */}
      <TechProfileAssessment
        open={techAssessmentOpen}
        onOpenChange={setTechAssessmentOpen}
      />

      {/* AI Goal Planner Dialog */}
      <AIGoalPlannerDialog
        open={aiPlannerOpen}
        onOpenChange={setAiPlannerOpen}
      />
    </div>
  );
}
