/*
 * @Description: Learn Tabs Component - Main tab navigation and content
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React, { useState, useEffect } from "react";
import { Tabs, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Typography } from "@/components/ui/typography";
import { Button } from "@/components/ui/button";
import LearningPathsTabContent from "./tabs/CreateLearningPathTabContent";
import HomeTabContent from "./tabs/HomeTabContent";
import InProgressTabContent from "./tabs/InProgressTabContent";
import SavedTabContent from "./tabs/SavedTabContent";
import CompletedTabContent from "./tabs/CompletedTabContent";
import MyCoursesTabContent from "./tabs/MyCoursesTabContent";
import RecentlyViewedTabContent from "./tabs/RecentlyViewedTabContent";

const tabItems = [
  { id: "create-path", label: "Learning Paths" },
  { id: "home", label: "Home" },
  { id: "in-progress", label: "In Progress" },
  { id: "saved", label: "Saved" },
  { id: "completed", label: "Completed" },
  { id: "my-courses", label: "My Courses" },
  { id: "recently-viewed", label: "Recently Viewed" },
];

export default function LearnTabs() {
  const [activeTab, setActiveTab] = useState("create-path");

  return (
    <div className="bg-[#f9fafb]">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        {/* Tab Navigation - 参考首页样式 */}
        <div className="border-b border-gray-200">
          <TabsList className="flex w-full justify-start p-1 bg-transparent border-b-0">
            {tabItems.map((tab) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className="px-4 py-3 text-sm font-medium border-b-2 border-transparent data-[state=active]:border-blue-600 data-[state=active]:text-blue-600 data-[state=active]:bg-transparent hover:text-blue-600 transition-colors rounded-none bg-transparent whitespace-nowrap"
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </div>

        {/* Tab Content */}
        <div className="py-8">
          <TabsContent value="create-path" className="mt-0">
            <LearningPathsTabContent />
          </TabsContent>

          <TabsContent value="home" className="mt-0">
            <HomeTabContent />
          </TabsContent>

          <TabsContent value="in-progress" className="mt-0">
            <InProgressTabContent />
          </TabsContent>

          <TabsContent value="saved" className="mt-0">
            <SavedTabContent />
          </TabsContent>

          <TabsContent value="completed" className="mt-0">
            <CompletedTabContent />
          </TabsContent>

          <TabsContent value="my-courses" className="mt-0">
            <MyCoursesTabContent />
          </TabsContent>

          <TabsContent value="recently-viewed" className="mt-0">
            <RecentlyViewedTabContent />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
