/*
 * @Description: Learn Home Page - User Learning Dashboard
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React, { useEffect, useState } from "react";
import { Container } from "@/components/ui/container";
import { OnboardingForm } from "@/feature/profile/components";
import { usePreferences } from "@/hooks/usePreferences";
import LearnHeader from "./components/LearnHeader";
import LearnTabs from "./components/LearnTabs";

export default function LearnHomePage() {
  const { loadPreferences, hasPreferences, isLoading } = usePreferences();
  const [showOnboarding, setShowOnboarding] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, [loadPreferences]);

  useEffect(() => {
    if (!isLoading) {
      if (!hasPreferences) {
        setShowOnboarding(true);
      } else {
        setShowOnboarding(false);
      }
    }
  }, [isLoading, hasPreferences]);

  const handleOnboardingComplete = () => {
    setShowOnboarding(false);
    // Reload preferences after onboarding is complete
    loadPreferences();
  };

  const handleOnboardingCancel = () => {
    setShowOnboarding(false);
  };

  // Show loading state while checking preferences
  if (isLoading) {
    return (
      <div className="bg-slate-50 min-h-screen pt-12 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show normal learning dashboard
  return (
    <div className="bg-slate-50 min-h-screen pt-12">
      <Container size="xl">
        {/* Header Section */}
        <LearnHeader />

        {/* Tabs Section */}
        <LearnTabs />
      </Container>

      {/* Onboarding Dialog for new users */}
      <OnboardingForm
        open={showOnboarding}
        onComplete={handleOnboardingComplete}
        onCancel={handleOnboardingCancel}
      />
    </div>
  );
}
