/*
 * @Description: Tech Profile Assessment Component - Evaluate technical skills through resume upload or questionnaire
 * @Author: Devin
 * @Date: 2025-08-13
 */
"use client";

import React, { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent } from "@/components/ui/dialog";

import {
  Upload,
  FileText,
  CheckCircle,
  Star,
  TrendingUp,
  Code,
  Database,
  Globe,
  Smartphone,
} from "lucide-react";

// 硬编码的技能数据
const SKILL_CATEGORIES = {
  frontend: {
    name: "前端开发",
    icon: Globe,
    skills: ["React", "Vue.js", "TypeScript", "HTML/CSS", "JavaScript"],
  },
  backend: {
    name: "后端开发",
    icon: Database,
    skills: ["Node.js", "Python", "Java", "Go", "PHP"],
  },
  mobile: {
    name: "移动开发",
    icon: Smartphone,
    skills: ["React Native", "Flutter", "iOS", "Android", "Xamarin"],
  },
  devops: {
    name: "DevOps",
    icon: Code,
    skills: ["Docker", "Kubernetes", "AWS", "CI/CD", "Linux"],
  },
};

const ASSESSMENT_QUESTIONS = [
  {
    id: 1,
    category: "frontend",
    question: "你在前端开发方面有多少年经验？",
    type: "select",
    options: ["0-1年", "1-3年", "3-5年", "5年以上"],
  },
  {
    id: 2,
    category: "frontend",
    question: "你最熟悉的前端框架是什么？",
    type: "select",
    options: ["React", "Vue.js", "Angular", "Svelte", "其他"],
  },
  {
    id: 3,
    category: "backend",
    question: "你在后端开发方面的经验如何？",
    type: "select",
    options: ["初学者", "有一定经验", "熟练", "专家级"],
  },
  {
    id: 4,
    category: "backend",
    question: "你最常使用的后端技术栈是？",
    type: "select",
    options: ["Node.js", "Python/Django", "Java/Spring", "Go", "其他"],
  },
  {
    id: 5,
    category: "general",
    question: "描述一下你最有成就感的技术项目",
    type: "textarea",
  },
];

// 硬编码的评估结果
const MOCK_ASSESSMENT_RESULT = {
  overallScore: 78,
  level: "中高级开发者",
  strengths: ["前端开发", "React生态", "TypeScript"],
  improvements: ["后端架构", "数据库优化", "系统设计"],
  skillBreakdown: {
    frontend: 85,
    backend: 65,
    mobile: 45,
    devops: 70,
  },
  recommendations: [
    "深入学习微服务架构设计",
    "提升数据库性能优化技能",
    "学习云原生技术栈",
  ],
};

interface TechProfileAssessmentProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export default function TechProfileAssessment({
  open,
  onOpenChange,
}: TechProfileAssessmentProps = {}) {
  const [activeTab, setActiveTab] = useState("upload");
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [answers, setAnswers] = useState<Record<number, string>>({});
  const [showResults, setShowResults] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const handleAnswerChange = (questionId: number, answer: string) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: answer,
    }));
  };

  const handleSubmitAssessment = async () => {
    setIsProcessing(true);
    // 模拟处理时间
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setIsProcessing(false);
    setShowResults(true);
  };

  const resetAssessment = () => {
    setShowResults(false);
    setUploadedFile(null);
    setAnswers({});
    setActiveTab("upload");
  };

  const content = showResults ? (
    <AssessmentResults
      result={MOCK_ASSESSMENT_RESULT}
      onReset={resetAssessment}
    />
  ) : (
    <div className="px-4 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <Typography variant="h2" className="text-2xl font-bold text-gray-900">
          技术画像评估
        </Typography>
        <Typography variant="muted" className="text-gray-600">
          通过上传简历或完成技能问卷，获得个性化的技术能力评估报告
        </Typography>
      </div>

      {/* Assessment Methods */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            选择评估方式
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="upload" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                上传简历
              </TabsTrigger>
              <TabsTrigger
                value="questionnaire"
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                技能问卷
              </TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="mt-6">
              <ResumeUploadTab
                uploadedFile={uploadedFile}
                onFileUpload={handleFileUpload}
                onSubmit={handleSubmitAssessment}
                isProcessing={isProcessing}
              />
            </TabsContent>

            <TabsContent value="questionnaire" className="mt-6">
              <QuestionnaireTab
                questions={ASSESSMENT_QUESTIONS}
                answers={answers}
                onAnswerChange={handleAnswerChange}
                onSubmit={handleSubmitAssessment}
                isProcessing={isProcessing}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );

  // 如果传入了open和onOpenChange，则包装在Dialog中
  if (open !== undefined && onOpenChange) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-4xl max-h-[80vh] overflow-y-auto">
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  // 否则直接返回内容（用于tab中显示）
  return content;
}

// 简历上传组件
function ResumeUploadTab({
  uploadedFile,
  onFileUpload,
  onSubmit,
  isProcessing,
}: {
  uploadedFile: File | null;
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: () => void;
  isProcessing: boolean;
}) {
  return (
    <div className="space-y-6">
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
        <Upload className="h-12 w-6 text-gray-400 mx-auto mb-4" />
        <Typography variant="h4" className="mb-2">
          上传你的简历
        </Typography>
        <Typography variant="muted" className="mb-4">
          支持 PDF、DOC、DOCX 格式，文件大小不超过 10MB
        </Typography>

        <Input
          type="file"
          accept=".pdf,.doc,.docx"
          onChange={onFileUpload}
          className="hidden"
          id="resume-upload"
        />
        <Label htmlFor="resume-upload" className="cursor-pointer">
          <Button variant="outline" asChild>
            <span>选择文件</span>
          </Button>
        </Label>
      </div>

      {uploadedFile && (
        <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg border border-green-200">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <div className="flex-1">
            <Typography variant="small" className="font-medium text-green-800">
              {uploadedFile.name}
            </Typography>
            <Typography variant="muted" className="text-green-600">
              {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
            </Typography>
          </div>
        </div>
      )}

      <div className="flex justify-end">
        <Button
          onClick={onSubmit}
          disabled={!uploadedFile || isProcessing}
          className="min-w-32"
        >
          {isProcessing ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent mr-2" />
              分析中...
            </>
          ) : (
            "开始分析"
          )}
        </Button>
      </div>
    </div>
  );
}

// 问卷组件
function QuestionnaireTab({
  questions,
  answers,
  onAnswerChange,
  onSubmit,
  isProcessing,
}: {
  questions: typeof ASSESSMENT_QUESTIONS;
  answers: Record<number, string>;
  onAnswerChange: (questionId: number, answer: string) => void;
  onSubmit: () => void;
  isProcessing: boolean;
}) {
  const answeredCount = Object.keys(answers).length;
  const progress = (answeredCount / questions.length) * 100;

  return (
    <div className="space-y-6">
      {/* Progress */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>完成进度</span>
          <span>
            {answeredCount}/{questions.length}
          </span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Questions */}
      <div className="space-y-6">
        {questions.map((question) => (
          <Card key={question.id} className="p-4">
            <div className="space-y-3">
              <Typography variant="small" className="font-medium">
                {question.question}
              </Typography>

              {question.type === "select" ? (
                <div className="grid grid-cols-2 gap-2">
                  {question.options?.map((option) => (
                    <Button
                      key={option}
                      variant={
                        answers[question.id] === option ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => onAnswerChange(question.id, option)}
                      className="justify-start"
                    >
                      {option}
                    </Button>
                  ))}
                </div>
              ) : (
                <Textarea
                  placeholder="请详细描述..."
                  value={answers[question.id] || ""}
                  onChange={(e) => onAnswerChange(question.id, e.target.value)}
                  rows={3}
                />
              )}
            </div>
          </Card>
        ))}
      </div>

      <div className="flex justify-end">
        <Button
          onClick={onSubmit}
          disabled={answeredCount < questions.length || isProcessing}
          className="min-w-32"
        >
          {isProcessing ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent mr-2" />
              评估中...
            </>
          ) : (
            "完成评估"
          )}
        </Button>
      </div>
    </div>
  );
}

// 评估结果组件
function AssessmentResults({
  result,
  onReset,
}: {
  result: typeof MOCK_ASSESSMENT_RESULT;
  onReset: () => void;
}) {
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <Typography variant="h2" className="text-2xl font-bold text-gray-900">
          你的技术画像报告
        </Typography>
        <Typography variant="muted" className="text-gray-600">
          基于你的信息，我们为你生成了个性化的技术能力评估
        </Typography>
      </div>

      {/* Overall Score */}
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="relative inline-flex items-center justify-center w-32 h-32">
              <svg className="w-32 h-32 transform -rotate-90">
                <circle
                  cx="64"
                  cy="64"
                  r="56"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="none"
                  className="text-gray-200"
                />
                <circle
                  cx="64"
                  cy="64"
                  r="56"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="none"
                  strokeDasharray={`${2 * Math.PI * 56}`}
                  strokeDashoffset={`${
                    2 * Math.PI * 56 * (1 - result.overallScore / 100)
                  }`}
                  className="text-blue-600"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-3xl font-bold text-gray-900">
                  {result.overallScore}
                </span>
              </div>
            </div>
            <div>
              <Typography variant="h3" className="font-semibold">
                {result.level}
              </Typography>
              <Typography variant="muted">综合技术能力评分</Typography>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Skill Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>技能分布</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(result.skillBreakdown).map(([key, score]) => {
            const category =
              SKILL_CATEGORIES[key as keyof typeof SKILL_CATEGORIES];
            const Icon = category.icon;

            return (
              <div key={key} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Icon className="h-4 w-4 text-blue-600" />
                    <span className="font-medium">{category.name}</span>
                  </div>
                  <span className="text-sm font-medium">{score}/100</span>
                </div>
                <Progress value={score} className="h-2" />
              </div>
            );
          })}
        </CardContent>
      </Card>

      {/* Strengths & Improvements */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-700">
              <Star className="h-5 w-5" />
              技术优势
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {result.strengths.map((strength) => (
                <Badge
                  key={strength}
                  variant="secondary"
                  className="bg-green-100 text-green-800"
                >
                  {strength}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <TrendingUp className="h-5 w-5" />
              提升方向
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {result.improvements.map((improvement) => (
                <Badge
                  key={improvement}
                  variant="secondary"
                  className="bg-orange-100 text-orange-800"
                >
                  {improvement}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>学习建议</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            {result.recommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm">{recommendation}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-center gap-4">
        <Button variant="outline" onClick={onReset}>
          重新评估
        </Button>
        <Button>制定学习计划</Button>
      </div>
    </div>
  );
}
