"use client";

import React, { use<PERSON>emo, useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { goalsService, type ChatResponse } from "@/service/goals.service";
import { handleApiError } from "@/lib/api-error-handler";
import type { SuggestedGoal } from "@/types/openapi";
import { toast } from "sonner";

interface AIGoalPlannerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGenerated?: (pathId: string) => void; // optional callback after generation
}

/**
 * Two-step dialog:
 * 1) User inputs origin goal -> POST /api/v1/goals/plan => suggested_goals
 * 2) User picks a suggested goal -> POST /api/v1/learning-paths/generate { goal }
 */
export default function AIGoalPlannerDialog({ open, onOpenChange, onGenerated }: AIGoalPlannerDialogProps) {
  const router = useRouter();
  const [conversationId, setConversationId] = useState<string | "">("");
  const [originGoal, setOriginGoal] = useState("");
  const [userMessage, setUserMessage] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [suggestedGoals, setSuggestedGoals] = useState<SuggestedGoal[]>([]);
  const [status, setStatus] = useState<string>("");
  const [aiMessage, setAiMessage] = useState<string>("");

  const canSubmitFirst = useMemo(() => originGoal.trim().length >= 6, [originGoal]);
  const canSendMessage = useMemo(() => userMessage.trim().length > 0, [userMessage]);

  const sendFirst = async () => {
    try {
      setLoading(true);
      const resp = await goalsService.chat({ origin_goal: originGoal.trim() });
      const data = (resp as any)?.success !== undefined ? (resp as any).data : (resp as ChatResponse);
      if (!data?.conversation_id) throw new Error("会话创建失败");
      setConversationId(data.conversation_id);
      setStatus(data.status);
  setAiMessage(data.message || "");
      setSuggestedGoals((data?.suggested_goals || []).slice(0, 4));
      if (data.status !== 'ready' && !data.message) {
        toast.info('AI 正在澄清你的需求，请继续补充描述');
      }
    } catch (err) {
      handleApiError(err, { showToast: true, defaultMessage: "无法开始对话" });
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = async () => {
    if (!conversationId) return;
    try {
      setLoading(true);
      const resp = await goalsService.chat({ conversation_id: conversationId, message: userMessage.trim() });
      const data = (resp as any)?.success !== undefined ? (resp as any).data : (resp as ChatResponse);
      setStatus(data.status);
  setAiMessage(data.message || "");
      setSuggestedGoals((data?.suggested_goals || []).slice(0, 4));
      setUserMessage("");
      if (data.status === 'ready' && (!data?.suggested_goals || data.suggested_goals.length === 0)) {
        toast.info('AI 已准备好，但没有提供建议，请继续提问或调整描述');
      }
    } catch (err) {
      handleApiError(err, { showToast: true, defaultMessage: "发送失败" });
    } finally {
      setLoading(false);
    }
  };

  const chooseGoal = async (index: number) => {
    if (!conversationId) return;
    try {
      setLoading(true);
      const resp = await goalsService.chooseGoal(conversationId, index);
      const data = (resp as any)?.success !== undefined ? (resp as any).data : (resp as { path_id: string });
      const pathId = data?.path_id;
      if (pathId) {
        toast.success("已触发生成，稍后可在路径详情查看进度");
        onOpenChange(false);
        // 清理状态
        setConversationId("");
        setOriginGoal("");
        setUserMessage("");
        setSuggestedGoals([]);
        setStatus("");
        if (onGenerated) onGenerated(pathId);
        router.push(`/learn/paths/${pathId}`);
      } else {
        toast.error("未获取到路径ID");
      }
    } catch (err) {
      handleApiError(err, { showToast: true, defaultMessage: "选择目标失败" });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = (nextOpen: boolean) => {
    if (!nextOpen) {
      // reset state on close
      setConversationId("");
      setOriginGoal("");
      setUserMessage("");
      setSuggestedGoals([]);
      setStatus("");
  setAiMessage("");
      setLoading(false);
    }
    onOpenChange(nextOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[85vh]">
        <DialogHeader>
          <DialogTitle>AI 个性化学习路径</DialogTitle>
          <DialogDescription>
            请输入你的学习目标，AI 会先澄清你的需求，准备好后给出 3~4 条建议；你也可以继续与 AI 对话来调整建议。
          </DialogDescription>
        </DialogHeader>

        {/* Scrollable content area */}
        <div className="max-h-[60vh] overflow-y-auto pr-1">
          {/* 第一次输入目标 */}
          {!conversationId && (
            <div className="space-y-3">
              <Label htmlFor="origin-goal">学习需求 / 目标</Label>
              <Textarea
                id="origin-goal"
                placeholder="例如：我想在三个月内找到前端开发实习，已掌握 HTML/CSS，有少量 JavaScript 基础，每周可投入 10 小时"
                value={originGoal}
                onChange={(e) => setOriginGoal(e.target.value)}
                rows={5}
              />
            </div>
          )}

          {/* 建议列表与对话输入 */}
          {conversationId && (
            <div className="space-y-4">
              {status && (
                <div className="text-xs text-gray-500">
                  对话状态：{status === 'ready' ? '已准备建议' : '澄清中'}
                </div>
              )}
              {aiMessage && (
                <div className="rounded-md bg-muted p-3 text-sm text-muted-foreground whitespace-pre-wrap">
                  {aiMessage}
                </div>
              )}
              {suggestedGoals.length > 0 && (
                <div className="grid gap-4">
                  {suggestedGoals.map((sg, idx) => (
                    <Card key={idx} className="hover:border-blue-200">
                      <CardHeader>
                        <CardTitle className="text-base">建议 {idx + 1}</CardTitle>
                        <CardDescription className="text-xs text-gray-500">
                          方向：{sg.focus_direction}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="prose max-w-none whitespace-pre-wrap text-sm leading-relaxed">
                          {sg.final_goal_description}
                        </div>
                        <div className="mt-4 flex justify-end">
                          <Button
                            variant="default"
                            onClick={() => chooseGoal(idx)}
                            disabled={loading}
                          >
                            选择此建议并生成路径
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* 自由输入继续对话 */}
              <div className="space-y-2">
                <Label htmlFor="user-message">与 AI 继续对话（可要求调整建议）</Label>
                <Textarea
                  id="user-message"
                  placeholder="例如：我更倾向于全栈方向，后端希望使用 Node.js；或者请再多提供基于项目驱动的方案"
                  value={userMessage}
                  onChange={(e) => setUserMessage(e.target.value)}
                  rows={3}
                  disabled={loading}
                />
                <div className="flex justify-end">
                  <Button onClick={sendMessage} disabled={!canSendMessage || loading}>
                    发送
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          {!conversationId ? (
            <div className="flex w-full items-center justify-end gap-3">
              <Button variant="outline" onClick={() => handleClose(false)} disabled={loading}>
                取消
              </Button>
              <Button onClick={sendFirst} disabled={!canSubmitFirst || loading}>
                {loading ? "生成中..." : "开始对话"}
              </Button>
            </div>
          ) : (
            <div className="flex w-full items-center justify-between">
              <Button variant="outline" onClick={() => handleClose(false)} disabled={loading}>
                结束对话
              </Button>
              <div className="text-xs text-gray-400">会话ID：{conversationId}</div>
            </div>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
