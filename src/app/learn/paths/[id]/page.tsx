/*
 * @Description: Learning Path Detail Page - shows nodes as a DAG
 * @Author: Devin
 * @Date: 2025-08-15
 */
"use client";

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useParams, useRouter } from "next/navigation";
import { learningNodeService } from "@/service/learning-nodes.service";
import { handleApiResponse, handleApiError } from "@/lib/api-error-handler";
import type { LearningNode } from "@/types/openapi";
import { Container } from "@/components/ui/container";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Loader2, Wand2 } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import { toast } from "sonner";
import PersonalizeAdjustDialog from "@/components/common/PersonalizeAdjustDialog";

// Very light-weight graph layout: stack by levels derived from prerequisites count
// For better visualization later, we can replace with @maxgraph/core already in deps
export default function PathDetailPage() {
  const params = useParams();
  const router = useRouter();
  const pathId = Array.isArray(params?.id)
    ? params.id[0]
    : (params?.id as string);
  const [nodes, setNodes] = useState<LearningNode[]>([]);
  const [loading, setLoading] = useState(true);
  const [scale, setScale] = useState(1);
  const [translate, setTranslate] = useState({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [draggingId, setDraggingId] = useState<string | null>(null);
  const [customPositions, setCustomPositions] = useState<
    Record<string, { x: number; y: number }>
  >({});
  const panStart = useRef<{ x: number; y: number } | null>(null);
  const translateStart = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const dragOffsetRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const dragMouseStartRef = useRef<{ x: number; y: number } | null>(null);
  const dragMovedRef = useRef<boolean>(false);
  const viewportRef = useRef<HTMLDivElement>(null);
  const storageKey = useMemo(
    () => (pathId ? `lp:pos:${pathId}` : "lp:pos"),
    [pathId]
  );

  // UI state for generate-course flow
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [targetNode, setTargetNode] = useState<LearningNode | null>(null);
  const [generating, setGenerating] = useState(false);
  // end UI state

  const [genResult, setGenResult] = useState<{ success: boolean; lessons: number } | null>(null);
  const [personalizeOpen, setPersonalizeOpen] = useState(false);
  const [personalizeDefaultScope, setPersonalizeDefaultScope] = useState<'path'|'node'|'lesson'>('path');
  const [personalizeTargetId, setPersonalizeTargetId] = useState<string | undefined>(undefined);


  // Reload nodes (reflect backend status changes)
  const reloadNodes = useCallback(async () => {
    if (!pathId) return;
    try {
      setLoading(true);
      const resp = await learningNodeService.listByPath(pathId);
      const data = handleApiResponse(resp, undefined, { showToast: false });
      setNodes(data || []);
    } catch (err) {
      handleApiError(err, {
        showToast: true,
        defaultMessage: "加载路径节点失败",
      });
    } finally {
      setLoading(false);
    }
  }, [pathId]);

  useEffect(() => {
    reloadNodes();
  }, [reloadNodes]);

  // Build adjacency from prerequisites and compute layered layout
  const { levels, byId } = useMemo(() => {
    const byId = new Map<string, LearningNode>();
    nodes.forEach((n) => n.id && byId.set(n.id, n));

    // topological layering by in-degree
    const indeg = new Map<string, number>();
    nodes.forEach((n) => {
      const id = n.id as string;
      indeg.set(id, 0);
    });
    nodes.forEach((n) => {
      const pres = (n.prerequisites || []) as string[];
      pres.forEach((_pid: string) => {
        if (indeg.has(n.id as string)) {
          indeg.set(n.id as string, (indeg.get(n.id as string) || 0) + 1);
        }
      });
    });

    const layers: string[][] = [];
    const q: string[] = [];
    indeg.forEach((v, k) => v === 0 && q.push(k));
    const remaining = new Map(indeg);

    while (q.length) {
      const layer: string[] = [];
      const size = q.length;
      for (let i = 0; i < size; i++) {
        const id = q.shift()!;
        layer.push(id);
        const n = byId.get(id);
        // decrement indegree of successors (nodes that depend on this id)
        nodes.forEach((m) => {
          const pres = (m.prerequisites || []) as string[];
          if (pres.includes(id)) {
            const cur = remaining.get(m.id as string) || 0;
            const next = cur - 1;
            remaining.set(m.id as string, next);
            if (next === 0) q.push(m.id as string);
          }
        });
      }
      layers.push(layer);
    }

    // fallback: if cycle or leftover, put the rest in the last layer
    const leftover = Array.from(remaining.entries())
      .filter(([, v]) => v > 0)
      .map(([k]) => k);
    if (leftover.length) layers.push(leftover);

    return {
      levels: layers.map((ids) =>
        ids.map((id) => byId.get(id)!).filter(Boolean)
      ),
      byId,
    } as any;
  }, [nodes]);

  // Compute positions for a simple layered layout
  const layout = useMemo(() => {
    const nodeWidth = 120; // diameter for circular nodes (smaller)
    const nodeHeight = 120;
    const hGap = 160; // keep generous spacing to reduce edge overlaps
    const vGap = 100;
    const padding = 60;

    const positions = new Map<string, { x: number; y: number }>();
    let maxCols = levels.length;
    let maxRows = 0;
    (levels as LearningNode[][]).forEach(
      (lvl: LearningNode[]) => (maxRows = Math.max(maxRows, lvl.length))
    );

    (levels as LearningNode[][]).forEach((lvl: LearningNode[], col: number) => {
      lvl.forEach((n: LearningNode, row: number) => {
        if (!n?.id) return;
        const x = padding + col * (nodeWidth + hGap);
        const y = padding + row * (nodeHeight + vGap);
        positions.set(n.id, { x, y });
      });
    });

    const width =
      padding * 2 +
      (maxCols > 0 ? maxCols * nodeWidth + (maxCols - 1) * hGap : 0);
    const height =
      padding * 2 +
      (maxRows > 0 ? maxRows * nodeHeight + (maxRows - 1) * vGap : 0);

    return { positions, nodeWidth, nodeHeight, width, height, padding };
  }, [levels]);

  // Compute transitive reduction info to hide redundant edges (e.g., A->C when A->B and B->C exist)
  const graphInfo = useMemo(() => {
    const ids = nodes.map((n) => n.id!).filter(Boolean);
    const out = new Map<string, string[]>();
    ids.forEach((id) => out.set(id, []));
    nodes.forEach((n) => {
      if (!n.id) return;
      const v = n.id as string;
      const pres = (n.prerequisites || []) as string[];
      pres.forEach((u) => {
        if (out.has(u)) out.get(u)!.push(v);
      });
    });

    // Build a topological-like order from levels (left-to-right, top-to-bottom)
    const topo: string[] = [];
    (levels as LearningNode[][]).forEach((lvl) =>
      lvl.forEach((n) => n?.id && topo.push(n.id))
    );
    ids.forEach((id) => {
      if (!topo.includes(id)) topo.push(id);
    });

    // Reachability sets via reverse topo DP: reachable[u] = neighbors(u) ∪ ⋃ reachable[neighbor]
    const reachable = new Map<string, Set<string>>();
    for (let i = topo.length - 1; i >= 0; i--) {
      const u = topo[i];
      const set = new Set<string>();
      const neigh = out.get(u) || [];
      for (const v of neigh) {
        set.add(v);
        const rV = reachable.get(v);
        if (rV) rV.forEach((x) => set.add(x));
      }
      reachable.set(u, set);
    }

    // An edge u->v is redundant if ∃ neighbor x != v such that v ∈ reachable[x]
    const redundant = new Set<string>();
    ids.forEach((u) => {
      const neigh = out.get(u) || [];
      for (const v of neigh) {
        const existsAlt = neigh.some(
          (x) => x !== v && (reachable.get(x)?.has(v) ?? false)
        );
        if (existsAlt) redundant.add(`${u}->${v}`);
      }
    });
    return { redundantEdges: redundant };
  }, [nodes, levels]);

  // Helper to get current position (custom override or default layout)
  const getPos = useCallback(
    (id: string | undefined | null) => {
      if (!id)
        return undefined as unknown as { x: number; y: number } | undefined;
      return customPositions[id] ?? layout.positions.get(id);
    },
    [customPositions, layout.positions]
  );

  // Load saved custom positions from localStorage (if any)
  useEffect(() => {
    try {
      const raw = storageKey ? localStorage.getItem(storageKey) : null;
      if (raw) {
        const saved = JSON.parse(raw) as Record<
          string,
          { x: number; y: number }
        >;
        // filter to current nodes only
        const idSet = new Set<string>(nodes.map((n) => n.id!).filter(Boolean));
        const filtered: Record<string, { x: number; y: number }> = {};
        Object.entries(saved).forEach(([k, v]) => {
          if (
            idSet.has(k) &&
            typeof v?.x === "number" &&
            typeof v?.y === "number"
          )
            filtered[k] = v;
        });
        setCustomPositions(filtered);
      }
    } catch {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [storageKey, nodes.length]);

  const savePositions = useCallback(() => {
    try {
      if (!storageKey) return;
      localStorage.setItem(storageKey, JSON.stringify(customPositions));
    } catch {}
  }, [customPositions, storageKey]);

  // Dynamic canvas size considering custom positions
  const canvasSize = useMemo(() => {
    let maxX = 0;
    let maxY = 0;
    nodes.forEach((n) => {
      if (!n.id) return;
      const p = getPos(n.id);
      if (!p) return;
      maxX = Math.max(maxX, p.x + layout.nodeWidth);
      maxY = Math.max(maxY, p.y + layout.nodeHeight);
    });
    const width = Math.max(layout.width, maxX + layout.padding);
    const height = Math.max(layout.height, maxY + layout.padding);
    return { width, height };
  }, [
    nodes,
    getPos,
    layout.width,
    layout.height,
    layout.nodeWidth,
    layout.nodeHeight,
    layout.padding,
  ]);

  return (
    <div className="bg-slate-50 min-h-screen pt-12">
      <Container size="xl">
        <div className="mb-4 flex items-center gap-3">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-1" /> 返回
          </Button>
          <h1 className="text-xl font-semibold">学习路径详情</h1>
          <div className="ml-auto flex items-center gap-2">
            <Button size="sm" variant="default" onClick={() => setPersonalizeOpen(true)}>
              <Wand2 className="w-4 h-4 mr-1" /> 个性化调整
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="h-64 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div
            ref={viewportRef}
            className={`overflow-auto relative ${
              isPanning ? "cursor-grabbing" : "cursor-grab"
            }`}
            onMouseDown={(e) => {
              // start panning only when not dragging a node
              if (draggingId) return;
              setIsPanning(true);
              panStart.current = { x: e.clientX, y: e.clientY };
              translateStart.current = { ...translate };
            }}
            onMouseUp={() => {
              if (draggingId) {
                setDraggingId(null);
                savePositions();
                dragMouseStartRef.current = null;
                // keep dragMovedRef as-is; it will be checked by click and then reset implicitly next drag
              }
              setIsPanning(false);
              panStart.current = null;
            }}
            onMouseLeave={() => {
              if (draggingId) {
                setDraggingId(null);
                savePositions();
                dragMouseStartRef.current = null;
              }
              setIsPanning(false);
              panStart.current = null;
            }}
            onMouseMove={(e) => {
              if (draggingId) {
                const rect = viewportRef.current?.getBoundingClientRect();
                if (!rect) return;
                const mx = e.clientX - rect.left;
                const my = e.clientY - rect.top;
                const gx = (mx - translate.x) / scale;
                const gy = (my - translate.y) / scale;
                const off = dragOffsetRef.current;
                const nx = gx - off.x;
                const ny = gy - off.y;
                setCustomPositions((prev) => ({
                  ...prev,
                  [draggingId]: { x: nx, y: ny },
                }));
                // mark as moved if exceeds small threshold
                const start = dragMouseStartRef.current;
                if (start) {
                  const dx = gx - start.x;
                  const dy = gy - start.y;
                  if (
                    !dragMovedRef.current &&
                    (Math.abs(dx) > 3 || Math.abs(dy) > 3)
                  ) {
                    dragMovedRef.current = true;
                  }
                }
                return;
              }
              if (!isPanning || !panStart.current) return;
              const dx = e.clientX - panStart.current.x;
              const dy = e.clientY - panStart.current.y;
              setTranslate({
                x: translateStart.current.x + dx,
                y: translateStart.current.y + dy,
              });
            }}
            onWheel={(e) => {
              if (!e.ctrlKey) return; // hold Ctrl to zoom
              e.preventDefault();
              const rect = viewportRef.current?.getBoundingClientRect();
              if (!rect) return;
              const mx = e.clientX - rect.left; // mouse in viewport coords
              const my = e.clientY - rect.top;
              const zoomFactor = e.deltaY < 0 ? 1.1 : 0.9;
              const newScale = Math.min(2.5, Math.max(0.5, scale * zoomFactor));
              const k = newScale / scale;
              // keep cursor position stable
              const newTx = translate.x + mx * (1 - k);
              const newTy = translate.y + my * (1 - k);
              setScale(newScale);
              setTranslate({ x: newTx, y: newTy });
            }}
          >
            <div className="absolute right-3 top-3 z-20 flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setScale((s) => Math.min(2.5, s * 1.1))}
              >
                +
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setScale((s) => Math.max(0.5, s * 0.9))}
              >
                -
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setScale(1);
                  setTranslate({ x: 0, y: 0 });
                }}
              >
                重置
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setCustomPositions({});
                  try {
                    if (storageKey) localStorage.removeItem(storageKey);
                  } catch {}
                }}
              >
                清除布局
              </Button>
            </div>
            <div
              className="relative"
              style={{
                width: Math.max(canvasSize.width, 1200),
                height: Math.max(canvasSize.height, 800),
                transform: `translate(${translate.x}px, ${translate.y}px) scale(${scale})`,
                transformOrigin: "0 0",
              }}
            >
              {/* Edges layer */}
              <svg
                width={canvasSize.width}
                height={canvasSize.height}
                className="absolute inset-0 z-0"
              >
                <defs>
                  <marker
                    id="arrow"
                    viewBox="0 0 10 10"
                    refX="10"
                    refY="5"
                    markerWidth="6"
                    markerHeight="6"
                    orient="auto-start-reverse"
                  >
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="#9ca3af" />
                  </marker>
                </defs>
                {nodes.map((n) => {
                  const to = getPos(n.id);
                  const pres = (n.prerequisites || []) as string[];
                  return pres
                    .filter((pid) => !!getPos(pid))
                    .map((pid) => {
                      // Skip transitive edge like A->C if A->B and B->C already connect A to C
                      const edgeKey = `${pid}->${n.id}`;
                      if (graphInfo.redundantEdges.has(edgeKey)) return null;
                      const from = getPos(pid)!;
                      if (!to || !from) return null;
                      const r = layout.nodeWidth / 2;
                      const fromCx = from.x + r;
                      const fromCy = from.y + r;
                      const toCx = to.x + r;
                      const toCy = to.y + r;
                      const vx = toCx - fromCx;
                      const vy = toCy - fromCy;
                      const dist = Math.max(1, Math.hypot(vx, vy));
                      const ux = vx / dist;
                      const uy = vy / dist;
                      // start/end on circle boundary along direction
                      const x1 = fromCx + ux * r;
                      const y1 = fromCy + uy * r;
                      const x2 = toCx - ux * r;
                      const y2 = toCy - uy * r;
                      const absDx = Math.abs(x2 - x1);
                      const k = Math.max(60, Math.min(200, absDx * 0.35));
                      const c1x = x1 + k;
                      const c1y = y1;
                      const c2x = x2 - k;
                      const c2y = y2;
                      const path = `M ${x1} ${y1} C ${c1x} ${c1y}, ${c2x} ${c2y}, ${x2} ${y2}`;
                      return (
                        <path
                          key={`${pid}->${n.id}`}
                          d={path}
                          stroke="#9ca3af"
                          strokeWidth={1.5}
                          fill="none"
                          markerEnd="url(#arrow)"
                        />
                      );
                    });
                })}
              </svg>

              {/* Nodes layer */}
              <div className="relative z-10">
                {nodes.map((n) => {
                  if (!n.id) return null;
                  const pos = getPos(n.id);
                  if (!pos) return null;
                  const size = layout.nodeWidth;

                  const colorForDifficulty = (d?: number) => {
                    const clamp = (v: number, min: number, max: number) =>
                      Math.max(min, Math.min(max, v));
                    const val = clamp(d ?? 5, 1, 10);
                    const stops = [
                      { v: 1, c: [16, 185, 129] },
                      { v: 5.5, c: [245, 158, 11] },
                      { v: 10, c: [239, 68, 68] },
                    ];
                    const t = (val - 1) / 9;
                    const lerp = (a: number[], b: number[], k: number) => [
                      Math.round(a[0] + (b[0] - a[0]) * k),
                      Math.round(a[1] + (b[1] - a[1]) * k),
                      Math.round(a[2] + (b[2] - a[2]) * k),
                    ];
                    const mid = 0.5;
                    let rgb: number[];
                    if (t <= mid) {
                      const k = t / mid;
                      rgb = lerp(stops[0].c, stops[1].c, k);
                    } else {
                      const k = (t - mid) / (1 - mid);
                      rgb = lerp(stops[1].c, stops[2].c, k);
                    }
                    const hex = `rgb(${rgb[0]}, ${rgb[1]}, ${rgb[2]})`;
                    const rgba = (alpha: number) =>
                      `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, ${alpha})`;
                    return { hex, rgba };
                  };

                  // const color = colorForDifficulty(n.difficulty); // 可用于后续视觉细化
                  const nodeStatus = (n.status as unknown as string) || "";
                  const isGenerating = nodeStatus === "generating";
                  const isFailed = nodeStatus === "failed";

                  return (
                    <div
                      key={n.id}
                      className="absolute group"
                      style={{ left: pos.x, top: pos.y, width: size, height: size }}
                      onMouseDown={(e) => {
                        e.stopPropagation();
                        if (!n.id) return;
                        setDraggingId(n.id);
                        const rect = viewportRef.current?.getBoundingClientRect();
                        if (!rect) return;
                        const mx = e.clientX - rect.left;
                        const my = e.clientY - rect.top;
                        const gx = (mx - translate.x) / scale;
                        const gy = (my - translate.y) / scale;
                        dragOffsetRef.current = { x: gx - pos.x, y: gy - pos.y };
                        dragMouseStartRef.current = { x: gx, y: gy };
                        dragMovedRef.current = false;
                      }}
                      onClick={async (e) => {
                        e.stopPropagation();
                        if (!n.id) return;
                        if (dragMovedRef.current) return;
                        if (n.status === 'active') {
                          router.push(`/courses/${n.id}`);
                          return;
                        }
                        if (isGenerating) {
                          // 先尝试刷新节点状态
                          try {
                            const resp = await learningNodeService.listByPath(pathId as string);
                            const data = handleApiResponse(resp, undefined, { showToast: false });
                            setNodes(data || []);
                            const latest = (data || []).find((x: any) => x.id === n.id);
                            const latestStatus = (latest?.status as unknown as string) || "";
                            if (latestStatus === 'active') {
                              router.push(`/courses/${n.id}`);
                            } else if (latestStatus === 'failed') {
                              // 失败则允许用户重新生成
                              setTargetNode(latest ?? n);
                              setGenResult(null);
                              setConfirmOpen(true);
                            } else {
                              toast.info('该节点仍在生成中，请稍后再试');
                            }
                          } catch (err) {
                            handleApiError(err, { showToast: true, defaultMessage: '刷新状态失败' });
                          }
                          return;
                        }
                        // 其他状态，打开生成确认
                        setTargetNode(n);
                        setGenResult(null);
                        setConfirmOpen(true);
                      }}
                    >
                      {/* Circular node */}
                      <div
                        className="w-full h-full rounded-full border shadow-sm flex items-center justify-center text-center px-4 relative"
                        style={{ width: size, height: size }}
                        onMouseDown={(e) => {
                          e.stopPropagation();
                          if (!n.id) return;
                          setDraggingId(n.id);
                          const rect = viewportRef.current?.getBoundingClientRect();
                          if (!rect) return;
                          const mx = e.clientX - rect.left;
                          const my = e.clientY - rect.top;
                          const gx = (mx - translate.x) / scale;
                          const gy = (my - translate.y) / scale;
                          dragOffsetRef.current = { x: gx - pos.x, y: gy - pos.y };
                          dragMouseStartRef.current = { x: gx, y: gy };
                          dragMovedRef.current = false;
                        }}
                        onClick={async (e) => {
                          e.stopPropagation();
                          if (!n.id) return;
                          if (dragMovedRef.current) return;
                          if (n.status === 'active') {
                            router.push(`/courses/${n.id}`);
                            return;
                          }
                          const isGeneratingInner = ((n.status as unknown as string) || '') === 'generating';
                          if (isGeneratingInner) {
                            try {
                              const resp = await learningNodeService.listByPath(pathId as string);
                              const data = handleApiResponse(resp, undefined, { showToast: false });
                              setNodes(data || []);
                              const latest = (data || []).find((x: any) => x.id === n.id);
                              const latestStatus = (latest?.status as unknown as string) || '';
                              if (latestStatus === 'active') {
                                router.push(`/courses/${n.id}`);
                              } else if (latestStatus === 'failed') {
                                setTargetNode(latest ?? n);
                                setGenResult(null);
                                setConfirmOpen(true);
                              } else {
                                toast.info('该节点仍在生成中，请稍后再试');
                              }
                            } catch (err) {
                              handleApiError(err, { showToast: true, defaultMessage: '刷新状态失败' });
                            }
                            return;
                          }
                          // 其他状态，打开生成确认
                          setTargetNode(n);
                          setGenResult(null);
                          setConfirmOpen(true);
                        }}
                      >
                        <div className="text-sm font-semibold line-clamp-3">{n.title}</div>
                        {nodeStatus && nodeStatus !== 'active' && (
                          <div
                            className="absolute -bottom-2 left-1/2 -translate-x-1/2 text-[10px] px-2 py-0.5 rounded-full border bg-white shadow-sm whitespace-nowrap"
                            style={{
                              borderColor: isFailed ? '#ef4444' : isGenerating ? '#f59e0b' : '#9ca3af',
                              color: isFailed ? '#ef4444' : isGenerating ? '#b45309' : '#6b7280',
                            }}
                          >
                            {isGenerating ? '生成中' : isFailed ? '生成失败' : nodeStatus}
                          </div>
                        )}
                      </div>
                      {/* Hover tooltip */}
                      <div className="pointer-events-none absolute left-1/2 -translate-x-1/2 top-full mt-2 hidden group-hover:block z-10">
                        <div className="w-80 rounded-md border bg-white p-3 text-xs text-gray-700 shadow-md">
                          <div className="font-medium mb-1">{n.title}</div>
                          <div className="text-gray-600 whitespace-pre-wrap mb-2">
                            {n.description || '无描述'}
                          </div>
                          <div className="flex gap-4 text-gray-500">
                            <span>时长: {n.estimated_times || 1}h</span>
                            <span>难度: {n.difficulty || 5}</span>
                            {nodeStatus && (
                              <span>
                                状态: {isGenerating ? '生成中' : isFailed ? '生成失败' : nodeStatus}
                              </span>
                            )}
                          </div>
                          <div className="mt-2 pointer-events-auto">
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={(e) => {
                                e.stopPropagation();
                                setPersonalizeDefaultScope('node');
                                setPersonalizeTargetId(n.id!);
                                setPersonalizeOpen(true);
                              }}
                            >
                              个性化此节点
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
            {/* Generate Course Confirm Dialog */}
            <Dialog
              open={confirmOpen}
              onOpenChange={(v) => {
                if (!v) {
                  setConfirmOpen(false);
                  setGenerating(false);
                  setGenResult(null);
                }
              }}
            >
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>生成课程内容</DialogTitle>
                  <DialogDescription>
                    {targetNode?.title
                      ? `是否使用 AI 为节点「${targetNode.title}」一键生成课程内容？`
                      : "是否使用 AI 一键生成课程内容？"}
                  </DialogDescription>
                </DialogHeader>
                <div className="py-2 text-sm text-gray-600">
                  该操作可能需要 2-3 分钟，请耐心等待。
                </div>
                {genResult && (
                  <div className="mt-2 rounded-md border bg-white p-3 text-sm">
                    <div className="font-medium text-green-700">生成完成</div>
                    <div>共生成课程章节：{genResult.lessons} 个</div>
                  </div>
                )}
                <DialogFooter>
                  {!genResult ? (
                    <Button
                      disabled={generating}
                      onClick={async () => {
                        if (!targetNode?.id) return;
                        try {
                          setGenerating(true);
                          const resp = await learningNodeService.generateCourse(
                            targetNode.id
                          );
                          const data = handleApiResponse(resp, undefined, {
                            showToast: false,
                          });
                          const ok = !!data?.success;
                          setGenResult({
                            success: ok,
                            lessons: data?.generated_lessons ?? 0,
                          });
                          if (ok) {
                            toast.success("生成完成");
                            // refresh nodes to reflect status change
                            await reloadNodes();
                          } else {
                            toast.error("生成失败");
                          }
                        } catch (err) {
                          handleApiError(err, {
                            showToast: true,
                            defaultMessage: "生成失败",
                          });
                        } finally {
                          setGenerating(false);
                        }
                      }}
                      className="rounded-full w-24 h-24 p-0 text-base"
                    >
                      {generating ? (
                        <span className="flex items-center justify-center w-full h-full">
                          <Loader2 className="w-6 h-6 animate-spin mr-2" />{" "}
                          生成中
                        </span>
                      ) : (
                        "一键生成"
                      )}
                    </Button>
                  ) : (
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => setConfirmOpen(false)}
                      >
                        关闭
                      </Button>
                      <Button
                        onClick={() => {
                          if (targetNode?.id)
                            router.push(`/courses/${targetNode.id}`);
                        }}
                      >
                        前往课程
                      </Button>
                    </div>
                  )}
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}
        <PersonalizeAdjustDialog
          open={personalizeOpen}
          onOpenChange={(v) => { setPersonalizeOpen(v); if (!v) { setPersonalizeDefaultScope('path'); setPersonalizeTargetId(undefined); } }}
          pathId={pathId}
          defaultScope={personalizeDefaultScope}
          targetId={personalizeTargetId}
          onCompleted={() => reloadNodes()}
        />
      </Container>
    </div>
  );
}
