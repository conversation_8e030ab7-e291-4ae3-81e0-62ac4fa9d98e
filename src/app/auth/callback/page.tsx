"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useTranslation } from "react-i18next";

export default function AuthCallbackPage() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleCallback = () => {
      // 获取 URL 参数
      const code = searchParams.get("code");
      const error = searchParams.get("error");
      const idToken = searchParams.get("id_token");

      if (error) {
        // 发送错误消息给父窗口
        window.opener?.postMessage(
          {
            type: "GOOGLE_AUTH_ERROR",
            error: error,
          },
          window.location.origin
        );
        window.close();
        return;
      }

      if (idToken) {
        // 发送成功消息给父窗口
        window.opener?.postMessage(
          {
            type: "GOOGLE_AUTH_SUCCESS",
            idToken: idToken,
          },
          window.location.origin
        );
        window.close();
        return;
      }

      if (code) {
        // 如果是授权码模式，需要后端处理
        window.opener?.postMessage(
          {
            type: "GOOGLE_AUTH_SUCCESS",
            authCode: code,
          },
          window.location.origin
        );
        window.close();
        return;
      }

      // 没有找到有效参数
      window.opener?.postMessage(
        {
          type: "GOOGLE_AUTH_ERROR",
          error: "未收到有效的认证参数",
        },
        window.location.origin
      );
      window.close();
    };

    // 延迟执行以确保页面完全加载
    const timer = setTimeout(handleCallback, 100);

    return () => clearTimeout(timer);
  }, [searchParams]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">{t("common.metadata.authCallback")}</p>
      </div>
    </div>
  );
}
