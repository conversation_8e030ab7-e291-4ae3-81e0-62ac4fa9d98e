@tailwind base;
@tailwind components;
@tailwind utilities;

/* KaTeX styles for math formulas */
@import "katex/dist/katex.min.css";

body {
  height: 100%;
  overflow-x: hidden;
  overflow-y: scroll;
  font-size: 100%;
  font-family: var(--font-family-custom);
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* 修复 DropdownMenuSubContent 的定位问题 */
[data-radix-dropdown-menu-sub-content] {
  transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  animation: none !important;
}

/* 强制子菜单在右侧显示 */
[data-radix-dropdown-menu-sub-content][data-side="left"] {
  --radix-dropdown-menu-content-transform-origin: right center;
  left: auto !important;
  right: 100% !important;
  transform: translateX(8px) !important;
}

@layer base {
  :root {
    --background: 210 50% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 215 100% 30%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 215 60% 92%;
    --secondary-foreground: 215 100% 25%;
 
    --muted: 215 15% 95%;
    --muted-foreground: 215 40% 40%;
 
    --accent: 215 100% 35%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 215 100% 30%;

    --radius: 0.5rem;

    /* 自定义字体大小和行高变量 */
    --h1-font-size: 40px;
    --h1-line-height: 48px;
    --h2-font-size: 32px;
    --h2-line-height: 40px;
    --h3-font-size: 28px;
    --h3-line-height: 36px;
    --h4-font-size: 24px;
    --h4-line-height: 32px;
    --h5-font-size: 20px;
    --h5-line-height: 28px;
    --h6-font-size: 16px;
    --h6-line-height: 24px;
    --body-large-font-size: 20px;
    --body-large-line-height: 28px;
    --paragraph-code-font-size: 16px;
    --paragraph-code-line-height: 24px;

    /* 自定义字体族变量 */
    --font-family-custom: "Helvetica Neue", "SF Pro Display", Arial, Roboto, system-ui, sans-serif;

    /* 课程 Markdown 内容字体变量 */
    --font-size-lesson-markdown: 18px;
    --line-height-lesson-markdown: 175%;
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --primary: 210 80% 50%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
 
    --accent: 210 75% 45%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 210 80% 50%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

}

@layer components {
  .gov-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .gov-card {
    @apply bg-white rounded-lg shadow-md border border-secondary hover:shadow-lg transition-shadow duration-300;
  }

  .gov-button-primary {
    @apply bg-primary hover:bg-primary/90 text-white font-medium rounded-md transition-colors;
  }

  .gov-button-secondary {
    @apply bg-secondary hover:bg-secondary/90 text-secondary-foreground font-medium rounded-md transition-colors;
  }

  .gov-input {
    @apply rounded-md border border-gray-300 focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all;
  }

  /* 文本截断样式 */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}

/* 自定义Toast消息样式 */
.toast-message {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  max-width: 450px;
  width: auto !important;
}

/* pageflux-keyword 自定义样式 */
[data-pageflux-keyword] {
  @apply inline-block px-3 py-1 mx-1 text-sm font-bold transition-all duration-200 cursor-pointer rounded-full;

  /* 默认样式 - 紫色主题，类似GitHub Merged标签 */
  background-color: #8957e5;
  color: white;
  border: none;

  /* 悬停效果 */
  &:hover {
    background-color: #7c3aed;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(137, 87, 229, 0.3);
  }

  /* 激活状态 */
  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(137, 87, 229, 0.2);
  }

  /* 主题变体 */
  &.keyword-primary {
    background-color: #8957e5;
    color: white;
  }

  &.keyword-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border-color: #d1d5db;
  }

  &.keyword-success {
    background-color: #dcfce7;
    color: #166534;
    border-color: #86efac;
  }

  &.keyword-warning {
    background-color: #fef3c7;
    color: #92400e;
    border-color: #fcd34d;
  }

  &.keyword-danger {
    background-color: #fee2e2;
    color: #dc2626;
    border-color: #fca5a5;
  }
}

/* 政府网站特有样式 */
.gov-footer {
  @apply bg-gray-100 text-gray-600 py-8 mt-auto border-t border-gray-200;
}

.gov-nav {
  @apply bg-white shadow-sm border-b border-gray-200;
}

.hero-pattern {
  background-color: #f8fafc;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23003d99' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.scrollbar-none {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */

  /* Chrome, Safari, Opera */
}
.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* 定价卡片翻转动画 - 翻转后回到正面 */
@keyframes cardFlip-0 {
  0% {
    transform: translateY(32px) perspective(1000px) rotateY(0deg);
  }
  50% {
    transform: translateY(32px) perspective(1000px) rotateY(90deg);
    opacity: 0.3;
  }
  100% {
    transform: translateY(32px) perspective(1000px) rotateY(0deg);
  }
}

@keyframes cardFlip-1 {
  0% {
    transform: translateY(-32px) perspective(1000px) rotateY(0deg);
  }
  50% {
    transform: translateY(-32px) perspective(1000px) rotateY(-90deg);
    opacity: 0.3;
  }
  100% {
    transform: translateY(-32px) perspective(1000px) rotateY(0deg);
  }
}

@keyframes cardFlip-2 {
  0% {
    transform: translateY(32px) perspective(1000px) rotateY(0deg);
  }
  50% {
    transform: translateY(32px) perspective(1000px) rotateY(-90deg);
    opacity: 0.3;
  }
  100% {
    transform: translateY(32px) perspective(1000px) rotateY(0deg);
  }
}

/* 评价滚动动画 */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

/* 定价卡片悬停效果 */
.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 强制重新触发动画 */
.pricing-card-item {
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* 评价滚动动画 */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}



#body {
    margin-right: 0px !important;
    overflow-y: scroll !important;
}

/* 方法1: 创建一个重置类 */
.browser-default {
  /* 重置所有继承的样式 */
  all: initial;
  
  /* 恢复浏览器默认样式 */
  font-family: Times, "Times New Roman", serif; /* 浏览器默认字体 */
  font-size: 16px;
  line-height: normal;
  color: black;
  background: white;
  margin: 0;
  padding: 0;
  
  /* 让子元素也使用默认样式 */
  * {
    all: unset;
    display: revert;
    box-sizing: revert;
  }
  
  /* 恢复常用HTML元素的默认样式 */
  h1, h2, h3, h4, h5, h6 {
    display: block;
    font-weight: bold;
    margin-top: 0.67em;
    margin-bottom: 0.67em;
  }
  
  h1 { font-size: 2em; }
  h2 { font-size: 1.5em; }
  h3 { font-size: 1.17em; }
  h4 { font-size: 1em; }
  h5 { font-size: 0.83em; }
  h6 { font-size: 0.67em; }
  
  p {
    display: block;
    margin-top: 1em;
    margin-bottom: 1em;
  }
  
  strong, b {
    font-weight: bold;
  }
  
  em, i {
    font-style: italic;
  }
  
  code {
    font-family: monospace;
  }
  
  ul, ol {
    display: block;
    list-style-type: disc;
    margin-top: 1em;
    margin-bottom: 1em;
    padding-left: 40px;
  }
  
  ol {
    list-style-type: decimal;
  }
  
  li {
    display: list-item;
  }
}

/* Dify 聊天机器人样式 */
#dify-chatbot-bubble-button {
  background-color: #1c64f2 !important;
}

#dify-chatbot-bubble-window {
  width: 24rem !important;
  height: 40rem !important;
}