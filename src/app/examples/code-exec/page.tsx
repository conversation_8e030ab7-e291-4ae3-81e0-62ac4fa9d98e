/*
 * @Description: CodeExec组件使用示例页面
 * @Author: Devin
 * @Date: 2025-08-07
 */
"use client";

import React from "react";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { CodeExecSimple } from "@/feature/playground";

const CodeExecExamples: React.FC = () => {
  // 示例代码
  const examples = {
    javascript: `// JavaScript 示例
console.log("Hello, World!");

// 计算斐波那契数列
function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log("斐波那契数列前10项:");
for (let i = 0; i < 10; i++) {
  console.log(\`F(\${i}) = \${fibonacci(i)}\`);
}`,
    python: `# Python 示例
print("Hello, World!")

# 计算斐波那契数列
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

print("斐波那契数列前10项:")
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")`,
    cpp: `// C++ 示例
#include <iostream>
using namespace std;

// 计算斐波那契数列
int fibonacci(int n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

int main() {
    cout << "Hello, World!" << endl;
    
    cout << "斐波那契数列前10项:" << endl;
    for (int i = 0; i < 10; i++) {
        cout << "F(" << i << ") = " << fibonacci(i) << endl;
    }
    
    return 0;
}`,
  };

  return (
    <div className=" bg-gray-50 py-8">
      <Container size="xl">
        {/* 页面标题 */}
        <div className="mb-8">
          <Typography variant="h1" className="font-bold text-gray-900 mb-2">
            CodeExec 组件示例
          </Typography>
          <Typography variant="h6" className="text-gray-600">
            展示如何在不同场景下使用可复用的代码执行组件
          </Typography>
        </div>

        <Tabs defaultValue="simple" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="simple">简化版组件</TabsTrigger>
            <TabsTrigger value="multiple">多语言对比</TabsTrigger>
          </TabsList>

          {/* 简化版组件示例 */}
          <TabsContent value="simple" className="space-y-6">
            <CodeExecSimple
              language="javascript"
              code={examples.javascript}
              height="700px"
              theme="dark"
              onCodeChange={(code) =>
                console.log("Code changed:", code.length, "characters")
              }
              onOutput={(output) => console.log("Output received")}
            />
          </TabsContent>

          {/* 多语言对比示例 */}
          <TabsContent value="multiple" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>多语言代码对比</CardTitle>
                <Typography variant="small" className="text-gray-600">
                  同一个算法的不同语言实现
                </Typography>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-4">
                  <div>
                    <Typography
                      variant="h6"
                      className="mb-2 flex items-center gap-2"
                    >
                      JavaScript
                      <Badge variant="outline">JS</Badge>
                    </Typography>
                    <CodeExecSimple
                      language="javascript"
                      code={examples.javascript}
                      height="350px"
                      theme="dark"
                    />
                  </div>
                  <div>
                    <Typography
                      variant="h6"
                      className="mb-2 flex items-center gap-2"
                    >
                      Python
                      <Badge variant="outline">PY</Badge>
                    </Typography>
                    <CodeExecSimple
                      language="python"
                      code={examples.python}
                      height="350px"
                      theme="dark"
                    />
                  </div>
                  <div>
                    <Typography
                      variant="h6"
                      className="mb-2 flex items-center gap-2"
                    >
                      C++
                      <Badge variant="outline">CPP</Badge>
                    </Typography>
                    <CodeExecSimple
                      language="cpp"
                      code={examples.cpp}
                      height="350px"
                      theme="dark"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </Container>
    </div>
  );
};

export default CodeExecExamples;
