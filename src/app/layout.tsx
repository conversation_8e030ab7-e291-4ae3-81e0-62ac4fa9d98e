/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-06-09 18:17:09
 */
import React from "react";
import "./globals.css";
import { Inter } from "next/font/google";
import { Toaster } from "sonner";
import { getLocaleOnServer } from "@/i18n/server";
import MainLayout from "@/components/layout/main-layout";
import { I18nProvider } from "@/i18n/i18n-provider";
import { AuthProvider } from "@/lib/auth-context";
import { AuthEnhancedProvider } from "@/components/auth/auth-provider-enhanced";
import type { Metadata } from "next";
import { DifyChat } from "@/components/dify-chat";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Pageflux AI",
  description: "Pageflux AI",
  icons: {
    icon: "/logo.svg",
    shortcut: "/logo.svg",
    apple: "/logo.svg",
  },
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const locale = getLocaleOnServer();

  return (
    <html lang={locale}>
      <body className={`${inter.className}`} id={"body"}>
        <I18nProvider locale={locale}>
          {/* 全局 Toaster 放在 Provider 外，保证任何时刻都已挂载 */}
          <Toaster
            position="top-right"
            expand={true}
            visibleToasts={6}
            closeButton={true}
            richColors={true}
            toastOptions={{
              duration: 5000,
              className: "toast-message",
              style: {
                marginBottom: "0.5rem",
              },
            }}
          />
          <AuthProvider>
            <AuthEnhancedProvider>
              <MainLayout>{children}</MainLayout>
            </AuthEnhancedProvider>
          </AuthProvider>
        </I18nProvider>
        <DifyChat
          type="floating"
          showHeader={true}
          allowFileUpload={true}
          theme="light"
          title="Pageflux AI"
          subtitle="Your AI Assistant"
          placeholder="Ask me anything..."
          maxWidth="30rem"
        />
      </body>
    </html>
  );
}
