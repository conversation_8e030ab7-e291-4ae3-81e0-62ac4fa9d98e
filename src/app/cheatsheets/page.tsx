/*
 * @Description: Cheatsheets Page - Free downloadable programming guides
 * @Author: <PERSON>
 * @Date: 2025-07-31
 */
"use client";

import React from "react";
import { Container } from "@/components/ui/container";
import { HeroSection, CheatsheetsGrid } from "@/components/cheatsheets";
import { mockCheatsheets } from "@/mockdata/cheatsheets";
import { Cheatsheet } from "@/types/cheatsheet";

export default function CheatsheetsPage() {
  const handleCardClick = (cheatsheet: Cheatsheet) => {
    console.log("Clicked cheatsheet:", cheatsheet.title);
    // Handle card click - could navigate to detail page or trigger download
  };

  return (
    <div className="min-h-screen ">
      {/* Hero Section */}
      <HeroSection />

      {/* Main Content */}
      <div className="bg-indigo-25">
        <Container size="lg" className="py-8 ">
          <CheatsheetsGrid
            cheatsheets={mockCheatsheets}
            onCardClick={handleCardClick}
          />
        </Container>
      </div>
    </div>
  );
}
