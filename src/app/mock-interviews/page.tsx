/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-30 10:30:52
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import MockInterviewHero from "@/feature/mock-interview/mock-interview-hero";
import MockInterviewTestimonials from "@/feature/mock-interview/mock-interview-testimonials";
import MockInterviewFreeSection from "@/feature/mock-interview/mock-interview-free-section";
import MockInterviewFeaturesGrid from "@/feature/mock-interview/mock-interview-features-grid";
import MockInterviewSearchSection from "@/feature/mock-interview/mock-interview-search-section";
import FAQSection from "@/feature/shared/FAQSection";

// Mock Interview FAQ data
const mockInterviewFAQs = [
  {
    question: "How does the AI mock interview work?",
    answer:
      "Our AI mock interview simulates real technical interviews using advanced natural language processing. You can interact through voice, text, or code, and receive immediate feedback on your performance, communication skills, and technical knowledge.",
  },
  {
    question: "What types of interviews can I practice?",
    answer:
      "You can practice various types of technical interviews including coding interviews, system design interviews, behavioral interviews, and domain-specific technical discussions. We cover all major tech companies' interview formats.",
  },
  {
    question: "Do I get feedback after each interview?",
    answer:
      "Yes! After each mock interview, you'll receive detailed feedback covering your technical accuracy, communication clarity, problem-solving approach, and areas for improvement. The AI provides specific suggestions to help you improve.",
  },
  {
    question: "Can I practice coding problems during the interview?",
    answer:
      "Absolutely! Our platform includes an integrated coding environment where you can write, test, and debug code in real-time during your mock interview. It supports multiple programming languages.",
  },
  {
    question: "How realistic are the AI interviews compared to real ones?",
    answer:
      "Our AI interviews are designed to closely mimic real technical interviews at top tech companies. They include similar question patterns, follow-up questions, and evaluation criteria used by actual interviewers.",
  },
  {
    question: "Is there a limit to how many mock interviews I can take?",
    answer:
      "With our unlimited plan, you can take as many mock interviews as you want. Free users get access to a limited number of practice sessions per month.",
  },
];

export default function MockInterviewPage() {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <MockInterviewHero />

      {/* Testimonials Section */}
      <MockInterviewTestimonials />

      {/* Free Section */}
      <MockInterviewFreeSection />

      {/* Features Grid Section */}
      <MockInterviewFeaturesGrid />

      {/* Search and Filter Section */}
      <MockInterviewSearchSection />

      {/* FAQ Section */}
      <FAQSection
        title={t("mockInterviews.faq.title")}
        containerSize="lg-plus"
        className="py-16"
      />
    </div>
  );
}
