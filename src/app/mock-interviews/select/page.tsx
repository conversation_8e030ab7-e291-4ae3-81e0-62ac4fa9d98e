"use client";

import React, { useState, useMemo } from "react";
import { Typography } from "@/components/ui/typography";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import InterviewCard from "@/components/cards/InterviewCard";
import InterviewSearchAndFilter from "@/components/common/InterviewSearchAndFilter";
import {
  ArrowLeft as ArrowLeftIcon,
  Grid as GridIcon,
  List as ListIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { mockInterviews } from "@/mockdata/mock-interviews";
import { InterviewSearchProps, MockInterview } from "@/types/mock-interview";

export default function InterviewSelectPage() {
  const [searchProps, setSearchProps] = useState<InterviewSearchProps>({
    searchQuery: "",
    filters: {
      categories: [],
      difficulties: [],
      companies: [],
      duration: { min: 0, max: 120 },
      isFree: undefined,
      isPopular: undefined,
    },
    sortBy: "popularity",
    sortOrder: "desc",
  });
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [bookmarkedInterviews, setBookmarkedInterviews] = useState<string[]>([]);

  // Filter and sort interviews based on search props
  const filteredAndSortedInterviews = useMemo(() => {
    let interviews = [...mockInterviews];

    // Apply search filter
    if (searchProps.searchQuery) {
      const query = searchProps.searchQuery.toLowerCase();
      interviews = interviews.filter(interview =>
        interview.title.toLowerCase().includes(query) ||
        interview.description.toLowerCase().includes(query) ||
        interview.tags.some(tag => tag.toLowerCase().includes(query)) ||
        (interview.company && interview.company.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (searchProps.filters.categories.length > 0) {
      interviews = interviews.filter(interview =>
        searchProps.filters.categories.includes(interview.category)
      );
    }

    // Apply difficulty filter
    if (searchProps.filters.difficulties.length > 0) {
      interviews = interviews.filter(interview =>
        searchProps.filters.difficulties.includes(interview.difficulty)
      );
    }

    // Apply company filter
    if (searchProps.filters.companies.length > 0) {
      interviews = interviews.filter(interview =>
        interview.company && searchProps.filters.companies.includes(interview.company)
      );
    }

    // Apply duration filter
    interviews = interviews.filter(interview =>
      interview.duration >= searchProps.filters.duration.min && 
      interview.duration <= searchProps.filters.duration.max
    );

    // Apply free filter
    if (searchProps.filters.isFree !== undefined) {
      interviews = interviews.filter(interview => 
        interview.isFree === searchProps.filters.isFree
      );
    }

    // Apply popular filter
    if (searchProps.filters.isPopular !== undefined) {
      interviews = interviews.filter(interview => 
        interview.isPopular === searchProps.filters.isPopular
      );
    }

    // Apply sorting
    interviews.sort((a, b) => {
      let comparison = 0;
      
      switch (searchProps.sortBy) {
        case "popularity":
          comparison = b.completionCount - a.completionCount;
          break;
        case "rating":
          comparison = b.averageRating - a.averageRating;
          break;
        case "difficulty":
          const difficultyOrder = { "Easy": 1, "Medium": 2, "Hard": 3 };
          comparison = difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
          break;
        case "duration":
          comparison = a.duration - b.duration;
          break;
        case "newest":
          // For demo purposes, assume newer interviews have higher IDs
          comparison = b.id.localeCompare(a.id);
          break;
        default:
          comparison = 0;
      }

      return searchProps.sortOrder === "asc" ? comparison : -comparison;
    });

    return interviews;
  }, [searchProps]);

  const handleStartInterview = (interviewId: string) => {
    console.log("Starting interview:", interviewId);
    // Navigate to interview session
    window.location.href = `/mock-interview/session/${interviewId}`;
  };

  const handleBookmarkInterview = (interviewId: string) => {
    setBookmarkedInterviews(prev => 
      prev.includes(interviewId)
        ? prev.filter(id => id !== interviewId)
        : [...prev, interviewId]
    );
  };

  const handleBackToHome = () => {
    window.location.href = "/mock-interview";
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToHome}
              className="flex items-center gap-2"
            >
              <ArrowLeftIcon className="w-4 h-4" />
              Back to Mock Interviews
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <Typography variant="h1" className="font-bold text-gray-900 mb-2">
                Select Your Interview
              </Typography>
              <Typography variant="large" className="text-gray-600">
                Choose from {mockInterviews.length} AI-powered mock interviews
              </Typography>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center gap-2 bg-white rounded-lg p-1 border">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="flex items-center gap-2"
              >
                <GridIcon className="w-4 h-4" />
                Grid
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="flex items-center gap-2"
              >
                <ListIcon className="w-4 h-4" />
                List
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <InterviewSearchAndFilter
            interviews={mockInterviews}
            onSearchChange={setSearchProps}
          />
        </div>

        {/* Results Summary */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Typography variant="large" className="text-gray-700">
              {filteredAndSortedInterviews.length} interviews found
            </Typography>
            {searchProps.searchQuery && (
              <Badge variant="outline">
                Search: "{searchProps.searchQuery}"
              </Badge>
            )}
          </div>

          {bookmarkedInterviews.length > 0 && (
            <Typography variant="small" className="text-gray-500">
              {bookmarkedInterviews.length} bookmarked
            </Typography>
          )}
        </div>

        {/* Interview Grid/List */}
        {filteredAndSortedInterviews.length > 0 ? (
          <div className={cn(
            viewMode === "grid" 
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              : "space-y-4"
          )}>
            {filteredAndSortedInterviews.map((interview) => (
              <InterviewCard
                key={interview.id}
                interview={interview}
                variant={viewMode === "list" ? "compact" : "default"}
                onStart={handleStartInterview}
                onBookmark={handleBookmarkInterview}
                isBookmarked={bookmarkedInterviews.includes(interview.id)}
                showCompany={true}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-16 bg-white rounded-lg">
            <div className="text-6xl mb-4">🔍</div>
            <Typography variant="h3" className="font-bold text-gray-900 mb-2">
              No interviews found
            </Typography>
            <Typography variant="large" className="text-gray-600 mb-6">
              Try adjusting your search criteria or filters
            </Typography>
            <Button
              onClick={() => setSearchProps({
                searchQuery: "",
                filters: {
                  categories: [],
                  difficulties: [],
                  companies: [],
                  duration: { min: 0, max: 120 },
                  isFree: undefined,
                  isPopular: undefined,
                },
                sortBy: "popularity",
                sortOrder: "desc",
              })}
            >
              Clear All Filters
            </Button>
          </div>
        )}

        {/* Load More Button */}
        {filteredAndSortedInterviews.length > 0 && filteredAndSortedInterviews.length >= 12 && (
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Load More Interviews
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
