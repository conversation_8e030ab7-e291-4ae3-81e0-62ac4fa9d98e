"use client";

import React, { useState } from "react";
import { Typography } from "@/components/ui/typography";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import InterviewHistory from "@/components/common/InterviewHistory";
import InterviewAnalytics from "@/components/common/InterviewAnalytics";
import InterviewCard from "@/components/cards/InterviewCard";
import {
  User as UserIcon,
  TrendingUp as TrendingUpIcon,
  Target as TargetIcon,
  Calendar as CalendarIcon,
  Award as AwardIcon,
  BookOpen as BookOpenIcon,
  Play as PlayIcon,
  Star as StarIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { mockInterviews, getFreeInterviews } from "@/mockdata/mock-interviews";
import { InterviewStats, Achievement, InterviewCategory, FeedbackScore } from "@/types/mock-interview";

export default function MockInterviewDashboard() {
  const [activeTab, setActiveTab] = useState("overview");

  // Mock user data
  const mockUserStats: InterviewStats = {
    totalInterviews: 12,
    completedInterviews: 8,
    averageScore: 76,
    strongestCategory: "System Design",
    weakestCategory: "Behavioral Interview",
    totalTimeSpent: 480, // 8 hours
    improvementTrend: "improving"
  };

  const mockAchievements: Achievement[] = [
    {
      id: "first-interview",
      title: "First Steps",
      description: "Completed your first mock interview",
      icon: "🎯",
      unlockedAt: new Date("2024-01-10"),
      category: "milestone"
    },
    {
      id: "system-design-master",
      title: "System Design Pro",
      description: "Scored 'Excellent' in 3 System Design interviews",
      icon: "🏗️",
      unlockedAt: new Date("2024-01-15"),
      category: "performance"
    },
    {
      id: "week-streak",
      title: "Weekly Warrior",
      description: "Practiced interviews for 7 consecutive days",
      icon: "🔥",
      unlockedAt: new Date("2024-01-20"),
      category: "streak"
    }
  ];

  const mockRecentScores = [
    { category: "System Design" as InterviewCategory, score: "Good" as FeedbackScore, date: new Date("2024-01-20") },
    { category: "Coding Interview" as InterviewCategory, score: "Excellent" as FeedbackScore, date: new Date("2024-01-18") },
    { category: "MAANG+" as InterviewCategory, score: "Average" as FeedbackScore, date: new Date("2024-01-15") },
    { category: "System Design" as InterviewCategory, score: "Good" as FeedbackScore, date: new Date("2024-01-12") },
  ];

  const recommendedInterviews = mockInterviews.filter(interview => 
    interview.category === mockUserStats.weakestCategory || interview.isPopular
  ).slice(0, 3);

  const handleStartInterview = (interviewId: string) => {
    window.location.href = `/mock-interview/session/${interviewId}`;
  };

  const handleViewSession = (sessionId: string) => {
    console.log("Viewing session:", sessionId);
  };

  const handleRetryInterview = (interviewId: string) => {
    window.location.href = `/mock-interview/session/${interviewId}`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="h1" className="font-bold text-gray-900 mb-2">
                Interview Dashboard
              </Typography>
              <Typography variant="large" className="text-gray-600">
                Track your progress and continue improving your interview skills
              </Typography>
            </div>
            <Button
              onClick={() => window.location.href = "/mock-interview/select"}
              className="flex items-center gap-2"
            >
              <PlayIcon className="w-4 h-4" />
              Start New Interview
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="achievements">Achievements</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Typography variant="small" className="text-gray-600 mb-1">
                        Total Interviews
                      </Typography>
                      <Typography variant="h2" className="font-bold text-gray-900">
                        {mockUserStats.totalInterviews}
                      </Typography>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <UserIcon className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Typography variant="small" className="text-gray-600 mb-1">
                        Average Score
                      </Typography>
                      <Typography variant="h2" className="font-bold text-gray-900">
                        {mockUserStats.averageScore}%
                      </Typography>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                      <TrendingUpIcon className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Typography variant="small" className="text-gray-600 mb-1">
                        Strongest Area
                      </Typography>
                      <Typography variant="large" className="font-semibold text-gray-900">
                        {mockUserStats.strongestCategory}
                      </Typography>
                    </div>
                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                      <TargetIcon className="w-6 h-6 text-purple-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Typography variant="small" className="text-gray-600 mb-1">
                        Time Practiced
                      </Typography>
                      <Typography variant="h2" className="font-bold text-gray-900">
                        {Math.round(mockUserStats.totalTimeSpent / 60)}h
                      </Typography>
                    </div>
                    <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                      <CalendarIcon className="w-6 h-6 text-orange-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity & Recommendations */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recommended Interviews */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpenIcon className="w-5 h-5" />
                    Recommended for You
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recommendedInterviews.map((interview) => (
                      <InterviewCard
                        key={interview.id}
                        interview={interview}
                        variant="compact"
                        onStart={handleStartInterview}
                        showCompany={true}
                      />
                    ))}
                  </div>
                  <Button
                    variant="outline"
                    className="w-full mt-4"
                    onClick={() => window.location.href = "/mock-interview/select"}
                  >
                    View All Interviews
                  </Button>
                </CardContent>
              </Card>

              {/* Recent Achievements */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AwardIcon className="w-5 h-5" />
                    Recent Achievements
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockAchievements.slice(0, 3).map((achievement) => (
                      <div key={achievement.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <div className="text-2xl">{achievement.icon}</div>
                        <div className="flex-1">
                          <Typography variant="small" className="font-medium text-gray-900">
                            {achievement.title}
                          </Typography>
                          <Typography variant="small" className="text-gray-600">
                            {achievement.description}
                          </Typography>
                        </div>
                        <Typography variant="small" className="text-gray-500">
                          {achievement.unlockedAt.toLocaleDateString()}
                        </Typography>
                      </div>
                    ))}
                  </div>
                  <Button
                    variant="outline"
                    className="w-full mt-4"
                    onClick={() => setActiveTab("achievements")}
                  >
                    View All Achievements
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Progress Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Your Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <InterviewAnalytics
                  stats={mockUserStats}
                  recentScores={mockRecentScores}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history">
            <InterviewHistory
              sessions={[]}
              onViewSession={handleViewSession}
              onRetryInterview={handleRetryInterview}
            />
          </TabsContent>

          <TabsContent value="analytics">
            <InterviewAnalytics
              stats={mockUserStats}
              recentScores={mockRecentScores}
            />
          </TabsContent>

          <TabsContent value="achievements" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AwardIcon className="w-5 h-5" />
                  Your Achievements
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {mockAchievements.map((achievement) => (
                    <div
                      key={achievement.id}
                      className="border rounded-lg p-4 text-center hover:shadow-md transition-shadow"
                    >
                      <div className="text-4xl mb-3">{achievement.icon}</div>
                      <Typography variant="h6" className="font-semibold mb-2">
                        {achievement.title}
                      </Typography>
                      <Typography variant="small" className="text-gray-600 mb-3">
                        {achievement.description}
                      </Typography>
                      <Typography variant="small" className="text-gray-500">
                        Unlocked {achievement.unlockedAt.toLocaleDateString()}
                      </Typography>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Achievement Categories */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {["milestone", "performance", "streak", "completion"].map((category) => (
                <Card key={category}>
                  <CardContent className="p-4 text-center">
                    <div className="w-12 h-12 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-3">
                      <StarIcon className="w-6 h-6 text-blue-600" />
                    </div>
                    <Typography variant="h6" className="font-semibold capitalize mb-1">
                      {category}
                    </Typography>
                    <Typography variant="small" className="text-gray-600">
                      {mockAchievements.filter(a => a.category === category).length} earned
                    </Typography>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
