"use client";

import React, { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import InterviewTimer from "@/components/common/InterviewTimer";
import CodeEditor from "@/components/common/CodeEditor";
import Whiteboard from "@/components/common/Whiteboard";
import InterviewFeedbackComponent from "@/components/common/InterviewFeedback";
import {
  ArrowLeft as ArrowLeftIcon,
  Mic as MicIcon,
  MicOff as MicOffIcon,
  Video as VideoIcon,
  VideoOff as VideoOffIcon,
  MessageSquare as MessageSquareIcon,
  Send as SendIcon,
  Lightbulb as HintIcon,
  CheckCircle as CheckIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { mockInterviews } from "@/mockdata/mock-interviews";
import { MockInterview, InterviewFeedback } from "@/types/mock-interview";

export default function InterviewSessionPage() {
  const { t } = useTranslation();
  const params = useParams();
  const interviewId = params.id as string;

  const [interview, setInterview] = useState<MockInterview | null>(null);
  const [sessionStarted, setSessionStarted] = useState(false);
  const [sessionCompleted, setSessionCompleted] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [timeSpent, setTimeSpent] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [isMicOn, setIsMicOn] = useState(false);
  const [isVideoOn, setIsVideoOn] = useState(false);
  const [chatMessages, setChatMessages] = useState<
    Array<{
      id: string;
      sender: "user" | "ai";
      message: string;
      timestamp: Date;
    }>
  >([]);
  const [currentMessage, setCurrentMessage] = useState("");
  const [showHints, setShowHints] = useState(false);
  const [code, setCode] = useState("");
  const [drawing, setDrawing] = useState("");
  const [feedback, setFeedback] = useState<InterviewFeedback | null>(null);

  useEffect(() => {
    const foundInterview = mockInterviews.find((i) => i.id === interviewId);
    if (foundInterview) {
      setInterview(foundInterview);
    }
  }, [interviewId]);

  const handleStartSession = () => {
    setSessionStarted(true);
    // Add initial AI message
    setChatMessages([
      {
        id: "1",
        sender: "ai",
        message: `Hello! I'm your AI interviewer. Today we'll be working on "${interview?.title}". Are you ready to begin?`,
        timestamp: new Date(),
      },
    ]);
  };

  const handleEndSession = () => {
    setSessionCompleted(true);
    // Generate mock feedback
    const mockFeedback: InterviewFeedback = {
      id: "feedback-1",
      interviewId: interviewId,
      overallScore: "Good",
      strengths: [
        "Clear communication throughout the interview",
        "Good understanding of system design principles",
        "Asked clarifying questions before starting",
      ],
      areasForImprovement: [
        "Could have considered more edge cases",
        "Database design could be more detailed",
        "Scalability discussion needed more depth",
      ],
      detailedFeedback: {
        problemSolving: "Good",
        communication: "Excellent",
        technicalKnowledge: "Good",
        systemDesignThinking: "Average",
      },
      recommendations: [
        "Practice more system design problems",
        "Study database sharding techniques",
        "Review microservices architecture patterns",
      ],
      nextSteps: [
        "Take another system design interview",
        "Review the feedback and practice weak areas",
        "Study real-world system architectures",
      ],
      estimatedReadiness: "Almost Ready",
    };
    setFeedback(mockFeedback);
  };

  const handleSendMessage = () => {
    if (!currentMessage.trim()) return;

    const userMessage = {
      id: Date.now().toString(),
      sender: "user" as const,
      message: currentMessage,
      timestamp: new Date(),
    };

    setChatMessages((prev) => [...prev, userMessage]);
    setCurrentMessage("");

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = {
        id: (Date.now() + 1).toString(),
        sender: "ai" as const,
        message:
          "That's a good point. Can you elaborate on how you would handle the scalability challenges?",
        timestamp: new Date(),
      };
      setChatMessages((prev) => [...prev, aiResponse]);
    }, 1000);
  };

  const handleTimeUp = () => {
    handleEndSession();
  };

  const handleTimeUpdate = (remainingTime: number) => {
    setTimeSpent((interview?.duration || 0) * 60 - remainingTime);
  };

  const handleBackToSelection = () => {
    window.location.href = "/mock-interview/select";
  };

  if (!interview) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Typography variant="h3">{t("errors.common.dataNotFound")}</Typography>
      </div>
    );
  }

  if (sessionCompleted && feedback) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <InterviewFeedbackComponent
            feedback={feedback}
            onRetry={() => {
              setSessionCompleted(false);
              setSessionStarted(false);
              setCurrentQuestionIndex(0);
              setTimeSpent(0);
              setChatMessages([]);
              setCode("");
              setDrawing("");
              setFeedback(null);
            }}
            onNext={() => (window.location.href = "/mock-interview/select")}
          />
        </div>
      </div>
    );
  }

  if (!sessionStarted) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={handleBackToSelection}
              className="flex items-center gap-2 mb-4"
            >
              <ArrowLeftIcon className="w-4 h-4" />
              Back to Selection
            </Button>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-2xl mb-2">
                    {interview.title}
                  </CardTitle>
                  <div className="flex items-center gap-2 mb-4">
                    <Badge
                      className={cn(
                        interview.difficulty === "Easy" &&
                          "bg-green-100 text-green-800",
                        interview.difficulty === "Medium" &&
                          "bg-yellow-100 text-yellow-800",
                        interview.difficulty === "Hard" &&
                          "bg-red-100 text-red-800"
                      )}
                    >
                      {interview.difficulty}
                    </Badge>
                    <Badge variant="outline">{interview.category}</Badge>
                    <Badge variant="outline">{interview.duration} min</Badge>
                    {interview.company && (
                      <Badge variant="outline">{interview.company}</Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Typography variant="large" className="text-gray-700 mb-6">
                {interview.description}
              </Typography>

              <div className="mb-6">
                <Typography variant="h6" className="font-semibold mb-3">
                  What you'll learn:
                </Typography>
                <ul className="space-y-2">
                  {interview.learningObjectives.map((objective, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckIcon className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <Typography variant="small">{objective}</Typography>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="bg-blue-50 rounded-lg p-4 mb-6">
                <Typography
                  variant="h6"
                  className="font-semibold mb-2 text-blue-900"
                >
                  Interview Setup
                </Typography>
                <div className="space-y-2 text-sm text-blue-800">
                  <p>• Make sure you have a stable internet connection</p>
                  <p>• Find a quiet environment for the best experience</p>
                  <p>• You can use voice, text, or both to communicate</p>
                  <p>• The AI interviewer will provide real-time feedback</p>
                </div>
              </div>

              <div className="flex gap-4">
                <Button
                  onClick={handleStartSession}
                  size="lg"
                  className="flex-1"
                >
                  Start Interview
                </Button>
                <Button
                  variant="outline"
                  onClick={handleBackToSelection}
                  size="lg"
                >
                  Choose Different Interview
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Typography variant="h6" className="font-semibold">
              {interview.title}
            </Typography>
            <Badge variant="outline">{interview.category}</Badge>
          </div>

          <div className="flex items-center gap-4">
            {/* Media Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsMicOn(!isMicOn)}
                className={cn(
                  isMicOn
                    ? "bg-green-100 text-green-700"
                    : "bg-red-100 text-red-700"
                )}
              >
                {isMicOn ? (
                  <MicIcon className="w-4 h-4" />
                ) : (
                  <MicOffIcon className="w-4 h-4" />
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsVideoOn(!isVideoOn)}
                className={cn(
                  isVideoOn
                    ? "bg-green-100 text-green-700"
                    : "bg-red-100 text-red-700"
                )}
              >
                {isVideoOn ? (
                  <VideoIcon className="w-4 h-4" />
                ) : (
                  <VideoOffIcon className="w-4 h-4" />
                )}
              </Button>
            </div>

            <Button variant="destructive" onClick={handleEndSession}>
              End Interview
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-120px)]">
          {/* Main Content Area */}
          <div className="lg:col-span-2 space-y-4">
            {/* Timer */}
            <InterviewTimer
              totalDuration={interview.duration * 60}
              onTimeUp={handleTimeUp}
              onTimeUpdate={handleTimeUpdate}
              autoStart={true}
            />

            {/* Question/Problem Area */}
            <Card className="flex-1">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Problem Statement</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowHints(!showHints)}
                  >
                    <HintIcon className="w-4 h-4 mr-2" />
                    {showHints ? "Hide Hints" : "Show Hints"}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Typography variant="large" className="mb-4">
                  {interview.description}
                </Typography>

                {showHints && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <Typography
                      variant="h6"
                      className="font-semibold mb-2 text-yellow-800"
                    >
                      Hints:
                    </Typography>
                    <ul className="space-y-1 text-sm text-yellow-700">
                      <li>• Think about the core requirements first</li>
                      <li>• Consider scalability from the beginning</li>
                      <li>• Don't forget about data consistency</li>
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Work Area */}
            <div className="flex-1">
              {interview.category === "Coding Interview" ? (
                <CodeEditor
                  initialCode="// Write your solution here\nfunction solution() {\n  \n}"
                  language="javascript"
                  onCodeChange={setCode}
                  testCases={[
                    {
                      id: "1",
                      input: "[1,2,3,4,5]",
                      expectedOutput: "[5,4,3,2,1]",
                      explanation: "Reverse the array",
                    },
                  ]}
                />
              ) : (
                <Whiteboard
                  width={800}
                  height={400}
                  onDrawingChange={setDrawing}
                />
              )}
            </div>
          </div>

          {/* Chat Sidebar */}
          <div className="flex flex-col bg-white rounded-lg border">
            <div className="p-4 border-b">
              <Typography
                variant="h6"
                className="font-semibold flex items-center gap-2"
              >
                <MessageSquareIcon className="w-5 h-5" />
                AI Interviewer
              </Typography>
            </div>

            <div className="flex-1 p-4 overflow-y-auto space-y-4">
              {chatMessages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "flex",
                    message.sender === "user" ? "justify-end" : "justify-start"
                  )}
                >
                  <div
                    className={cn(
                      "max-w-[80%] rounded-lg p-3 text-sm",
                      message.sender === "user"
                        ? "bg-blue-600 text-white"
                        : "bg-gray-100 text-gray-900"
                    )}
                  >
                    {message.message}
                  </div>
                </div>
              ))}
            </div>

            <div className="p-4 border-t">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={currentMessage}
                  onChange={(e) => setCurrentMessage(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                  placeholder="Type your message..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <Button onClick={handleSendMessage} size="sm">
                  <SendIcon className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
