"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import { Box } from "@/components/ui/box";
import PricingPlans from "@/feature/price/PricingPlans";
import AILearningSection from "@/feature/shared/AILearningSection";
import FAQSection from "@/feature/shared/FAQSection";
import TestimonialsCarousel from "@/feature/shared/TestimonialsCarousel";
import LogoCarousel from "@/components/common/logo-carousel";

// 公司logos数据
const companyLogos = [
  {
    name: "Netflix",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Apple",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "<PERSON><PERSON>",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Google",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Amazon",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
  {
    name: "Coinbase",
    logoPath: "/images/home/<USER>",
    hasLogo: true,
  },
];

// 获取用户评价数据的函数
const getTestimonials = (t: any) => {
  const sampleTestimonials = t("pricing.sampleTestimonials", {
    returnObjects: true,
  }) as Array<{
    name: string;
    role: string;
    content: string;
  }>;

  return sampleTestimonials.map((testimonial, index) => ({
    ...testimonial,
    avatar:
      [
        "https://www.educative.io/static/imgs/FelipeMatheus.png",
        "https://www.educative.io/static/imgs/AdinaOng.png",
        "/static/imgs/CliffordFajardo.png",
        "https://www.educative.io/static/imgs/ThomasChang.png",
      ][index] || "https://www.educative.io/static/imgs/FelipeMatheus.png",
  }));
};

export default function PricingPage() {
  const { t } = useTranslation();
  const [billingPeriod, setBillingPeriod] = useState<
    "monthly" | "annual" | "twoYear"
  >("annual");
  const [timeLeft, setTimeLeft] = useState({
    days: 1,
    hours: 14,
    minutes: 35,
    seconds: 20,
  });

  // 获取翻译后的testimonials数据
  const testimonials = getTestimonials(t);

  return (
    <Box className="bg-white min-h-screen">
      <Container size="lg" className="py-12">
        {/* 主标题 */}
        <Box className="text-center my-12">
          <Typography className="font-bold text-gray-900 mb-4 text-3xl md:text-4xl">
            {t("pricing.hero.title")}
          </Typography>

          {/* 公司logos */}
          <div className="mt-8 flex flex-col items-center w-full">
            <div className="flex text-md text-gray-700 text-center gap-6">
              <div className="mb-4">
                {t("pricing.hero.companies.text")}{" "}
                <span className="text-[#6366f1] font-bold">
                  {t("pricing.hero.companies.count")}
                </span>{" "}
                {t("pricing.hero.companies.suffix")}
              </div>
              <LogoCarousel
                logos={companyLogos}
                speed={20}
                className="max-w-sm mx-auto"
              />
            </div>
          </div>
        </Box>
        {/* 定价卡片 */}
        <PricingPlans></PricingPlans>
      </Container>

      {/* 用户评价走马灯 */}
      <TestimonialsCarousel testimonials={testimonials} title="" subtitle="" />

      {/* AI功能介绍 - 全宽背景 */}
      <AILearningSection showCTAButton={false} />

      {/* FAQ部分 */}
      <FAQSection />
    </Box>
  );
}
