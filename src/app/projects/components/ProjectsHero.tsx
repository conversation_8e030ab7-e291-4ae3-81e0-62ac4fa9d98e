// components/ProjectsHero.tsx
import { Container } from "@/components/ui/container";
import FluxIcon from "@/components/fluxIcon";
import React from "react";

const features = [
  {
    iconName: "no-third-party",
    text: "No third-party access needed",
  },
  {
    iconName: "code-browser",
    text: "Code inside the browser",
  },
  {
    iconName: "no-sandbox-setup",
    text: "No development sandbox set-up",
  },
  {
    iconName: "computing-resources",
    text: "Access to computing resources and storage space",
  },
];

const ProjectsHero = () => {
  return (
    <Container
      size="lg-plus"
      className="bg-gray-50 dark:bg-gray-900 w-full py-16"
    >
      <div className="mx-auto flex max-w-6xl flex-col lg:flex-row items-start justify-between gap-10">
        {/* Left Content */}
        <div className="flex-1">
          <div className="mb-4 flex items-center gap-2 text-sm uppercase tracking-wide text-gray-500 dark:text-gray-300">
            <FluxIcon
              name="projects-logo"
              width={16}
              height={16}
              className="fill-current"
            />
            <span className="font-medium">Pageflux AI Projects</span>
          </div>

          <h1 className="text-4xl font-bold text-gray-900 dark:text-white leading-tight mb-6">
            The Best Way To Learn
            <br />
            Is By Doing
          </h1>

          <p className=" text-gray-600 dark:text-gray-400 max-w-xl leading-relaxed text-sm">
            Pageflux AI Projects enables software engineers through a
            learn-by-building model. Pageflux AI's hands-on interactive learning
            platform allows learners to develop and apply skills through
            full-featured technical projects that don’t require any production
            setup.
          </p>
        </div>

        {/* Right Feature List */}
        <div className="flex flex-col space-y-6">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center gap-4 max-w-sm">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 text-indigo-600">
                <FluxIcon name={feature.iconName} width={20} height={20} />
              </div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                {feature.text}
              </p>
            </div>
          ))}
        </div>
      </div>
    </Container>
  );
};

export default ProjectsHero;
