/*
 * @Description: Projects Page
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import FilterableContentLayout from "@/components/common/FilterableContentLayout";
import ProjectsHero from "./components/ProjectsHero";
import {
  projectsData,
  projectTopics,
  projectTechnologies,
} from "@/mockdata/projects";

export default function ProjectsPage() {
  const { t } = useTranslation();

  const filterSections = [
    {
      title: t("projects.filters.skillLevel"),
      key: "skillLevel",
      options: [
        { id: "Beginner", label: t("common.difficulty.beginner") },
        { id: "Intermediate", label: t("common.difficulty.intermediate") },
        { id: "Advanced", label: t("common.difficulty.advanced") },
      ],
    },
    {
      title: t("projects.filters.topics"),
      key: "topics",
      options: projectTopics.map((topic) => ({
        id: topic,
        label: topic,
      })),
      showMore: true,
      initialShowCount: 10,
    },
    {
      title: t("projects.filters.technologies"),
      key: "technologies",
      options: projectTechnologies.map((tech) => ({
        id: tech,
        label: tech,
      })),
      showMore: true,
      initialShowCount: 10,
    },
  ];

  const handleItemClick = (id: string) => {
    console.log("Project clicked:", id);
    // TODO: Navigate to project detail page
  };

  const handleFavoriteToggle = (id: string) => {
    console.log("Favorite toggled for project:", id);
    // TODO: Implement favorite functionality
  };

  return (
    <div>
      <ProjectsHero />
      <FilterableContentLayout
        searchPlaceholder={t("projects.search.placeholder")}
        filterSections={filterSections}
        items={projectsData}
        type="project"
        onItemClick={handleItemClick}
        onFavoriteToggle={handleFavoriteToggle}
      />
    </div>
  );
}
