/*
 * @Description: Business Page - Coming Soon
 * @Author: <PERSON>
 * @Date: 2025-08-11
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  Building2,
  Users,
  TrendingUp,
  Shield,
  Clock,
  Mail,
  Bell,
} from "lucide-react";

export default function BusinessPage() {
  const { t } = useTranslation();
  const [email, setEmail] = React.useState("");
  const [isSubscribed, setIsSubscribed] = React.useState(false);

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      setEmail("");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Container size="xl" className="py-20">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Clock className="w-4 h-4" />
            {t("business.comingSoon")}
          </div>

          <Typography
            variant="h1"
            className="text-4xl md:text-6xl font-bold text-gray-900 mb-6"
          >
            Pageflux AI
            <span className="block text-blue-600">Business</span>
          </Typography>

          <Typography
            variant="h3"
            className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto"
          >
            {t("business.hero.subtitle")}
          </Typography>

          {/* Email Subscription */}
          <div className="max-w-md mx-auto">
            {!isSubscribed ? (
              <form onSubmit={handleSubscribe} className="flex gap-3">
                <Input
                  type="email"
                  placeholder={t("business.emailSubscription.placeholder")}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="flex-1"
                  required
                />
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                  {t("business.emailSubscription.button")}
                </Button>
              </form>
            ) : (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-center gap-2 text-green-700">
                  <Mail className="w-5 h-5" />
                  <span className="font-medium">
                    {t("business.emailSubscription.success")}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Contact Info */}
        <div className="text-center mt-12">
          <Typography className="text-gray-600 mb-4">
            {t("business.contact.question")}
          </Typography>
          <Typography className="text-blue-600 font-medium">
            {t("business.contact.email")}
          </Typography>
        </div>
      </Container>
    </div>
  );
}
