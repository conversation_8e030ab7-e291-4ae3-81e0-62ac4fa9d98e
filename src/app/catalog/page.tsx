"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { Typography } from "@/components/ui/typography";
import { Container } from "@/components/ui/container";
import SubjectCard from "@/components/cards/SubjectCard";
import { getAllCategoryInfos } from "@/mockdata/maindata";

// 所有主题和语言
const popularLanguages = [
  // 热门主题（第一行）
  {
    title: "Grow My Skill Set",
    icon: <span className="text-lg">✖️</span>,
    color: "bg-gray-50",
  },
  {
    title: "Interview Prep",
    icon: <span className="text-lg">💼</span>,
    color: "bg-blue-50",
  },
  {
    title: "Algorithms",
    icon: <span className="text-lg font-bold text-blue-600">📊</span>,
    color: "bg-blue-50",
  },
  {
    title: "C",
    icon: <span className="text-lg font-bold text-blue-600">C</span>,
    color: "bg-blue-50",
  },
  // 编程语言和技术
  {
    title: "C#",
    icon: <span className="text-lg font-bold text-purple-600">C#</span>,
    color: "bg-purple-50",
  },
  {
    title: "C++",
    icon: <span className="text-lg font-bold text-blue-600">C++</span>,
    color: "bg-blue-50",
  },
  {
    title: "Cloud",
    icon: <span className="text-lg">☁️</span>,
    color: "bg-sky-50",
  },
  {
    title: "CSS",
    icon: <span className="text-lg font-bold text-orange-600">🎨</span>,
    color: "bg-orange-50",
  },
  {
    title: "GraphQL",
    icon: <span className="text-lg">🔗</span>,
    color: "bg-pink-50",
  },
  {
    title: "HTML",
    icon: <span className="text-lg font-bold text-red-600">🌐</span>,
    color: "bg-red-50",
  },
  {
    title: "Java",
    icon: <span className="text-lg">☕</span>,
    color: "bg-red-50",
  },
  {
    title: "JavaScript",
    icon: (
      <span className="text-lg font-bold text-yellow-600 bg-yellow-400 px-1 rounded">
        JS
      </span>
    ),
    color: "bg-yellow-50",
  },
  {
    title: "Machine Learning",
    icon: <span className="text-lg">🧠</span>,
    color: "bg-red-50",
  },
  {
    title: "Object Oriented Design",
    icon: <span className="text-lg">📋</span>,
    color: "bg-blue-50",
  },
  {
    title: "Python",
    icon: <span className="text-lg">🐍</span>,
    color: "bg-blue-50",
  },
  {
    title: "React",
    icon: (
      <span className="text-lg font-bold text-cyan-600 bg-cyan-100 px-1 rounded">
        ⚛️
      </span>
    ),
    color: "bg-cyan-50",
  },
];

const moreSubjects = [
  "A-frame",
  "Absinthe",
  "AI Agent",
  "AI & ML",
  "Apache Cassandra",
  "Appium",
  "ASP.NET Core",
  "ASP.NET",
  "Assess my Skills",
  "Auth0",
  "Bash",
  "Become a developer",
  "Bedrock",
  "Blazor",
  "Career Management",
  "CakePHP",
  "Clojure",
  "Coding Interview Patterns",
  "CoinAPI",
  "Copilot",
  "CrewAI",
  "D",
  "dart",
  "Database",
  "Databases",
  "DeepSeek",
  "Demo",
  ".NET",
  "DynamoDB",
  "Ecto",
  "Elixir",
  "Erlang",
];

export default function CatalogPage() {
  const { t } = useTranslation();
  const router = useRouter();

  // 获取所有分类数据
  const allCategories = getAllCategoryInfos();

  const handleSubjectClick = (title: string) => {
    // 首先尝试从分类数据中找到对应的分类
    const categoryConfig = allCategories.find((cat) => cat.title === title);

    if (categoryConfig) {
      router.push(`/catalog/${categoryConfig.id}`);
    } else {
      // 如果找不到，使用转换方法生成slug
      const categorySlug = title
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[^a-z0-9-]/g, "")
        .replace(/--+/g, "-")
        .replace(/^-|-$/g, "");
      router.push(`/catalog/${categorySlug}`);
    }
  };

  return (
    <div className="min-h-screen">
      {/* Popular subjects & languages - 白底 */}
      <div className="bg-white py-12">
        <Container size="lg-plus">
          <Typography
            variant="h4"
            className="text-4xl md:text-4xl font-bold text-gray-900 mt-6 mb-12"
          >
            {t("catalog.popular.title")}
          </Typography>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {popularLanguages.map((language, index) => (
              <SubjectCard
                key={index}
                title={language.title}
                icon={language.icon}
                color={language.color}
                onClick={() => handleSubjectClick(language.title)}
              />
            ))}
          </div>
        </Container>
      </div>

      {/* Find more subjects & languages - 灰底 */}
      <div className="bg-gray-50 py-12">
        <Container size="lg-plus">
          <Typography
            variant="h4"
            className="text-3xl md:text-2xl font-bold text-gray-900 mt-6 mb-12"
          >
            {t("catalog.more.title")}
          </Typography>
          <div className="grid grid-cols-4 gap-4">
            {moreSubjects.map((subject, index) => (
              <div
                key={index}
                className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer text-sm font-medium py-2"
                onClick={() => console.log(`Clicked on ${subject}`)}
              >
                {subject}
              </div>
            ))}
          </div>
        </Container>
      </div>
    </div>
  );
}
