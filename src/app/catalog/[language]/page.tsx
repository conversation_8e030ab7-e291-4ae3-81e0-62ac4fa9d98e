"use client";

import React, { useState, useEffect, useCallback } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";
import { Container } from "@/components/ui/container";
import { Button } from "@/components/ui/button";
import CourseMeta from "./CourseMeta";

import { learningNodeService } from "@/service/learning-nodes.service";
import {
  handleApiError,
  handlePaginatedApiResponse,
} from "@/lib/api-error-handler";
import type { LearningNode } from "@/types/openapi";
import { formatDuration } from "@/lib/utils";

import { getCategoryInfo } from "@/constants/catalog";
import { useAuth } from "@/lib/auth-context";
import { useAuthModal } from "@/hooks/useAuthModal";
import FAQSection from "@/feature/shared/FAQSection";
import CategoryInfoSection from "@/components/sections/CategoryInfoSection";

export default function LanguagePage() {
  const { t } = useTranslation();
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const { openLogin } = useAuthModal();
  const language = params.language as string;
  const [activeTab, setActiveTab] = useState("courses");

  // API state
  const [learningNodes, setLearningNodes] = useState<LearningNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);
  const pageSize = 8;

  // 从maindata获取分类信息
  const categoryInfo = getCategoryInfo(language);

  // 处理按钮点击
  const handleButtonClick = () => {
    if (isAuthenticated) {
      // 已登录：滚动到 "Explore Everything" 部分
      const exploreSection = document.getElementById("explore-section");
      if (exploreSection) {
        exploreSection.scrollIntoView({ behavior: "smooth" });
      }
    } else {
      // 未登录：打开登录模态框
      openLogin();
    }
  };

  // Load learning nodes from API
  const loadLearningNodes = useCallback(
    async (page: number = 1, append: boolean = false) => {
      try {
        if (page === 1) {
          setLoading(true);
        } else {
          setLoadingMore(true);
        }

        const response = await learningNodeService.list({
          page,
          page_size: pageSize,
          search_fields: language,
        });

        const data = handlePaginatedApiResponse(response, undefined, {
          showToast: false,
        });

        if (data) {
          const nodes = data.data || [];
          if (append) {
            setLearningNodes((prev) => [...prev, ...nodes]);
          } else {
            setLearningNodes(nodes);
          }
          setTotal(data.total || 0);
          // 计算是否还有更多数据
          const currentTotal = append
            ? learningNodes.length + nodes.length
            : nodes.length;
          setHasMore(currentTotal < (data.total || 0));
        }
      } catch (err) {
        handleApiError(err, {
          showToast: true,
          defaultMessage: "加载学习节点失败",
        });
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [language, pageSize]
  );

  // Load data on mount and language change
  useEffect(() => {
    setCurrentPage(1);
    loadLearningNodes(1, false);
  }, [loadLearningNodes]);

  // 如果找不到分类信息，使用默认值
  const data = {
    name: categoryInfo?.title || t("catalog.defaults.categoryName"),
    description:
      categoryInfo?.description || t("catalog.defaults.categoryDescription"),
    icon: categoryInfo?.icon || "💻", // 使用分类配置的图标，如果没有则使用默认图标
    color: "bg-blue-50", // 可以后续根据分类设置不同颜色
    stats: {
      courses: total,
      paths: 0,
      cloudLabs: 0,
      assessments: 0,
      projects: 0,
      total: total,
    },
    category: language,
  };

  // Handle load more
  const handleLoadMore = useCallback(async () => {
    if (!hasMore || loadingMore) return;
    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);
    await loadLearningNodes(nextPage, true);
  }, [currentPage, hasMore, loadingMore, loadLearningNodes]);

  // 只显示有内容的tabs - 现在只显示courses (learning nodes)
  const tabs = [
    data.stats.courses > 0 && {
      id: "courses",
      label: `Courses (${data.stats.courses})`,
    },
  ].filter(Boolean) as Array<{ id: string; label: string }>;

  // 确保 activeTab 与可用的 tabs 匹配
  useEffect(() => {
    if (tabs.length > 0 && !tabs.find((tab) => tab.id === activeTab)) {
      setActiveTab(tabs[0].id);
    }
  }, [tabs, activeTab]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 介绍部分 - 白底 */}
      <div className="bg-white pt-16 pb-8">
        <Container size="lg-plus">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <Typography
                variant="h3"
                className="text-4xl md:text-5xl font-bold text-gray-900 mb-6"
              >
                {data.name} {t("catalog.hero.coursesAndGuides")}
              </Typography>
              <Typography
                variant="p"
                className="text-md mb-8 max-w-2xl leading-relaxed "
              >
                {data.description}
              </Typography>
              <Button
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg text-base"
                onClick={handleButtonClick}
              >
                {isAuthenticated
                  ? t("catalog.hero.exploreAll")
                  : t("catalog.hero.startFreeTrial")}
              </Button>
            </div>
            <div className="flex-shrink-0 ml-8">
              <div
                className={`w-48 h-48 rounded-2xl flex items-center justify-center`}
              >
                {data.icon.startsWith("/images/") ? (
                  <img
                    src={data.icon}
                    alt={data.name}
                    className="w-full h-full object-contain"
                  />
                ) : (
                  <span className="text-8xl">{data.icon}</span>
                )}
              </div>
            </div>
          </div>
        </Container>
      </div>

      {/* 标题和Tabs部分 - 白底，与上面融合 */}
      <div
        id="explore-section"
        className="bg-white pb-0 sticky top-16 z-10 pt-8"
      >
        <Container size="lg-plus">
          <Typography
            variant="h3"
            className="text-2xl font-bold text-gray-900 mb-8"
          >
            Explore Everything {data.name}
          </Typography>

          {/* Tabs */}
          {tabs.length > 0 ? (
            <div className="border-b border-gray-200">
              <div className="flex">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`pb-4 px-6 border-b-2 font-medium text-sm transition-colors whitespace-nowrap ${
                      activeTab === tab.id
                        ? "border-blue-600 text-blue-600"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>
          ) : (
            <div className="border-b border-gray-200 pb-4">
              <Typography className="text-gray-500 text-center py-8">
                No content available for this category yet.
              </Typography>
            </div>
          )}
        </Container>
      </div>

      {/* 内容部分 - 灰底 */}
      <div className="bg-gray-50 py-8 ">
        <Container size="lg-plus">
          {/* 内容列表 */}
          {loading ? (
            <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
              <Typography className="text-gray-500">Loading...</Typography>
            </div>
          ) : learningNodes.length > 0 ? (
            <div className="bg-white rounded-lg border border-gray-200 p-6 pb-0">
              {learningNodes.map((item, index) => (
                <div key={item.id}>
                  <div
                    className="p-4 my-4 cursor-pointer hover:bg-gray-50 transition-colors rounded-lg"
                    onClick={() => router.push(`/courses/${item.id}`)}
                  >
                    <div className="flex items-start space-x-4 gap-4">
                      <div className="flex-shrink-0 w-[20%]">
                        <img
                          src={item.cover_image || "/images/default-course.png"}
                          alt={item.title || "Learning Node"}
                          className="w-full h-auto rounded-lg"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <Typography
                          variant="h3"
                          className="text-base font-semibold text-gray-900 line-clamp-1"
                        >
                          <a
                            href="#"
                            className="hover:text-blue-600 transition-colors cursor-pointer"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              router.push(`/courses/${item.id}`);
                            }}
                          >
                            {item.title}
                          </a>
                        </Typography>
                        <Typography
                          variant="small"
                          className="text-sm text-gray-600 my-3 line-clamp-1"
                          title={item.description}
                        >
                          {item.description}
                        </Typography>
                        <CourseMeta
                          duration={formatDuration(item.estimated_times || 0)}
                          level={
                            item.difficulty && item.difficulty <= 3
                              ? "Beginner"
                              : item.difficulty && item.difficulty <= 7
                              ? "Intermediate"
                              : "Advanced"
                          }
                          playgrounds={0}
                          quizzes={0}
                        />
                      </div>
                    </div>
                  </div>
                  {/* 分割线 - 除了最后一个项目 */}
                  {index < learningNodes.length - 1 && (
                    <div className="border-b border-gray-200"></div>
                  )}
                </div>
              ))}

              {/* Load More 按钮 */}
              {hasMore && (
                <div className="p-4 border-t text-center">
                  <button
                    onClick={handleLoadMore}
                    disabled={loadingMore}
                    className="px-2 text-center text-blue-600 hover:text-blue-700 font-medium text-sm py-2 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
                  >
                    {loadingMore ? "Loading..." : "Load More"}
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
              <Typography className="text-gray-500">
                No learning nodes available for {language}.
              </Typography>
            </div>
          )}
        </Container>
      </div>

      {/* Category Info Section */}
      {categoryInfo?.infoSections && categoryInfo.infoSections.length > 0 && (
        <div className="bg-gray-50 ">
          <CategoryInfoSection
            sections={categoryInfo.infoSections}
            containerSize="lg-plus"
          />
        </div>
      )}

      {/* FAQs Section */}
      {categoryInfo?.faqs && categoryInfo.faqs.length > 0 && (
        <div className="bg-gray-50">
          <FAQSection
            title={t("shared.faq.title")}
            faqs={categoryInfo.faqs.map((faq) => ({
              question: faq.question,
              answer: faq.answer,
            }))}
            containerSize="lg-plus"
          />
        </div>
      )}
    </div>
  );
}
