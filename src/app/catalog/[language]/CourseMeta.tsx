/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-25 13:14:39
 */
// components/CourseMeta.tsx
import React from "react";
import Image from "next/image";
import FluxIcon from "@/components/fluxIcon";
import { capitalize } from "@/utils";
import DifficultyIcon from "@/components/icons/DifficultyIcon";

interface CourseMetaProps {
  duration: string;
  level: string;
  playgrounds: number;
  quizzes: number;
}

const CourseMeta: React.FC<CourseMetaProps> = ({
  duration,
  level,
  playgrounds,
  quizzes,
}) => {
  return (
    <div className="">
      <div className="flex items-center flex-wrap gap-y-2">
        {/* Duration */}
        <div className="mr-6 flex items-center">
          <div className="mr-1.5 hidden sm:block">
            <FluxIcon
              name="clock"
              width={14}
              height={14}
              className="text-indigo-400 dark:text-indigo-200"
            />
          </div>
          <p className="text-gray-600 dark:text-gray-100 text-sm">{duration}</p>
        </div>

        {/* Level */}
        <div className="mr-6 flex items-center">
          <div className="mr-1.5 hidden sm:block">
            <DifficultyIcon
              difficulty={
                (level || "beginner").toLowerCase() as
                  | "beginner"
                  | "intermediate"
                  | "advanced"
              }
              className="text-indigo-400 dark:text-gray-100"
              size={16}
            />
          </div>
          <p className="text-gray-600 dark:text-gray-100 text-sm">
            {capitalize(level)}
          </p>
        </div>

        {/* Playgrounds */}
        {playgrounds > 0 && (
          <div className="mr-6 flex items-center">
            <div className="mr-1.5 hidden sm:block">
              <FluxIcon
                name="playground"
                width={20}
                height={20}
                className="text-indigo-400 dark:text-indigo-200"
              />
            </div>
            <p className="text-gray-600 dark:text-gray-100 text-sm">
              {playgrounds} Playgrounds
            </p>
          </div>
        )}

        {/* Quizzes */}
        {quizzes > 0 && (
          <div className="mr-6 flex items-center">
            <div className="mr-1.5 hidden sm:block">
              <FluxIcon
                name="quiz"
                width={20}
                height={20}
                className="text-indigo-400 dark:text-indigo-200"
              />
            </div>
            <p className="text-gray-600 dark:text-gray-100 text-sm">
              {quizzes} Quizzes
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CourseMeta;
