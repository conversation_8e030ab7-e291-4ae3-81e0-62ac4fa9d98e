import React from "react";
import FluxIcon from "@/components/fluxIcon";

interface DifficultyIconProps {
  difficulty: "beginner" | "intermediate" | "advanced";
  className?: string;
  size?: number;
}

const DifficultyIcon: React.FC<DifficultyIconProps> = ({
  difficulty,
  className = "text-gray-500",
  size = 12,
}) => {
  return (
    <FluxIcon
      name={difficulty}
      width={size}
      height={size}
      className={className}
    />
  );
};

export default DifficultyIcon;
