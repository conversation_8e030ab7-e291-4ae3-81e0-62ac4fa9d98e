import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const listVariants = cva("", {
  variants: {
    variant: {
      default: "",
      ordered: "list-decimal",
      unordered: "list-disc",
    },
    spacing: {
      none: "space-y-0",
      sm: "space-y-1",
      md: "space-y-2",
      lg: "space-y-3",
    },
  },
  defaultVariants: {
    variant: "default",
    spacing: "sm",
  },
})

const List = React.forwardRef<
  HTMLUListElement | HTMLOListElement,
  React.HTMLAttributes<HTMLUListElement | HTMLOListElement> &
    VariantProps<typeof listVariants> & {
      ordered?: boolean
    }
>(({ className, variant, spacing, ordered, ...props }, ref) => {
  const Component = ordered ? "ol" : "ul"
  const listVariant = ordered ? "ordered" : variant === "unordered" ? "unordered" : "default"
  
  return (
    <Component
      ref={ref as any}
      className={cn(listVariants({ variant: listVariant, spacing }), className)}
      {...props}
    />
  )
})
List.displayName = "List"

const ListItem = React.forwardRef<
  HTMLLIElement,
  React.HTMLAttributes<HTMLLIElement> & {
    icon?: React.ReactNode
  }
>(({ className, icon, children, ...props }, ref) => (
  <li
    ref={ref}
    className={cn(
      "flex items-start gap-2",
      icon && "pl-0",
      className
    )}
    {...props}
  >
    {icon && (
      <span className="flex-shrink-0 mt-0.5">
        {icon}
      </span>
    )}
    <span className="flex-1">{children}</span>
  </li>
))
ListItem.displayName = "ListItem"

export { List, ListItem }
