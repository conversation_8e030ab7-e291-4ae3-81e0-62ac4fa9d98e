"use client"

import * as React from "react"
import { Star } from "lucide-react"
import { cn } from "@/lib/utils"

export interface RatingProps {
  value?: number
  max?: number
  precision?: number
  size?: "small" | "medium" | "large"
  readOnly?: boolean
  onChange?: (value: number) => void
  className?: string
}

const Rating = React.forwardRef<HTMLDivElement, RatingProps>(
  ({ 
    value = 0, 
    max = 5, 
    precision = 1, 
    size = "medium", 
    readOnly = false, 
    onChange,
    className,
    ...props 
  }, ref) => {
    const [hoverValue, setHoverValue] = React.useState<number | null>(null)
    
    const sizeClasses = {
      small: "h-4 w-4",
      medium: "h-5 w-5", 
      large: "h-6 w-6"
    }
    
    const handleClick = (starValue: number) => {
      if (!readOnly && onChange) {
        onChange(starValue)
      }
    }
    
    const handleMouseEnter = (starValue: number) => {
      if (!readOnly) {
        setHoverValue(starValue)
      }
    }
    
    const handleMouseLeave = () => {
      if (!readOnly) {
        setHoverValue(null)
      }
    }
    
    const getStarValue = (index: number) => {
      return index + 1
    }
    
    const isStarFilled = (starValue: number) => {
      const currentValue = hoverValue !== null ? hoverValue : value
      return currentValue >= starValue
    }
    
    const isStarPartiallyFilled = (starValue: number) => {
      const currentValue = hoverValue !== null ? hoverValue : value
      return currentValue >= starValue - 1 && currentValue < starValue && precision < 1
    }

    return (
      <div
        ref={ref}
        className={cn("flex items-center gap-1", className)}
        {...props}
      >
        {Array.from({ length: max }, (_, index) => {
          const starValue = getStarValue(index)
          const filled = isStarFilled(starValue)
          const partiallyFilled = isStarPartiallyFilled(starValue)
          
          return (
            <button
              key={index}
              type="button"
              disabled={readOnly}
              className={cn(
                "transition-colors",
                !readOnly && "hover:scale-110 cursor-pointer",
                readOnly && "cursor-default"
              )}
              onClick={() => handleClick(starValue)}
              onMouseEnter={() => handleMouseEnter(starValue)}
              onMouseLeave={handleMouseLeave}
            >
              <Star
                className={cn(
                  sizeClasses[size],
                  "transition-colors",
                  filled 
                    ? "text-yellow-400 fill-yellow-400" 
                    : partiallyFilled
                    ? "text-yellow-400 fill-yellow-200"
                    : "text-gray-300"
                )}
              />
            </button>
          )
        })}
      </div>
    )
  }
)

Rating.displayName = "Rating"

export { Rating }
