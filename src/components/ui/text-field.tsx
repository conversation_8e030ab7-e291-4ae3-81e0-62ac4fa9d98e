"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

export interface TextFieldProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: boolean;
  helperText?: string;
  variant?: "outlined" | "filled" | "standard";
  fullWidth?: boolean;
  multiline?: boolean;
  rows?: number;
}

const TextField = React.forwardRef<HTMLInputElement, TextFieldProps>(
  (
    {
      className,
      label,
      error,
      helperText,
      variant = "outlined",
      fullWidth = false,
      multiline = false,
      rows = 4,
      id,
      ...props
    },
    ref
  ) => {
    const inputId = id || React.useId();

    return (
      <div className={cn("space-y-2", fullWidth && "w-full")}>
        {label && (
          <Label htmlFor={inputId} className={cn(error && "text-destructive")}>
            {label}
          </Label>
        )}
        {multiline ? (
          <Textarea
            id={inputId}
            rows={rows}
            className={cn(
              error && "border-destructive focus-visible:ring-destructive",
              className
            )}
            {...(props as any)}
          />
        ) : (
          <Input
            ref={ref}
            id={inputId}
            className={cn(
              error && "border-destructive focus-visible:ring-destructive",
              className
            )}
            {...props}
          />
        )}
        {helperText && (
          <p
            className={cn(
              "text-sm",
              error ? "text-destructive" : "text-muted-foreground"
            )}
          >
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

TextField.displayName = "TextField";

export { TextField };
