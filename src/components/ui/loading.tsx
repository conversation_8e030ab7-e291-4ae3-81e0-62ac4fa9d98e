"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import { Typography } from "./typography";

interface LoadingProps {
  message?: string;
  size?: number;
  className?: string;
}

export default function Loading({
  message,
  size = 40,
  className,
}: LoadingProps) {
  const { t } = useTranslation();
  const loadingMessage = message || t("common.status.loading");

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center min-h-[200px] gap-4",
        className
      )}
    >
      <div
        className="animate-spin rounded-full border-4 border-primary border-t-transparent"
        style={{ width: size, height: size }}
      />
      {loadingMessage && (
        <Typography variant="muted">{loadingMessage}</Typography>
      )}
    </div>
  );
}
