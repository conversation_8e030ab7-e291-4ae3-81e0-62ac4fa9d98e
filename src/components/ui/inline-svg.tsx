/*
 * @Description: 内联SVG组件 - 通过fetch获取SVG内容并内联渲染
 * @Author: Devin
 * @Date: 2025-08-19
 */
"use client";

import React, { useState, useEffect } from "react";

interface InlineSvgProps {
  url: string;
  className?: string;
  style?: React.CSSProperties;
  width?: string | number;
  height?: string | number;
  alt?: string;
}

const InlineSvg: React.FC<InlineSvgProps> = ({
  url,
  className = "",
  style = {},
  width,
  height,
  alt = "SVG Image",
}) => {
  const [svgContent, setSvgContent] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    const fetchSvg = async () => {
      try {
        setLoading(true);
        setError(false);

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const svgText = await response.text();

        // 验证是否为有效的SVG内容
        if (!svgText.includes("<svg")) {
          throw new Error("Invalid SVG content");
        }

        // 处理SVG内容，添加width和height属性
        let processedSvg = svgText;
        if (width || height) {
          processedSvg = svgText.replace(
            /<svg([^>]*)>/,
            (match, attributes) => {
              let newAttributes = attributes;
              if (width && !attributes.includes("width=")) {
                newAttributes += ` width="${width}"`;
              }
              if (height && !attributes.includes("height=")) {
                newAttributes += ` height="${height}"`;
              }
              return `<svg${newAttributes} style="max-width: 100%; height: auto;">`;
            }
          );
        }

        setSvgContent(processedSvg);
      } catch (err) {
        console.warn("Failed to fetch SVG:", url, err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    if (url) {
      fetchSvg();
    }
  }, [url, width, height]);

  if (loading) {
    return (
      <div
        className={`inline-block ${className}`}
        style={{
          ...style,
          width: width || "24px",
          height: height || "24px",
          backgroundColor: "#f3f4f6",
          borderRadius: "4px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <div className="animate-pulse text-gray-400 text-xs">...</div>
      </div>
    );
  }

  if (error || !svgContent) {
    return (
      <div
        className={`inline-block ${className}`}
        style={{
          ...style,
          width: width || "24px",
          height: height || "24px",
          backgroundColor: "#fee2e2",
          borderRadius: "4px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
        title={`Failed to load SVG: ${url}`}
      >
        <span className="text-red-500 text-xs">⚠️</span>
      </div>
    );
  }

  return (
    <div
      className={`inline-svg ${className}`}
      style={style}
      dangerouslySetInnerHTML={{ __html: svgContent }}
      role="img"
      aria-label={alt}
    />
  );
};

export default InlineSvg;
