/*
 * @Description: Table of Contents component for course content
 * @Author: Devin
 * @Date: 2025-07-23
 */
"use client";

import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

export interface TOCItem {
  id: string;
  title: string;
  level?: number; // 标题级别 (h1=1, h2=2, etc.)
}

interface TableOfContentsProps {
  items: TOCItem[];
  className?: string;
  showProgress?: boolean;
}

export default function TableOfContents({
  items,
  className = "",
  showProgress = true,
}: TableOfContentsProps) {
  const { t } = useTranslation();
  const [activeId, setActiveId] = useState<string>("");
  const [isVisible, setIsVisible] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isMenuVisible, setIsMenuVisible] = useState(false);

  // 创建稳定的依赖项，避免无限循环
  const itemsLength = items.length;

  // 滚动到指定元素
  const scrollToElement = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      // 计算元素位置，减去导航栏高度（64px）和一些额外的间距
      const elementTop = element.offsetTop;
      const navbarHeight = 64; // 导航栏高度 h-16 = 64px
      const extraPadding = 20; // 额外的间距，让内容不紧贴导航栏
      const targetPosition = elementTop - navbarHeight - extraPadding;

      window.scrollTo({
        top: Math.max(0, targetPosition), // 确保不会滚动到负数位置
        behavior: "smooth",
      });
    }
  };

  // 监听滚动，更新当前活跃的标题
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // 计算阅读进度
      const scrollProgress =
        (scrollTop / (documentHeight - windowHeight)) * 100;
      setProgress(Math.min(100, Math.max(0, scrollProgress)));

      // 查找当前可见的标题
      let currentActiveId = "";
      for (const item of items) {
        const element = document.getElementById(item.id);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100) {
            currentActiveId = item.id;
          }
        }
      }
      setActiveId(currentActiveId);
    };

    // 重置 activeId 当 items 变化时
    setActiveId("");

    // 只有当 items 有内容时才添加滚动监听
    if (items.length > 0) {
      window.addEventListener("scroll", handleScroll);
      // 延迟初始调用，确保 DOM 已更新
      setTimeout(handleScroll, 100);
    }

    return () => window.removeEventListener("scroll", handleScroll);
  }, [items]); // 依赖整个 items 数组

  // 检查是否有内容可显示
  useEffect(() => {
    setIsVisible(items.length > 0);
  }, [itemsLength]);

  if (!isVisible) return null;

  return (
    <div
      className={`tailwind-hidden fixed right-0 z-10 mt-16 flex-col gap-y-6 sm:flex ${className}`}
      style={{
        top: "calc(var(--header-height) + var(--site-wide-banner-height))",
        maxHeight: "550px",
      }}
      onMouseEnter={() => setIsMenuVisible(true)}
      onMouseLeave={() => setIsMenuVisible(false)}
    >
      {/* 目录指示器 */}
      <div className="no-scrollbar z-10 mr-5 transform cursor-pointer overflow-hidden">
        <div className="flex flex-col items-end">
          {items.map((item) => (
            <div key={item.id} className="mt-4 items-end">
              <div
                className={`h-0.5 rounded-sm w-4 transition-colors duration-200 ${
                  activeId === item.id
                    ? "bg-blue-500"
                    : "bg-gray-300 dark:bg-gray-700"
                }`}
                onClick={() => scrollToElement(item.id)}
              />
            </div>
          ))}
        </div>
      </div>

      {/* 目录内容面板 - 悬停时显示 */}
      <div
        className="no-scrollbar fixed z-20 mr-5 max-w-full transform overflow-auto overflow-x-auto rounded-md
           bg-white shadow-md dark:border-gray-800 dark:bg-gray-900 right-0 "
        style={{
          opacity: isMenuVisible ? 1 : 0,
          visibility: isMenuVisible ? "visible" : "hidden",
          transition: "opacity 0.3s, visibility 0.3s",
          width: "320px",
          maxHeight: "70vh",
          top: "calc(var(--header-height))",
        }}
      >
        <div className="body-small px-8 py-3 font-medium text-gray-500 shadow-md dark:border-gray-800 dark:bg-gray-800 dark:text-gray-100">
          {items.map((item) => (
            <div
              key={item.id}
              className="hover:bg-gray-100 dark:hover:bg-gray-700 clamp-2 my-1 line-clamp-2 overflow-hidden text-ellipsis py-1 px-3 text-sm"
              style={{
                borderRadius: "4px",
                width: "264px",
              }}
            >
              <a
                href={`#${item.id}`}
                onClick={(e) => {
                  e.preventDefault();
                  scrollToElement(item.id);
                  setIsMenuVisible(false);
                }}
                className={`block transition-colors duration-200 ${
                  activeId === item.id
                    ? "text-blue-600 font-medium dark:text-blue-400"
                    : "text-gray-700 dark:text-gray-300"
                }`}
                aria-label={t("common.accessibility.readMore", {
                  title: item.title,
                })}
              >
                {item.title}
              </a>
            </div>
          ))}
        </div>
      </div>

      {/* 阅读进度圆环 */}
      {showProgress && (
        <div className="sm:mr-4 sm:mt-6">
          <div
            className="relative flex justify-center"
            style={{ width: "24px", height: "24px" }}
          >
            {
              //   progress >= 100 ? (
              //   // 100% 完成时显示完成图标
              //   <div className="bg-green-500 flex h-7 w-7 items-center justify-center rounded-full">
              //     <svg
              //       width="16px"
              //       height="16px"
              //       viewBox="0 0 24 24"
              //       fill="white"
              //       xmlns="http://www.w3.org/2000/svg"
              //     >
              //       <path d="m8.795 15.875-4.17-4.17-1.42 1.41 5.59 5.59 12-12-1.41-1.41-10.59 10.58Z"></path>
              //     </svg>
              //   </div>
              // ) : (
              // 未完成时显示进度圆环
              <svg
                className="absolute"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                style={{ transform: "rotate(-90deg)" }}
              >
                {/* 背景圆环 */}
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  fill="none"
                  className="text-gray-200 dark:text-gray-700 stroke-current"
                  strokeWidth="4"
                  strokeDasharray="62.83185307179586, 62.83185307179586"
                />
                {/* 进度圆环 */}
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  fill="none"
                  className="text-green-500 stroke-current"
                  strokeWidth="4"
                  strokeDasharray="62.83185307179586, 62.83185307179586"
                  strokeDashoffset={62.83185307179586 * (1 - progress / 100)}
                  strokeLinecap="round"
                  style={{ transition: "stroke-dashoffset 0.3s ease" }}
                />
              </svg>
              // )
            }
          </div>
        </div>
      )}
    </div>
  );
}

// Hook 用于自动提取页面标题
export function useTableOfContents() {
  const [tocItems, setTocItems] = useState<TOCItem[]>([]);

  useEffect(() => {
    // 自动提取页面中的标题元素
    const headings = document.querySelectorAll("h1, h2, h3, h4, h5, h6");
    const items: TOCItem[] = [];

    headings.forEach((heading) => {
      if (heading.id) {
        const level = parseInt(heading.tagName.charAt(1));
        items.push({
          id: heading.id,
          title: heading.textContent || "",
          level,
        });
      }
    });

    setTocItems(items);
  }, []);

  return tocItems;
}
