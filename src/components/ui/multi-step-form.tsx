"use client";

import React, { useState, ReactNode } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Typography } from "@/components/ui/typography";
import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";

export interface StepConfig {
  id: string;
  title: string;
  description?: string;
  component: ReactNode;
  isValid?: boolean;
  canSkip?: boolean;
}

interface MultiStepFormProps {
  steps: StepConfig[];
  currentStep: number;
  onStepChange: (step: number) => void;
  onComplete: () => void;
  onCancel?: () => void;
  className?: string;
  showProgress?: boolean;
  showStepNumbers?: boolean;
  allowSkipSteps?: boolean;
  completeButtonText?: string;
  nextButtonText?: string;
  previousButtonText?: string;
  cancelButtonText?: string;
  title?: string;
}

export default function MultiStepForm({
  steps,
  currentStep,
  onStepChange,
  onComplete,
  onCancel,
  className,
  showProgress = false,
  showStepNumbers = true,
  allowSkipSteps = false,
  completeButtonText,
  nextButtonText,
  previousButtonText,
  cancelButtonText = "Cancel",
  title,
}: MultiStepFormProps) {
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Set default button texts with translations
  const defaultCompleteText =
    completeButtonText || t("preferences.onboarding.navigation.complete");
  const defaultNextText =
    nextButtonText || t("preferences.onboarding.navigation.next");
  const defaultPreviousText =
    previousButtonText || t("preferences.onboarding.navigation.previous");

  const currentStepConfig = steps[currentStep];
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === steps.length - 1;
  const progressPercentage = ((currentStep + 1) / steps.length) * 100;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      onStepChange(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      onStepChange(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    setIsSubmitting(true);
    try {
      await onComplete();
    } finally {
      setIsSubmitting(false);
    }
  };

  const canProceed =
    currentStepConfig?.isValid !== false ||
    (allowSkipSteps && currentStepConfig?.canSkip);

  return (
    <div className={cn("w-full h-full flex flex-col", className)}>
      {/* Header */}
      <div className="mb-6 flex-shrink-0">
        {/* Title */}
        <div className="text-center mb-6">
          <Typography
            variant="h3"
            className="w-full text-center uppercase font-bold mb-3 text-xs leading-4 text-gray-500"
            style={{ letterSpacing: "2.5px" }}
          >
            {title}
          </Typography>
        </div>

        {/* Step Indicators */}
        {showStepNumbers && (
          <div className="flex items-center justify-center mb-4">
            {steps.map((step, index) => (
              <React.Fragment key={step.id}>
                <div
                  className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-medium transition-colors",
                    index <= currentStep
                      ? "bg-blue-600 border-blue-600 text-white"
                      : "bg-white border-gray-300 text-gray-500"
                  )}
                >
                  {index + 1}
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "w-12 h-0.5 mx-2 transition-colors",
                      index < currentStep ? "bg-blue-600" : "bg-gray-300"
                    )}
                  />
                )}
              </React.Fragment>
            ))}
          </div>
        )}

        {/* Progress Bar */}
        {showProgress && (
          <div className="mb-4">
            <Progress value={progressPercentage} className="h-2" />
            <div className="flex justify-between text-sm text-gray-500 mt-2">
              <span>
                Step {currentStep + 1} of {steps.length}
              </span>
              <span>{Math.round(progressPercentage)}% Complete</span>
            </div>
          </div>
        )}

        {/* Current Step Title and Description */}
        <div className="text-center">
          <Typography
            variant="h2"
            className="font-semibold text-gray-900 mb-0 mt-2 text-xl"
          >
            {currentStepConfig?.title}
          </Typography>
          {currentStepConfig?.description && (
            <span className="text-gray-600 text-sm mt-0">
              {currentStepConfig.description}
            </span>
          )}
        </div>
      </div>

      {/* Step Content */}
      <div className="flex-1 overflow-y-auto mb-6">
        {currentStepConfig?.component}
      </div>

      {/* Navigation Buttons - Fixed at bottom */}
      <div className="flex items-center justify-between flex-shrink-0 pt-4 border-t border-gray-200">
        <div className="flex items-center gap-3">
          {!isFirstStep && (
            <Button
              variant="outline"
              onClick={handlePrevious}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="w-4 h-4" />
              {defaultPreviousText}
            </Button>
          )}

          {allowSkipSteps && (
            <Button
              variant="ghost"
              onClick={onCancel}
              className="text-gray-500 hover:text-gray-700"
            >
              {t("preferences.onboarding.navigation.skip")}
            </Button>
          )}
        </div>

        <div className="flex items-center gap-3">
          {isLastStep ? (
            <Button
              onClick={handleComplete}
              disabled={!canProceed || isSubmitting}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8"
            >
              {isSubmitting
                ? t("preferences.messages.saving")
                : defaultCompleteText}
            </Button>
          ) : (
            <Button
              onClick={handleNext}
              disabled={!canProceed}
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 px-8"
            >
              {defaultNextText}
              <ChevronRight className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
