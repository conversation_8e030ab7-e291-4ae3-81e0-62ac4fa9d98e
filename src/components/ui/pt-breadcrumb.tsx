/*
 * @Description: Course Breadcrumb navigation component
 * @Author: Devin
 * @Date: 2025-07-23
 */
"use client";

import React from "react";
import { useRouter } from "next/navigation";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "./breadcrumb";

export interface CourseBreadcrumbItem {
  key: string;
  value: string;
  path?: string;
  isEllipsis?: boolean;
}

interface CourseBreadcrumbProps {
  items: CourseBreadcrumbItem[];
  className?: string;
  separator?: React.ReactNode;
}

export default function CourseBreadcrumb({
  items,
  className = "",
  separator,
}: CourseBreadcrumbProps) {
  const router = useRouter();

  const handleClick = (path: string) => {
    router.push(path);
  };

  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <BreadcrumbItem>
              {item.path ? (
                <BreadcrumbLink asChild className="hover:text-[#5553ff]">
                  <span
                    onClick={() => handleClick(item.path!)}
                    className=" text-gray-500 dark:text-gray-100 cursor-pointer"
                  >
                    {item.value}
                  </span>
                </BreadcrumbLink>
              ) : (
                <BreadcrumbPage className="text-gray-500 dark:text-gray-100">
                  {item.value}
                </BreadcrumbPage>
              )}
            </BreadcrumbItem>

            {/* 如果当前项有 isEllipsis，在后面添加省略号 */}
            {item.isEllipsis && (
              <>
                <BreadcrumbSeparator>
                  {separator || <span>/</span>}
                </BreadcrumbSeparator>
                <p className="m-0 bg-transparent p-0 py-1 text-gray-L500 outline-none dark:text-gray-D100">
                  ...
                </p>
              </>
            )}

            {/* 正常项目之间的分隔符 */}
            {index < items.length - 1 && (
              <BreadcrumbSeparator>
                {separator || <span>/</span>}
              </BreadcrumbSeparator>
            )}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
