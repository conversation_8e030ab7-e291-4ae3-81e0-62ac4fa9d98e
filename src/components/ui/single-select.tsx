"use client";

import React, { useState } from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface Option {
  value: string;
  label: string;
}

interface SingleSelectProps {
  options: Option[];
  selectedValue: string;
  onSelectionChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  label?: string;
}

export function SingleSelect({
  options,
  selectedValue,
  onSelectionChange,
  placeholder = "Select option...",
  className,
  label,
}: SingleSelectProps) {
  const [open, setOpen] = useState(false);

  const handleSelect = (value: string) => {
    // Toggle selection: if same value is clicked, deselect it
    const newValue = selectedValue === value ? "" : value;
    onSelectionChange(newValue);
    setOpen(false);
  };

  const getDisplayText = () => {
    if (label) return label;
    
    if (!selectedValue) {
      return placeholder;
    }
    
    const selectedOption = options.find(opt => opt.value === selectedValue);
    return selectedOption ? selectedOption.label : placeholder;
  };

  const hasSelection = !!selectedValue;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-auto min-w-[120px] h-8 text-sm border-gray-300 rounded-md justify-between",
            hasSelection && "border-blue-500 bg-blue-50 text-blue-700",
            className
          )}
        >
          <span className="truncate">{getDisplayText()}</span>
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-48 p-2" align="start">
        <div className="space-y-1">
          {options.map((option) => (
            <div
              key={option.value}
              className="flex items-center space-x-2 p-2 hover:bg-gray-50 cursor-pointer rounded"
              onClick={() => handleSelect(option.value)}
            >
              <div
                className={cn(
                  "w-4 h-4 border border-gray-300 rounded-full flex items-center justify-center",
                  selectedValue === option.value && "border-blue-600"
                )}
              >
                {selectedValue === option.value && (
                  <div className="w-2 h-2 bg-blue-600 rounded-full" />
                )}
              </div>
              <span className="text-sm">{option.label}</span>
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}
