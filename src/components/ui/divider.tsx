"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export interface DividerProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: "horizontal" | "vertical"
  variant?: "fullWidth" | "inset" | "middle"
}

const Divider = React.forwardRef<HTMLDivElement, DividerProps>(
  ({ className, orientation = "horizontal", variant = "fullWidth", ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "border-border",
          orientation === "horizontal" 
            ? "w-full border-t" 
            : "h-full border-l",
          variant === "inset" && orientation === "horizontal" && "ml-4",
          variant === "inset" && orientation === "vertical" && "mt-4",
          variant === "middle" && orientation === "horizontal" && "mx-4",
          variant === "middle" && orientation === "vertical" && "my-4",
          className
        )}
        {...props}
      />
    )
  }
)

Divider.displayName = "Divider"

export { Divider }
