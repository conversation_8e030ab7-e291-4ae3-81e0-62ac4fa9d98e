/*
 * @Description: Save Button Component - Reusable save button with save.svg icon
 * @Author: Devin
 * @Date: 2025-07-22
 */
"use client";

import * as React from "react";
import { useTranslation } from "react-i18next";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "./button";
import { IconButton } from "./icon-button";
import FluxIcon from "@/components/fluxIcon";

const saveButtonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        success: "bg-green-500 hover:bg-green-600 text-white",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

// Save Icon Component
interface SaveIconProps {
  className?: string;
  size?: number;
}

const SaveIcon = React.forwardRef<HTMLDivElement, SaveIconProps>(
  ({ className, size = 16 }, ref) => {
    return (
      <div ref={ref} className={cn("inline-flex", className)}>
        <FluxIcon
          name="save"
          width={size}
          height={size}
          className="text-current"
        />
      </div>
    );
  }
);
SaveIcon.displayName = "SaveIcon";

// Save Button Props
export interface SaveButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof saveButtonVariants> {
  asChild?: boolean;
  loading?: boolean;
  iconOnly?: boolean;
  iconSize?: number;
  children?: React.ReactNode;
}

// Main Save Button Component
const SaveButton = React.forwardRef<HTMLButtonElement, SaveButtonProps>(
  (
    {
      className,
      variant = "default",
      size = "default",
      asChild = false,
      loading = false,
      iconOnly = false,
      iconSize = 16,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const { t } = useTranslation();
    const isDisabled = disabled || loading;

    // If iconOnly is true, render as IconButton
    if (iconOnly) {
      return (
        <IconButton
          ref={ref}
          className={cn(
            saveButtonVariants({ variant, size: "icon", className })
          )}
          disabled={isDisabled}
          {...props}
        >
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
          ) : (
            <SaveIcon size={iconSize} />
          )}
        </IconButton>
      );
    }

    // Regular button with icon and text
    return (
      <Button
        ref={ref}
        className={cn(saveButtonVariants({ variant, size, className }))}
        disabled={isDisabled}
        {...props}
      >
        {loading ? (
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent mr-2" />
        ) : (
          <SaveIcon className="mr-2" size={iconSize} />
        )}
        {children || t("common.buttons.save")}
      </Button>
    );
  }
);
SaveButton.displayName = "SaveButton";

export { SaveButton, SaveIcon, saveButtonVariants };
