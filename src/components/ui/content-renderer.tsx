/*
 * @Description: 内容渲染组件 - 支持 Markdown 和 HTML 模式
 * @Author: Devin
 * @Date: 2025-08-05
 */
"use client";

import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import "highlight.js/styles/github.css"; // 代码高亮样式
import InlineSvg from "./inline-svg";

// 优化的图片组件，使用 React.memo 防止不必要的重新渲染
const OptimizedImage = React.memo(
  ({ src, alt, style, width, height, ...props }: any) => {
    const [imageError, setImageError] = React.useState(false);
    const [imageLoaded, setImageLoaded] = React.useState(false);

    // 当 src 改变时重置状态
    React.useEffect(() => {
      setImageError(false);
      setImageLoaded(false);
    }, [src]);

    const handleImageError = React.useCallback(() => {
      // 检查是否是网络不可达的内网地址

      setImageError(true);
    }, [src]);

    const handleImageLoad = React.useCallback(() => {
      setImageLoaded(true);
    }, []);

    // 检查是否为SVG文件
    const isSvg = React.useMemo(
      () => src && (src.toLowerCase().endsWith(".svg") || src.includes("svg")),
      [src]
    );

    // 计算图片尺寸
    const { imgWidth, imgHeight } = React.useMemo(
      () => ({
        imgWidth: width ? `${width}px` : "80px",
        imgHeight: height ? `${height}px` : "80px",
      }),
      [width, height]
    );

    // 如果是SVG文件，使用InlineSvg组件
    if (isSvg && src) {
      return (
        <div className="flex flex-col items-center mb-4">
          <div className="rounded-lg p-4 bg-white dark:bg-gray-800 mb-2">
            <InlineSvg
              url={src}
              width={width}
              height={height}
              alt={alt || "SVG Image"}
              style={style}
              className="max-w-full"
            />
          </div>
          {alt && (
            <div className="text-center text-sm text-gray-600 dark:text-gray-400 px-3 py-1 border border-gray-300 rounded bg-white dark:bg-gray-800">
              {alt}
            </div>
          )}
        </div>
      );
    }

    if (imageError) {
      return (
        <div className="flex flex-col items-center mb-4">
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50 dark:bg-gray-800 flex flex-col items-center justify-center mb-2"
            style={{ width: imgWidth, minHeight: "120px" }}
          >
            <div className="text-4xl mb-2">🖼️</div>
            <div className="text-gray-500 text-sm text-center">
              <div>图片加载失败</div>
            </div>
            {src && (
              <div
                className="text-xs mt-2 text-gray-400 break-all max-w-xs text-center"
                title={src}
              >
                {src.length > 50 ? `...${src.slice(-47)}` : src}
              </div>
            )}
          </div>
          {alt && (
            <div className="text-center text-sm text-gray-600 dark:text-gray-400 px-3 py-1 border border-gray-300 rounded bg-white dark:bg-gray-800">
              {alt}
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center mb-4">
        <div className="rounded-lg p-4 bg-white dark:bg-gray-800 mb-2 relative">
          {!imageLoaded && (
            <div
              className="absolute inset-0 border-2 border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-800 flex items-center justify-center animate-pulse"
              style={{ width: imgWidth, height: imgHeight }}
            >
              <div className="text-gray-400 text-sm">加载中...</div>
            </div>
          )}
          <img
            src={src}
            alt={alt || "Course Image"}
            width={width}
            height={height}
            style={{
              ...style,
              width: imgWidth,
              height: "auto",
              objectFit: "contain",
              opacity: imageLoaded ? 1 : 0,
              transition: "opacity 0.3s ease-in-out",
            }}
            onError={handleImageError}
            onLoad={handleImageLoad}
            loading="lazy"
            {...props}
          />
        </div>
        {alt && (
          <div className="text-center text-sm text-gray-600 dark:text-gray-400 px-3 py-1 border border-gray-300 rounded bg-white dark:bg-gray-800">
            {alt}
          </div>
        )}
      </div>
    );
  }
);

OptimizedImage.displayName = "OptimizedImage";

interface ContentRendererProps {
  type: "markdown" | "html";
  htmlContent?: string;
  mdContent?: string;
  className?: string;
}

// 生成标题 ID 的工具函数
const generateHeadingId = (children: React.ReactNode): string => {
  const text =
    typeof children === "string"
      ? children
      : Array.isArray(children)
      ? children.join("")
      : String(children);

  return text
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/^-+|-+$/g, "");
};

// 静态的 ReactMarkdown 组件配置，避免每次渲染时重新创建
const markdownComponents = {
  div: React.memo(({ node, style, className, children, ...props }: any) => {
    // 检查是否包含文本内容，如果是则渲染为段落
    const hasTextContent = React.useMemo(
      () =>
        React.Children.toArray(children).some(
          (child) => typeof child === "string" && child.trim().length > 0
        ),
      [children]
    );

    // 处理样式冲突：如果同时有flex和width，优先使用width
    const processedStyle = React.useMemo(() => {
      if (style && style.width && style.flex) {
        return {
          ...style,
          flex: "none", // 移除flex，使用width
          flexShrink: 0, // 防止收缩
        };
      }
      return style;
    }, [style]);

    if (hasTextContent && processedStyle && processedStyle.width) {
      return (
        <div
          style={{ ...processedStyle, gap: "0" }}
          className={className}
          {...props}
        >
          <p
            className="text-lesson-markdown font-custom mb-4"
            data-pfchat="true"
          >
            {children}
          </p>
        </div>
      );
    }

    return (
      <div
        style={{ ...processedStyle, gap: "0" }}
        className={className}
        {...props}
      >
        {children}
      </div>
    );
  }),

  // 自定义组件样式，使用 CSS 变量和自定义字体
  // 为标题自动生成 id 属性以支持目录导航
  h1: ({ children }: any) => (
    <h1
      id={generateHeadingId(children)}
      className="text-h1 font-custom font-bold mb-6 mt-8"
    >
      {children}
    </h1>
  ),
  h2: ({ children }: any) => (
    <h2
      id={generateHeadingId(children)}
      className="text-h2 font-custom font-semibold mb-4 mt-6"
    >
      {children}
    </h2>
  ),
  h3: ({ children }: any) => (
    <h3
      id={generateHeadingId(children)}
      className="text-h3 font-custom font-medium mb-3 mt-5"
    >
      {children}
    </h3>
  ),
  h4: ({ children }: any) => (
    <h4
      id={generateHeadingId(children)}
      className="text-h4 font-custom font-medium mb-2 mt-4"
    >
      {children}
    </h4>
  ),
  h5: ({ children }: any) => (
    <h5
      id={generateHeadingId(children)}
      className="text-h5 font-custom font-medium mb-2 mt-3"
    >
      {children}
    </h5>
  ),
  h6: ({ children }: any) => (
    <h6
      id={generateHeadingId(children)}
      className="text-h6 font-custom font-medium mb-2 mt-3"
    >
      {children}
    </h6>
  ),
  p: ({ children }: any) => (
    <p
      className="text-lesson-markdown font-custom mb-4 inline"
      data-pfchat="true"
    >
      {children}
    </p>
  ),
  ul: ({ children }: any) => (
    <ul className="list-disc list-inside mb-4 space-y-2 text-lesson-markdown font-custom">
      {children}
    </ul>
  ),
  ol: ({ children }: any) => (
    <ol className="list-decimal list-inside mb-4 space-y-2 text-lesson-markdown font-custom">
      {children}
    </ol>
  ),
  li: ({ children }: any) => (
    <li className="text-lesson-markdown font-custom">{children}</li>
  ),
  blockquote: ({ children }: any) => (
    <blockquote className="border-l-4 border-blue-500 pl-4 py-2 mb-4 bg-blue-50 dark:bg-blue-900/20 text-lesson-markdown font-custom">
      {children}
    </blockquote>
  ),
  code: ({ inline, className, children, ...props }: any) => {
    const match = /language-(\w+)/.exec(className || "");
    return !inline && match ? (
      <pre className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto mb-4">
        <code className={className} {...props}>
          {children}
        </code>
      </pre>
    ) : (
      <code
        className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono"
        {...props}
      >
        {children}
      </code>
    );
  },
  pre: ({ children }: any) => (
    <pre className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto mb-4">
      {children}
    </pre>
  ),
  a: ({ href, children }: any) => (
    <a
      href={href}
      className="text-blue-600 dark:text-blue-400 hover:underline"
      target="_blank"
      rel="noopener noreferrer"
    >
      {children}
    </a>
  ),
  img: (props: any) => <OptimizedImage {...props} />,
  hr: () => <hr className="border-gray-300 dark:border-gray-600 my-6" />,
  table: ({ children }: any) => (
    <div className="overflow-x-auto mb-4">
      <table className="min-w-full border-collapse border border-gray-300 dark:border-gray-600">
        {children}
      </table>
    </div>
  ),
  thead: ({ children }: any) => (
    <thead className="bg-gray-50 dark:bg-gray-800">{children}</thead>
  ),
  tbody: ({ children }: any) => <tbody>{children}</tbody>,
  tr: ({ children }: any) => (
    <tr className="border-b border-gray-300 dark:border-gray-600">
      {children}
    </tr>
  ),
  th: ({ children }: any) => (
    <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-semibold text-lesson-markdown font-custom">
      {children}
    </th>
  ),
  td: ({ children }: any) => (
    <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-lesson-markdown font-custom">
      {children}
    </td>
  ),
  strong: ({ children }: any) => (
    <strong className="font-bold text-lesson-markdown font-custom">
      {children}
    </strong>
  ),
  em: ({ children }: any) => (
    <em className="italic text-lesson-markdown font-custom">{children}</em>
  ),
  // 自定义 pageflux-keyword 标签处理
  "pageflux-keyword": ({ children, className, theme, ...props }: any) => {
    // 提取文本内容作为关键词
    const keyword =
      typeof children === "string"
        ? children
        : Array.isArray(children)
        ? children.join("")
        : String(children);

    // 根据theme属性确定样式类
    const themeClass = theme ? `keyword-${theme}` : "keyword-primary";
    const combinedClassName = className
      ? `${className} ${themeClass}`
      : themeClass;

    return (
      <span
        className={combinedClassName}
        data-pageflux-keyword={keyword}
        title={`${keyword}`}
        onClick={() => {
          // 可以在这里添加点击事件处理，比如搜索相关内容
          console.log("Keyword clicked:", keyword);
        }}
        {...props}
      >
        <svg
          height="16"
          className="inline-block mr-1 align-text-bottom"
          viewBox="0 0 16 16"
          version="1.1"
          width="16"
          aria-hidden="true"
          style={{ fill: "currentColor" }}
        >
          <path d="M5.45 5.154A4.25 4.25 0 0 0 9.25 7.5h1.378a2.251 2.251 0 1 1 0 1.5H9.25A5.734 5.734 0 0 1 5 7.123v3.505a2.25 2.25 0 1 1-1.5 0V5.372a2.25 2.25 0 1 1 1.95-.218ZM4.25 13.5a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm8.5-4.5a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5ZM5 3.25a.75.75 0 1 0 0 .005V3.25Z"></path>
        </svg>
        {children}
      </span>
    );
  },
} as any;

const ContentRenderer: React.FC<ContentRendererProps> = ({
  type,
  htmlContent,
  mdContent,
  className = "",
}) => {
  // HTML 模式
  if (type === "html" && htmlContent) {
    return (
      <div
        className={`browser-default ${className}`}
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    );
  }

  // Markdown 模式
  if (type === "markdown" && mdContent) {
    // 预处理：将HTML中的Markdown图片语法转换为HTML img标签
    const processedContent = mdContent.replace(
      /!\[([^\]]*)\]\(([^)]+)\)/g,
      '<img src="$2" alt="$1" />'
    );

    return (
      <div className={`course-content-body ${className}`}>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeHighlight, rehypeRaw]}
          components={markdownComponents}
        >
          {processedContent}
        </ReactMarkdown>
      </div>
    );
  }

  // 错误状态
  return (
    <div className={`text-center py-8 ${className}`}>
      <p className="text-gray-500 italic">
        No content available or unsupported content type.
      </p>
    </div>
  );
};

export default ContentRenderer;
