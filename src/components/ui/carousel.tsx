"use client";

import React, { useState, ReactNode } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface CarouselProps {
  children: ReactNode[];
  itemsPerSlide?: number;
  showArrows?: boolean;
  showIndicators?: boolean;
  className?: string;
  arrowClassName?: string;
  indicatorClassName?: string;
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

const Carousel: React.FC<CarouselProps> = ({
  children,
  itemsPerSlide = 4,
  showArrows = true,
  showIndicators = true,
  className = "",
  arrowClassName = "",
  indicatorClassName = "",
  autoPlay = false,
  autoPlayInterval = 3000,
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  // 计算总页数
  const totalSlides = Math.ceil(children.length / itemsPerSlide);

  // 根据 itemsPerSlide 生成网格类名
  const getGridClassName = () => {
    const baseClasses = "grid gap-4";
    switch (itemsPerSlide) {
      case 1:
        return `${baseClasses} grid-cols-1`;
      case 2:
        return `${baseClasses} grid-cols-1 md:grid-cols-2`;
      case 3:
        return `${baseClasses} grid-cols-1 md:grid-cols-2 lg:grid-cols-3`;
      case 4:
        return `${baseClasses} grid-cols-1 md:grid-cols-2 lg:grid-cols-4`;
      case 5:
        return `${baseClasses} grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5`;
      case 6:
        return `${baseClasses} grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6`;
      default:
        return `${baseClasses} grid-cols-1 md:grid-cols-2 lg:grid-cols-4`;
    }
  };

  // 处理上一页
  const handlePrevSlide = () => {
    setCurrentSlide((prev) => (prev > 0 ? prev - 1 : totalSlides - 1));
  };

  // 处理下一页
  const handleNextSlide = () => {
    setCurrentSlide((prev) => (prev < totalSlides - 1 ? prev + 1 : 0));
  };

  // 自动播放
  React.useEffect(() => {
    if (autoPlay && totalSlides > 1) {
      const interval = setInterval(handleNextSlide, autoPlayInterval);
      return () => clearInterval(interval);
    }
  }, [autoPlay, autoPlayInterval, totalSlides]);

  if (totalSlides <= 1) {
    return <div className={cn(getGridClassName(), className)}>{children}</div>;
  }

  return (
    <div className={cn("relative", className)}>
      {/* 左右箭头导航 */}
      {showArrows && totalSlides > 1 && (
        <>
          <button
            onClick={handlePrevSlide}
            className={cn(
              "absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 z-10 bg-white rounded-full shadow-lg border border-gray-200 p-2 hover:shadow-xl transition-shadow",
              arrowClassName
            )}
            aria-label="Previous slide"
          >
            <ChevronLeft className="h-5 w-5 text-gray-600" />
          </button>
          <button
            onClick={handleNextSlide}
            className={cn(
              "absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 z-10 bg-white rounded-full shadow-lg border border-gray-200 p-2 hover:shadow-xl transition-shadow",
              arrowClassName
            )}
            aria-label="Next slide"
          >
            <ChevronRight className="h-5 w-5 text-gray-600" />
          </button>
        </>
      )}

      {/* 内容区域 */}
      <div className="overflow-hidden">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{
            transform: `translateX(-${currentSlide * 100}%)`,
          }}
        >
          {Array.from({ length: totalSlides }).map((_, slideIndex) => (
            <div key={slideIndex} className="w-full flex-shrink-0">
              <div className={getGridClassName()}>
                {children.slice(
                  slideIndex * itemsPerSlide,
                  (slideIndex + 1) * itemsPerSlide
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 走马灯指示器 */}
      {showIndicators && totalSlides > 1 && (
        <div className="flex justify-center mt-6 space-x-1">
          {Array.from({ length: totalSlides }).map((_, index) => (
            <button
              key={index}
              className={cn(
                "w-4 h-1 rounded-full transition-colors duration-300",
                currentSlide === index
                  ? "bg-[#5553ff]"
                  : "bg-gray-300 hover:bg-gray-400",
                indicatorClassName
              )}
              onClick={() => setCurrentSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Carousel;
