"use client";

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import Image from "next/image";
import FluxIcon from "@/components/fluxIcon";
import { Typography } from "./typography";

const iTitleIconVariants = cva(
  "flex justify-center items-center rounded-full",
  {
    variants: {
      variant: {
        default: "", // No background class when using custom styles
        primary: "bg-blue-50",
        secondary: "bg-gray-50",
        success: "bg-green-50",
        warning: "bg-yellow-50",
        danger: "bg-red-50",
        info: "bg-cyan-50",
        purple: "bg-purple-50",
        indigo: "bg-indigo-50",
        pink: "bg-pink-50",
        certification: "", // For certification style like #eef
      },
      size: {
        sm: "w-8 h-8",
        default: "w-10 h-10", // 2.5rem equivalent
        lg: "w-12 h-12",
        xl: "w-16 h-16",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

const iconSizeVariants = cva("", {
  variants: {
    size: {
      sm: "w-4 h-4",
      default: "w-5 h-5",
      lg: "w-6 h-6",
      xl: "w-8 h-8",
    },
  },
  defaultVariants: {
    size: "default",
  },
});

export interface ITitleIconProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof iTitleIconVariants> {
  icon?: React.ReactNode;
  iconColor?: string;
  customStyle?: React.CSSProperties;
  src?: string; // Image source path
  alt?: string; // Image alt text
  title?: string; // Title text to display next to icon
  titleVariant?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "p"; // Typography variant for title
  titleClassName?: string; // Custom className for title
  backgroundColor?: string; // Background color (e.g., "#eef")
  iconWidth?: number; // Icon width
  iconHeight?: number; // Icon height
  containerWidth?: string; // Container width (e.g., "2.5rem")
  containerHeight?: string; // Container height (e.g., "2.5rem")
}

const ITitleIcon = React.forwardRef<HTMLDivElement, ITitleIconProps>(
  (
    {
      className,
      variant,
      size,
      icon,
      iconColor = "#5553ff",
      customStyle,
      src,
      alt = "Icon",
      title,
      titleVariant = "h3",
      titleClassName = "font-bold text-gray-900",
      backgroundColor,
      iconWidth = 20,
      iconHeight = 20,
      containerWidth,
      containerHeight,
      ...props
    },
    ref
  ) => {
    // Default In Progress icon if no icon is provided
    const defaultIcon = (
      <FluxIcon
        name="clipboard"
        width={16}
        height={18}
        className={cn(iconSizeVariants({ size }))}
        style={{ color: iconColor }}
      />
    );

    const renderIcon = () => {
      // If src is provided, use Image component
      if (src) {
        return (
          <Image
            src={src}
            alt={alt}
            width={iconWidth}
            height={iconHeight}
            className={cn(iconSizeVariants({ size }))}
            style={{ color: iconColor }}
          />
        );
      }

      // If custom icon is provided, apply size and color
      if (icon) {
        if (React.isValidElement(icon)) {
          return React.cloneElement(icon as React.ReactElement, {
            className: cn(
              iconSizeVariants({ size }),
              (icon as React.ReactElement).props?.className
            ),
            style: {
              color: iconColor,
              ...(icon as React.ReactElement).props?.style,
            },
          });
        }
        return icon;
      }

      // Return default icon
      return defaultIcon;
    };

    // Determine container style
    const containerStyle = {
      width: containerWidth || (size === "default" ? "2.5rem" : undefined),
      height: containerHeight || (size === "default" ? "2.5rem" : undefined),
      backgroundColor: backgroundColor,
      ...customStyle,
    };

    // If title is provided, render as a flex container with icon and title
    if (title) {
      return (
        <div className="flex items-center gap-3 mb-2" ref={ref} {...props}>
          <div
            className={cn(iTitleIconVariants({ variant, size, className }))}
            style={containerStyle}
          >
            {renderIcon()}
          </div>
          <Typography variant={titleVariant} className={titleClassName}>
            {title}
          </Typography>
        </div>
      );
    }

    // Otherwise, render just the icon container
    return (
      <div
        className={cn(iTitleIconVariants({ variant, size, className }))}
        style={containerStyle}
        ref={ref}
        {...props}
      >
        {renderIcon()}
      </div>
    );
  }
);

ITitleIcon.displayName = "ITitleIcon";

export { ITitleIcon, iTitleIconVariants };
