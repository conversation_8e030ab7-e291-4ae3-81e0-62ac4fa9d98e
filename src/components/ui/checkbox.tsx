/*
 * @Description: Checkbox UI Component
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import * as React from "react";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

export interface CheckboxProps {
  id?: string;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ id, checked = false, onCheckedChange, disabled = false, className, children, ...props }, ref) => {
    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      if (onCheckedChange) {
        onCheckedChange(event.target.checked);
      }
    };

    return (
      <div className="flex items-center space-x-2">
        <div className="relative">
          <input
            ref={ref}
            id={id}
            type="checkbox"
            checked={checked}
            onChange={handleChange}
            disabled={disabled}
            className="sr-only"
            {...props}
          />
          <div
            className={cn(
              "w-4 h-4 border border-gray-300 rounded flex items-center justify-center cursor-pointer transition-colors",
              checked && "bg-blue-600 border-blue-600",
              disabled && "opacity-50 cursor-not-allowed",
              className
            )}
            onClick={() => {
              if (!disabled && onCheckedChange) {
                onCheckedChange(!checked);
              }
            }}
          >
            {checked && (
              <Check className="w-3 h-3 text-white" strokeWidth={3} />
            )}
          </div>
        </div>
        {children && (
          <label
            htmlFor={id}
            className={cn(
              "text-sm font-medium leading-none cursor-pointer",
              disabled && "opacity-50 cursor-not-allowed"
            )}
          >
            {children}
          </label>
        )}
      </div>
    );
  }
);

Checkbox.displayName = "Checkbox";

export { Checkbox };
