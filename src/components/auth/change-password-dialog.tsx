"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Eye, EyeOff, AlertCircle } from "lucide-react";
import { userService } from "@/service/user";
import { showToast } from "@/lib/toast-utils";
import { extractErrorMessage } from "@/lib/api-error-handler";

interface ChangePasswordDialogProps {
  open: boolean;
  onClose: () => void;
}

interface FormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export default function ChangePasswordDialog({
  open,
  onClose,
}: ChangePasswordDialogProps) {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<FormData>({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (error) setError("");
  };

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.currentPassword.trim()) {
      setError("Please enter your current password");
      return false;
    }

    if (!formData.newPassword.trim()) {
      setError("Please enter a new password");
      return false;
    }

    if (formData.newPassword.length < 8) {
      setError("New password must be at least 8 characters long");
      return false;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      setError("New passwords do not match");
      return false;
    }

    if (formData.currentPassword === formData.newPassword) {
      setError("New password must be different from current password");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const response = await userService.changePassword({
        current_password: formData.currentPassword,
        new_password: formData.newPassword,
      });

      if (response.success) {
        showToast.success("Password changed successfully");
        handleClose();
      } else {
        // Handle specific error messages
        let errorMessage = response.message || "Failed to change password";

        // Provide user-friendly error messages for common cases
        if (
          errorMessage
            .toLowerCase()
            .includes("current password is incorrect") ||
          errorMessage.toLowerCase().includes("invalid password") ||
          errorMessage.toLowerCase().includes("wrong password")
        ) {
          errorMessage = "Current password is incorrect. Please try again.";
          // Clear current password field for easier retry
          setFormData((prev) => ({ ...prev, currentPassword: "" }));
          // Focus on current password field after a short delay
          setTimeout(() => {
            const currentPasswordInput =
              document.getElementById("currentPassword");
            if (currentPasswordInput) {
              currentPasswordInput.focus();
            }
          }, 100);
        }

        setError(errorMessage);
        // Don't close dialog on error - keep it open for user to retry
      }
    } catch (error) {
      console.error("Change password error:", error);
      const errorMessage = extractErrorMessage(
        error,
        "Failed to change password. Please try again."
      );
      setError(errorMessage);
      // Don't close dialog on error - keep it open for user to retry
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    // Don't close if there's an ongoing operation
    if (isLoading) {
      return;
    }

    setFormData({
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    });
    setShowPasswords({
      current: false,
      new: false,
      confirm: false,
    });
    setError("");
    setIsLoading(false);
    onClose();
  };

  const handleDialogOpenChange = (open: boolean) => {
    // Only allow closing if not loading
    // Users can still close even with errors by clicking Cancel button
    if (!open && !isLoading) {
      handleClose();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !isLoading) {
      handleSubmit(e as any);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Change Password</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Current Password */}
          <div className="space-y-2">
            <Label htmlFor="currentPassword">Enter your current password</Label>
            <div className="relative">
              <Input
                id="currentPassword"
                name="currentPassword"
                type={showPasswords.current ? "text" : "password"}
                value={formData.currentPassword}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder="Current password"
                disabled={isLoading}
                className="pr-10"
                autoComplete="current-password"
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility("current")}
                disabled={isLoading}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 z-10 bg-background"
                aria-label="Toggle password visibility"
              >
                {showPasswords.current ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>

          {/* New Password */}
          <div className="space-y-2">
            <Label htmlFor="newPassword">Enter a new password</Label>
            <div className="relative">
              <Input
                id="newPassword"
                name="newPassword"
                type={showPasswords.new ? "text" : "password"}
                value={formData.newPassword}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder="New password (min. 8 characters)"
                disabled={isLoading}
                className="pr-10"
                autoComplete="new-password"
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility("new")}
                disabled={isLoading}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 z-10 bg-background"
                aria-label="Toggle password visibility"
              >
                {showPasswords.new ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>

          {/* Confirm New Password */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm new password</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type={showPasswords.confirm ? "text" : "password"}
                value={formData.confirmPassword}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder="Confirm new password"
                disabled={isLoading}
                className="pr-10"
                autoComplete="new-password"
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility("confirm")}
                disabled={isLoading}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 z-10 bg-background"
                aria-label="Toggle password visibility"
              >
                {showPasswords.confirm ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>
        </form>

        <DialogFooter className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? "Changing..." : "Change Password"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
