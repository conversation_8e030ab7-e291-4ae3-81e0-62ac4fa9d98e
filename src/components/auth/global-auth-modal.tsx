/*
 * @Description: 全局认证模态框组件 - 使用全局状态管理
 * @Author: Devin
 * @Date: 2025-08-11
 */
"use client";

import React from "react";
import AuthModal from "./auth-modal";
import { useUIStore } from "@/store";

/**
 * 全局认证模态框组件
 * 使用全局状态管理，可以在应用的任何地方触发显示
 */
export default function GlobalAuthModal() {
  const { authModalOpen, authModalMode, closeAuthModal } = useUIStore();

  return (
    <AuthModal
      open={authModalOpen}
      onClose={closeAuthModal}
      initialMode={authModalMode}
    />
  );
}
