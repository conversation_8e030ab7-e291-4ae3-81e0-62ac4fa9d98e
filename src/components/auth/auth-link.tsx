"use client";

import React from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth-context";

interface AuthLinkProps {
  children: React.ReactNode;
  className?: string;
  authenticatedHref?: string;
  unauthenticatedHref?: string;
  onClick?: () => void;
}

interface AuthButtonProps {
  children: React.ReactNode;
  className?: string;
  authenticatedAction?: () => void;
  unauthenticatedAction?: () => void;
  onClick?: () => void;
}

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAuth?: boolean;
}

/**
 * 统一授权链接组件
 * 根据用户登录状态决定跳转链接
 * - 已登录：跳转到 authenticatedHref (默认 /learn/home)
 * - 未登录：跳转到 unauthenticatedHref (默认 /)
 */
export function AuthLink({
  children,
  className,
  authenticatedHref = "/learn/home",
  unauthenticatedHref = "/",
  onClick,
}: AuthLinkProps) {
  const { isAuthenticated } = useAuth();

  const href = isAuthenticated ? authenticatedHref : unauthenticatedHref;

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  return (
    <Link href={href} className={className} onClick={handleClick}>
      {children}
    </Link>
  );
}

/**
 * 统一授权按钮组件
 * 根据用户登录状态执行不同的操作
 */
export function AuthButton({
  children,
  className,
  authenticatedAction,
  unauthenticatedAction,
  onClick,
}: AuthButtonProps) {
  const { isAuthenticated } = useAuth();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (isAuthenticated && authenticatedAction) {
      authenticatedAction();
    } else if (!isAuthenticated && unauthenticatedAction) {
      unauthenticatedAction();
    }
  };

  return (
    <button className={className} onClick={handleClick}>
      {children}
    </button>
  );
}

/**
 * 授权守卫组件
 * 根据登录状态显示不同内容
 */
export function AuthGuard({
  children,
  fallback,
  requireAuth = true,
}: AuthGuardProps) {
  const { isAuthenticated } = useAuth();

  if (requireAuth && !isAuthenticated) {
    return fallback || null;
  }

  if (!requireAuth && isAuthenticated) {
    return fallback || null;
  }

  return <>{children}</>;
}

/**
 * 授权导航钩子
 * 提供基于登录状态的导航功能
 */
export function useAuthNavigation() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();

  const navigateBasedOnAuth = (
    authenticatedPath: string = "/learn/home",
    unauthenticatedPath: string = "/"
  ) => {
    const path = isAuthenticated ? authenticatedPath : unauthenticatedPath;
    router.push(path);
  };

  const requireAuthNavigation = (path: string, fallbackAction?: () => void) => {
    if (isAuthenticated) {
      router.push(path);
    } else if (fallbackAction) {
      fallbackAction();
    } else {
      router.push("/");
    }
  };

  return {
    isAuthenticated,
    navigateBasedOnAuth,
    requireAuthNavigation,
  };
}

// 默认导出保持向后兼容
export default AuthLink;
