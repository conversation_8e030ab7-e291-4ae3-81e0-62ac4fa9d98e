"use client";

import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Typography } from "@/components/ui/typography";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import FluxIcon from "@/components/fluxIcon";
import {
  X as CloseIcon,
  Mail as EmailIcon,
  ArrowLeft as ArrowBackIcon,
  Eye as Visibility,
  EyeOff as VisibilityOff,
} from "lucide-react";

// Custom Google Icon Component
const GoogleIcon = () => <FluxIcon name="google" width={20} height={20} />;
import { useAuth } from "@/lib/auth-context";

interface AuthModalProps {
  open: boolean;
  onClose: () => void;
  initialMode?: "login" | "signup";
}

type ViewType = "providers" | "email-form";
type AuthMode = "login" | "signup";

export default function AuthModal({
  open,
  onClose,
  initialMode = "login",
}: AuthModalProps) {
  const { t } = useTranslation();
  const [currentView, setCurrentView] = useState<ViewType>("providers");
  const [authMode, setAuthMode] = useState<AuthMode>(initialMode);
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const {
    emailLogin,
    register,
    googleAuth,
    isEmailLoginLoading,
    isRegisterLoading,
    isGoogleAuthLoading,
  } = useAuth();

  // 计算当前操作的加载状态
  const isCurrentOperationLoading =
    (authMode === "login" && isEmailLoginLoading) ||
    (authMode === "signup" && isRegisterLoading) ||
    isGoogleAuthLoading;

  // 任何操作都在加载中
  const isAnyLoading =
    isEmailLoginLoading || isRegisterLoading || isGoogleAuthLoading;

  // Reset form when modal opens/closes or mode changes
  useEffect(() => {
    if (open) {
      setCurrentView("providers");
      setAuthMode(initialMode);
      resetForm();
    }
  }, [open, initialMode]);

  const resetForm = () => {
    setFormData({
      fullName: "",
      email: "",
      password: "",
      confirmPassword: "",
    });
    setError("");
    setFormErrors({});
    setShowPassword(false);
    setShowConfirmPassword(false);
    setRememberMe(false);
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Email validation
    if (!formData.email) {
      errors.email = t("auth.validation.email");
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = t("auth.validation.email");
    }

    // Password validation
    if (!formData.password) {
      errors.password = t("auth.validation.password");
    } else if (formData.password.length < 6) {
      errors.password = t("auth.validation.password");
    }

    // Full name validation for signup
    if (authMode === "signup" && !formData.fullName.trim()) {
      errors.fullName = t("auth.validation.fullName");
    }

    // Confirm password validation for signup
    if (authMode === "signup") {
      if (!formData.confirmPassword) {
        errors.confirmPassword = t("auth.validation.confirmPassword");
      } else if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = t("auth.validation.passwordMatch");
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear specific field error when user starts typing
    if (formErrors[name]) {
      setFormErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setError("");

    try {
      if (authMode === "login") {
        // Use email/password login
        const success = await emailLogin(formData.email, formData.password);
        if (success) {
          onClose();
        }
      } else {
        // Handle signup
        const success = await register(
          formData.fullName,
          formData.email,
          formData.password
        );
        if (success) {
          // 注册成功后切换到登录模式，而不是关闭弹框
          setAuthMode("login");
          setCurrentView("providers");
          resetForm();
          // 保留邮箱信息，方便用户登录
          setFormData((prev) => ({ ...prev, email: formData.email }));
        }
      }
    } catch (err) {
      setError(
        authMode === "login"
          ? t("auth.errors.invalidCredentials")
          : t("auth.errors.serverError")
      );
    }
  };

  const handleGoogleAuth = async () => {
    setError("");

    try {
      const success = await googleAuth();
      if (success) {
        onClose();
      }
    } catch (err) {
      setError(t("auth.errors.serverError"));
    }
  };

  const toggleAuthMode = () => {
    setAuthMode(authMode === "login" ? "signup" : "login");
    resetForm();
  };

  const handleEmailProviderClick = () => {
    setCurrentView("email-form");
  };

  const handleBackToProviders = () => {
    setCurrentView("providers");
    setError("");
  };

  const handleModalClose = () => {
    resetForm();
    setCurrentView("providers");
    onClose();
  };

  // Render provider selection view
  const renderProvidersView = () => (
    <div>
      {/* Header */}
      <Typography
        variant="h6"
        className="font-semibold mb-2 text-center text-xl"
      >
        {authMode === "login" ? t("auth.login.title") : t("auth.signup.title")}
      </Typography>

      <Typography
        variant="p"
        className="text-gray-600 text-center mb-6 text-sm"
      >
        {authMode === "login" ? t("auth.login.title") : t("auth.signup.title")}
      </Typography>

      {/* Google OAuth Button */}
      <div className="flex justify-center mb-4">
        <Button
          variant="outline"
          className="w-4/5 py-3 font-medium text-sm border-gray-300 text-gray-700 hover:border-gray-300 hover:bg-gray-50"
          onClick={handleGoogleAuth}
          disabled={isGoogleAuthLoading}
        >
          <GoogleIcon />
          <span className="ml-2">
            {isGoogleAuthLoading
              ? t("common.loading")
              : t("auth.social.continueWith", { provider: "Google" })}
          </span>
        </Button>
      </div>

      <div className="flex items-center my-6">
        <div className="flex-1 h-px bg-gray-300"></div>
        <Typography variant="small" className="px-4 text-gray-500 text-xs">
          {t("auth.social.or")}
        </Typography>
        <div className="flex-1 h-px bg-gray-300"></div>
      </div>

      {/* Email Button */}
      <div className="flex justify-center">
        <Button
          variant="outline"
          className="w-4/5 py-3 font-medium text-sm border-blue-500 text-blue-500 hover:border-blue-600 hover:bg-blue-50"
          onClick={handleEmailProviderClick}
          disabled={isAnyLoading}
        >
          <EmailIcon className="text-blue-500 w-4 h-4" />
          <span className="ml-2">
            {t("auth.social.continueWith", { provider: "Email" })}
          </span>
        </Button>
      </div>

      {/* Toggle between login/signup */}
      <div className="text-center mt-8">
        <Typography variant="small" className="text-gray-600 text-sm">
          {authMode === "login"
            ? t("auth.login.noAccount")
            : t("auth.signup.hasAccount")}
          <button
            type="button"
            onClick={toggleAuthMode}
            className="text-blue-500 font-medium hover:underline cursor-pointer px-3 py-2 ml-2 rounded hover:bg-gray-100"
          >
            {authMode === "login"
              ? t("auth.login.signUpLink")
              : t("auth.signup.signInLink")}
          </button>
        </Typography>
      </div>
    </div>
  );

  // Render email form view
  const renderEmailFormView = () => (
    <div>
      {/* Back button and header */}
      <div className="flex items-center mb-6">
        <button
          onClick={handleBackToProviders}
          className="mr-2 p-1 hover:bg-gray-100 rounded"
          disabled={isAnyLoading}
        >
          <ArrowBackIcon className="w-5 h-5" />
        </button>
        <Typography variant="h6" className="font-semibold">
          {authMode === "login"
            ? t("auth.login.title")
            : t("auth.signup.title")}
        </Typography>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="px-4">
        {/* Full Name - only for signup */}
        {authMode === "signup" && (
          <div className="mb-4 flex flex-col items-center">
            <Label className="mb-2 font-medium text-sm w-full text-left">
              {t("auth.fields.fullName")}
              <span style={{ color: "#ff4444" }}>*</span>
            </Label>
            <Input
              placeholder={t("auth.fields.fullName")}
              name="fullName"
              value={formData.fullName}
              onChange={handleInputChange}
              disabled={isCurrentOperationLoading}
              className="w-full text-sm"
            />
            {formErrors.fullName && (
              <p className="text-red-500 text-xs mt-1 w-full text-left">
                {formErrors.fullName}
              </p>
            )}
          </div>
        )}

        {/* Email */}
        <div className="mb-4 flex flex-col items-center">
          <Label className="mb-2 font-medium text-sm w-full text-left">
            {t("auth.fields.email")}
            <span style={{ color: "#ff4444" }}>*</span>
          </Label>
          <Input
            placeholder={t("auth.fields.email")}
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            disabled={isCurrentOperationLoading}
            className="w-full text-sm"
          />
          {formErrors.email && (
            <p className="text-red-500 text-xs mt-1 w-full text-left">
              {formErrors.email}
            </p>
          )}
        </div>

        {/* Password */}
        <div
          className={`mb-${
            authMode === "signup" ? "4" : "3"
          } flex flex-col items-center`}
        >
          <div className="flex justify-between items-center mb-2 w-full">
            <Label className="font-medium text-sm">
              {t("auth.fields.password")}
              <span style={{ color: "#ff4444" }}>*</span>
            </Label>
            {authMode === "login" && (
              <Link
                href="#"
                className="text-blue-600 no-underline text-xs hover:underline"
              >
                {t("auth.login.forgotPassword")}
              </Link>
            )}
          </div>
          <div className="relative w-full">
            <Input
              placeholder={t("auth.fields.password")}
              name="password"
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={handleInputChange}
              disabled={isCurrentOperationLoading}
              className="w-full text-sm pr-10"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isCurrentOperationLoading}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showPassword ? (
                <VisibilityOff className="w-4 h-4" />
              ) : (
                <Visibility className="w-4 h-4" />
              )}
            </button>
          </div>
          {formErrors.password && (
            <p className="text-red-500 text-xs mt-1 w-full text-left">
              {formErrors.password}
            </p>
          )}
        </div>

        {/* Confirm Password - only for signup */}
        {authMode === "signup" && (
          <div className="mb-4 flex flex-col items-center">
            <Label className="mb-2 font-medium text-sm w-full text-left">
              {t("auth.fields.confirmPassword")}
              <span style={{ color: "#ff4444" }}>*</span>
            </Label>
            <div className="relative w-full">
              <Input
                placeholder={t("auth.fields.confirmPassword")}
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                value={formData.confirmPassword}
                onChange={handleInputChange}
                disabled={isCurrentOperationLoading}
                className="w-full text-sm pr-10"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={isCurrentOperationLoading}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                {showConfirmPassword ? (
                  <VisibilityOff className="w-4 h-4" />
                ) : (
                  <Visibility className="w-4 h-4" />
                )}
              </button>
            </div>
            {formErrors.confirmPassword && (
              <p className="text-red-500 text-xs mt-1 w-full text-left">
                {formErrors.confirmPassword}
              </p>
            )}
          </div>
        )}

        {/* Remember me checkbox - only for login */}
        {authMode === "login" && (
          <div className="flex justify-between items-center mb-6">
            <div />
          </div>
        )}

        {/* Error message */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Submit button */}
        <div className="flex justify-center my-6">
          <Button
            type="submit"
            disabled={isCurrentOperationLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 text-sm"
          >
            {isCurrentOperationLoading
              ? t("common.loading")
              : authMode === "login"
              ? t("auth.login.submit")
              : t("auth.signup.submit")}
          </Button>
        </div>

        {/* Toggle between login/signup */}
        <div className="text-center">
          <Typography variant="small" className="text-gray-600 text-sm">
            {authMode === "login"
              ? t("auth.login.noAccount")
              : t("auth.signup.hasAccount")}{" "}
            <button
              type="button"
              onClick={toggleAuthMode}
              disabled={isAnyLoading}
              className="text-blue-600 no-underline font-medium cursor-pointer px-3 py-2 ml-2 rounded-md hover:underline hover:bg-gray-100"
            >
              {authMode === "login"
                ? t("auth.login.signUpLink")
                : t("auth.signup.signInLink")}
            </button>
          </Typography>
        </div>
      </form>
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={handleModalClose}>
      <DialogContent className="p-0 max-w-md w-full rounded-lg">
        <div className="p-6 relative">
          {/* Logo */}
          <div className="flex items-center mb-6">
            <div className="w-7 h-7 bg-indigo-500 rounded flex items-center justify-center mr-3">
              <Typography className="text-white font-bold text-base">
                P
              </Typography>
            </div>
            <Typography variant="h6" className="font-semibold text-lg">
              Pageflux AI
            </Typography>
          </div>

          {/* Dynamic Content */}
          {currentView === "providers"
            ? renderProvidersView()
            : renderEmailFormView()}

          {/* Terms */}
          <div className="mt-8 text-center">
            <Typography variant="small" className="text-gray-500 text-xs">
              {t("auth.terms.agreement")}{" "}
              <Link
                href="#"
                className="text-blue-600 no-underline hover:underline"
              >
                {t("auth.terms.privacyPolicy")}
              </Link>{" "}
              {t("auth.terms.and")}{" "}
              <Link
                href="#"
                className="text-blue-600 no-underline hover:underline"
              >
                {t("auth.terms.termsOfService")}
              </Link>
              . {t("auth.terms.recaptcha")}
            </Typography>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
