"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/lib/auth-context";
import {
  shouldGuardRoute,
  getDefaultHomePage,
  canAccessRoute,
  hasPermission,
  type Permission,
  type Role,
} from "@/lib/auth-utils-enhanced";
import { PROTECTED_PATHS } from "@/config/protected-routes";
import { showToast } from "@/lib/toast-utils";
import { useAuthModal } from "@/hooks/useAuthModal";

interface AuthEnhancedContextType {
  // 基础认证状态
  isAuthenticated: boolean;
  userRole: string | null;

  // 权限检查方法
  hasPermission: (permission: Permission) => boolean;
  canAccessRoute: (route: string) => boolean;

  // 导航方法
  navigateToDefaultHome: () => void;
  navigateWithAuth: (path: string, fallbackPath?: string) => void;

  // 路由守卫状态
  isRouteAllowed: boolean;
  isLoading: boolean;
}

const AuthEnhancedContext = createContext<AuthEnhancedContextType | undefined>(
  undefined
);

interface AuthEnhancedProviderProps {
  children: React.ReactNode;
  enableRouteGuard?: boolean;
  loadingComponent?: React.ComponentType;
  unauthorizedComponent?: React.ComponentType<{ redirectPath?: string }>;
}

/**
 * 增强的授权提供者组件
 * 提供路由守卫、权限检查等功能
 */
export function AuthEnhancedProvider({
  children,
  enableRouteGuard = true,
  loadingComponent: LoadingComponent,
  unauthorizedComponent: UnauthorizedComponent,
}: AuthEnhancedProviderProps) {
  const { isAuthenticated, userInfo, loading, isLogout, setIsLogout } =
    useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const { openLogin } = useAuthModal();

  const [isRouteAllowed, setIsRouteAllowed] = useState(true);
  const [isInitializing, setIsInitializing] = useState(true);

  const userRole = userInfo?.role || null;

  // 处理未授权访问时的认证模态框
  useEffect(() => {
    if (enableRouteGuard && !isRouteAllowed && !isAuthenticated && !isLogout) {
      openLogin();
    }
  }, [enableRouteGuard, isRouteAllowed, isAuthenticated, openLogin, isLogout]);

  // 权限检查方法
  const checkPermission = (permission: Permission): boolean => {
    if (!userRole) return false;
    return hasPermission(userRole, permission);
  };

  // 路由访问检查
  const checkRouteAccess = (route: string): boolean => {
    if (!userRole) return false;
    return canAccessRoute(userRole, route);
  };

  // 导航到默认首页
  const navigateToDefaultHome = () => {
    if (userRole) {
      const defaultHome = getDefaultHomePage(userRole);
      router.push(defaultHome);
    } else {
      router.push("/");
    }
  };

  // 带权限检查的导航
  const navigateWithAuth = (path: string, fallbackPath: string = "/") => {
    if (isAuthenticated && userRole && checkRouteAccess(path)) {
      router.push(path);
    } else {
      router.push(fallbackPath);
    }
  };

  // 路由守卫逻辑
  useEffect(() => {
    if (loading) return;

    setIsInitializing(false);

    if (!enableRouteGuard) {
      setIsRouteAllowed(true);
      return;
    }

    // 首页始终允许访问，不需要任何守卫
    if (pathname === "/") {
      setIsRouteAllowed(true);
      return;
    }

    // 如果用户未登录，检查是否访问受保护路由
    if (!isAuthenticated) {
      const isProtectedPath = PROTECTED_PATHS.some((path) =>
        pathname.startsWith(path)
      );

      if (isProtectedPath) {
        setIsRouteAllowed(false);
        router.push("/");
        return;
      }
    }

    // 如果用户已登录，检查角色权限
    if (isAuthenticated && userRole) {
      const guardResult = shouldGuardRoute(pathname, userRole);

      if (guardResult.shouldGuard) {
        setIsRouteAllowed(false);
        router.push(guardResult.redirectTo || "/");
        return;
      }
    }

    setIsRouteAllowed(true);
  }, [isAuthenticated, userRole, pathname, loading, enableRouteGuard, router]);

  const contextValue: AuthEnhancedContextType = {
    isAuthenticated,
    userRole,
    hasPermission: checkPermission,
    canAccessRoute: checkRouteAccess,
    navigateToDefaultHome,
    navigateWithAuth,
    isRouteAllowed,
    isLoading: loading || isInitializing,
  };

  // 显示加载状态
  if (loading || isInitializing) {
    if (LoadingComponent) {
      return <LoadingComponent />;
    }
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // 显示未授权状态：AuthModal通过useEffect处理
  if (enableRouteGuard && !isRouteAllowed) {
    if (UnauthorizedComponent) {
      return <UnauthorizedComponent redirectPath="/" />;
    }
    // AuthModal的打开通过useEffect处理，避免在render中直接调用
    return null;
  }

  return (
    <AuthEnhancedContext.Provider value={contextValue}>
      {children}
    </AuthEnhancedContext.Provider>
  );
}

/**
 * 使用增强授权上下文的钩子
 */
export function useAuthEnhanced() {
  const context = useContext(AuthEnhancedContext);
  if (context === undefined) {
    throw new Error(
      "useAuthEnhanced must be used within an AuthEnhancedProvider"
    );
  }
  return context;
}

/**
 * 权限检查高阶组件
 */
export function withPermission<T extends object>(
  Component: React.ComponentType<T>,
  requiredPermission: Permission,
  fallback?: React.ComponentType
) {
  return function PermissionGuardedComponent(props: T) {
    const { hasPermission } = useAuthEnhanced();

    if (!hasPermission(requiredPermission)) {
      if (fallback) {
        const FallbackComponent = fallback;
        return <FallbackComponent />;
      }
      return (
        <div className="p-4 text-center">
          <p className="text-gray-600">您没有权限查看此内容</p>
        </div>
      );
    }

    return <Component {...props} />;
  };
}

/**
 * 角色检查高阶组件
 */
export function withRole<T extends object>(
  Component: React.ComponentType<T>,
  allowedRoles: Role[],
  fallback?: React.ComponentType
) {
  return function RoleGuardedComponent(props: T) {
    const { userRole } = useAuthEnhanced();

    if (!userRole || !allowedRoles.includes(userRole as Role)) {
      if (fallback) {
        const FallbackComponent = fallback;
        return <FallbackComponent />;
      }
      return (
        <div className="p-4 text-center">
          <p className="text-gray-600">您的角色无权访问此内容</p>
        </div>
      );
    }

    return <Component {...props} />;
  };
}
