/*
 * @Description: Cheatsheets Grid Component for displaying cheatsheet cards
 * @Author: Devin
 * @Date: 2025-07-31
 */
"use client";

import React from "react";
import { Cheatsheet } from "@/types/cheatsheet";

interface CheatsheetsGridProps {
  cheatsheets: Cheatsheet[];
  onCardClick?: (cheatsheet: Cheatsheet) => void;
}

export default function CheatsheetsGrid({
  cheatsheets,
  onCardClick,
}: CheatsheetsGridProps) {
  // Group cheatsheets into rows of 3
  const rows = [];
  for (let i = 0; i < cheatsheets.length; i += 3) {
    rows.push(cheatsheets.slice(i, i + 3));
  }

  return (
    <div className="space-y-6 ">
      {rows.map((row, rowIndex) => (
        <div
          key={rowIndex}
          className="flex flex-col items-center gap-6 self-stretch lg:flex-row lg:items-start"
        >
          {row.map((cheatsheet) => (
            <div
              key={cheatsheet.id}
              className="flex w-full flex-col items-start gap-4 rounded-md border border-solid border-gray-300 bg-white px-5 pb-8 pt-7 hover:cursor-pointer hover:border-indigo-300 hover:shadow-md dark:border-gray-800 dark:bg-gray-900 hover:dark:border-indigo-300 hover:dark:shadow-md lg:h-48 lg:w-1/3 lg:max-w-[364px]"
              onClick={() => onCardClick?.(cheatsheet)}
            >
              {/* Category Badge */}
              <div className="inline-flex items-start justify-start gap-3">
                <div
                  className={`flex items-center justify-start gap-2 rounded px-3 py-0.5 bg-indigo-25 text-indigo-500 dark:bg-indigo-700 dark:text-white`}
                >
                  <div className="text-center font-sans text-xs font-bold uppercase leading-tight tracking-widest">
                    {cheatsheet.category}
                  </div>
                </div>
              </div>

              {/* Title */}
              <div className="inline-flex items-end justify-start gap-4 self-stretch">
                <div className="shrink grow basis-0 self-stretch text-start font-sans text-2xl font-bold leading-8 text-gray-900 dark:text-gray-100">
                  {cheatsheet.title}
                </div>
              </div>
            </div>
          ))}
          {/* Fill empty slots in the last row */}
          {row.length < 3 && (
            <>
              {Array.from({ length: 3 - row.length }).map((_, index) => (
                <div
                  key={`empty-${index}`}
                  className="hidden lg:block lg:w-1/3 lg:max-w-[364px]"
                />
              ))}
            </>
          )}
        </div>
      ))}
    </div>
  );
}
