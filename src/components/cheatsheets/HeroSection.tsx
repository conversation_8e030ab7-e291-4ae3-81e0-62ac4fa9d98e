/*
 * @Description: Hero Section Component for Cheatsheets Page
 * @Author: Devin
 * @Date: 2025-07-31
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import FluxIcon from "@/components/fluxIcon";
import { Container } from "@/components/ui/container";

export default function HeroSection() {
  const { t } = useTranslation();

  return (
    <div className="bg-indigo-25">
      <Container size="lg">
        <div className="py-16">
          <div className="flex flex-col items-center justify-between self-stretch lg:flex-row">
            {/* Left Content */}
            <div
              className="flex flex-col items-start gap-5"
              style={{ maxWidth: "692px" }}
            >
              <div className="text-center font-sans text-xs font-bold uppercase leading-tight tracking-wider">
                <span className="text-indigo-500 dark:text-indigo-300">
                  {t("cheatsheets.hero.brand")}
                </span>
                <span className="text-black dark:text-gray-50">
                  {" "}
                  {t("cheatsheets.hero.brings")}
                </span>
              </div>
              <div className="text-start font-sans text-5xl font-bold leading-10 text-black dark:text-gray-25">
                {t("cheatsheets.hero.title")}
              </div>
              <div className="self-stretch font-sans text-base font-normal text-gray-600 dark:text-gray-100">
                {t("cheatsheets.hero.description")}
              </div>
            </div>

            {/* Right Illustration */}
            <div className="flex h-72 w-72 items-center justify-center px-2 py-6">
              <FluxIcon
                name="cheatsheets-hero"
                width={270}
                height={242}
                className="w-full h-full"
              />
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
}
