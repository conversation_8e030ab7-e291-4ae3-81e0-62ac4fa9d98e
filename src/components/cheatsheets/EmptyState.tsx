/*
 * @Description: Empty State Component for Cheatsheets Page
 * @Author: <PERSON>
 * @Date: 2025-07-31
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";

interface EmptyStateProps {
  title?: string;
  description?: string;
}

export default function EmptyState({ title, description }: EmptyStateProps) {
  const { t } = useTranslation();

  return (
    <div className="text-center py-16">
      <Typography variant="h5" className="text-gray-600 mb-2">
        {title || t("cheatsheets.empty.title")}
      </Typography>
      <Typography variant="p" className="text-gray-500">
        {description || t("cheatsheets.empty.description")}
      </Typography>
    </div>
  );
}
