"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  FaGoogle,
  FaGithub,
  FaApple,
  FaFacebook,
  FaLinkedin,
} from "react-icons/fa";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { Eye, EyeOff } from "lucide-react";

type SignupFormData = {
  fullName: string;
  email: string;
  password: string;
};

interface SignupFormProps {
  className?: string;
}

const SignupForm: React.FC<SignupFormProps> = ({ className }) => {
  const { t } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 动态创建验证模式
  const signupSchema = z.object({
    fullName: z.string().min(2, t("auth.validation.fullName")),
    email: z.string().email(t("auth.validation.email")),
    password: z.string().min(8, t("auth.validation.password")),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SignupFormData>({
    resolver: zodResolver(signupSchema),
  });

  const onSubmit = async (data: SignupFormData) => {
    setIsLoading(true);
    try {
      // TODO: Implement signup logic
      console.log("Signup data:", data);
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      console.error("Signup error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLogin = (provider: string) => {
    console.log(`Login with ${provider}`);
    // TODO: Implement social login
  };

  return (
    <div
      className={cn(
        "w-full max-w-sm bg-white rounded-lg shadow-lg p-6",
        className
      )}
    >
      {/* Header */}
      <div className="text-center mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          {t("auth.signup.title")}
        </h2>

        {/* Social Login Buttons */}
        <div className="flex justify-center gap-2 mb-4">
          <button
            onClick={() => handleSocialLogin("google")}
            className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            aria-label="Sign up with Google"
          >
            <FaGoogle className="w-4 h-4 text-red-500" />
          </button>
          <button
            onClick={() => handleSocialLogin("github")}
            className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            aria-label="Sign up with GitHub"
          >
            <FaGithub className="w-4 h-4 text-gray-700" />
          </button>
          <button
            onClick={() => handleSocialLogin("apple")}
            className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            aria-label="Sign up with Apple"
          >
            <FaApple className="w-4 h-4 text-gray-900" />
          </button>
          <button
            onClick={() => handleSocialLogin("facebook")}
            className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            aria-label="Sign up with Facebook"
          >
            <FaFacebook className="w-4 h-4 text-blue-600" />
          </button>
          <button
            onClick={() => handleSocialLogin("linkedin")}
            className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            aria-label="Sign up with LinkedIn"
          >
            <FaLinkedin className="w-4 h-4 text-blue-700" />
          </button>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* Full Name Field */}
        <div>
          <Label
            htmlFor="fullName"
            className="text-sm font-medium text-gray-700 mb-1 block"
          >
            {t("auth.fields.fullName")}*
          </Label>
          <Input
            id="fullName"
            type="text"
            placeholder={t("auth.fields.fullName")}
            className={cn(
              "h-11 bg-gray-50 border-gray-200 focus:bg-white focus:border-blue-500 focus-visible:ring-blue-500",
              errors.fullName && "border-red-500 focus-visible:ring-red-500"
            )}
            {...register("fullName")}
          />
          {errors.fullName && (
            <p className="mt-1 text-sm text-red-600">
              {errors.fullName.message}
            </p>
          )}
        </div>

        {/* Email Field */}
        <div>
          <Label
            htmlFor="email"
            className="text-sm font-medium text-gray-700 mb-1 block"
          >
            {t("auth.fields.email")}*
          </Label>
          <Input
            id="email"
            type="email"
            placeholder={t("auth.fields.email")}
            className={cn(
              "h-11 bg-gray-50 border-gray-200 focus:bg-white focus:border-blue-500 focus-visible:ring-blue-500",
              errors.email && "border-red-500 focus-visible:ring-red-500"
            )}
            {...register("email")}
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
          )}
        </div>

        {/* Password Field */}
        <div>
          <Label
            htmlFor="password"
            className="text-sm font-medium text-gray-700 mb-1 block"
          >
            {t("auth.fields.password")}*
          </Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              placeholder={t("auth.fields.password")}
              className={cn(
                "h-11 bg-gray-50 border-gray-200 focus:bg-white focus:border-blue-500 focus-visible:ring-blue-500 pr-10",
                errors.password && "border-red-500 focus-visible:ring-red-500"
              )}
              {...register("password")}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-600">
              {errors.password.message}
            </p>
          )}
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 rounded-md transition-colors"
          disabled={isLoading}
        >
          {isLoading ? t("auth.signup.creating") : t("auth.signup.submit")}
        </Button>
      </form>
    </div>
  );
};

export default SignupForm;
