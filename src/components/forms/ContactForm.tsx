import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useTranslation } from "react-i18next";
interface FormField {
  name: string;
  label: string;
  type: "text" | "email" | "tel" | "textarea" | "select";
  required?: boolean;
  options?: string[];
  rows?: number;
  placeholder?: string;
}

interface ContactFormProps {
  title?: string;
  subtitle?: string;
  fields: FormField[];
  submitButtonText?: string;
  onSubmit: (data: Record<string, string>) => void | Promise<void>;
  variant?: "default" | "card" | "minimal";
  showSuccessMessage?: boolean;
}

export default function ContactForm({
  title = "Contact Us",
  subtitle,
  fields,
  submitButtonText = "Submit",
  onSubmit,
  variant = "card",
  showSuccessMessage = true,
}: ContactFormProps) {
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (error) setError(null);
  };

  const { t } = useTranslation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    // Validate required fields
    const missingFields = fields
      .filter((field) => field.required && !formData[field.name]?.trim())
      .map((field) => field.label);

    if (missingFields.length > 0) {
      setError(
        t("common.formErrors.missingRequiredFields", {
          fields: missingFields.join(", "),
        })
      );
      setIsSubmitting(false);
      return;
    }

    try {
      await onSubmit(formData);
      if (showSuccessMessage) {
        setIsSubmitted(true);
        setFormData({});
      }
    } catch (err) {
      setError(t("common.formErrors.submitError"));
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderField = (field: FormField) => {
    const value = formData[field.name] || "";

    if (field.type === "select") {
      return (
        <div key={field.name} className="space-y-2">
          <Label htmlFor={field.name}>
            {field.label}
            {field.required && <span className="text-destructive ml-1">*</span>}
          </Label>
          <Select
            value={value}
            onValueChange={(value) => handleInputChange(field.name, value)}
            required={field.required}
          >
            <SelectTrigger id={field.name}>
              <SelectValue
                placeholder={t("common.forms.selectPlaceholder", {
                  label: field.label.toLowerCase(),
                })}
              />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );
    }

    if (field.type === "textarea") {
      return (
        <div key={field.name} className="space-y-2">
          <Label htmlFor={field.name}>
            {field.label}
            {field.required && <span className="text-destructive ml-1">*</span>}
          </Label>
          <Textarea
            id={field.name}
            value={value}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            required={field.required}
            placeholder={field.placeholder}
            rows={field.rows || 4}
          />
        </div>
      );
    }

    return (
      <div key={field.name} className="space-y-2">
        <Label htmlFor={field.name}>
          {field.label}
          {field.required && <span className="text-destructive ml-1">*</span>}
        </Label>
        <Input
          id={field.name}
          type={field.type}
          value={value}
          onChange={(e) => handleInputChange(field.name, e.target.value)}
          required={field.required}
          placeholder={field.placeholder}
        />
      </div>
    );
  };

  const formContent = (
    <div>
      {/* Header */}
      {(title || subtitle) && (
        <div className="mb-6 text-center">
          {title && (
            <Typography
              variant="h3"
              className="font-semibold text-gray-900 mb-2"
            >
              {title}
            </Typography>
          )}
          {subtitle && (
            <Typography variant="p" className="text-gray-600 leading-relaxed">
              {subtitle}
            </Typography>
          )}
        </div>
      )}

      {/* Success Message */}
      {isSubmitted && showSuccessMessage && (
        <Alert
          variant="success"
          className="mb-4"
          onClose={() => setIsSubmitted(false)}
        >
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Thank you for your message! We'll get back to you soon.
          </AlertDescription>
        </Alert>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {fields.map((field) => renderField(field))}

        <Button
          type="submit"
          className="w-full"
          size="lg"
          disabled={isSubmitting}
        >
          {isSubmitting ? t("common.status.submitting") : submitButtonText}
        </Button>
      </form>
    </div>
  );

  if (variant === "card") {
    return (
      <Card className="max-w-lg mx-auto shadow-lg">
        <CardContent className="p-6">{formContent}</CardContent>
      </Card>
    );
  }

  if (variant === "minimal") {
    return <div className="max-w-md mx-auto">{formContent}</div>;
  }

  return (
    <div className="max-w-lg mx-auto p-6 bg-white rounded-xl shadow-md">
      {formContent}
    </div>
  );
}
