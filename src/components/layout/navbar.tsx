"use client";

import React, { useState } from "react";
import { Search, ChevronDown } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useAuth } from "@/lib/auth-context";
import { AuthLink } from "@/components/auth/auth-link";
import { useAuthModal } from "@/hooks/useAuthModal";
import UserAuthSection from "./user-auth-section";
import ExploreDropdown from "./ExploreDropdown";
import ResourcesDropdown from "./ResourcesDropdown";
import LanguageSwitcher from "@/components/common/language-switcher";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { Container } from "@/components/ui/container";
import { cn } from "@/lib/utils";

export default function Navbar() {
  const { logout } = useAuth();
  const { openLogin, openSignup } = useAuthModal();
  const { t } = useTranslation();
  const [exploreOpen, setExploreOpen] = useState(false);
  const [resourcesOpen, setResourcesOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // 使用延迟关闭来避免鼠标快速移动时的闪烁
  const [closeTimeout, setCloseTimeout] = useState<NodeJS.Timeout | null>(null);

  const handleExploreMouseEnter = () => {
    if (closeTimeout) {
      clearTimeout(closeTimeout);
      setCloseTimeout(null);
    }
    setExploreOpen(true);
  };

  const handleExploreMouseLeave = () => {
    const timeout = setTimeout(() => {
      setExploreOpen(false);
    }, 100); // 100ms延迟
    setCloseTimeout(timeout);
  };

  const handleResourcesMouseEnter = () => {
    if (closeTimeout) {
      clearTimeout(closeTimeout);
      setCloseTimeout(null);
    }
    setResourcesOpen(true);
  };

  const handleResourcesMouseLeave = () => {
    const timeout = setTimeout(() => {
      setResourcesOpen(false);
    }, 100); // 100ms延迟
    setCloseTimeout(timeout);
  };

  const handleLogout = () => {
    logout();
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // 这里可以添加搜索逻辑，比如跳转到搜索页面
      window.location.href = `/catalog/search?q=${encodeURIComponent(
        searchQuery.trim()
      )}`;
    }
  };

  const navigationItems = [
    {
      label: t("layout.navbar.explore", "Explore"),
      hasDropdown: true,
      key: "explore",
      href: "/explore",
    },
    {
      label: t("layout.navbar.pricing", "Pricing"),
      hasDropdown: false,
      href: "/pricing",
    },
    {
      label: t("layout.navbar.forBusiness", "For Business"),
      hasDropdown: false,
      href: "/business",
    },
    // { label: t("layout.navbar.resources", "Resources"), hasDropdown: true, key: "resources" },
  ];

  return (
    <>
      <header className="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 shadow-sm z-50 h-16">
        <Container size="2xl">
          <div className="flex items-center px-0 min-h-[64px]">
            {/* Left side - Logo and Navigation */}
            <div className="flex items-center flex-1">
              {/* Logo */}
              <AuthLink className="flex items-center gap-2 no-underline">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <Typography
                    as="span"
                    className="text-white font-bold text-xl"
                  >
                    P
                  </Typography>
                </div>
                <Typography
                  variant="h6"
                  className="font-semibold text-gray-800 text-xl"
                >
                  {t("layout.navbar.title", "Pageflux AI")}
                </Typography>
              </AuthLink>

              {/* Navigation Menu - Following Logo */}
              <div className="hidden md:flex items-center gap-0 ml-8">
                {navigationItems.map((item) => (
                  <div
                    key={item.label}
                    className="relative"
                    onMouseEnter={() => {
                      if (item.key === "explore") {
                        handleExploreMouseEnter();
                      } else if (item.key === "resources") {
                        handleResourcesMouseEnter();
                      }
                    }}
                    onMouseLeave={() => {
                      if (item.key === "explore") {
                        handleExploreMouseLeave();
                      } else if (item.key === "resources") {
                        handleResourcesMouseLeave();
                      }
                    }}
                  >
                    {item.href ? (
                      <Link href={item.href} className="no-underline">
                        <Button
                          variant="ghost"
                          className={cn(
                            "text-gray-700 font-medium text-sm px-4 py-2 min-w-auto hover:bg-transparent hover:text-blue-500",
                            item.hasDropdown && "pr-2"
                          )}
                        >
                          {item.label}
                          {item.hasDropdown && (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          )}
                        </Button>
                      </Link>
                    ) : (
                      <Link href={item.href || "#"} className="no-underline">
                        <Button
                          variant="ghost"
                          className={cn(
                            "text-gray-700 font-medium text-sm px-4 py-2 min-w-auto hover:bg-transparent hover:text-blue-500",
                            item.hasDropdown && "pr-2"
                          )}
                        >
                          {item.label}
                          {item.hasDropdown && (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          )}
                        </Button>
                      </Link>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Right side - Search Form and User Actions */}
            <div className="flex items-center">
              {/* Search Form */}
              <form
                onSubmit={handleSearchSubmit}
                className="relative hidden md:block  mr-4"
              >
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder={t("layout.navbar.search", "Search")}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-48 pl-10 pr-4 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </form>

              {/* User Authentication */}
              <UserAuthSection
                onLoginClick={openLogin}
                onSignupClick={openSignup}
                onLogout={handleLogout}
              />
            </div>
          </div>
        </Container>
      </header>

      {/* Explore Mega Menu */}
      <ExploreDropdown
        open={exploreOpen}
        onMouseEnter={handleExploreMouseEnter}
        onMouseLeave={handleExploreMouseLeave}
      />

      {/* Resources Dropdown */}
      <ResourcesDropdown
        open={resourcesOpen}
        onMouseEnter={handleResourcesMouseEnter}
        onMouseLeave={handleResourcesMouseLeave}
      />
    </>
  );
}
