"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Facebook, Twitter, Linkedin, Youtube, Instagram } from "lucide-react";
import Link from "next/link";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export default function Footer() {
  const { t } = useTranslation();

  // 获取翻译后的页脚数据
  const footerSections = t("footer.sections", {
    returnObjects: true,
  }) as Array<{
    title: string;
    links: Array<{ text: string; href: string }>;
  }>;

  return (
    <footer className="bg-gray-800 text-white mt-auto pt-16 pb-8">
      <Container size="lg">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-4">
            <Typography
              variant="h6"
              className="font-semibold text-blue-500 mb-4 text-2xl"
            >
              Pageflux AI
            </Typography>
            <Typography
              variant="small"
              className="text-gray-400 mb-6 leading-relaxed"
            >
              {t("footer.description")}
            </Typography>

            {/* Social Media Icons */}
            <div className="flex gap-2">
              {[Facebook, Twitter, Linkedin, Youtube, Instagram].map(
                (Icon, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="icon"
                    className="text-gray-400 hover:text-blue-500 hover:bg-transparent"
                  >
                    <Icon className="h-5 w-5" />
                  </Button>
                )
              )}
            </div>
          </div>

          {/* Footer Links */}
          {footerSections?.map((section, index) => (
            <div key={index} className="col-span-6 md:col-span-2">
              <Typography
                variant="h6"
                className="font-semibold mb-4 text-base text-white"
              >
                {section.title}
              </Typography>
              <div className="flex flex-col gap-2">
                {section.links.map((link, linkIndex) => (
                  <Link
                    key={linkIndex}
                    href={link.href}
                    className="text-gray-400 text-sm hover:text-blue-500 no-underline transition-colors"
                  >
                    {link.text}
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="border-t border-gray-700 my-8" />

        {/* Bottom Section */}
        <div className="flex justify-between items-center flex-wrap gap-4">
          <Typography variant="small" className="text-gray-400 text-sm">
            {t("footer.copyright")}
          </Typography>

          <div className="flex gap-6">
            <Link
              href="#"
              className="text-gray-400 no-underline text-sm hover:text-blue-500 transition-colors"
            >
              {t("footer.privacyPolicy")}
            </Link>
            <Link
              href="#"
              className="text-gray-400 no-underline text-sm hover:text-blue-500 transition-colors"
            >
              {t("footer.termsOfService")}
            </Link>
          </div>
        </div>
      </Container>
    </footer>
  );
}
