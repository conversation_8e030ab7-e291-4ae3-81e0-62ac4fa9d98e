import React from "react";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import FluxIcon from "@/components/fluxIcon";
import { POINTER_TYPES } from "@/mockdata/maindata";

import { CATEGORY_TYPES } from "@/constants";

interface ExploreDropdownProps {
  open: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

const ExploreDropdown: React.FC<ExploreDropdownProps> = ({
  open,
  onMouseEnter,
  onMouseLeave,
}) => {
  const router = useRouter();
  const { t } = useTranslation();

  if (!open) return null;

  // 获取分类数据，只取前12个
  const categories = Object.values(CATEGORY_TYPES).slice(0, 12);

  // 处理分类点击跳转
  const handleLanguageClick = (slug: string) => {
    router.push(`/catalog/${slug}`);
  };

  // 处理工具点击跳转
  const handleToolClick = (path: string) => {
    router.push(path);
  };

  const iconMap: Record<string, React.ReactNode> = {
    Courses: <FluxIcon name="book" className="h-6 w-6" />,
    "Cloud Labs": <FluxIcon name="cloud-labs" className="h-6 w-6" />,
    "Skill Paths": <FluxIcon name="skill-paths" className="h-6 w-6" />,
    Projects: <FluxIcon name="projects-nav" className="h-6 w-6" />,
    "Mock Interviews": <FluxIcon name="mock-interviews" className="h-6 w-6" />,
    "Personalized Interview Prep": (
      <FluxIcon name="personalized" className="h-6 w-6" />
    ),
    Assessments: <FluxIcon name="assessments" className="h-6 w-6" />,
    "Personalized Paths": (
      <FluxIcon name="personalized-paths" className="h-6 w-6" />
    ),
  };

  const tools = [
    {
      title: POINTER_TYPES.course.title,
      subtitle: POINTER_TYPES.course.description,
      path: "/explore",
    },
    {
      title: POINTER_TYPES.cloudlab.title,
      subtitle: POINTER_TYPES.cloudlab.description,
      path: "/cloud-labs",
    },
    {
      title: POINTER_TYPES.path.title,
      subtitle: POINTER_TYPES.path.description,
      path: "/paths",
    },
    {
      title: POINTER_TYPES.project.title,
      subtitle: POINTER_TYPES.project.description,
      path: "/projects",
    },
    {
      title: POINTER_TYPES.mockinterview.title,
      subtitle: POINTER_TYPES.mockinterview.description,
      badge: "New",
      path: "/mock-interviews",
    },
    {
      title: POINTER_TYPES.interview_prep.title,
      subtitle: POINTER_TYPES.interview_prep.description,
      badge: "New",
      path: "/interview",
    },
    {
      title: POINTER_TYPES.assessment.title,
      subtitle: POINTER_TYPES.assessment.description,
      path: "/assessments",
    },
    {
      title: POINTER_TYPES.personalized_path.title,
      subtitle: POINTER_TYPES.personalized_path.description,
      path: "/learning-plans",
    },
  ];

  return (
    <div
      className="fixed top-16 left-0 right-0 bg-white shadow-xl z-[1200] border-t border-gray-200 w-[75vw] mx-auto"
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="flex">
        {/* LEFT COLUMN */}
        <div className="w-[30%] bg-[#1f1e63] p-4">
          <div className=" text-white p-4 rounded-xl mb-4">
            <div className="mb-2 text-lg">
              {t("layout.exploreDropdown.exploreCatalog")}
            </div>
            <div className="mb-3 opacity-90 leading-6 text-sm">
              {t("layout.exploreDropdown.superchargeCareer")
                .split("\n")
                .map((line, index) => (
                  <React.Fragment key={index}>
                    {line}
                    {index === 0 && <br />}
                  </React.Fragment>
                ))}
            </div>
            <Button
              variant="outline"
              className="bg-white text-blue-700 font-semibold rounded-md px-6 py-2 hover:bg-slate-50 border-blue-700"
              onClick={() => router.push("/explore")}
            >
              {t("layout.exploreDropdown.viewAllCourses")}
            </Button>
          </div>
          {/* LEARNING TOOLS Section */}
          <div className="text-white p-4 rounded-xl mp-4 mt-12">
            <div className="mb-2 text-lg">
              {t("layout.exploreDropdown.learningTools")}
            </div>
            <div className="mb-3  leading-6 text-xs">
              {t("layout.exploreDropdown.completeLearningPlatform")
                .split("\n")
                .map((line, index) => (
                  <React.Fragment key={index}>
                    {line}
                    {index === 0 && <br />}
                  </React.Fragment>
                ))}
            </div>
          </div>
        </div>

        {/* RIGHT COLUMN */}
        <div className="w-[70%]">
          {/* Tool Cards Grid */}
          {/* Categories Grid */}
          <div className="grid grid-cols-4 gap-[1rem] px-8 py-8">
            {categories.map((category) => (
              <span
                key={category.id}
                className="flex justify-start text-[#0c1322] font-normal text-md tracking-[0.031rem] py-1 px-2 rounded-md cursor-pointer transition-colors  hover:text-[#5553ff]"
                onClick={() => handleLanguageClick(category.id)}
              >
                {category.title}
              </span>
            ))}
          </div>
          {/* Tools Grid */}
          <div className="flex flex-wrap gap-[1rem] px-8 py-8">
            {tools.map((item) => (
              <div
                key={item.title}
                className="cursor-pointer p-2 min-w-[40%] w-fit h-auto hover:bg-[#f9fafb] transition-colors duration-200 rounded-md group"
                onClick={() => item.path && handleToolClick(item.path)}
              >
                <div className="flex">
                  <div className="flex items-start">
                    <div className="flex items-center justify-center w-fit h-10 rounded bg-[#eef]">
                      <div className="mx-2 flex items-center justify-center h-6 w-6 text-[#5553ff] fill-current stroke-current">
                        {iconMap[item.title]}
                      </div>
                    </div>
                    <div className="ml-3">
                      <span className="flex items-center">
                        <span className="font-normal text-sm text-[#0c1322] group-hover:text-[#5553ff] transition-colors duration-200">
                          {item.title}
                        </span>
                        {item.badge && (
                          <span className="ml-2 bg-blue-500 text-white px-1 py-0.5 rounded text-xs font-semibold">
                            {item.badge}
                          </span>
                        )}
                      </span>
                      <span className="block text-xs text-gray-500 mt-1">
                        {item.subtitle}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Banner */}
      <div className="bg-[#86198f] text-white px-4 py-2  flex items-center justify-center gap-3">
        {/* LEFT SIDE */}
        <div className="flex gap-4 justify-center items-center">
          <Button className="bg-purple-600 text-white uppercase text-xs rounded-md px-6 py-2 ml-8 hover:bg-purple-700">
            Learn to Code
          </Button>
          <div className="text-md mb-1 text-white">
            Check out our beginner friendly courses.
          </div>
        </div>
        <div className="flex justify-end">
          <div className="text-xl leading-none">→</div>
        </div>
      </div>
    </div>
  );
};

export default ExploreDropdown;
