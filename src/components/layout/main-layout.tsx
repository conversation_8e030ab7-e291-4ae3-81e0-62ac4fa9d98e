/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-07-17 15:34:35
 */
"use client";

import React from "react";
import { usePathname } from "next/navigation";
import Navbar from "./navbar";
import Footer from "./footer";
import GlobalAuthModal from "@/components/auth/global-auth-modal";

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const pathname = usePathname();

  // 定义不显示 footer 的页面路径
  const pagesWithoutFooter = [
    "/courses",
    "/course", // 如果有单独的 course 路径
    // 可以添加更多路径
  ];

  // 检查当前路径是否应该隐藏 footer
  const shouldHideFooter = pagesWithoutFooter.some((path) =>
    pathname.startsWith(path)
  );

  return (
    <div className="flex flex-col min-h-screen">
      {/* Navbar */}
      <Navbar />

      {/* Content */}
      <main className="flex-1 mt-16">{children}</main>

      {/* Footer - 根据路径条件渲染 */}
      {!shouldHideFooter && <Footer />}

      {/* Global Auth Modal */}
      <GlobalAuthModal />
    </div>
  );
}
