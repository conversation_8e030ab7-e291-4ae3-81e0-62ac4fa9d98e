"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useTranslation } from "react-i18next";

import { useAuth } from "@/lib/auth-context";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import FluxIcon from "@/components/fluxIcon";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import * as RadioGroup from "@/components/ui/radio-group";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  getUserDisplayName,
  getUserInitial,
  getUserRoleDisplayName,
} from "@/lib/user-utils";
import LanguageSwitcher from "@/components/common/language-switcher";

interface UserAuthSectionProps {
  onLoginClick: () => void;
  onSignupClick: () => void;
  onLogout: () => void;
}

export default function UserAuthSection({
  onLoginClick,
  onSignupClick,
  onLogout,
}: UserAuthSectionProps) {
  const { isAuthenticated, userInfo } = useAuth();
  const { t } = useTranslation();

  // 临时模拟主题选项（UI 展示用）
  const [theme, setTheme] = useState("system");

  if (isAuthenticated && userInfo) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="rounded-full">
            <Avatar className="w-8 h-8">
              <AvatarFallback>{getUserInitial(userInfo)}</AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-72 p-0 overflow-hidden">
          {/* 用户信息 */}
          <Link
            href={"/profile/view"}
            className="flex items-center gap-3 px-4 py-3 border-b hover:bg-gray-50"
          >
            <Avatar className="w-12 h-12">
              <AvatarFallback>{getUserInitial(userInfo)}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col pt-1">
              <Typography variant="small" className="font-semibold">
                {getUserDisplayName(userInfo)}
              </Typography>
              <div className="text-xs mt-1 text-gray-600">
                {/* {getUserRoleDisplayName(userInfo.role)} • */}
                {t("profile.viewProfile")}
              </div>
            </div>
          </Link>

          {/* 状态标签 */}
          {/* <div className="flex items-center gap-2 px-4 py-2 border-b text-sm bg-gray-50">
            <FluxIcon
              name="github"
              width={18}
              height={18}
              className="text-black"
            />
            <span>GitHub Student Pack</span>
            <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">
              Active
            </span>
          </div> */}

          {/* 菜单项 */}
          <DropdownMenuItem asChild>
            <Link
              href="/transactions"
              className="pl-4 py-3 inline text-sm leading-normal tracking-wide dark:text-gray-50 border-b hover:bg-gray-50 cursor-pointer"
            >
              <FluxIcon
                name="purchases"
                width={20}
                height={20}
                className="mr-3 text-[#4d5663]"
              />
              {t("profile.menu.purchases")}
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link
              href="/profile/edit"
              className="pl-4 py-3 inline text-sm leading-normal tracking-wide dark:text-gray-50 border-b hover:bg-gray-50 cursor-pointer"
            >
              <FluxIcon
                name="settings"
                width={20}
                height={20}
                className="mr-3 text-[#4d5663]"
              />
              {t("profile.menu.settings")}
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link
              href="/certificates"
              className="pl-4 py-3 inline text-sm leading-normal tracking-wide dark:text-gray-50 border-b hover:bg-gray-50 
              focus:bg-gray-50
              cursor-pointer"
            >
              <FluxIcon
                name="certificates"
                width={20}
                height={20}
                className="mr-3 text-[#4d5663]"
              />
             
              {t("profile.menu.certificates")}
            </Link>
          </DropdownMenuItem>

          {/* Color Profile - Accordion */}
          {/* <Accordion
            type="single"
            collapsible
            defaultValue="color-profile"
            className="w-full "
          >
            <AccordionItem value="color-profile" className=" border-gray-200">
              <AccordionTrigger className="px-3 py-3 text-sm hover:bg-gray-50 hover:no-underline  border-b">
                <div className="flex items-center gap-3 pl-2">
                  <FluxIcon
                    name="color-profile"
                    height={12}
                    width={12}
                    className="text-current"
                  />
                  <span>Color Profile</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pb-2">
                <div className="pl-8 py-3 bg-gray-50">
                  <RadioGroup.Root
                    value={theme}
                    onValueChange={setTheme}
                    className="space-y-3"
                  >
                    <div className="flex items-center space-x-3">
                      <RadioGroup.Item
                        value="system"
                        id="system"
                        className="w-5 h-5"
                      />
                      <label
                        htmlFor="system"
                        className="text-sm cursor-pointer"
                      >
                        Same as System
                      </label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <RadioGroup.Item
                        value="dark"
                        id="dark"
                        className="w-5 h-5"
                      />
                      <label htmlFor="dark" className="text-sm cursor-pointer">
                        Dark Mode
                      </label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <RadioGroup.Item
                        value="light"
                        id="light"
                        className="w-5 h-5"
                      />
                      <label htmlFor="light" className="text-sm cursor-pointer">
                        Light Mode
                      </label>
                    </div>
                  </RadioGroup.Root>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion> */}

          {/* Language Switcher - Accordion */}
          <LanguageSwitcher mode="accordion" />

          {/* Logout */}
          <DropdownMenuItem
            onClick={onLogout}
            className="pl-4 py-3  text-sm leading-normal tracking-wide dark:text-gray-50  hover:bg-gray-50 cursor-pointer"
          >
            <FluxIcon
              name="logout"
              width={20}
              height={20}
              className="mr-3 text-[#4d5663]"
            />
            {t("layout.logout", "Logout")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // 未登录状态
  return (
    <>
      <LanguageSwitcher
        mode="dropdown"
        variant="ghost"
        size="sm"
        showText={true}
      />
      <Button
        variant="ghost"
        onClick={onLoginClick}
        className="text-gray-700 font-medium text-sm pl-4 pr-6 py-2 hover:bg-transparent hover:text-blue-500"
      >
        {t("layout.navbar.logIn", "Log In")}
      </Button>
      <Button
        onClick={onSignupClick}
        className="bg-blue-500 text-white font-semibold text-sm px-6 py-2 rounded-md shadow-none hover:bg-blue-600"
      >
        {t("layout.navbar.joinForFree", "Join for free")}
      </Button>
    </>
  );
}
