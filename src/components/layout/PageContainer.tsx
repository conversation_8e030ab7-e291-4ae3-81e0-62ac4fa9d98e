import React from "react";
import NextLink from "next/link";
import PageLayout from "./PageLayout";
import { Typography } from "@/components/ui/typography";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

interface BreadcrumbItemType {
  label: string;
  href?: string;
  current?: boolean;
}

interface PageContainerProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  breadcrumbs?: BreadcrumbItemType[];
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "full";
  backgroundColor?: string;
  fullWidth?: boolean;
  showBreadcrumbs?: boolean;
  headerActions?: React.ReactNode;
}

export default function PageContainer({
  children,
  title,
  subtitle,
  breadcrumbs,
  maxWidth = "xl",
  backgroundColor = "#ffffff",
  fullWidth = false,
  showBreadcrumbs = true,
  headerActions,
}: PageContainerProps) {
  return (
    <PageLayout
      maxWidth={maxWidth}
      backgroundColor={backgroundColor}
      fullWidth={fullWidth}
    >
      {/* Breadcrumbs */}
      {showBreadcrumbs && breadcrumbs && breadcrumbs.length > 0 && (
        <div className="mb-6">
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbs.map((item, index) => (
                <React.Fragment key={index}>
                  <BreadcrumbItem>
                    {item.current || !item.href ? (
                      <BreadcrumbPage
                        className={
                          item.current
                            ? "font-medium text-foreground"
                            : "text-muted-foreground"
                        }
                      >
                        {item.label}
                      </BreadcrumbPage>
                    ) : (
                      <BreadcrumbLink asChild>
                        <NextLink
                          href={item.href}
                          className="text-blue-600 hover:text-blue-800 hover:underline text-sm"
                        >
                          {item.label}
                        </NextLink>
                      </BreadcrumbLink>
                    )}
                  </BreadcrumbItem>
                  {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
                </React.Fragment>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      )}

      {/* Page Header */}
      {(title || subtitle || headerActions) && (
        <div className="mb-8 flex items-start justify-between gap-6 flex-wrap">
          <div className="flex-1 min-w-0">
            {title && (
              <Typography
                variant="h3"
                className={`font-bold text-gray-800 text-3xl md:text-4xl leading-tight ${
                  subtitle ? "mb-4" : ""
                }`}
              >
                {title}
              </Typography>
            )}
            {subtitle && (
              <Typography
                variant="h6"
                className="text-gray-600 font-normal leading-relaxed max-w-2xl"
              >
                {subtitle}
              </Typography>
            )}
          </div>
          {headerActions && (
            <div className="flex-shrink-0">{headerActions}</div>
          )}
        </div>
      )}

      {/* Page Content */}
      {children}
    </PageLayout>
  );
}
