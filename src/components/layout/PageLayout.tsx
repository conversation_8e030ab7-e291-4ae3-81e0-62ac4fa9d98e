/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-17 22:57:57
 */
import React from "react";
import { Container } from "@/components/ui/container";
import { cn } from "@/lib/utils";

interface PageLayoutProps {
  children: React.ReactNode;
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "full";
  backgroundColor?: string;
  fullWidth?: boolean;
  noPadding?: boolean;
  className?: string;
  customPadding?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };
}

export default function PageLayout({
  children,
  maxWidth = "xl",
  backgroundColor = "#ffffff",
  fullWidth = false,
  noPadding = false,
  className,
  customPadding,
}: PageLayoutProps) {
  const paddingTop = customPadding?.top ?? (noPadding ? 0 : 10); // 80px (10 * 8px)
  const paddingBottom = customPadding?.bottom ?? (noPadding ? 0 : 4); // 32px (4 * 8px)
  const paddingLeft = customPadding?.left ?? 0;
  const paddingRight = customPadding?.right ?? 0;

  const content = fullWidth ? (
    <div
      className={cn(
        paddingLeft || paddingRight ? `px-${paddingLeft || 0}` : "",
        className
      )}
    >
      {children}
    </div>
  ) : (
    <Container size={maxWidth}>{children}</Container>
  );

  return (
    <div
      className={cn("min-h-screen relative", className)}
      style={{
        backgroundColor,
        paddingTop: `${paddingTop * 8}px`,
        paddingBottom: `${paddingBottom * 8}px`,
      }}
    >
      {content}
    </div>
  );
}
