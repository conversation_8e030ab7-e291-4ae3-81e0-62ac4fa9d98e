/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-07-31 03:27:53
 */
import React from "react";
import { useRouter } from "next/navigation";
import { Mail, FileText, Download, HelpCircle, Gamepad2 } from "lucide-react";
import FluxIcon from "@/components/fluxIcon";

interface ResourcesDropdownProps {
  open: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

const ResourcesDropdown: React.FC<ResourcesDropdownProps> = ({
  open,
  onMouseEnter,
  onMouseLeave,
}) => {
  const router = useRouter();

  if (!open) return null;

  const resourceItems = [
    {
      icon: <FluxIcon name="newsletter" width={24} height={24} />,
      title: "Newsletter",
      description: "Curated insights on AI, Cloud & System Design",
      href: "/newsletter",
    },
    {
      icon: <FileText className="w-5 h-5" />,
      title: "Blog",
      description: "For developers, By developers",
      href: "/blog",
    },
    {
      icon: <FluxIcon name="newsletter" width={24} height={24} />,
      title: "Free Cheatsheets",
      description: "Download handy guides for tech topics",
      href: "/cheatsheets",
    },
    {
      icon: <HelpCircle className="w-5 h-5" />,
      title: "Answers",
      description: "Trusted answers to developer questions",
      href: "/answers",
    },
    {
      icon: <Gamepad2 className="w-5 h-5" />,
      title: "Games",
      description: "Sharpen your skills with daily challenges",
      href: "/games",
    },
  ];

  const handleItemClick = (href: string) => {
    router.push(href);
  };

  return (
    <div
      className="absolute top-16 left-1/2 transform -translate-x-1/2 bg-white shadow-xl z-[1200] border border-gray-200 rounded-lg"
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="py-4 px-2">
        <div className="">
          <div className="space-y-1">
            {resourceItems.map((item) => (
              <div
                key={item.title}
                className="flex items-start p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200 group"
                onClick={() => handleItemClick(item.href)}
              >
                <div className="flex-shrink-0 mr-3 mt-0.5">
                  <div className="w-10 h-10  bg-[#eef] rounded-lg flex items-center justify-center text-[#5553ff]  transition-colors duration-200">
                    {item.icon}
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                    {item.title}
                  </h3>
                  <p className="text-xs text-gray-500 mt-0.5 leading-relaxed">
                    {item.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResourcesDropdown;
