/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-08-15 10:58:28
 */
"use client";

import { useState, useMemo } from "react";
import {
  DifyChatbot,
  DifyFloatingChatbot,
  DifyTextSelectionChatbot,
  presetThemes,
} from "@codedevin/dify-chat";
import "@codedevin/dify-chat/dist/style.css";

// import {
//   DifyChatbot,
//   DifyFloatingChatbot,
//   DifyTextSelectionChatbot,
//   presetThemes,
// } from "../../../../difys/dev/dify-chat/dist/index.esm";
// import "../../../../difys/dev/dify-chat/dist/style.css";

export type Type = "embedded" | "floating" | "text-selection";

interface DifyChatProps {
  baseUrl?: string;
  apiKey?: string;
  className?: string;
  type?: Type;
  showHeader?: boolean;
  allowFileUpload?: boolean;
  theme?: "light" | "dark";
  userId?: string;
  inputs?: Record<string, any>;
  fileUploadTypes?: string[];
  title?: string;
  subtitle?: string;
  [key: string]: any;
}

export function DifyChat({
  baseUrl = "/api/v1/pfchat",
  apiKey = "",
  className = "",
  type = "embedded",
  showHeader,
  allowFileUpload = false,
  theme = "light",
  userId,
  inputs = {},
  fileUploadTypes = ["image/png", "image/jpeg"],
  title,
  subtitle,
  ...props
}: DifyChatProps) {
  // Dify API 配置
  const difyConfig = useMemo(() => {
    return {
      baseUrl,
      apiKey,
      userId: userId || "demo-user",
      inputs,
    };
  }, [baseUrl, apiKey, userId, inputs]);

  return (
    <>
      {type === "embedded" ? (
        <DifyChatbot
          config={difyConfig}
          className={className}
          theme={theme === "dark" ? presetThemes.dark : presetThemes.light}
          showHeader={showHeader}
          allowFileUpload={allowFileUpload}
          allowedFileTypes={allowFileUpload ? fileUploadTypes : undefined}
          title={title}
          subtitle={subtitle}
          {...props}
        />
      ) : type === "floating" ? (
        <DifyFloatingChatbot
          config={difyConfig}
          className={className}
          position="bottom-right"
          theme={theme === "dark" ? presetThemes.dark : presetThemes.light}
          showHeader={showHeader}
          allowFileUpload={allowFileUpload}
          allowedFileTypes={allowFileUpload ? fileUploadTypes : undefined}
          title={title}
          subtitle={subtitle}
          {...props}
        />
      ) : (
        <DifyTextSelectionChatbot
          config={difyConfig}
          className={className}
          theme={theme === "dark" ? presetThemes.dark : presetThemes.light}
          showHeader={showHeader}
          targetAttribute="data-pfchat"
          allowFileUpload={allowFileUpload}
          allowedFileTypes={allowFileUpload ? fileUploadTypes : undefined}
          title={title}
          subtitle={subtitle}
          {...props}
        />
      )}
    </>
  );
}
