import React from "react";
import { useTranslation } from "react-i18next";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  CheckCircle as CheckIcon,
  XCircle as XIcon,
  AlertCircle as AlertIcon,
  TrendingUp as TrendingUpIcon,
  Target as TargetIcon,
  BookOpen as BookOpenIcon,
  Star as StarIcon,
  Award as AwardIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { InterviewFeedback, FeedbackScore } from "@/types/mock-interview";

interface InterviewFeedbackProps {
  feedback: InterviewFeedback;
  onRetry?: () => void;
  onNext?: () => void;
  showActions?: boolean;
  className?: string;
}

export default function InterviewFeedbackComponent({
  feedback,
  onRetry,
  onNext,
  showActions = true,
  className,
}: InterviewFeedbackProps) {
  const { t } = useTranslation();
  const getScoreColor = (score: FeedbackScore): string => {
    switch (score) {
      case "Excellent":
        return "text-green-600 bg-green-100";
      case "Good":
        return "text-blue-600 bg-blue-100";
      case "Average":
        return "text-yellow-600 bg-yellow-100";
      case "Needs Improvement":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getScoreIcon = (score: FeedbackScore) => {
    switch (score) {
      case "Excellent":
        return <CheckIcon className="w-4 h-4" />;
      case "Good":
        return <CheckIcon className="w-4 h-4" />;
      case "Average":
        return <AlertIcon className="w-4 h-4" />;
      case "Needs Improvement":
        return <XIcon className="w-4 h-4" />;
      default:
        return <AlertIcon className="w-4 h-4" />;
    }
  };

  const getScorePercentage = (score: FeedbackScore): number => {
    switch (score) {
      case "Excellent":
        return 90;
      case "Good":
        return 75;
      case "Average":
        return 60;
      case "Needs Improvement":
        return 40;
      default:
        return 50;
    }
  };

  const getReadinessColor = (readiness: string): string => {
    switch (readiness) {
      case "Ready":
        return "text-green-600 bg-green-100 border-green-200";
      case "Almost Ready":
        return "text-yellow-600 bg-yellow-100 border-yellow-200";
      case "Needs More Practice":
        return "text-red-600 bg-red-100 border-red-200";
      default:
        return "text-gray-600 bg-gray-100 border-gray-200";
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Overall Score */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <AwardIcon className="w-5 h-5" />
              {t("interview.feedback.title")}
            </CardTitle>
            <Badge
              className={cn("px-3 py-1", getScoreColor(feedback.overallScore))}
            >
              {getScoreIcon(feedback.overallScore)}
              <span className="ml-1">{feedback.overallScore}</span>
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <Typography variant="large" className="font-semibold">
              {t("interview.feedback.overallPerformance")}
            </Typography>
            <Typography
              variant="h2"
              className={cn(
                "font-bold",
                getScoreColor(feedback.overallScore).split(" ")[0]
              )}
            >
              {getScorePercentage(feedback.overallScore)}%
            </Typography>
          </div>
          <Progress
            value={getScorePercentage(feedback.overallScore)}
            className="h-3 mb-4"
          />
          <Badge
            className={cn(
              "px-3 py-1",
              getReadinessColor(feedback.estimatedReadiness)
            )}
          >
            <TargetIcon className="w-4 h-4 mr-1" />
            {feedback.estimatedReadiness}
          </Badge>
        </CardContent>
      </Card>

      {/* Detailed Scores */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <StarIcon className="w-5 h-5" />
            {t("interview.feedback.detailedAssessment")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Typography variant="small" className="font-medium">
                  {t("interview.feedback.categories.problemSolving")}
                </Typography>
                <Badge
                  className={cn(
                    "text-xs",
                    getScoreColor(feedback.detailedFeedback.problemSolving)
                  )}
                >
                  {feedback.detailedFeedback.problemSolving}
                </Badge>
              </div>
              <Progress
                value={getScorePercentage(
                  feedback.detailedFeedback.problemSolving
                )}
                className="h-2"
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Typography variant="small" className="font-medium">
                  Communication
                </Typography>
                <Badge
                  className={cn(
                    "text-xs",
                    getScoreColor(feedback.detailedFeedback.communication)
                  )}
                >
                  {feedback.detailedFeedback.communication}
                </Badge>
              </div>
              <Progress
                value={getScorePercentage(
                  feedback.detailedFeedback.communication
                )}
                className="h-2"
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Typography variant="small" className="font-medium">
                  Technical Knowledge
                </Typography>
                <Badge
                  className={cn(
                    "text-xs",
                    getScoreColor(feedback.detailedFeedback.technicalKnowledge)
                  )}
                >
                  {feedback.detailedFeedback.technicalKnowledge}
                </Badge>
              </div>
              <Progress
                value={getScorePercentage(
                  feedback.detailedFeedback.technicalKnowledge
                )}
                className="h-2"
              />
            </div>

            {feedback.detailedFeedback.codeQuality && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Typography variant="small" className="font-medium">
                    Code Quality
                  </Typography>
                  <Badge
                    className={cn(
                      "text-xs",
                      getScoreColor(feedback.detailedFeedback.codeQuality)
                    )}
                  >
                    {feedback.detailedFeedback.codeQuality}
                  </Badge>
                </div>
                <Progress
                  value={getScorePercentage(
                    feedback.detailedFeedback.codeQuality
                  )}
                  className="h-2"
                />
              </div>
            )}

            {feedback.detailedFeedback.systemDesignThinking && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Typography variant="small" className="font-medium">
                    System Design Thinking
                  </Typography>
                  <Badge
                    className={cn(
                      "text-xs",
                      getScoreColor(
                        feedback.detailedFeedback.systemDesignThinking
                      )
                    )}
                  >
                    {feedback.detailedFeedback.systemDesignThinking}
                  </Badge>
                </div>
                <Progress
                  value={getScorePercentage(
                    feedback.detailedFeedback.systemDesignThinking
                  )}
                  className="h-2"
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Strengths and Areas for Improvement */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-600">
              <CheckIcon className="w-5 h-5" />
              {t("interview.feedback.strengths")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {feedback.strengths.map((strength, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckIcon className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <Typography variant="small">{strength}</Typography>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-600">
              <TrendingUpIcon className="w-5 h-5" />
              {t("interview.feedback.areasForImprovement")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {feedback.areasForImprovement.map((area, index) => (
                <li key={index} className="flex items-start gap-2">
                  <TrendingUpIcon className="w-4 h-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <Typography variant="small">{area}</Typography>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpenIcon className="w-5 h-5" />
            Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-3">
            {feedback.recommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start gap-2">
                <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-medium mt-0.5 flex-shrink-0">
                  {index + 1}
                </div>
                <Typography variant="small">{recommendation}</Typography>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TargetIcon className="w-5 h-5" />
            {t("interview.feedback.nextSteps")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            {feedback.nextSteps.map((step, index) => (
              <li key={index} className="flex items-start gap-2">
                <div className="w-2 h-2 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
                <Typography variant="small">{step}</Typography>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      {/* Actions */}
      {showActions && (
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          {onRetry && (
            <Button variant="outline" onClick={onRetry} className="flex-1">
              {t("interview.feedback.actions.tryAgain")}
            </Button>
          )}
          {onNext && (
            <Button onClick={onNext} className="flex-1">
              {t("interview.feedback.actions.nextInterview")}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
