"use client";

import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Brain as BrainIcon,
  Loader2 as LoaderIcon,
  CheckCircle as CheckIcon,
  AlertCircle as AlertIcon,
  TrendingUp as TrendingUpIcon,
  Target as TargetIcon,
  BookOpen as BookOpenIcon,
  Lightbulb as LightbulbIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  InterviewFeedback,
  FeedbackScore,
  MockInterview,
} from "@/types/mock-interview";

interface AIFeedbackGeneratorProps {
  interview: MockInterview;
  userResponse: {
    code?: string;
    drawing?: string;
    chatMessages: Array<{ id: string; sender: "user" | "ai"; message: string }>;
    timeSpent: number;
  };
  onFeedbackGenerated: (feedback: InterviewFeedback) => void;
  className?: string;
}

interface AnalysisStep {
  id: string;
  title: string;
  description: string;
  status: "pending" | "analyzing" | "complete";
  score?: FeedbackScore;
  insights?: string[];
}

export default function AIFeedbackGenerator({
  interview,
  userResponse,
  onFeedbackGenerated,
  className,
}: AIFeedbackGeneratorProps) {
  const { t } = useTranslation();
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [analysisSteps, setAnalysisSteps] = useState<AnalysisStep[]>([
    {
      id: "communication",
      title: t("aiFeedback.steps.communication.title"),
      description: t("aiFeedback.steps.communication.description"),
      status: "pending",
    },
    {
      id: "technical",
      title: t("aiFeedback.steps.technical.title"),
      description: t("aiFeedback.steps.technical.description"),
      status: "pending",
    },
    {
      id: "problem-solving",
      title: t("aiFeedback.steps.problemSolving.title"),
      description: t("aiFeedback.steps.problemSolving.description"),
      status: "pending",
    },
    {
      id: "code-quality",
      title: t("aiFeedback.steps.codeQuality.title"),
      description: t("aiFeedback.steps.codeQuality.description"),
      status: "pending",
    },
    {
      id: "overall",
      title: t("aiFeedback.steps.overall.title"),
      description: t("aiFeedback.steps.overall.description"),
      status: "pending",
    },
  ]);

  useEffect(() => {
    if (isGenerating && currentStep < analysisSteps.length) {
      const timer = setTimeout(() => {
        setAnalysisSteps((prev) =>
          prev.map((step, index) => {
            if (index === currentStep) {
              return { ...step, status: "analyzing" };
            }
            return step;
          })
        );

        // Simulate analysis completion
        setTimeout(() => {
          setAnalysisSteps((prev) =>
            prev.map((step, index) => {
              if (index === currentStep) {
                const mockScore = generateMockScore();
                const mockInsights = generateMockInsights(step.id);
                return {
                  ...step,
                  status: "complete",
                  score: mockScore,
                  insights: mockInsights,
                };
              }
              return step;
            })
          );

          if (currentStep === analysisSteps.length - 1) {
            // Generate final feedback
            setTimeout(() => {
              generateFinalFeedback();
            }, 1000);
          } else {
            setCurrentStep((prev) => prev + 1);
          }
        }, 2000 + Math.random() * 2000); // 2-4 seconds per step
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isGenerating, currentStep, analysisSteps.length]);

  const generateMockScore = (): FeedbackScore => {
    const scores: FeedbackScore[] = [
      "Excellent",
      "Good",
      "Average",
      "Needs Improvement",
    ];
    return scores[Math.floor(Math.random() * scores.length)];
  };

  const generateMockInsights = (stepId: string): string[] => {
    const insights: { [key: string]: string[] } = {
      communication: [
        "Clear articulation of thought process",
        "Good use of technical terminology",
        "Effective questioning and clarification",
      ],
      technical: [
        "Strong understanding of core concepts",
        "Accurate implementation details",
        "Good grasp of system architecture",
      ],
      "problem-solving": [
        "Systematic approach to problem breakdown",
        "Logical progression through solution",
        "Good consideration of edge cases",
      ],
      "code-quality": [
        "Clean and readable code structure",
        "Appropriate use of data structures",
        "Good error handling practices",
      ],
      overall: [
        "Well-rounded technical interview performance",
        "Strong foundation for real interviews",
        "Ready for next level challenges",
      ],
    };

    return insights[stepId] || ["Analysis completed successfully"];
  };

  const generateFinalFeedback = () => {
    const mockFeedback: InterviewFeedback = {
      id: `feedback-${Date.now()}`,
      interviewId: interview.id,
      overallScore: "Good",
      strengths: [
        "Clear communication throughout the interview",
        "Good understanding of system design principles",
        "Systematic approach to problem-solving",
        "Asked relevant clarifying questions",
      ],
      areasForImprovement: [
        "Could have considered more edge cases",
        "Database design could be more detailed",
        "Scalability discussion needed more depth",
        "Time management could be improved",
      ],
      detailedFeedback: {
        problemSolving:
          analysisSteps.find((s) => s.id === "problem-solving")?.score ||
          "Good",
        communication:
          analysisSteps.find((s) => s.id === "communication")?.score || "Good",
        technicalKnowledge:
          analysisSteps.find((s) => s.id === "technical")?.score || "Good",
        codeQuality:
          interview.category === "Coding Interview"
            ? analysisSteps.find((s) => s.id === "code-quality")?.score ||
              "Good"
            : undefined,
        systemDesignThinking:
          interview.category === "System Design" ? "Good" : undefined,
      },
      recommendations: [
        "Practice more system design problems focusing on scalability",
        "Study database sharding and partitioning techniques",
        "Review microservices architecture patterns",
        "Work on explaining complex concepts more concisely",
      ],
      nextSteps: [
        "Take another interview in your weak areas",
        "Review the detailed feedback and practice accordingly",
        "Study real-world system architectures",
        "Practice with time constraints to improve efficiency",
      ],
      estimatedReadiness:
        userResponse.timeSpent < interview.duration * 60 * 0.8
          ? "Almost Ready"
          : "Needs More Practice",
    };

    setIsGenerating(false);
    onFeedbackGenerated(mockFeedback);
  };

  const startAnalysis = () => {
    setIsGenerating(true);
    setCurrentStep(0);
    setAnalysisSteps((prev) =>
      prev.map((step) => ({ ...step, status: "pending" }))
    );
  };

  const getStepIcon = (step: AnalysisStep) => {
    switch (step.status) {
      case "analyzing":
        return <LoaderIcon className="w-5 h-5 animate-spin text-blue-600" />;
      case "complete":
        return <CheckIcon className="w-5 h-5 text-green-600" />;
      default:
        return <AlertIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStepColor = (step: AnalysisStep) => {
    switch (step.status) {
      case "analyzing":
        return "border-blue-200 bg-blue-50";
      case "complete":
        return "border-green-200 bg-green-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  const completedSteps = analysisSteps.filter(
    (step) => step.status === "complete"
  ).length;
  const progress = (completedSteps / analysisSteps.length) * 100;

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BrainIcon className="w-6 h-6 text-blue-600" />
            AI Feedback Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!isGenerating && completedSteps === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <BrainIcon className="w-8 h-8 text-blue-600" />
              </div>
              <Typography variant="h6" className="font-semibold mb-2">
                Ready to Analyze Your Performance
              </Typography>
              <Typography variant="large" className="text-gray-600 mb-6">
                Our AI will analyze your interview performance across multiple
                dimensions
              </Typography>
              <Button onClick={startAnalysis} size="lg">
                Generate AI Feedback
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Progress Overview */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Typography variant="h6" className="font-semibold">
                    Analysis Progress
                  </Typography>
                  <Typography variant="small" className="text-gray-600">
                    {completedSteps} of {analysisSteps.length} complete
                  </Typography>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              {/* Analysis Steps */}
              <div className="space-y-4">
                {analysisSteps.map((step, index) => (
                  <div
                    key={step.id}
                    className={cn(
                      "border rounded-lg p-4 transition-all duration-300",
                      getStepColor(step)
                    )}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-1">
                        {getStepIcon(step)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <Typography variant="h6" className="font-semibold">
                            {step.title}
                          </Typography>
                          {step.score && (
                            <Badge variant="outline" className="text-xs">
                              {step.score}
                            </Badge>
                          )}
                        </div>
                        <Typography
                          variant="small"
                          className="text-gray-600 mb-2"
                        >
                          {step.description}
                        </Typography>

                        {step.status === "analyzing" && (
                          <div className="flex items-center gap-2 text-blue-600">
                            <LoaderIcon className="w-4 h-4 animate-spin" />
                            <Typography variant="small">
                              Analyzing...
                            </Typography>
                          </div>
                        )}

                        {step.insights && step.insights.length > 0 && (
                          <div className="mt-3">
                            <Typography
                              variant="small"
                              className="font-medium mb-2 text-green-700"
                            >
                              Key Insights:
                            </Typography>
                            <ul className="space-y-1">
                              {step.insights.map((insight, idx) => (
                                <li
                                  key={idx}
                                  className="flex items-start gap-2"
                                >
                                  <div className="w-1.5 h-1.5 rounded-full bg-green-500 mt-2 flex-shrink-0" />
                                  <Typography
                                    variant="small"
                                    className="text-green-700"
                                  >
                                    {insight}
                                  </Typography>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Analysis Summary */}
              {isGenerating && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <LoaderIcon className="w-5 h-5 animate-spin text-blue-600" />
                    <div>
                      <Typography
                        variant="h6"
                        className="font-semibold text-blue-900"
                      >
                        AI Analysis in Progress
                      </Typography>
                      <Typography variant="small" className="text-blue-700">
                        Please wait while we analyze your interview
                        performance...
                      </Typography>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LightbulbIcon className="w-5 h-5" />
            While You Wait
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                <BookOpenIcon className="w-4 h-4 text-yellow-600" />
              </div>
              <div>
                <Typography variant="small" className="font-medium mb-1">
                  Review Your Approach
                </Typography>
                <Typography variant="small" className="text-gray-600">
                  Think about what went well and what you'd do differently
                </Typography>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                <TargetIcon className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <Typography variant="small" className="font-medium mb-1">
                  Set Learning Goals
                </Typography>
                <Typography variant="small" className="text-gray-600">
                  Identify specific areas you want to improve next
                </Typography>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
