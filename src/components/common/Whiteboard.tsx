"use client";

import React, { useRef, useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pen as PenIcon,
  Square as RectangleIcon,
  Circle as CircleIcon,
  ArrowRight as ArrowIcon,
  Type as TextIcon,
  Eraser as EraserIcon,
  RotateCcw as UndoIcon,
  RotateCw as RedoIcon,
  Download as DownloadIcon,
  Trash2 as ClearIcon,
  Move as MoveIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface WhiteboardProps {
  width?: number;
  height?: number;
  onDrawingChange?: (drawing: string) => void;
  className?: string;
  readOnly?: boolean;
}

type Tool =
  | "pen"
  | "rectangle"
  | "circle"
  | "arrow"
  | "text"
  | "eraser"
  | "move";

interface DrawingElement {
  id: string;
  type: Tool;
  x: number;
  y: number;
  width?: number;
  height?: number;
  endX?: number;
  endY?: number;
  text?: string;
  color: string;
  strokeWidth: number;
  path?: { x: number; y: number }[];
}

export default function Whiteboard({
  width = 800,
  height = 600,
  onDrawingChange,
  className,
  readOnly = false,
}: WhiteboardProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentTool, setCurrentTool] = useState<Tool>("pen");
  const [currentColor, setCurrentColor] = useState("#000000");
  const [strokeWidth, setStrokeWidth] = useState(2);
  const [elements, setElements] = useState<DrawingElement[]>([]);
  const [history, setHistory] = useState<DrawingElement[][]>([[]]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const [currentPath, setCurrentPath] = useState<{ x: number; y: number }[]>(
    []
  );

  const tools = [
    { value: "pen" as Tool, label: "Pen", icon: PenIcon },
    { value: "rectangle" as Tool, label: "Rectangle", icon: RectangleIcon },
    { value: "circle" as Tool, label: "Circle", icon: CircleIcon },
    { value: "arrow" as Tool, label: "Arrow", icon: ArrowIcon },
    { value: "text" as Tool, label: "Text", icon: TextIcon },
    { value: "eraser" as Tool, label: "Eraser", icon: EraserIcon },
    { value: "move" as Tool, label: "Move", icon: MoveIcon },
  ];

  const colors = [
    "#000000",
    "#FF0000",
    "#00FF00",
    "#0000FF",
    "#FFFF00",
    "#FF00FF",
    "#00FFFF",
    "#FFA500",
    "#800080",
    "#008000",
  ];

  useEffect(() => {
    redrawCanvas();
  }, [elements]);

  useEffect(() => {
    if (onDrawingChange) {
      onDrawingChange(JSON.stringify(elements));
    }
  }, [elements, onDrawingChange]);

  const getCanvasCoordinates = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  };

  const redrawCanvas = () => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    elements.forEach((element) => {
      ctx.strokeStyle = element.color;
      ctx.lineWidth = element.strokeWidth;
      ctx.lineCap = "round";
      ctx.lineJoin = "round";

      switch (element.type) {
        case "pen":
          if (element.path && element.path.length > 1) {
            ctx.beginPath();
            ctx.moveTo(element.path[0].x, element.path[0].y);
            element.path.forEach((point) => {
              ctx.lineTo(point.x, point.y);
            });
            ctx.stroke();
          }
          break;

        case "rectangle":
          if (element.width && element.height) {
            ctx.strokeRect(element.x, element.y, element.width, element.height);
          }
          break;

        case "circle":
          if (element.width && element.height) {
            const radius =
              Math.sqrt(element.width ** 2 + element.height ** 2) / 2;
            ctx.beginPath();
            ctx.arc(
              element.x + element.width / 2,
              element.y + element.height / 2,
              radius,
              0,
              2 * Math.PI
            );
            ctx.stroke();
          }
          break;

        case "arrow":
          if (element.endX && element.endY) {
            // Draw arrow line
            ctx.beginPath();
            ctx.moveTo(element.x, element.y);
            ctx.lineTo(element.endX, element.endY);
            ctx.stroke();

            // Draw arrow head
            const angle = Math.atan2(
              element.endY - element.y,
              element.endX - element.x
            );
            const headLength = 15;
            ctx.beginPath();
            ctx.moveTo(element.endX, element.endY);
            ctx.lineTo(
              element.endX - headLength * Math.cos(angle - Math.PI / 6),
              element.endY - headLength * Math.sin(angle - Math.PI / 6)
            );
            ctx.moveTo(element.endX, element.endY);
            ctx.lineTo(
              element.endX - headLength * Math.cos(angle + Math.PI / 6),
              element.endY - headLength * Math.sin(angle + Math.PI / 6)
            );
            ctx.stroke();
          }
          break;

        case "text":
          if (element.text) {
            ctx.fillStyle = element.color;
            ctx.font = `${element.strokeWidth * 8}px Arial`;
            ctx.fillText(element.text, element.x, element.y);
          }
          break;
      }
    });
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (readOnly) return;

    const pos = getCanvasCoordinates(e);
    setIsDrawing(true);
    setStartPos(pos);

    if (currentTool === "pen") {
      setCurrentPath([pos]);
    } else if (currentTool === "text") {
      const text = prompt("Enter text:");
      if (text) {
        const newElement: DrawingElement = {
          id: Date.now().toString(),
          type: "text",
          x: pos.x,
          y: pos.y,
          text,
          color: currentColor,
          strokeWidth,
        };
        addElement(newElement);
      }
      setIsDrawing(false);
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || readOnly) return;

    const pos = getCanvasCoordinates(e);

    if (currentTool === "pen") {
      setCurrentPath((prev) => [...prev, pos]);

      // Draw temporary line
      const canvas = canvasRef.current;
      const ctx = canvas?.getContext("2d");
      if (ctx) {
        ctx.strokeStyle = currentColor;
        ctx.lineWidth = strokeWidth;
        ctx.lineCap = "round";
        ctx.lineJoin = "round";

        if (currentPath.length > 1) {
          const prevPoint = currentPath[currentPath.length - 2];
          ctx.beginPath();
          ctx.moveTo(prevPoint.x, prevPoint.y);
          ctx.lineTo(pos.x, pos.y);
          ctx.stroke();
        }
      }
    }
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || readOnly) return;

    const pos = getCanvasCoordinates(e);
    setIsDrawing(false);

    const newElement: DrawingElement = {
      id: Date.now().toString(),
      type: currentTool,
      x: startPos.x,
      y: startPos.y,
      color: currentColor,
      strokeWidth,
    };

    switch (currentTool) {
      case "pen":
        newElement.path = currentPath;
        break;
      case "rectangle":
      case "circle":
        newElement.width = pos.x - startPos.x;
        newElement.height = pos.y - startPos.y;
        break;
      case "arrow":
        newElement.endX = pos.x;
        newElement.endY = pos.y;
        break;
    }

    if (currentTool !== "text") {
      addElement(newElement);
    }

    setCurrentPath([]);
  };

  const addElement = (element: DrawingElement) => {
    const newElements = [...elements, element];
    setElements(newElements);

    // Update history
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newElements);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setElements(history[historyIndex - 1]);
    }
  };

  const redo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setElements(history[historyIndex + 1]);
    }
  };

  const clearCanvas = () => {
    setElements([]);
    setHistory([[]]);
    setHistoryIndex(0);
  };

  const downloadCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement("a");
    link.download = "whiteboard.png";
    link.href = canvas.toDataURL();
    link.click();
  };

  return (
    <div className={cn("flex flex-col bg-white border rounded-lg", className)}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50 flex-wrap gap-4">
        <div className="flex items-center gap-4">
          <Typography variant="h6" className="font-semibold">
            Whiteboard
          </Typography>

          {/* Tools */}
          <div className="flex items-center gap-1">
            {tools.map((tool) => {
              const IconComponent = tool.icon;
              return (
                <Button
                  key={tool.value}
                  variant={currentTool === tool.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentTool(tool.value)}
                  disabled={readOnly}
                  className="flex items-center gap-1"
                >
                  <IconComponent className="w-4 h-4" />
                  <span className="hidden sm:inline">{tool.label}</span>
                </Button>
              );
            })}
          </div>
        </div>

        <div className="flex items-center gap-4">
          {/* Colors */}
          <div className="flex items-center gap-1">
            {colors.map((color) => (
              <button
                key={color}
                onClick={() => setCurrentColor(color)}
                disabled={readOnly}
                className={cn(
                  "w-6 h-6 rounded border-2 transition-all",
                  currentColor === color
                    ? "border-gray-800 scale-110"
                    : "border-gray-300"
                )}
                style={{ backgroundColor: color }}
              />
            ))}
          </div>

          {/* Stroke Width */}
          <Select
            value={strokeWidth.toString()}
            onValueChange={(value) => setStrokeWidth(Number(value))}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1px</SelectItem>
              <SelectItem value="2">2px</SelectItem>
              <SelectItem value="4">4px</SelectItem>
              <SelectItem value="6">6px</SelectItem>
              <SelectItem value="8">8px</SelectItem>
            </SelectContent>
          </Select>

          {/* Actions */}
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={undo}
              disabled={historyIndex <= 0 || readOnly}
            >
              <UndoIcon className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={redo}
              disabled={historyIndex >= history.length - 1 || readOnly}
            >
              <RedoIcon className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={downloadCanvas}>
              <DownloadIcon className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearCanvas}
              disabled={readOnly}
              className="text-red-600 hover:text-red-700"
            >
              <ClearIcon className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Canvas */}
      <div className="flex-1 p-4">
        <canvas
          ref={canvasRef}
          width={width}
          height={height}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          className="border border-gray-200 rounded cursor-crosshair bg-white"
          style={{ maxWidth: "100%", height: "auto" }}
        />
      </div>
    </div>
  );
}
