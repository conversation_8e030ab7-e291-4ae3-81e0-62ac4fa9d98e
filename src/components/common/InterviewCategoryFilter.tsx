import React from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import {
  ChevronDown as ChevronDownIcon,
  Filter as FilterIcon,
  X as XIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { InterviewCategory, InterviewDifficulty, CompanyType, InterviewFilter } from "@/types/mock-interview";

interface InterviewCategoryFilterProps {
  filters: InterviewFilter;
  onFiltersChange: (filters: InterviewFilter) => void;
  className?: string;
}

const categories: InterviewCategory[] = [
  "System Design",
  "Coding Interview",
  "MAANG+",
  "Behavioral Interview",
  "Low Level Design",
  "API Design",
  "Artificial Intelligence",
  "DS/ML",
  "Full Stack Development",
  "Advanced System Design",
  "Generative AI System Design",
  "AWS Cloud",
];

const difficulties: InterviewDifficulty[] = ["Easy", "Medium", "Hard"];

const companies: CompanyType[] = [
  "Google",
  "Meta",
  "Amazon",
  "Apple",
  "Netflix",
  "Microsoft",
  "LinkedIn",
  "Oracle",
  "Uber",
  "Stripe",
];

export default function InterviewCategoryFilter({
  filters,
  onFiltersChange,
  className,
}: InterviewCategoryFilterProps) {
  const handleCategoryToggle = (category: InterviewCategory) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category];
    
    onFiltersChange({
      ...filters,
      categories: newCategories,
    });
  };

  const handleDifficultyToggle = (difficulty: InterviewDifficulty) => {
    const newDifficulties = filters.difficulties.includes(difficulty)
      ? filters.difficulties.filter(d => d !== difficulty)
      : [...filters.difficulties, difficulty];
    
    onFiltersChange({
      ...filters,
      difficulties: newDifficulties,
    });
  };

  const handleCompanyToggle = (company: CompanyType) => {
    const newCompanies = filters.companies.includes(company)
      ? filters.companies.filter(c => c !== company)
      : [...filters.companies, company];
    
    onFiltersChange({
      ...filters,
      companies: newCompanies,
    });
  };

  const handleFreeToggle = () => {
    onFiltersChange({
      ...filters,
      isFree: filters.isFree === undefined ? true : filters.isFree ? false : undefined,
    });
  };

  const handlePopularToggle = () => {
    onFiltersChange({
      ...filters,
      isPopular: filters.isPopular === undefined ? true : filters.isPopular ? false : undefined,
    });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      categories: [],
      difficulties: [],
      companies: [],
      duration: { min: 0, max: 120 },
      isFree: undefined,
      isPopular: undefined,
    });
  };

  const getActiveFilterCount = () => {
    let count = 0;
    count += filters.categories.length;
    count += filters.difficulties.length;
    count += filters.companies.length;
    if (filters.isFree !== undefined) count++;
    if (filters.isPopular !== undefined) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <div className={cn("flex flex-wrap items-center gap-2", className)}>
      {/* Quick category filters */}
      <div className="flex flex-wrap gap-2">
        {["System Design", "Coding Interview", "MAANG+", "Behavioral Interview"].map((category) => (
          <Badge
            key={category}
            variant={filters.categories.includes(category as InterviewCategory) ? "default" : "outline"}
            className={cn(
              "cursor-pointer transition-colors hover:bg-blue-100",
              filters.categories.includes(category as InterviewCategory) && "bg-blue-600 text-white"
            )}
            onClick={() => handleCategoryToggle(category as InterviewCategory)}
          >
            {category}
          </Badge>
        ))}
      </div>

      {/* Advanced filters dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="relative">
            <FilterIcon className="w-4 h-4 mr-2" />
            More Filters
            {activeFilterCount > 0 && (
              <Badge className="ml-2 h-5 w-5 rounded-full p-0 text-xs bg-blue-600">
                {activeFilterCount}
              </Badge>
            )}
            <ChevronDownIcon className="w-4 h-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-80" align="start">
          <DropdownMenuLabel>Filter Options</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {/* Difficulty Filter */}
          <div className="p-2">
            <Typography variant="small" className="font-medium mb-2">
              Difficulty
            </Typography>
            <div className="flex gap-2">
              {difficulties.map((difficulty) => (
                <Badge
                  key={difficulty}
                  variant={filters.difficulties.includes(difficulty) ? "default" : "outline"}
                  className="cursor-pointer"
                  onClick={() => handleDifficultyToggle(difficulty)}
                >
                  {difficulty}
                </Badge>
              ))}
            </div>
          </div>

          <DropdownMenuSeparator />

          {/* Company Filter */}
          <div className="p-2">
            <Typography variant="small" className="font-medium mb-2">
              Companies
            </Typography>
            <div className="grid grid-cols-2 gap-1 max-h-32 overflow-y-auto">
              {companies.map((company) => (
                <DropdownMenuCheckboxItem
                  key={company}
                  checked={filters.companies.includes(company)}
                  onCheckedChange={() => handleCompanyToggle(company)}
                  className="text-sm"
                >
                  {company}
                </DropdownMenuCheckboxItem>
              ))}
            </div>
          </div>

          <DropdownMenuSeparator />

          {/* Special Filters */}
          <div className="p-2">
            <Typography variant="small" className="font-medium mb-2">
              Special
            </Typography>
            <div className="space-y-1">
              <DropdownMenuCheckboxItem
                checked={filters.isFree === true}
                onCheckedChange={handleFreeToggle}
              >
                Free Only
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filters.isPopular === true}
                onCheckedChange={handlePopularToggle}
              >
                Popular Only
              </DropdownMenuCheckboxItem>
            </div>
          </div>

          {activeFilterCount > 0 && (
            <>
              <DropdownMenuSeparator />
              <div className="p-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <XIcon className="w-4 h-4 mr-2" />
                  Clear All Filters
                </Button>
              </div>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Active filters display */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-1">
          {filters.categories.map((category) => (
            <Badge
              key={category}
              variant="secondary"
              className="text-xs cursor-pointer hover:bg-red-100"
              onClick={() => handleCategoryToggle(category)}
            >
              {category}
              <XIcon className="w-3 h-3 ml-1" />
            </Badge>
          ))}
          {filters.difficulties.map((difficulty) => (
            <Badge
              key={difficulty}
              variant="secondary"
              className="text-xs cursor-pointer hover:bg-red-100"
              onClick={() => handleDifficultyToggle(difficulty)}
            >
              {difficulty}
              <XIcon className="w-3 h-3 ml-1" />
            </Badge>
          ))}
          {filters.companies.slice(0, 3).map((company) => (
            <Badge
              key={company}
              variant="secondary"
              className="text-xs cursor-pointer hover:bg-red-100"
              onClick={() => handleCompanyToggle(company)}
            >
              {company}
              <XIcon className="w-3 h-3 ml-1" />
            </Badge>
          ))}
          {filters.companies.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{filters.companies.length - 3} companies
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
