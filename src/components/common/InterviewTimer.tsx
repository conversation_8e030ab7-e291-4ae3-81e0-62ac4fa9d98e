import React, { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Play as PlayIcon,
  Pause as PauseIcon,
  Square as StopIcon,
  Clock as ClockIcon,
  AlertTriangle as AlertIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface InterviewTimerProps {
  totalDuration: number; // in seconds
  onTimeUp?: () => void;
  onTimeUpdate?: (remainingTime: number) => void;
  autoStart?: boolean;
  showControls?: boolean;
  warningThreshold?: number; // seconds before warning (default: 300 = 5 minutes)
  className?: string;
}

export default function InterviewTimer({
  totalDuration,
  onTimeUp,
  onTimeUpdate,
  autoStart = false,
  showControls = true,
  warningThreshold = 300,
  className,
}: InterviewTimerProps) {
  const { t } = useTranslation();
  const [remainingTime, setRemainingTime] = useState(totalDuration);
  const [isRunning, setIsRunning] = useState(autoStart);
  const [isPaused, setIsPaused] = useState(false);

  const formatTime = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }
    return `${minutes.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  }, []);

  const getProgressPercentage = useCallback((): number => {
    return ((totalDuration - remainingTime) / totalDuration) * 100;
  }, [totalDuration, remainingTime]);

  const getTimeStatus = useCallback((): "normal" | "warning" | "critical" => {
    if (remainingTime <= 60) return "critical"; // Last minute
    if (remainingTime <= warningThreshold) return "warning";
    return "normal";
  }, [remainingTime, warningThreshold]);

  const handleStart = () => {
    setIsRunning(true);
    setIsPaused(false);
  };

  const handlePause = () => {
    setIsRunning(false);
    setIsPaused(true);
  };

  const handleStop = () => {
    setIsRunning(false);
    setIsPaused(false);
    setRemainingTime(totalDuration);
  };

  const handleResume = () => {
    setIsRunning(true);
    setIsPaused(false);
  };

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isRunning && remainingTime > 0) {
      interval = setInterval(() => {
        setRemainingTime((prev) => {
          const newTime = prev - 1;

          // Call onTimeUpdate callback
          if (onTimeUpdate) {
            onTimeUpdate(newTime);
          }

          // Check if time is up
          if (newTime <= 0) {
            setIsRunning(false);
            if (onTimeUp) {
              onTimeUp();
            }
            return 0;
          }

          return newTime;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isRunning, remainingTime, onTimeUp, onTimeUpdate]);

  const timeStatus = getTimeStatus();
  const progressPercentage = getProgressPercentage();

  return (
    <div
      className={cn(
        "flex items-center gap-4 p-4 bg-white rounded-lg border shadow-sm",
        className
      )}
    >
      {/* Timer Display */}
      <div className="flex items-center gap-2">
        <ClockIcon
          className={cn(
            "w-5 h-5",
            timeStatus === "critical" && "text-red-500 animate-pulse",
            timeStatus === "warning" && "text-yellow-500",
            timeStatus === "normal" && "text-gray-500"
          )}
        />
        <Typography
          variant="h3"
          className={cn(
            "font-mono font-bold",
            timeStatus === "critical" && "text-red-600 animate-pulse",
            timeStatus === "warning" && "text-yellow-600",
            timeStatus === "normal" && "text-gray-900"
          )}
        >
          {formatTime(remainingTime)}
        </Typography>
      </div>

      {/* Progress Bar */}
      <div className="flex-1">
        <Progress
          value={progressPercentage}
          className={cn(
            "h-2",
            timeStatus === "critical" && "[&>div]:bg-red-500",
            timeStatus === "warning" && "[&>div]:bg-yellow-500",
            timeStatus === "normal" && "[&>div]:bg-blue-500"
          )}
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>0:00</span>
          <span>{formatTime(totalDuration)}</span>
        </div>
      </div>

      {/* Warning Indicator */}
      {timeStatus !== "normal" && (
        <div className="flex items-center gap-1">
          <AlertIcon
            className={cn(
              "w-4 h-4",
              timeStatus === "critical" && "text-red-500 animate-pulse",
              timeStatus === "warning" && "text-yellow-500"
            )}
          />
          <Typography
            variant="small"
            className={cn(
              "font-medium",
              timeStatus === "critical" && "text-red-600",
              timeStatus === "warning" && "text-yellow-600"
            )}
          >
            {timeStatus === "critical"
              ? t("interview.timer.timeAlmostUp")
              : t("interview.timer.timeWarning")}
          </Typography>
        </div>
      )}

      {/* Controls */}
      {showControls && (
        <div className="flex items-center gap-2">
          {!isRunning && !isPaused && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleStart}
              className="flex items-center gap-1"
            >
              <PlayIcon className="w-4 h-4" />
              {t("interview.timer.start")}
            </Button>
          )}

          {isRunning && (
            <Button
              variant="outline"
              size="sm"
              onClick={handlePause}
              className="flex items-center gap-1"
            >
              <PauseIcon className="w-4 h-4" />
              {t("interview.timer.pause")}
            </Button>
          )}

          {isPaused && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleResume}
              className="flex items-center gap-1"
            >
              <PlayIcon className="w-4 h-4" />
              {t("interview.timer.resume")}
            </Button>
          )}

          {(isRunning || isPaused) && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleStop}
              className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <StopIcon className="w-4 h-4" />
              {t("interview.timer.stop")}
            </Button>
          )}
        </div>
      )}

      {/* Time Status Text */}
      <div className="text-right">
        <Typography variant="small" className="text-gray-500">
          {isRunning ? "Running" : isPaused ? "Paused" : "Stopped"}
        </Typography>
        <Typography variant="small" className="text-gray-400">
          {Math.round(progressPercentage)}% complete
        </Typography>
      </div>
    </div>
  );
}
