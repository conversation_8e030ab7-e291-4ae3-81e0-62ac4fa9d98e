import React from "react";
import { cn } from "@/lib/utils";

interface ContentGridProps {
  children: React.ReactNode;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: number;
  className?: string;
}

export default function ContentGrid({
  children,
  columns = { xs: 1, sm: 2, lg: 3 },
  gap = 4,
  className,
}: ContentGridProps) {
  const getGridClasses = () => {
    const classes = ["grid"];
    
    // Add column classes
    if (columns.xs) classes.push(`grid-cols-${columns.xs}`);
    if (columns.sm) classes.push(`sm:grid-cols-${columns.sm}`);
    if (columns.md) classes.push(`md:grid-cols-${columns.md}`);
    if (columns.lg) classes.push(`lg:grid-cols-${columns.lg}`);
    if (columns.xl) classes.push(`xl:grid-cols-${columns.xl}`);
    
    // Add gap class
    classes.push(`gap-${gap}`);
    
    return classes.join(" ");
  };

  return (
    <div className={cn(getGridClasses(), className)}>
      {children}
    </div>
  );
}
