"use client";

import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import FluxIcon from "@/components/fluxIcon";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import * as RadioGroup from "@/components/ui/radio-group";

import { setLocaleOnClient, getLocaleOnClient, type Locale } from "@/i18n";

// 支持的语言列表
const supportedLanguages = [
  {
    value: "zh-CN",
    name: "简体中文",
  },
  {
    value: "en-US",
    name: "English",
  },
];

interface LanguageSwitcherProps {
  mode?: "dropdown" | "accordion";
  className?: string;
  showText?: boolean;
  variant?: "ghost" | "outline" | "default";
  size?: "sm" | "default" | "lg";
}

export default function LanguageSwitcher({
  mode = "dropdown",
  className = "",
  showText = true,
  variant = "ghost",
  size = "default",
}: LanguageSwitcherProps) {
  const { t } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState<Locale>("zh-CN");

  useEffect(() => {
    // 获取当前语言设置
    const locale = getLocaleOnClient();
    setCurrentLanguage(locale);
  }, []);

  const handleLanguageChange = (locale: Locale) => {
    setCurrentLanguage(locale);
    setLocaleOnClient(locale, false); // 切换语言但不刷新页面
  };

  const getCurrentLanguageDisplay = () => {
    const lang = supportedLanguages.find(
      (lang) => lang.value === currentLanguage
    );
    return lang?.name || "简体中文";
  };

  const getCurrentLanguageShort = () => {
    const lang = supportedLanguages.find(
      (lang) => lang.value === currentLanguage
    );
    if (lang?.value === "zh-CN") return "中文";
    if (lang?.value === "en-US") return "EN";
    return "中文";
  };

  if (mode === "accordion") {
    return (
      <Accordion
        type="single"
        collapsible
        defaultValue="language"
        className="w-full"
      >
        <AccordionItem value="language" className="border-gray-200">
          <AccordionTrigger className="px-3 py-3 text-sm hover:bg-gray-50 hover:no-underline border-b">
            <div className="flex items-center pl-1">
              <FluxIcon
                name="language"
                width={20}
                height={20}
                className="mr-3 text-[#4d5663]"
              />
              <span>{t("layout.language", "语言")}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="py-0">
            <div className="pl-8 py-3 bg-gray-50">
              <RadioGroup.Root
                value={currentLanguage}
                onValueChange={handleLanguageChange}
              >
                {supportedLanguages.map((language) => (
                  <div
                    key={language.value}
                    className="flex items-center space-x-3 py-1"
                  >
                    <RadioGroup.Item
                      value={language.value}
                      id={language.value}
                      className="w-4 h-4"
                    />
                    <label
                      htmlFor={language.value}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                    >
                      {language.name}
                    </label>
                  </div>
                ))}
              </RadioGroup.Root>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    );
  }

  // Dropdown mode
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size === "sm" ? "sm" : "default"}
          className={`${className} flex items-center gap-2 text-[#4d5663]
          hover:bg-transparent hover:text-blue-500`}
        >
          {showText && (
            <span className="text-sm">
              {size === "sm"
                ? getCurrentLanguageShort()
                : getCurrentLanguageDisplay()}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {supportedLanguages.map((language) => (
          <DropdownMenuItem
            key={language.value}
            onClick={() => handleLanguageChange(language.value as Locale)}
            className={`cursor-pointer ${
              currentLanguage === language.value
                ? "bg-blue-50 text-blue-600"
                : ""
            }`}
          >
            <div className="flex items-center justify-between w-full">
              <span>{language.name}</span>
              {currentLanguage === language.value && (
                <div className="w-2 h-2 bg-blue-600 rounded-full" />
              )}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
