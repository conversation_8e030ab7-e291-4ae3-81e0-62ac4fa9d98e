"use client";

import React, { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Typography } from "@/components/ui/typography";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import {
  Search as SearchIcon,
  Filter as FilterIcon,
  SortAsc as SortIcon,
  X as XIcon,
  ChevronDown as ChevronDownIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  InterviewSearchProps,
  InterviewFilter,
  MockInterview,
} from "@/types/mock-interview";

interface InterviewSearchAndFilterProps {
  interviews: MockInterview[];
  onSearchChange: (searchProps: InterviewSearchProps) => void;
  className?: string;
}

export default function InterviewSearchAndFilter({
  interviews,
  onSearchChange,
  className,
}: InterviewSearchAndFilterProps) {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<InterviewFilter>({
    categories: [],
    difficulties: [],
    companies: [],
    duration: { min: 0, max: 120 },
    isFree: undefined,
    isPopular: undefined,
  });
  const [sortBy, setSortBy] = useState<
    "popularity" | "difficulty" | "duration" | "rating" | "newest"
  >("popularity");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Get unique values from interviews for filter options
  const filterOptions = useMemo(() => {
    const categories = Array.from(new Set(interviews.map((i) => i.category)));
    const difficulties = Array.from(
      new Set(interviews.map((i) => i.difficulty))
    );
    const companies = Array.from(
      new Set(
        interviews
          .map((i) => i.company)
          .filter((company) => company !== undefined)
      )
    );

    return { categories, difficulties, companies };
  }, [interviews]);

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    updateSearch({ searchQuery: query });
  };

  const handleFiltersChange = (newFilters: InterviewFilter) => {
    setFilters(newFilters);
    updateSearch({ filters: newFilters });
  };

  const handleSortChange = (
    newSortBy: typeof sortBy,
    newSortOrder?: typeof sortOrder
  ) => {
    setSortBy(newSortBy);
    if (newSortOrder) setSortOrder(newSortOrder);
    updateSearch({ sortBy: newSortBy, sortOrder: newSortOrder || sortOrder });
  };

  const updateSearch = (updates: Partial<InterviewSearchProps>) => {
    onSearchChange({
      searchQuery,
      filters,
      sortBy,
      sortOrder,
      ...updates,
    });
  };

  const handleCategoryToggle = (category: string) => {
    const newCategories = filters.categories.includes(category as any)
      ? filters.categories.filter((c) => c !== category)
      : [...filters.categories, category as any];

    handleFiltersChange({ ...filters, categories: newCategories });
  };

  const handleDifficultyToggle = (difficulty: string) => {
    const newDifficulties = filters.difficulties.includes(difficulty as any)
      ? filters.difficulties.filter((d) => d !== difficulty)
      : [...filters.difficulties, difficulty as any];

    handleFiltersChange({ ...filters, difficulties: newDifficulties });
  };

  const handleCompanyToggle = (company: string) => {
    const newCompanies = filters.companies.includes(company as any)
      ? filters.companies.filter((c) => c !== company)
      : [...filters.companies, company as any];

    handleFiltersChange({ ...filters, companies: newCompanies });
  };

  const clearAllFilters = () => {
    const clearedFilters: InterviewFilter = {
      categories: [],
      difficulties: [],
      companies: [],
      duration: { min: 0, max: 120 },
      isFree: undefined,
      isPopular: undefined,
    };
    setSearchQuery("");
    setFilters(clearedFilters);
    onSearchChange({
      searchQuery: "",
      filters: clearedFilters,
      sortBy,
      sortOrder,
    });
  };

  const getActiveFilterCount = () => {
    let count = 0;
    count += filters.categories.length;
    count += filters.difficulties.length;
    count += filters.companies.length;
    if (filters.isFree !== undefined) count++;
    if (filters.isPopular !== undefined) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search Bar */}
      <div className="relative">
        <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <Input
          type="text"
          placeholder={t("interview.search.placeholder")}
          value={searchQuery}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="pl-10 pr-4 py-3 text-base"
        />
      </div>

      {/* Filter and Sort Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-wrap gap-2 items-center">
          {/* Quick Category Filters */}
          {[
            "System Design",
            "Coding Interview",
            "MAANG+",
            "Behavioral Interview",
          ].map((category) => (
            <Badge
              key={category}
              variant={
                filters.categories.includes(category as any)
                  ? "default"
                  : "outline"
              }
              className={cn(
                "cursor-pointer transition-colors hover:bg-blue-100",
                filters.categories.includes(category as any) &&
                  "bg-blue-600 text-white"
              )}
              onClick={() => handleCategoryToggle(category)}
            >
              {category}
            </Badge>
          ))}

          {/* Advanced Filters */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="relative">
                <FilterIcon className="w-4 h-4 mr-2" />
                Filters
                {activeFilterCount > 0 && (
                  <Badge className="ml-2 h-5 w-5 rounded-full p-0 text-xs bg-blue-600">
                    {activeFilterCount}
                  </Badge>
                )}
                <ChevronDownIcon className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-80" align="start">
              <DropdownMenuLabel>Advanced Filters</DropdownMenuLabel>
              <DropdownMenuSeparator />

              {/* Difficulty Filter */}
              <div className="p-3">
                <Typography variant="small" className="font-medium mb-2">
                  Difficulty
                </Typography>
                <div className="flex gap-2">
                  {filterOptions.difficulties.map((difficulty) => (
                    <Badge
                      key={difficulty}
                      variant={
                        filters.difficulties.includes(difficulty as any)
                          ? "default"
                          : "outline"
                      }
                      className="cursor-pointer text-xs"
                      onClick={() => handleDifficultyToggle(difficulty)}
                    >
                      {difficulty}
                    </Badge>
                  ))}
                </div>
              </div>

              <DropdownMenuSeparator />

              {/* Company Filter */}
              {filterOptions.companies.length > 0 && (
                <>
                  <div className="p-3">
                    <Typography variant="small" className="font-medium mb-2">
                      Companies
                    </Typography>
                    <div className="grid grid-cols-2 gap-1 max-h-32 overflow-y-auto">
                      {filterOptions.companies.map((company) => (
                        <DropdownMenuCheckboxItem
                          key={company}
                          checked={filters.companies.includes(company as any)}
                          onCheckedChange={() => handleCompanyToggle(company)}
                          className="text-sm"
                        >
                          {company}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                </>
              )}

              {/* Special Filters */}
              <div className="p-3">
                <Typography variant="small" className="font-medium mb-2">
                  Special
                </Typography>
                <div className="space-y-1">
                  <DropdownMenuCheckboxItem
                    checked={filters.isFree === true}
                    onCheckedChange={(checked) =>
                      handleFiltersChange({
                        ...filters,
                        isFree: checked ? true : undefined,
                      })
                    }
                  >
                    Free Only
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={filters.isPopular === true}
                    onCheckedChange={(checked) =>
                      handleFiltersChange({
                        ...filters,
                        isPopular: checked ? true : undefined,
                      })
                    }
                  >
                    Popular Only
                  </DropdownMenuCheckboxItem>
                </div>
              </div>

              {activeFilterCount > 0 && (
                <>
                  <DropdownMenuSeparator />
                  <div className="p-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearAllFilters}
                      className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <XIcon className="w-4 h-4 mr-2" />
                      Clear All Filters
                    </Button>
                  </div>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Sort Controls */}
        <div className="flex items-center gap-2">
          <Typography variant="small" className="text-gray-600">
            Sort by:
          </Typography>
          <Select
            value={sortBy}
            onValueChange={(value) => handleSortChange(value as typeof sortBy)}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="popularity">Popularity</SelectItem>
              <SelectItem value="rating">Rating</SelectItem>
              <SelectItem value="difficulty">Difficulty</SelectItem>
              <SelectItem value="duration">Duration</SelectItem>
              <SelectItem value="newest">Newest</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              handleSortChange(sortBy, sortOrder === "asc" ? "desc" : "asc")
            }
          >
            <SortIcon
              className={cn("w-4 h-4", sortOrder === "desc" && "rotate-180")}
            />
          </Button>
        </div>
      </div>

      {/* Active Filters Display */}
      {(searchQuery || activeFilterCount > 0) && (
        <div className="flex flex-wrap gap-2 items-center">
          {searchQuery && (
            <Badge variant="secondary" className="text-sm">
              Search: "{searchQuery}"
              <button
                onClick={() => handleSearchChange("")}
                className="ml-2 hover:bg-gray-300 rounded-full p-0.5"
              >
                <XIcon className="w-3 h-3" />
              </button>
            </Badge>
          )}

          {filters.categories.map((category) => (
            <Badge key={category} variant="secondary" className="text-sm">
              {category}
              <button
                onClick={() => handleCategoryToggle(category)}
                className="ml-2 hover:bg-gray-300 rounded-full p-0.5"
              >
                <XIcon className="w-3 h-3" />
              </button>
            </Badge>
          ))}

          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-red-600 hover:text-red-700 text-sm"
            >
              Clear all
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
