"use client";

import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Play as PlayIcon,
  RotateCcw as ResetIcon,
  Copy as CopyIcon,
  Download as DownloadIcon,
  CheckCircle as CheckIcon,
  XCircle as XIcon,
} from "lucide-react";
import { Editor } from "@monaco-editor/react";
import { cn } from "@/lib/utils";

// Monaco Editor 语言映射
const getMonacoLanguage = (language: string): string => {
  const languageMap: { [key: string]: string } = {
    javascript: "javascript",
    python: "python",
    java: "java",
    cpp: "cpp",
    typescript: "typescript",
    c: "c",
    csharp: "csharp",
    go: "go",
    rust: "rust",
    php: "php",
    ruby: "ruby",
    swift: "swift",
    kotlin: "kotlin",
    scala: "scala",
    html: "html",
    css: "css",
    json: "json",
    xml: "xml",
    yaml: "yaml",
    sql: "sql",
    shell: "shell",
    bash: "bash",
    powershell: "powershell",
  };
  return languageMap[language] || "plaintext";
};

interface TestCase {
  id: string;
  input: string;
  expectedOutput: string;
  explanation?: string;
}

interface CodeEditorProps {
  initialCode?: string;
  language?: string;
  testCases?: TestCase[];
  onCodeChange?: (code: string) => void;
  onRun?: (
    code: string,
    language: string
  ) => Promise<{ output: string; error?: string }>;
  className?: string;
  readOnly?: boolean;
}

export default function CodeEditor({
  initialCode = "",
  language = "javascript",
  testCases = [],
  onCodeChange,
  onRun,
  className,
  readOnly = false,
}: CodeEditorProps) {
  const { t } = useTranslation();
  const [code, setCode] = useState(initialCode);
  const [selectedLanguage, setSelectedLanguage] = useState(language);
  const [output, setOutput] = useState("");
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<{ [key: string]: boolean }>(
    {}
  );

  // 获取语言列表的函数
  const getLanguages = (t: any) => [
    { value: "javascript", label: t("common.codeEditor.languages.javascript") },
    { value: "python", label: t("common.codeEditor.languages.python") },
    { value: "java", label: t("common.codeEditor.languages.java") },
    { value: "cpp", label: t("common.codeEditor.languages.cpp") },
    { value: "typescript", label: t("common.codeEditor.languages.typescript") },
  ];

  // 获取翻译后的语言列表
  const languages = getLanguages(t);

  useEffect(() => {
    if (onCodeChange) {
      onCodeChange(code);
    }
  }, [code, onCodeChange]);

  const handleCodeChange = (newCode: string) => {
    setCode(newCode);
  };

  const handleRunCode = async () => {
    if (!onRun) return;

    setIsRunning(true);
    setOutput("");
    setTestResults({});

    try {
      const result = await onRun(code, selectedLanguage);
      setOutput(result.output);

      if (result.error) {
        setOutput(`Error: ${result.error}`);
      }

      // Run test cases if available
      if (testCases.length > 0) {
        const results: { [key: string]: boolean } = {};
        for (const testCase of testCases) {
          // Simulate test case execution
          const passed = Math.random() > 0.3; // Mock test result
          results[testCase.id] = passed;
        }
        setTestResults(results);
      }
    } catch (error) {
      setOutput(`Error: ${error}`);
    } finally {
      setIsRunning(false);
    }
  };

  const handleReset = () => {
    setCode(initialCode);
    setOutput("");
    setTestResults({});
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(code);
  };

  const handleDownload = () => {
    const blob = new Blob([code], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `solution.${
      selectedLanguage === "cpp" ? "cpp" : selectedLanguage
    }`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div
      className={cn(
        "flex flex-col h-full bg-white border rounded-lg",
        className
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center gap-4">
          <Typography variant="h6" className="font-semibold">
            Code Editor
          </Typography>

          <Select
            value={selectedLanguage}
            onValueChange={setSelectedLanguage}
            disabled={readOnly}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {languages.map((lang) => (
                <SelectItem key={lang.value} value={lang.value}>
                  {lang.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            className="flex items-center gap-2"
          >
            <CopyIcon className="w-4 h-4" />
            Copy
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            className="flex items-center gap-2"
          >
            <DownloadIcon className="w-4 h-4" />
            Download
          </Button>

          {!readOnly && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="flex items-center gap-2"
              >
                <ResetIcon className="w-4 h-4" />
                Reset
              </Button>

              <Button
                onClick={handleRunCode}
                disabled={isRunning}
                className="flex items-center gap-2"
              >
                <PlayIcon className="w-4 h-4" />
                {isRunning ? "Running..." : "Run"}
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Code Editor */}
      <div className="flex-1 flex">
        <div className="flex-1 relative">
          <Editor
            height="500px"
            language={getMonacoLanguage(selectedLanguage)}
            value={code}
            onChange={(value) => handleCodeChange(value || "")}
            theme="vs-dark"
            options={{
              fontSize: 14,
              fontFamily:
                "'JetBrains Mono', 'Fira Code', 'Consolas', monospace",
              minimap: { enabled: false },
              scrollBeyondLastLine: true,
              automaticLayout: true,
              tabSize: 2,
              insertSpaces: true,
              wordWrap: "on",
              lineNumbers: "on",
              renderLineHighlight: "line",
              selectOnLineNumbers: true,
              roundedSelection: false,
              readOnly: readOnly,
              cursorStyle: "line",
              scrollbar: {
                vertical: "visible",
                horizontal: "visible",
                verticalScrollbarSize: 10,
                horizontalScrollbarSize: 10,
              },
            }}
          />
        </div>

        {/* Output Panel */}
        {(output || testCases.length > 0) && (
          <div className="w-1/3 border-l bg-gray-50">
            <div className="p-4 border-b">
              <Typography variant="h6" className="font-semibold">
                Output & Test Results
              </Typography>
            </div>

            <div className="p-4 space-y-4">
              {/* Console Output */}
              {output && (
                <div>
                  <Typography variant="small" className="font-medium mb-2">
                    Console Output:
                  </Typography>
                  <pre className="bg-gray-900 text-green-400 p-3 rounded text-sm font-mono whitespace-pre-wrap">
                    {output}
                  </pre>
                </div>
              )}

              {/* Test Cases */}
              {testCases.length > 0 && (
                <div>
                  <Typography variant="small" className="font-medium mb-2">
                    Test Cases:
                  </Typography>
                  <div className="space-y-2">
                    {testCases.map((testCase) => (
                      <div key={testCase.id} className="border rounded p-3">
                        <div className="flex items-center justify-between mb-2">
                          <Typography variant="small" className="font-medium">
                            Test Case {testCase.id}
                          </Typography>
                          {testResults[testCase.id] !== undefined && (
                            <Badge
                              variant={
                                testResults[testCase.id]
                                  ? "default"
                                  : "destructive"
                              }
                              className="flex items-center gap-1"
                            >
                              {testResults[testCase.id] ? (
                                <CheckIcon className="w-3 h-3" />
                              ) : (
                                <XIcon className="w-3 h-3" />
                              )}
                              {testResults[testCase.id] ? "Passed" : "Failed"}
                            </Badge>
                          )}
                        </div>
                        <div className="text-xs space-y-1">
                          <div>
                            <span className="font-medium">Input:</span>{" "}
                            {testCase.input}
                          </div>
                          <div>
                            <span className="font-medium">Expected:</span>{" "}
                            {testCase.expectedOutput}
                          </div>
                          {testCase.explanation && (
                            <div>
                              <span className="font-medium">Explanation:</span>{" "}
                              {testCase.explanation}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
