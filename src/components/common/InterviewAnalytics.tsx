"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Target as TargetIcon,
  Clock as ClockIcon,
  Award as AwardIcon,
  BarChart3 as ChartIcon,
  Calendar as CalendarIcon,
  Users as UsersIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { InterviewStats, InterviewCategory, FeedbackScore } from "@/types/mock-interview";

interface InterviewAnalyticsProps {
  stats: InterviewStats;
  recentScores?: { category: InterviewCategory; score: FeedbackScore; date: Date }[];
  className?: string;
}

export default function InterviewAnalytics({
  stats,
  recentScores = [],
  className,
}: InterviewAnalyticsProps) {
  const getScoreValue = (score: FeedbackScore): number => {
    switch (score) {
      case "Excellent": return 90;
      case "Good": return 75;
      case "Average": return 60;
      case "Needs Improvement": return 40;
      default: return 50;
    }
  };

  const getScoreColor = (score: FeedbackScore): string => {
    switch (score) {
      case "Excellent": return "text-green-600 bg-green-100";
      case "Good": return "text-blue-600 bg-blue-100";
      case "Average": return "text-yellow-600 bg-yellow-100";
      case "Needs Improvement": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getTrendIcon = (trend: "improving" | "stable" | "declining") => {
    switch (trend) {
      case "improving":
        return <TrendingUpIcon className="w-4 h-4 text-green-600" />;
      case "declining":
        return <TrendingDownIcon className="w-4 h-4 text-red-600" />;
      default:
        return <TargetIcon className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: "improving" | "stable" | "declining") => {
    switch (trend) {
      case "improving": return "text-green-600 bg-green-100";
      case "declining": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const completionRate = stats.totalInterviews > 0 
    ? Math.round((stats.completedInterviews / stats.totalInterviews) * 100)
    : 0;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <Typography variant="small" className="text-gray-600 mb-1">
                  Total Interviews
                </Typography>
                <Typography variant="h2" className="font-bold text-gray-900">
                  {stats.totalInterviews}
                </Typography>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <UsersIcon className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <Typography variant="small" className="text-gray-600 mb-1">
                  Completion Rate
                </Typography>
                <Typography variant="h2" className="font-bold text-gray-900">
                  {completionRate}%
                </Typography>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <TargetIcon className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <Progress value={completionRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <Typography variant="small" className="text-gray-600 mb-1">
                  Average Score
                </Typography>
                <Typography variant="h2" className="font-bold text-gray-900">
                  {stats.averageScore}%
                </Typography>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                <AwardIcon className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <Progress value={stats.averageScore} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <Typography variant="small" className="text-gray-600 mb-1">
                  Time Spent
                </Typography>
                <Typography variant="h2" className="font-bold text-gray-900">
                  {Math.round(stats.totalTimeSpent / 60)}h
                </Typography>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                <ClockIcon className="w-6 h-6 text-orange-600" />
              </div>
            </div>
            <Typography variant="small" className="text-gray-500 mt-1">
              {stats.totalTimeSpent} minutes total
            </Typography>
          </CardContent>
        </Card>
      </div>

      {/* Performance Trend */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ChartIcon className="w-5 h-5" />
            Performance Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Strongest Category */}
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-3">
                <TrendingUpIcon className="w-8 h-8 text-green-600" />
              </div>
              <Typography variant="h6" className="font-semibold text-gray-900 mb-1">
                Strongest Area
              </Typography>
              <Typography variant="large" className="text-green-600 font-medium">
                {stats.strongestCategory}
              </Typography>
              <Typography variant="small" className="text-gray-500 mt-1">
                Keep up the great work!
              </Typography>
            </div>

            {/* Improvement Trend */}
            <div className="text-center">
              <div className={cn(
                "w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-3",
                getTrendColor(stats.improvementTrend)
              )}>
                {getTrendIcon(stats.improvementTrend)}
              </div>
              <Typography variant="h6" className="font-semibold text-gray-900 mb-1">
                Trend
              </Typography>
              <Typography variant="large" className={cn(
                "font-medium capitalize",
                stats.improvementTrend === "improving" && "text-green-600",
                stats.improvementTrend === "declining" && "text-red-600",
                stats.improvementTrend === "stable" && "text-gray-600"
              )}>
                {stats.improvementTrend}
              </Typography>
              <Typography variant="small" className="text-gray-500 mt-1">
                {stats.improvementTrend === "improving" && "You're getting better!"}
                {stats.improvementTrend === "declining" && "Focus on practice"}
                {stats.improvementTrend === "stable" && "Consistent performance"}
              </Typography>
            </div>

            {/* Weakest Category */}
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center mb-3">
                <TargetIcon className="w-8 h-8 text-red-600" />
              </div>
              <Typography variant="h6" className="font-semibold text-gray-900 mb-1">
                Focus Area
              </Typography>
              <Typography variant="large" className="text-red-600 font-medium">
                {stats.weakestCategory}
              </Typography>
              <Typography variant="small" className="text-gray-500 mt-1">
                Needs more practice
              </Typography>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Performance */}
      {recentScores.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CalendarIcon className="w-5 h-5" />
              Recent Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentScores.slice(0, 5).map((score, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 rounded-full bg-blue-500" />
                    <div>
                      <Typography variant="small" className="font-medium text-gray-900">
                        {score.category}
                      </Typography>
                      <Typography variant="small" className="text-gray-500">
                        {score.date.toLocaleDateString()}
                      </Typography>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Progress 
                      value={getScoreValue(score.score)} 
                      className="w-20 h-2"
                    />
                    <Badge className={cn("text-xs", getScoreColor(score.score))}>
                      {score.score}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TargetIcon className="w-5 h-5" />
            Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats.averageScore < 70 && (
              <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                <div className="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-yellow-600 text-sm font-bold">!</span>
                </div>
                <div>
                  <Typography variant="small" className="font-medium text-yellow-800 mb-1">
                    Focus on Fundamentals
                  </Typography>
                  <Typography variant="small" className="text-yellow-700">
                    Your average score suggests focusing on core concepts. Consider taking easier interviews first.
                  </Typography>
                </div>
              </div>
            )}

            {stats.completedInterviews < 5 && (
              <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-bold">i</span>
                </div>
                <div>
                  <Typography variant="small" className="font-medium text-blue-800 mb-1">
                    Practice More
                  </Typography>
                  <Typography variant="small" className="text-blue-700">
                    Complete more interviews to get better insights and improve your skills.
                  </Typography>
                </div>
              </div>
            )}

            {stats.improvementTrend === "declining" && (
              <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
                <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <TrendingDownIcon className="w-4 h-4 text-red-600" />
                </div>
                <div>
                  <Typography variant="small" className="font-medium text-red-800 mb-1">
                    Review and Reflect
                  </Typography>
                  <Typography variant="small" className="text-red-700">
                    Your recent performance shows a declining trend. Review your past feedback and focus on weak areas.
                  </Typography>
                </div>
              </div>
            )}

            {stats.averageScore >= 80 && stats.completedInterviews >= 10 && (
              <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <AwardIcon className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <Typography variant="small" className="font-medium text-green-800 mb-1">
                    Ready for Real Interviews
                  </Typography>
                  <Typography variant="small" className="text-green-700">
                    Great job! Your performance indicates you're ready for actual technical interviews.
                  </Typography>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
