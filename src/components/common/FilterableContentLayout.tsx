/*
 * @Description: Reusable Layout for Filterable Content Pages (Based on Pageflux AI design)
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React, { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import { Input } from "@/components/ui/input";
import { Search, X } from "lucide-react";
import ItemCard from "./ItemCard";
import FilterSidebar from "./FilterSidebar";
import CategoryOverview from "@/app/cloud-labs/components/CategoryOverview";

interface FilterSection {
  title: string;
  key: string;
  options: { id: string; label: string; count?: number }[];
  showMore?: boolean;
  initialShowCount?: number;
}

interface ContentItem {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  category?: string;
  technologies?: string[];
  topics?: string[];
  isFavorite?: boolean;
}

interface FilterableContentLayoutProps {
  searchPlaceholder?: string;
  filterSections: FilterSection[];
  items: ContentItem[];
  onItemClick?: (id: string) => void;
  onFavoriteToggle?: (id: string) => void;
  type?: "cloudlab" | "path" | "project" | "course";
  initialCategory?: string | null;
  onBackToOverview?: () => void;
}

const FilterableContentLayout: React.FC<FilterableContentLayoutProps> = ({
  searchPlaceholder,
  filterSections,
  items,
  onItemClick,
  onFavoriteToggle,
  type = "course",
  initialCategory,
  onBackToOverview,
}) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");

  // 使用传入的占位符或默认翻译
  const placeholder = searchPlaceholder || t("common.search.placeholder");
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string[]>
  >({});
  const [showCategoryOverview, setShowCategoryOverview] = useState(
    !initialCategory && type === "cloudlab"
  );
  const [challengeFilter, setChallengeFilter] = useState<"all" | "challenges">(
    "all"
  );

  // Set initial category filter if provided
  React.useEffect(() => {
    if (initialCategory) {
      setSelectedFilters((prev) => ({
        ...prev,
        categories: [initialCategory],
      }));
      setShowCategoryOverview(false);
    }
  }, [initialCategory]);

  // Filter items based on search and filters
  const filteredItems = useMemo(() => {
    let filtered = items;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.title.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query) ||
          item.category?.toLowerCase().includes(query) ||
          item.technologies?.some((tech) =>
            tech.toLowerCase().includes(query)
          ) ||
          item.topics?.some((topic) => topic.toLowerCase().includes(query))
      );
    }

    // Apply challenge filter (only for cloudlab type)
    if (type === "cloudlab" && challengeFilter === "challenges") {
      filtered = filtered.filter((item: any) => item.isChallenge === true);
    }

    // Apply category/topic filters
    Object.entries(selectedFilters).forEach(([sectionKey, selectedOptions]) => {
      if (selectedOptions.length > 0) {
        filtered = filtered.filter((item) => {
          switch (sectionKey) {
            case "skillLevel":
              return selectedOptions.includes(item.difficulty);
            case "categories":
            case "topics":
              return (
                (item.category && selectedOptions.includes(item.category)) ||
                item.topics?.some((topic) => selectedOptions.includes(topic))
              );
            case "technologies":
              return item.technologies?.some((tech) =>
                selectedOptions.includes(tech)
              );
            default:
              return true;
          }
        });
      }
    });

    return filtered;
  }, [items, searchQuery, selectedFilters, challengeFilter, type]);

  const handleSidebarSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  const handleMainSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);

    // Hide category overview when user starts searching
    if (value.trim() && type === "cloudlab") {
      setShowCategoryOverview(false);
    } else if (!value.trim() && type === "cloudlab") {
      // Check if no filters are applied, if so show category overview
      const hasAnyFilters = Object.values(selectedFilters).some(
        (filters) => filters.length > 0
      );
      if (!hasAnyFilters) {
        setShowCategoryOverview(true);
      }
    }
  };

  const clearSearch = () => {
    setSearchQuery("");
    // Check if no filters are applied, if so and it's cloudlab type, show category overview
    const hasAnyFilters = Object.values(selectedFilters).some(
      (filters) => filters.length > 0
    );
    if (!hasAnyFilters && type === "cloudlab") {
      setShowCategoryOverview(true);
    }
  };

  const handleFilterChange = (
    sectionKey: string,
    optionId: string,
    checked: boolean
  ) => {
    setSelectedFilters((prev) => {
      const currentFilters = prev[sectionKey] || [];
      let newFilters;
      if (checked) {
        newFilters = {
          ...prev,
          [sectionKey]: [...currentFilters, optionId],
        };
      } else {
        newFilters = {
          ...prev,
          [sectionKey]: currentFilters.filter((id) => id !== optionId),
        };
      }

      // Check if all filters are empty, if so and it's cloudlab type, show category overview
      const hasAnyFilters = Object.values(newFilters).some(
        (filters) => filters.length > 0
      );
      if (!hasAnyFilters && type === "cloudlab") {
        setShowCategoryOverview(true);
      } else {
        setShowCategoryOverview(false);
      }

      return newFilters;
    });
  };

  const handleCategoryClick = (categoryName: string) => {
    setSelectedFilters((prev) => ({
      ...prev,
      categories: [categoryName],
    }));
    setShowCategoryOverview(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 mb-16">
      {/* Main Content */}
      <section className="bg-gray-50">
        <Container size="full" className="px-0">
          <div className="flex">
            {/* Left Sidebar - FilterSidebar */}
            <FilterSidebar
              searchPlaceholder="Search filters here"
              filterSections={filterSections}
              onSearchChange={handleSidebarSearchChange}
              onFilterChange={handleFilterChange}
              selectedFilters={selectedFilters}
            />
            {/* Right Content Area */}
            <div className="flex-1 p-6">
              {/* Search Bar */}
              <div className="mb-6">
                <div className="flex items-center gap-4">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="text"
                      placeholder={placeholder}
                      value={searchQuery}
                      onChange={handleMainSearchChange}
                      className="pl-10 pr-10 py-1 w-full border border-gray-300 h-8"
                    />
                    {searchQuery && (
                      <button
                        onClick={clearSearch}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>

                  {/* Challenge Filter Buttons - only for cloudlab type */}
                  {type === "cloudlab" && (
                    <div className="flex gap-2">
                      <button
                        onClick={() => {
                          setChallengeFilter("all");
                          // Check if should show category overview
                          const hasAnyFilters = Object.values(
                            selectedFilters
                          ).some((filters) => filters.length > 0);
                          if (!hasAnyFilters && !searchQuery.trim()) {
                            setShowCategoryOverview(true);
                          }
                        }}
                        className={`px-4 py-1 text-sm font-medium rounded-full border transition-colors ${
                          challengeFilter === "all"
                            ? "bg-blue-600 text-white border-blue-600"
                            : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                        }`}
                      >
                        All
                      </button>
                      <button
                        onClick={() => {
                          setChallengeFilter("challenges");
                          setShowCategoryOverview(false);
                        }}
                        className={`px-4 py-1 text-sm font-medium rounded-full border transition-colors ${
                          challengeFilter === "challenges"
                            ? "bg-purple-600 text-white border-purple-600"
                            : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                        }`}
                      >
                        Challenges
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Content Area - Category Overview or Items Grid */}
              {showCategoryOverview && type === "cloudlab" ? (
                /* Category Overview - only for cloud labs */
                <CategoryOverview onCategoryClick={handleCategoryClick} />
              ) : (
                /* Items Grid - for all types */
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
                    {filteredItems.map((item) => (
                      <ItemCard
                        key={item.id}
                        {...item}
                        type={type}
                        onClick={onItemClick}
                        onFavoriteToggle={onFavoriteToggle}
                      />
                    ))}
                  </div>

                  {/* No Results */}
                  {filteredItems.length === 0 && (
                    <div className="text-center py-12">
                      <Typography variant="h6" className="text-gray-500 mb-2">
                        {t("common.noResults.title")}
                      </Typography>
                      <Typography variant="p" className="text-gray-400">
                        {t("common.noResults.description")}
                      </Typography>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </Container>
      </section>
    </div>
  );
};

export default FilterableContentLayout;
