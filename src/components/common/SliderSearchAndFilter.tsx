/*
 * @Description: Slider Search and Filter Component for Course Sidebar
 * @Author: Devin
 * @Date: 2025-07-23
 */
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search as SearchIcon, Filter, X, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface FilterOption {
  label: string;
  value: string;
}

interface SliderSearchAndFilterProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  filters?: {
    label: string;
    value: string;
    options: FilterOption[];
    onChange: (value: string) => void;
  }[];
  placeholder?: string;
  className?: string;
  showFilters?: boolean;
  onToggleFilters?: () => void;
  resultsCount?: number;
  compactMode?: boolean;
}

export default function SliderSearchAndFilter({
  searchValue,
  onSearchChange,
  filters = [],
  placeholder = "Search lessons...",
  className,
  showFilters = false,
  onToggleFilters,
  resultsCount,
  compactMode = false,
}: SliderSearchAndFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const hasActiveFilters = filters.some(
    (filter) => filter.value && filter.value !== "all" && filter.value !== ""
  );

  const clearAllFilters = () => {
    filters.forEach((filter) => filter.onChange("all"));
    onSearchChange("");
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search Input */}
      <div className="relative">
        <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder={placeholder}
          value={searchValue}
          onChange={(e) => onSearchChange(e.target.value)}
          className={cn(
            "pl-10 pr-12 transition-all duration-200",
            compactMode ? "h-8 text-sm" : "h-10",
            "border-gray-200 focus:border-blue-500 focus:ring-blue-500"
          )}
        />
        {(searchValue || hasActiveFilters) && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
}
