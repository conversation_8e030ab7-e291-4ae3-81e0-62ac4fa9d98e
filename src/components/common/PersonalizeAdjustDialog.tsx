"use client";

import React, { useEffect, useMemo, useRef, useState } from "react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { personalizeService } from "@/service/personalize.service";
import { handleApiError, handleApiResponse } from "@/lib/api-error-handler";
import type { AdjustScope, PersonalizeStartRequest, PersonalizeStatusResponse, LearningNode, NodeLesson } from "@/types/openapi";
import { learningNodeService } from "@/service/learning-nodes.service";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export interface PersonalizeAdjustDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pathId: string;
  defaultScope?: AdjustScope;
  targetId?: string; // when scope is node/lesson
  onCompleted?: () => void;
}

export default function PersonalizeAdjustDialog(props: PersonalizeAdjustDialogProps) {
  const { open, onOpenChange, pathId, defaultScope = "path", targetId, onCompleted } = props;

  const [studentNeed, setStudentNeed] = useState("");
  const [scope, setScope] = useState<AdjustScope>(defaultScope);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [status, setStatus] = useState<PersonalizeStatusResponse | null>(null);
  const [question, setQuestion] = useState("");
  const [starting, setStarting] = useState(false);
  const [replying, setReplying] = useState(false);
  const pollRef = useRef<NodeJS.Timeout | null>(null);
  const [nodes, setNodes] = useState<LearningNode[]>([]);
  const [selectedNodeId, setSelectedNodeId] = useState<string>("");
  const [lessons, setLessons] = useState<NodeLesson[]>([]);
  const [selectedLessonId, setSelectedLessonId] = useState<string>("");

  useEffect(() => {
    if (!open) {
      // reset state when closed
      setStudentNeed("");
      setScope(defaultScope);
      setConversationId(null);
      setStatus(null);
      setQuestion("");
      setNodes([]);
      setSelectedNodeId("");
      setLessons([]);
      setSelectedLessonId("");
      if (pollRef.current) {
        clearInterval(pollRef.current);
        pollRef.current = null;
      }
    }
  }, [open, defaultScope]);

  // Preselect target when provided
  useEffect(() => {
    if (!open) return;
    if (defaultScope === "node" && targetId) {
      setScope("node");
      setSelectedNodeId(targetId);
    }
    if (defaultScope === "lesson" && targetId) {
      setScope("lesson");
      // lesson 需要节点信息以定位课时；若未选择节点，仍可直接提交（依赖 targetId）
      setSelectedLessonId(targetId);
    }
  }, [open, defaultScope, targetId]);

  // Load nodes when scope requires target selection
  useEffect(() => {
    const needNodes = open && (scope === "node" || scope === "lesson");
    if (!needNodes) return;
    (async () => {
      try {
        const resp = await learningNodeService.listByPath(pathId);
        const data = handleApiResponse(resp, undefined, { showToast: false }) || [];
        setNodes(data);
      } catch (err) {
        handleApiError(err, { showToast: true, defaultMessage: "加载节点失败" });
      }
    })();
  }, [open, scope, pathId]);

  // Load lessons when scope is lesson and node selected
  useEffect(() => {
    if (!(open && scope === "lesson" && selectedNodeId)) return;
    (async () => {
      try {
        const resp = await learningNodeService.listLessons(selectedNodeId);
        const data = handleApiResponse(resp, undefined, { showToast: false }) || [];
        setLessons(data);
      } catch (err) {
        handleApiError(err, { showToast: true, defaultMessage: "加载课时失败" });
      }
    })();
  }, [open, scope, selectedNodeId]);

  useEffect(() => {
    if (!conversationId) return;
    // start polling status
    const tick = async () => {
      try {
        const resp = await personalizeService.status(conversationId);
        const data = handleApiResponse(resp, undefined, { showToast: false });
        setStatus(data || null);
      } catch (err) {
        handleApiError(err, { showToast: false, defaultMessage: "获取进度失败" });
      }
    };
    // immediate then every 3s
    tick();
    pollRef.current = setInterval(tick, 3000);
    return () => {
      if (pollRef.current) {
        clearInterval(pollRef.current);
        pollRef.current = null;
      }
    };
  }, [conversationId]);

  const canReply = useMemo(() => {
    return !!status?.ask_question && !!conversationId;
  }, [status?.ask_question, conversationId]);

  const handleStart = async () => {
    if (!pathId) return;
    try {
      setStarting(true);
      const body: PersonalizeStartRequest = {
        student_need: studentNeed,
        path_id: pathId,
        adjust_scope: scope,
      };
      // decide target_id
      if (scope === "node") {
        const id = selectedNodeId || targetId || "";
        if (!id) throw new Error("请选择节点");
        body.target_id = id;
      } else if (scope === "lesson") {
        const id = selectedLessonId || targetId || "";
        if (!id) throw new Error("请选择课时");
        body.target_id = id;
      }
      const resp = await personalizeService.start(body);
      const data = handleApiResponse(resp, "已开始个性化调整", { showToast: true });
      if (data?.conversation_id) {
        setConversationId(data.conversation_id);
      }
    } catch (err) {
      handleApiError(err, { showToast: true, defaultMessage: "启动个性化失败" });
    } finally {
      setStarting(false);
    }
  };

  const handleReply = async () => {
    if (!conversationId || !question.trim()) return;
    try {
      setReplying(true);
      const resp = await personalizeService.reply({ conversation_id: conversationId, reply: question.trim() });
      handleApiResponse(resp, "已发送回答", { showToast: true });
      setQuestion("");
    } catch (err) {
      handleApiError(err, { showToast: true, defaultMessage: "发送失败" });
    } finally {
      setReplying(false);
    }
  };

  const finished = useMemo(() => {
    const s = (status?.status || "").toLowerCase();
    return s.includes("complete") || s === "done" || s === "success";
  }, [status?.status]);

  useEffect(() => {
    if (finished && onCompleted) {
      onCompleted();
    }
  }, [finished, onCompleted]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>个性化调整</DialogTitle>
          <DialogDescription>
            根据你的学习需求自动调整路径/节点/课时的内容。启动后大约需要 30-45 秒，请稍候。
          </DialogDescription>
        </DialogHeader>

        {!conversationId ? (
          <div className="space-y-3">
            <div className="text-sm text-gray-700">调整范围</div>
            <div className="flex gap-2">
              {(["path", "node", "lesson"] as AdjustScope[]).map((s) => (
                <Button key={s} size="sm" variant={scope === s ? "default" : "outline"} onClick={() => { setScope(s); }}>
                  {s === "path" ? "路径" : s === "node" ? "节点" : "课时"}
                </Button>
              ))}
            </div>
            {scope === "node" && (
              <div className="space-y-2">
                <div className="text-sm text-gray-700">选择节点</div>
                <Select value={selectedNodeId} onValueChange={(v) => setSelectedNodeId(v)}>
                  <SelectTrigger>
                    <SelectValue placeholder={nodes.length ? "请选择节点" : "暂无可用节点"} />
                  </SelectTrigger>
                  <SelectContent>
                    {nodes.map((n) => (
                      <SelectItem key={n.id} value={n.id || ""}>
                        {n.title || n.id}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
            {scope === "lesson" && (
              <div className="space-y-2">
                <div className="text-sm text-gray-700">选择节点</div>
                <Select value={selectedNodeId} onValueChange={(v) => { setSelectedNodeId(v); setSelectedLessonId(""); }}>
                  <SelectTrigger>
                    <SelectValue placeholder={nodes.length ? "请选择节点" : "暂无可用节点"} />
                  </SelectTrigger>
                  <SelectContent>
                    {nodes.map((n) => (
                      <SelectItem key={n.id} value={n.id || ""}>
                        {n.title || n.id}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="text-sm text-gray-700">选择课时</div>
                <Select value={selectedLessonId} onValueChange={(v) => setSelectedLessonId(v)} disabled={!selectedNodeId || lessons.length === 0}>
                  <SelectTrigger>
                    <SelectValue placeholder={!selectedNodeId ? "请先选择节点" : lessons.length ? "请选择课时" : "该节点暂无课时"} />
                  </SelectTrigger>
                  <SelectContent>
                    {lessons.map((l) => (
                      <SelectItem key={l.lesson_id} value={l.lesson_id}>
                        {l.lesson_title || l.lesson_id}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
            <div className="text-sm text-gray-700">你的学习需求</div>
            <textarea
              className="w-full min-h-28 rounded-md border p-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-300"
              placeholder="例如：我只有每周 4 小时，希望更注重项目实践，减少数学推导。"
              value={studentNeed}
              onChange={(e) => setStudentNeed(e.target.value)}
            />
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm">
              <span className="text-gray-600">状态：</span>
              <span className="px-2 py-0.5 rounded bg-slate-100 text-gray-700">{status?.status || "等待中"}</span>
              {!finished && <Loader2 className="w-4 h-4 animate-spin text-gray-500" />}
            </div>
            {status?.last_thought && (
              <div>
                <div className="text-sm text-gray-600 mb-1">最新进展</div>
                <pre className="whitespace-pre-wrap text-sm rounded-md border bg-white p-2 max-h-52 overflow-auto">{status.last_thought}</pre>
              </div>
            )}
            {status?.ask_question && (
              <div>
                <div className="text-sm text-gray-600 mb-1">需要你的回答：</div>
                <div className="text-sm text-gray-800 mb-2">{status.ask_question}</div>
                <textarea
                  className="w-full min-h-24 rounded-md border p-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-300"
                  placeholder="输入你的回答"
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                />
              </div>
            )}
            {status?.error && (
              <div className="text-sm text-red-600">错误：{status.error}</div>
            )}
          </div>
        )}

        <DialogFooter className="flex items-center justify-between">
          {!conversationId ? (
            <Button
              disabled={
                starting ||
                !studentNeed.trim() ||
                (scope === "node" && !(selectedNodeId || targetId)) ||
                (scope === "lesson" && !(selectedLessonId || targetId))
              }
              onClick={handleStart}
            >
              {starting ? <><Loader2 className="w-4 h-4 animate-spin mr-2" /> 启动中</> : "开始调整"}
            </Button>
          ) : (
            <div className="flex gap-2 ml-auto">
              {canReply && (
                <Button variant="secondary" disabled={replying || !question.trim()} onClick={handleReply}>
                  {replying ? <><Loader2 className="w-4 h-4 animate-spin mr-2" /> 发送中</> : "发送回答"}
                </Button>
              )}
              <Button onClick={() => onOpenChange(false)}>关闭</Button>
            </div>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
