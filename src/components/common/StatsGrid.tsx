"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Grid, GridItem } from "@/components/ui/grid";

interface StatItem {
  title: string;
  value: string;
  description?: string;
  icon?: React.ReactNode;
}

interface StatsGridProps {
  stats: StatItem[];
  className?: string;
}

export default function StatsGrid({ stats, className }: StatsGridProps) {
  return (
    <Grid cols={4} gap={6} className={className} responsive={{ sm: 2, md: 4 }}>
      {stats.map((stat, index) => (
        <GridItem key={index}>
          <Card className="text-center p-6 h-full">
            <CardContent className="space-y-2">
              {stat.icon && (
                <div className="flex justify-center mb-4">
                  {stat.icon}
                </div>
              )}
              <Typography variant="h3" className="font-bold text-primary">
                {stat.value}
              </Typography>
              <Typography variant="h6" className="font-semibold">
                {stat.title}
              </Typography>
              {stat.description && (
                <Typography variant="small" className="text-muted-foreground">
                  {stat.description}
                </Typography>
              )}
            </CardContent>
          </Card>
        </GridItem>
      ))}
    </Grid>
  );
}
