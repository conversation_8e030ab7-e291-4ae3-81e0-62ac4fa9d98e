/*
 * @Description: Search Not Found Component - Display when search returns no results
 * @Author: Devin
 * @Date: 2025-07-22
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import FluxIcon from "@/components/fluxIcon";
import { Typography } from "@/components/ui/typography";
import { Button } from "@/components/ui/button";

interface SearchNotFoundProps {
  searchQuery: string;
  onClearSearch?: () => void;
  showClearButton?: boolean;
  className?: string;
}

export default function SearchNotFound({
  searchQuery,
  onClearSearch,
  showClearButton = true,
  className = "",
}: SearchNotFoundProps) {
  const { t } = useTranslation();

  return (
    <div className={`text-center py-16 ${className}`}>
      {/* Search File Icon */}
      <div className="flex justify-center mb-6">
        <FluxIcon
          name="search-file"
          width={320}
          height={240}
          className="h-60 w-80"
        />
      </div>

      {/* Main Message */}
      <Typography
        variant="h5"
        className="font-bold text-xl leading-7 mt-[1.375rem] text-[#0c1322]"
      >
        {t("common.searchNotFound.message", { query: searchQuery })}
      </Typography>

      {/* Clear Search Button */}
      {showClearButton && onClearSearch && (
        <Button
          variant="outline"
          onClick={onClearSearch}
          className="mt-5  border-blue-600 hover:bg-blue-50"
        >
          {t("common.searchNotFound.clearButton")}
        </Button>
      )}
    </div>
  );
}
