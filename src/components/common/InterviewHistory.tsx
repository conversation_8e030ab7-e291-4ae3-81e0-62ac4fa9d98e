"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Calendar as CalendarIcon,
  Clock as ClockIcon,
  TrendingUp as TrendingUpIcon,
  Eye as EyeIcon,
  RotateCcw as RetryIcon,
  Filter as FilterIcon,
  Download as DownloadIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { InterviewSession, FeedbackScore, InterviewCategory } from "@/types/mock-interview";

interface InterviewHistoryProps {
  sessions: InterviewSession[];
  onViewSession?: (sessionId: string) => void;
  onRetryInterview?: (interviewId: string) => void;
  className?: string;
}

export default function InterviewHistory({
  sessions,
  onViewSession,
  onRetryInterview,
  className,
}: InterviewHistoryProps) {
  const [filterCategory, setFilterCategory] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("date");

  // Mock data for demonstration
  const mockSessions: InterviewSession[] = [
    {
      id: "session-1",
      interviewId: "sd-youtube-free",
      userId: "user-1",
      startTime: new Date("2024-01-15T10:00:00"),
      endTime: new Date("2024-01-15T10:45:00"),
      status: "completed",
      currentQuestionIndex: 1,
      responses: [],
      timeSpent: 2700, // 45 minutes
      feedback: {
        id: "feedback-1",
        interviewId: "sd-youtube-free",
        overallScore: "Good",
        strengths: [],
        areasForImprovement: [],
        detailedFeedback: {
          problemSolving: "Good",
          communication: "Excellent",
          technicalKnowledge: "Good",
        },
        recommendations: [],
        nextSteps: [],
        estimatedReadiness: "Almost Ready"
      }
    },
    {
      id: "session-2",
      interviewId: "ci-patterns-free",
      userId: "user-1",
      startTime: new Date("2024-01-12T14:30:00"),
      endTime: new Date("2024-01-12T14:42:00"),
      status: "completed",
      currentQuestionIndex: 1,
      responses: [],
      timeSpent: 720, // 12 minutes
      feedback: {
        id: "feedback-2",
        interviewId: "ci-patterns-free",
        overallScore: "Average",
        strengths: [],
        areasForImprovement: [],
        detailedFeedback: {
          problemSolving: "Average",
          communication: "Good",
          technicalKnowledge: "Average",
          codeQuality: "Average",
        },
        recommendations: [],
        nextSteps: [],
        estimatedReadiness: "Needs More Practice"
      }
    },
    {
      id: "session-3",
      interviewId: "maang-google-coding",
      userId: "user-1",
      startTime: new Date("2024-01-10T09:15:00"),
      status: "in_progress",
      currentQuestionIndex: 0,
      responses: [],
      timeSpent: 1200, // 20 minutes so far
    }
  ];

  const allSessions = sessions.length > 0 ? sessions : mockSessions;

  const getScoreColor = (score: FeedbackScore): string => {
    switch (score) {
      case "Excellent": return "text-green-600 bg-green-100";
      case "Good": return "text-blue-600 bg-blue-100";
      case "Average": return "text-yellow-600 bg-yellow-100";
      case "Needs Improvement": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case "completed": return "text-green-600 bg-green-100";
      case "in_progress": return "text-blue-600 bg-blue-100";
      case "paused": return "text-yellow-600 bg-yellow-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getInterviewTitle = (interviewId: string): string => {
    const titles: { [key: string]: string } = {
      "sd-youtube-free": "YouTube System Design",
      "ci-patterns-free": "Coding Patterns",
      "maang-google-coding": "Google Coding Interview",
    };
    return titles[interviewId] || "Unknown Interview";
  };

  const getInterviewCategory = (interviewId: string): InterviewCategory => {
    const categories: { [key: string]: InterviewCategory } = {
      "sd-youtube-free": "System Design",
      "ci-patterns-free": "Coding Interview",
      "maang-google-coding": "MAANG+",
    };
    return categories[interviewId] || "Coding Interview";
  };

  const filteredSessions = allSessions.filter(session => {
    if (filterCategory !== "all" && getInterviewCategory(session.interviewId) !== filterCategory) {
      return false;
    }
    if (filterStatus !== "all" && session.status !== filterStatus) {
      return false;
    }
    return true;
  });

  const sortedSessions = [...filteredSessions].sort((a, b) => {
    switch (sortBy) {
      case "date":
        return new Date(b.startTime).getTime() - new Date(a.startTime).getTime();
      case "score":
        const scoreA = a.feedback?.overallScore || "Needs Improvement";
        const scoreB = b.feedback?.overallScore || "Needs Improvement";
        const scoreOrder = { "Excellent": 4, "Good": 3, "Average": 2, "Needs Improvement": 1 };
        return scoreOrder[scoreB] - scoreOrder[scoreA];
      case "duration":
        return b.timeSpent - a.timeSpent;
      default:
        return 0;
    }
  });

  const completedSessions = allSessions.filter(s => s.status === "completed");
  const averageScore = completedSessions.length > 0 
    ? completedSessions.reduce((acc, session) => {
        const scoreValue = session.feedback?.overallScore === "Excellent" ? 90 :
                          session.feedback?.overallScore === "Good" ? 75 :
                          session.feedback?.overallScore === "Average" ? 60 : 40;
        return acc + scoreValue;
      }, 0) / completedSessions.length
    : 0;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <Typography variant="h2" className="font-bold text-blue-600">
                {allSessions.length}
              </Typography>
              <Typography variant="small" className="text-gray-600">
                Total Interviews
              </Typography>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <Typography variant="h2" className="font-bold text-green-600">
                {completedSessions.length}
              </Typography>
              <Typography variant="small" className="text-gray-600">
                Completed
              </Typography>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <Typography variant="h2" className="font-bold text-purple-600">
                {Math.round(averageScore)}%
              </Typography>
              <Typography variant="small" className="text-gray-600">
                Average Score
              </Typography>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <Typography variant="h2" className="font-bold text-orange-600">
                {Math.round(allSessions.reduce((acc, s) => acc + s.timeSpent, 0) / 3600)}h
              </Typography>
              <Typography variant="small" className="text-gray-600">
                Total Time
              </Typography>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <CalendarIcon className="w-5 h-5" />
              Interview History
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <DownloadIcon className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="System Design">System Design</SelectItem>
                <SelectItem value="Coding Interview">Coding Interview</SelectItem>
                <SelectItem value="MAANG+">MAANG+</SelectItem>
                <SelectItem value="Behavioral Interview">Behavioral</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date</SelectItem>
                <SelectItem value="score">Score</SelectItem>
                <SelectItem value="duration">Duration</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Session List */}
          <div className="space-y-4">
            {sortedSessions.map((session) => (
              <div
                key={session.id}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <Typography variant="h6" className="font-semibold">
                        {getInterviewTitle(session.interviewId)}
                      </Typography>
                      <Badge variant="outline">
                        {getInterviewCategory(session.interviewId)}
                      </Badge>
                      <Badge className={cn("text-xs", getStatusColor(session.status))}>
                        {session.status.replace("_", " ")}
                      </Badge>
                      {session.feedback && (
                        <Badge className={cn("text-xs", getScoreColor(session.feedback.overallScore))}>
                          {session.feedback.overallScore}
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-6 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-1">
                        <CalendarIcon className="w-4 h-4" />
                        <span>{session.startTime.toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <ClockIcon className="w-4 h-4" />
                        <span>{formatDuration(session.timeSpent)}</span>
                      </div>
                      {session.feedback && (
                        <div className="flex items-center gap-1">
                          <TrendingUpIcon className="w-4 h-4" />
                          <span>Readiness: {session.feedback.estimatedReadiness}</span>
                        </div>
                      )}
                    </div>

                    {session.feedback && (
                      <div className="mb-3">
                        <Typography variant="small" className="text-gray-600 mb-2">
                          Performance Breakdown:
                        </Typography>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div>
                            <Typography variant="small" className="text-gray-500">
                              Problem Solving
                            </Typography>
                            <Badge className={cn("text-xs mt-1", getScoreColor(session.feedback.detailedFeedback.problemSolving))}>
                              {session.feedback.detailedFeedback.problemSolving}
                            </Badge>
                          </div>
                          <div>
                            <Typography variant="small" className="text-gray-500">
                              Communication
                            </Typography>
                            <Badge className={cn("text-xs mt-1", getScoreColor(session.feedback.detailedFeedback.communication))}>
                              {session.feedback.detailedFeedback.communication}
                            </Badge>
                          </div>
                          <div>
                            <Typography variant="small" className="text-gray-500">
                              Technical
                            </Typography>
                            <Badge className={cn("text-xs mt-1", getScoreColor(session.feedback.detailedFeedback.technicalKnowledge))}>
                              {session.feedback.detailedFeedback.technicalKnowledge}
                            </Badge>
                          </div>
                          {session.feedback.detailedFeedback.codeQuality && (
                            <div>
                              <Typography variant="small" className="text-gray-500">
                                Code Quality
                              </Typography>
                              <Badge className={cn("text-xs mt-1", getScoreColor(session.feedback.detailedFeedback.codeQuality))}>
                                {session.feedback.detailedFeedback.codeQuality}
                              </Badge>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    {session.status === "completed" && onViewSession && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onViewSession(session.id)}
                      >
                        <EyeIcon className="w-4 h-4 mr-1" />
                        View
                      </Button>
                    )}
                    {onRetryInterview && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onRetryInterview(session.interviewId)}
                      >
                        <RetryIcon className="w-4 h-4 mr-1" />
                        Retry
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {sortedSessions.length === 0 && (
            <div className="text-center py-8">
              <Typography variant="h6" className="text-gray-500 mb-2">
                No interviews found
              </Typography>
              <Typography variant="small" className="text-gray-400">
                Try adjusting your filters or take your first interview
              </Typography>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
