/*
 * @Description: Reusable Filter Sidebar Component
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Typography } from "@/components/ui/typography";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, ChevronDown, ChevronUp, X } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

interface FilterOption {
  id: string;
  label: string;
  count?: number;
}

interface FilterSection {
  title: string;
  key: string;
  options: FilterOption[];
  showMore?: boolean;
  initialShowCount?: number;
}

interface FilterSidebarProps {
  searchPlaceholder?: string;
  filterSections: FilterSection[];
  onSearchChange: (query: string) => void;
  onFilterChange: (
    sectionKey: string,
    optionId: string,
    checked: boolean
  ) => void;
  selectedFilters: Record<string, string[]>;
}

const FilterSidebar: React.FC<FilterSidebarProps> = ({
  searchPlaceholder,
  filterSections,
  onSearchChange,
  onFilterChange,
  selectedFilters,
}) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");

  // 使用传入的占位符或默认翻译
  const placeholder = searchPlaceholder || t("common.search.filters");
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({});

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearchChange(query);
  };

  const handleSearchExpand = () => {
    setIsSearchExpanded(true);
  };

  const handleSearchCollapse = () => {
    setIsSearchExpanded(false);
    setSearchQuery("");
    onSearchChange("");
  };

  const handleClearAll = () => {
    // Clear all selected filters
    Object.keys(selectedFilters).forEach((sectionKey) => {
      selectedFilters[sectionKey].forEach((optionId) => {
        onFilterChange(sectionKey, optionId, false);
      });
    });
  };

  // Check if any filters are selected
  const hasSelectedFilters = Object.values(selectedFilters).some(
    (filters) => filters.length > 0
  );

  const toggleSection = (sectionKey: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [sectionKey]: !prev[sectionKey],
    }));
  };

  const getVisibleOptions = (section: FilterSection) => {
    if (!section.showMore || expandedSections[section.key]) {
      return section.options;
    }
    return section.options.slice(0, section.initialShowCount || 5);
  };

  return (
    <div className="w-80 border-r border-gray-200 ">
      <div className="sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto p-6 px-12">
        {/* Search Filters Header */}
        <div className="mb-6">
          {/* Search Input - Collapsed State */}
          {!isSearchExpanded ? (
            <div className="flex items-center justify-between cursor-pointer py-2">
              <div
                className="flex gap-4 items-center"
                onClick={handleSearchExpand}
              >
                <span className="text-sm text-gray-700 font-medium">
                  {t("common.search.searchFilters")}
                </span>
                <Search className="h-4 w-4 text-gray-500" />
              </div>
              {hasSelectedFilters && (
                <div
                  className="text-xs cursor-pointer text-blue-600 hover:text-blue-800"
                  onClick={handleClearAll}
                >
                  {t("common.search.clearAll")}
                </div>
              )}
            </div>
          ) : (
            /* Search Input - Expanded State */
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 text-sm" />
              <Input
                type="text"
                placeholder={placeholder}
                value={searchQuery}
                onChange={handleSearchChange}
                className="pl-10 pr-10 py-1 w-full border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 placeholder:text-xs h-8"
                autoFocus
              />
              <button
                onClick={handleSearchCollapse}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
        <div className="border-0 border-b border-solid border-gray-L200 dark:border-gray-D1100 mb-4 mt-5 w-full"></div>
        {/* Filter Sections */}
        <div className="space-y-6">
          {filterSections.map((section) => (
            <div key={section.key} className="border-gray-100 pb-6">
              <Typography
                variant="small"
                className="font-semibold text-gray-900 mb-3"
              >
                {section.title}
              </Typography>

              <div className="space-y-2">
                {getVisibleOptions(section).map((option) => (
                  <div key={option.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`${section.key}-${option.id}`}
                      checked={
                        selectedFilters[section.key]?.includes(option.id) ||
                        false
                      }
                      onCheckedChange={(checked) =>
                        onFilterChange(
                          section.key,
                          option.id,
                          checked as boolean
                        )
                      }
                    />
                    <label
                      htmlFor={`${section.key}-${option.id}`}
                      className="text-sm text-gray-700 cursor-pointer flex-1"
                    >
                      {option.label}
                      {option.count && (
                        <span className="text-gray-400 ml-1">
                          ({option.count})
                        </span>
                      )}
                    </label>
                  </div>
                ))}
              </div>

              {/* Show More/Less Button */}
              {section.showMore &&
                section.options.length > (section.initialShowCount || 5) && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleSection(section.key)}
                    className="mt-3 text-blue-600 p-0 h-auto font-normal"
                  >
                    <>
                      {expandedSections[section.key]
                        ? t("common.showLess")
                        : t("common.showMore")}
                    </>
                  </Button>
                )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FilterSidebar;
