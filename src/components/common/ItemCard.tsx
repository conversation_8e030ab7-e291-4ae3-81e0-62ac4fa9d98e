/*
 * @Description: Reusable Item Card Component (Based on SimpleCourseCard style)
 * @Author: Devin
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Card, CardContent } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import FluxIcon from "@/components/fluxIcon";
import { cn } from "@/lib/utils";
import DifficultyIcon from "@/components/icons/DifficultyIcon";

interface ItemCardProps {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  category?: string;
  technologies?: string[];
  topics?: string[];
  isFavorite?: boolean;
  isChallenge?: boolean;
  type?: "cloudlab" | "path" | "project" | "course";
  onClick?: (id: string) => void;
  onFavoriteToggle?: (id: string) => void;
}

const ItemCard: React.FC<ItemCardProps> = ({
  id,
  title,
  description,
  duration,
  difficulty,
  category,
  technologies = [],
  topics = [],
  isFavorite = false,
  isChallenge = false,
  type = "course",
  onClick,
  onFavoriteToggle,
}) => {
  const { t } = useTranslation();
  const handleClick = () => {
    if (onClick) {
      onClick(id);
    }
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onFavoriteToggle) {
      onFavoriteToggle(id);
    }
  };

  const getTypeConfig = () => {
    switch (type) {
      case "cloudlab":
        return {
          label: t("common.types.cloudLab"),
          icon: <FluxIcon name="cloud" className="w-[14px] h-[14px] mr-1" />,
          bgColor: "bg-[#e6f3ff]",
          textColor: "text-[#0066cc]",
        };
      case "path":
        return {
          label: t("common.types.skillPath"),
          icon: <FluxIcon name="path" className="w-[14px] h-[14px] mr-1" />,
          bgColor: "bg-[#f0e6ff]",
          textColor: "text-[#7c3aed]",
        };
      case "project":
        return {
          label: t("common.types.project"),
          icon: <FluxIcon name="project" className="w-[14px] h-[14px] mr-1" />,
          bgColor: "bg-[#e6fff0]",
          textColor: "text-[#059669]",
        };
      default:
        return {
          label: t("common.types.course"),
          icon: (
            <FluxIcon name="course-icon" className="w-[14px] h-[14px] mr-1" />
          ),
          bgColor: "bg-[#eef]",
          textColor: "text-[#2b2a83]",
        };
    }
  };

  const typeConfig = getTypeConfig();

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border border-gray-200 h-68"
      )}
      onClick={handleClick}
    >
      <CardContent className="p-4 h-full flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <div
            className={cn(
              "flex items-center h-6 font-semibold text-ms leading-[0.75rem]  py-0 text-gray-500"
            )}
            style={{
              fontFamily:
                '"Helvetica Neue", "SF Pro Display", Arial, Roboto, system-ui',
            }}
          >
            {category}
          </div>
          <button
            onClick={handleFavoriteClick}
            className="text-gray-400 hover:text-gray-600"
          >
            <FluxIcon
              name="bookmark"
              className="w-5 h-5"
              style={{ fill: isFavorite ? "currentColor" : "none" }}
            />
          </button>
        </div>

        <div className="mb-2">
          <Typography
            variant="small"
            className="font-semibold text-gray-800 text-ms leading-5"
          >
            {title}
            {isChallenge && type === "cloudlab" && (
              <span className="inline-block mt-1 px-1  text-[10px] font-medium text-white bg-purple-600 rounded ml-1">
                {t("common.challenge")}
              </span>
            )}
          </Typography>
          {/* Challenge Badge - only for cloudlab type */}
        </div>

        <span className="text-gray-600 text-xs line-clamp-6 flex-1 overflow-hidden">
          {description}
        </span>

        <div className="flex items-center justify-between text-[10px] text-gray-500 mt-4">
          <span className="flex items-center mr-4">
            <FluxIcon name="clock-simple" className="w-3 h-3 mr-1" />
            <div>{duration}</div>
          </span>
          <span className="flex items-center">
            <span className="flex items-center">
              <DifficultyIcon
                difficulty={
                  difficulty.toLowerCase() as
                    | "beginner"
                    | "intermediate"
                    | "advanced"
                }
                className="mr-1 text-gray-500"
                size={12}
              />
              {difficulty}
            </span>
          </span>
        </div>
      </CardContent>
    </Card>
  );
};

export default ItemCard;
