/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-18 17:31:14
 */
"use client";

import React from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface LogoItem {
  name: string;
  logoPath?: string;
  color?: string;
  hasLogo: boolean;
}

interface LogoCarouselProps {
  logos: LogoItem[];
  className?: string;
  speed?: number;
}

export default function LogoCarousel({
  logos,
  className,
  speed = 20,
}: LogoCarouselProps) {
  // 复制logos数组以创建无缝循环效果
  const duplicatedLogos = [...logos, ...logos];

  return (
    <div className={cn("overflow-hidden relative", className)}>
      <div
        className="flex items-center space-x-4 animate-marquee gap-4"
        style={{
          animationDuration: `${speed}s`,
          width: `${duplicatedLogos.length * 100}px`,
        }}
      >
        {duplicatedLogos.map((logo, index) => (
          <div
            key={`${logo.name}-${index}`}
            className="flex-shrink-0 flex items-center justify-center w-16"
          >
            {logo.hasLogo && logo.logoPath ? (
              <Image
                src={logo.logoPath}
                alt={`${logo.name} logo`}
                width={96}
                height={32}
                className="h-6 w-auto  hover:opacity-100 transition-opacity duration-200"
              />
            ) : (
              <span
                className={cn(
                  "font-bold text-base tracking-wide opacity-60 hover:opacity-100 transition-opacity duration-200 whitespace-nowrap",
                  logo.color || "text-gray-600"
                )}
              >
                {logo.name}
              </span>
            )}
          </div>
        ))}
      </div>

      <style jsx global>{`
        @keyframes marquee {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        .animate-marquee {
          animation: marquee ${speed}s linear infinite;
        }
      `}</style>
    </div>
  );
}
