import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Clock as TimeIcon,
  Users as PeopleIcon,
  Star as StarIcon,
  Play as PlayIcon,
  Code as CodeIcon,
  FileText as QuizIcon,
  Zap as ChallengeIcon,
  Image as IllustrationIcon,
  ExternalLink,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Course } from "@/types/catalog";

interface LanguageCourseCardProps {
  course: Course;
  variant?: "default" | "compact" | "featured";
  onEnroll?: (courseId: string) => void;
  onBookmark?: (courseId: string) => void;
  languageConfig?: {
    name: string;
    icon: string;
    color: string;
  };
}

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty.toLowerCase()) {
    case "beginner":
      return "bg-green-100 text-green-800 border-green-200";
    case "intermediate":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "advanced":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getPriceColor = (price?: string) => {
  if (price === "Free") {
    return "bg-green-100 text-green-800 border-green-200";
  }
  return "bg-blue-100 text-blue-800 border-blue-200";
};

export default function LanguageCourseCard({
  course,
  variant = "default",
  onEnroll,
  onBookmark,
  languageConfig,
}: LanguageCourseCardProps) {
  const handleEnroll = () => {
    if (onEnroll) {
      onEnroll(course.id);
    } else {
      // Open course URL in new tab
      window.open(course.url, "_blank");
    }
  };

  const handleBookmark = () => {
    if (onBookmark) {
      onBookmark(course.id);
    }
  };

  const isFeatured = variant === "featured";

  return (
    <Card
      className={cn(
        "h-full rounded-xl shadow-sm transition-all duration-300 cursor-pointer border border-gray-200 hover:shadow-xl hover:-translate-y-2 group",
        isFeatured &&
          "border-blue-200 bg-gradient-to-br from-blue-50/50 to-purple-50/30 shadow-md"
      )}
    >
      {/* Course Header with Python Icon */}
      <div
        className={cn(
          "h-48 flex items-center justify-center rounded-t-xl relative overflow-hidden",
          "bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600"
        )}
      >
        {/* Difficulty Badge */}
        <Badge
          variant="secondary"
          className={cn(
            "absolute top-4 left-4 font-medium text-xs shadow-sm border",
            getDifficultyColor(course.difficulty)
          )}
        >
          {course.difficulty}
        </Badge>

        {/* Price Badge */}
        {course.price && (
          <Badge
            variant="secondary"
            className={cn(
              "absolute top-4 right-4 font-medium text-xs shadow-sm border",
              getPriceColor(course.price)
            )}
          >
            {course.price}
          </Badge>
        )}

        {/* Language Logo */}
        <div className="text-center">
          {languageConfig?.icon ? (
            <img
              src={languageConfig.icon}
              alt={languageConfig.name}
              className="w-16 h-16 mx-auto mb-2 object-contain"
            />
          ) : (
            <div className="text-6xl mb-2">💻</div>
          )}
          <Typography variant="small" className="text-white/90 font-medium">
            {languageConfig?.name || course.language}
          </Typography>
        </div>

        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
      </div>

      <CardContent className="p-6 h-[calc(100%-12rem)] flex flex-col">
        {/* Course Title */}
        <Typography
          variant="h6"
          className="font-bold text-gray-800 mb-3 leading-tight text-lg line-clamp-2 group-hover:text-blue-600 transition-colors"
        >
          {course.title}
        </Typography>

        {/* Course Description */}
        <Typography
          variant="small"
          className="text-gray-600 mb-4 leading-relaxed line-clamp-3 flex-1"
        >
          {course.description}
        </Typography>

        {/* Course Stats */}
        <div className="flex items-center gap-4 mb-4 flex-wrap">
          {course.duration && (
            <div className="flex items-center gap-1">
              <TimeIcon className="h-4 w-4 text-gray-500" />
              <Typography variant="muted" className="text-xs font-medium">
                {course.duration}
              </Typography>
            </div>
          )}
          {course.rating && (
            <div className="flex items-center gap-1">
              <StarIcon className="h-4 w-4 text-yellow-500 fill-current" />
              <Typography variant="muted" className="text-xs font-medium">
                {course.rating}
              </Typography>
            </div>
          )}
          {course.students && (
            <div className="flex items-center gap-1">
              <PeopleIcon className="h-4 w-4 text-gray-500" />
              <Typography variant="muted" className="text-xs font-medium">
                {course.students}
              </Typography>
            </div>
          )}
        </div>

        {/* Course Content Stats */}
        <div className="flex items-center gap-3 mb-4 flex-wrap">
          {course.playgrounds && (
            <div className="flex items-center gap-1">
              <CodeIcon className="h-3 w-3 text-blue-500" />
              <Typography variant="muted" className="text-xs">
                {course.playgrounds} Playgrounds
              </Typography>
            </div>
          )}
          {course.challenges && (
            <div className="flex items-center gap-1">
              <ChallengeIcon className="h-3 w-3 text-orange-500" />
              <Typography variant="muted" className="text-xs">
                {course.challenges} Challenges
              </Typography>
            </div>
          )}
          {course.quizzes && (
            <div className="flex items-center gap-1">
              <QuizIcon className="h-3 w-3 text-green-500" />
              <Typography variant="muted" className="text-xs">
                {course.quizzes} Quizzes
              </Typography>
            </div>
          )}
          {course.illustrations && (
            <div className="flex items-center gap-1">
              <IllustrationIcon className="h-3 w-3 text-purple-500" />
              <Typography variant="muted" className="text-xs">
                {course.illustrations} Illustrations
              </Typography>
            </div>
          )}
        </div>

        {/* Tags */}
        {course.tags && course.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {course.tags.slice(0, 3).map((tag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="text-xs bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
              >
                {tag}
              </Badge>
            ))}
            {course.tags.length > 3 && (
              <Badge
                variant="secondary"
                className="text-xs bg-gray-100 text-gray-500"
              >
                +{course.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="mt-auto">
          <Button
            onClick={handleEnroll}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium text-sm rounded-lg py-3 hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg group"
          >
            <span className="flex items-center justify-center gap-2">
              View Course
              <ExternalLink className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
