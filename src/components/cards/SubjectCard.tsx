/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-19 10:44:54
 */
"use client";

import React from "react";
import { Typography } from "@/components/ui/typography";
import { cn } from "@/lib/utils";

interface SubjectCardProps {
  title: string;
  icon?: React.ReactNode;
  iconUrl?: string;
  description?: string;
  color?: string;
  className?: string;
  onClick?: () => void;
}

export default function SubjectCard({
  title,
  icon,
  iconUrl,
  description,
  color = "bg-gray-100",
  className,
  onClick,
}: SubjectCardProps) {
  return (
    <div
      className={cn(
        "group cursor-pointer rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-gray-300 hover:shadow-lg",
        className
      )}
      onClick={onClick}
    >
      {/* Icon Section - 左对齐，更小 */}
      <div className="mb-3 pt-2 pl-2 flex items-center justify-start">
        {iconUrl ? (
          <div
            className={cn(
              "flex h-8 w-8 items-center justify-center rounded-lg",
              color
            )}
          >
            <img src={iconUrl} alt={title} className="h-4 w-4" />
          </div>
        ) : icon ? (
          <div
            className={cn(
              "flex h-8 w-8 items-center justify-center rounded-lg",
              color
            )}
          >
            {icon}
          </div>
        ) : (
          <div
            className={cn(
              "flex h-8 w-8 items-center justify-center rounded-lg text-lg font-bold text-white",
              color
            )}
          >
            {title.charAt(0).toUpperCase()}
          </div>
        )}
      </div>

      {/* Content - 左对齐 */}
      <div className="text-left mt-6 pl-4">
        <Typography
          variant="h6"
          className="mb-1 font-semibold text-gray-900 group-hover:text-blue-600 transition-colors text-sm"
        >
          {title}
        </Typography>
        {description && (
          <Typography variant="small" className="text-gray-600 text-xs">
            {description}
          </Typography>
        )}
      </div>
    </div>
  );
}
