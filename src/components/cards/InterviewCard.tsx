import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Clock as TimeIcon,
  Users as PeopleIcon,
  Star as StarIcon,
  Play as PlayIcon,
  Bookmark as BookmarkIcon,
  Zap as ZapIcon,
  Crown as CrownIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { MockInterview, InterviewDifficulty } from "@/types/mock-interview";

interface InterviewCardProps {
  interview: MockInterview;
  variant?: "default" | "featured" | "compact";
  onStart?: (interviewId: string) => void;
  onBookmark?: (interviewId: string) => void;
  isBookmarked?: boolean;
  showCompany?: boolean;
}

export default function InterviewCard({
  interview,
  variant = "default",
  onStart,
  onBookmark,
  isBookmarked = false,
  showCompany = true,
}: InterviewCardProps) {
  const handleStart = () => {
    if (onStart) {
      onStart(interview.id);
    }
  };

  const handleBookmark = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onBookmark) {
      onBookmark(interview.id);
    }
  };

  const getDifficultyColor = (difficulty: InterviewDifficulty) => {
    switch (difficulty) {
      case "Easy":
        return "bg-green-100 text-green-800 border-green-200";
      case "Medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Hard":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "System Design":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "Coding Interview":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "MAANG+":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "Behavioral Interview":
        return "bg-teal-100 text-teal-800 border-teal-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getCompanyLogo = (company: string) => {
    // In a real app, you'd have actual company logos
    const logos: { [key: string]: string } = {
      Google: "🔍",
      Meta: "📘",
      Amazon: "📦",
      Apple: "🍎",
      Netflix: "🎬",
      Microsoft: "🪟",
      LinkedIn: "💼",
      Oracle: "🔮",
      Uber: "🚗",
      Stripe: "💳",
    };
    return logos[company] || "🏢";
  };

  if (variant === "compact") {
    return (
      <Card className="hover:shadow-md transition-shadow cursor-pointer group">
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                {interview.company && showCompany && (
                  <span className="text-lg">{getCompanyLogo(interview.company)}</span>
                )}
                <Typography variant="h6" className="font-semibold line-clamp-1">
                  {interview.title}
                </Typography>
                {interview.isFree && (
                  <Badge variant="secondary" className="text-xs">
                    Free
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                <div className="flex items-center gap-1">
                  <TimeIcon className="w-4 h-4" />
                  <span>{interview.duration} min</span>
                </div>
                <div className="flex items-center gap-1">
                  <StarIcon className="w-4 h-4 fill-current text-yellow-400" />
                  <span>{interview.averageRating}</span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={cn("text-xs", getDifficultyColor(interview.difficulty))}>
                  {interview.difficulty}
                </Badge>
                <Badge className={cn("text-xs", getCategoryColor(interview.category))}>
                  {interview.category}
                </Badge>
              </div>
            </div>
            <Button
              size="sm"
              onClick={handleStart}
              className="ml-4 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <PlayIcon className="w-4 h-4 mr-1" />
              Start
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "hover:shadow-lg transition-all duration-300 cursor-pointer group",
      variant === "featured" && "border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {interview.company && showCompany && (
                <div className="flex items-center gap-2">
                  <span className="text-xl">{getCompanyLogo(interview.company)}</span>
                  <Typography variant="small" className="text-gray-600 font-medium">
                    {interview.company}
                  </Typography>
                </div>
              )}
              {interview.isPopular && (
                <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                  <ZapIcon className="w-3 h-3 mr-1" />
                  Popular
                </Badge>
              )}
              {interview.isFree && (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Free
                </Badge>
              )}
              {variant === "featured" && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  <CrownIcon className="w-3 h-3 mr-1" />
                  Featured
                </Badge>
              )}
            </div>
            <CardTitle className="text-lg font-bold line-clamp-2 mb-2">
              {interview.title}
            </CardTitle>
            <CardDescription className="line-clamp-3 text-sm">
              {interview.description}
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBookmark}
            className={cn(
              "ml-2 opacity-0 group-hover:opacity-100 transition-opacity",
              isBookmarked && "opacity-100 text-blue-600"
            )}
          >
            <BookmarkIcon className={cn("w-4 h-4", isBookmarked && "fill-current")} />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
          <div className="flex items-center gap-1">
            <TimeIcon className="w-4 h-4" />
            <span>{interview.duration} min</span>
          </div>
          <div className="flex items-center gap-1">
            <PeopleIcon className="w-4 h-4" />
            <span>{interview.completionCount.toLocaleString()}</span>
          </div>
          <div className="flex items-center gap-1">
            <StarIcon className="w-4 h-4 fill-current text-yellow-400" />
            <span>{interview.averageRating}</span>
            <span className="text-gray-400">({interview.ratingCount})</span>
          </div>
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Badge className={cn("text-xs", getDifficultyColor(interview.difficulty))}>
              {interview.difficulty}
            </Badge>
            <Badge className={cn("text-xs", getCategoryColor(interview.category))}>
              {interview.category}
            </Badge>
          </div>
        </div>

        {interview.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {interview.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {interview.tags.length > 3 && (
              <Badge variant="outline" className="text-xs text-gray-500">
                +{interview.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}

        <Button
          onClick={handleStart}
          className="w-full group-hover:bg-blue-600 transition-colors"
          size="sm"
        >
          <PlayIcon className="w-4 h-4 mr-2" />
          Start Interview
        </Button>
      </CardContent>
    </Card>
  );
}
