import React from "react";
import { useTranslation } from "react-i18next";
import { Card, CardContent } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { cn } from "@/lib/utils";
import FluxIcon from "@/components/fluxIcon";
import DifficultyIcon from "@/components/icons/DifficultyIcon";

interface SimpleCourseCardProps {
  course: {
    id: string | number;
    title: string;
    description: string;
    duration: string;
    difficulty?: string;
    level?: string;
    bookmarked?: boolean;
  };
  className?: string;
  onClick?: () => void;
}

const SimpleCourseCard: React.FC<SimpleCourseCardProps> = ({
  course,
  className,
  onClick,
}) => {
  const { t } = useTranslation();

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border border-gray-200 h-68",
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-4 h-full flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <div
            className="flex items-center h-6 text-[#2b2a83] font-semibold text-[10px] leading-[0.75rem] rounded-full bg-[#eef] px-2 py-0"
            style={{
              fontFamily:
                '"Helvetica Neue", "SF Pro Display", Arial, Roboto, system-ui',
            }}
          >
            <FluxIcon name="course-icon" className="w-[14px] h-[14px] mr-1" />
            {t("common.types.course")}
          </div>
          <button className="text-gray-400 hover:text-gray-600 ">
            <FluxIcon
              name="bookmark"
              className="w-4 h-4"
              style={{
                fill: course.bookmarked ? "currentColor" : "none",
              }}
            />
          </button>
        </div>

        <Typography
          variant="small"
          className="font-semibold text-gray-800 mb-2 text-ms leading-5"
        >
          {course.title}
        </Typography>

        <span className=" text-sm line-clamp-6 flex-1 overflow-hidden min-h-[7.5rem]">
          {course.description}
        </span>

        <div className="flex items-center justify-between text-[10px] text-gray-500 mt-4">
          <span className="flex items-center mr-4">
            <FluxIcon name="clock-simple" className="w-3 h-3 mr-1" />
            {course.duration}
          </span>
          <span className="flex items-center">
            <DifficultyIcon
              difficulty={
                (
                  course.level ||
                  course.difficulty ||
                  "beginner"
                ).toLowerCase() as "beginner" | "intermediate" | "advanced"
              }
              className="mr-1 text-gray-500"
              size={12}
            />
            {course.difficulty || course.level}
          </span>
        </div>
      </CardContent>
    </Card>
  );
};

export default SimpleCourseCard;
