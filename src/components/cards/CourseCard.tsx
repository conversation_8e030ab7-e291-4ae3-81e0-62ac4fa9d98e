import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Clock as TimeIcon,
  Users as PeopleIcon,
  Star as StarIcon,
  Play as PlayIcon,
  Bookmark as BookmarkIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Course } from "@/types/catalog";

interface CourseCardProps {
  course: Course;
  variant?: "default" | "featured" | "compact";
  onEnroll?: (courseId: string) => void;
  languageConfig?: {
    name: string;
    icon: string;
    color: string;
  };
  showLanguage?: boolean;
}

export default function CourseCard({
  course,
  variant = "default",
  onEnroll,
  languageConfig,
  showLanguage = false,
}: CourseCardProps) {
  const handleEnroll = () => {
    if (onEnroll) {
      onEnroll(course.id);
    } else {
      window.open(course.url, "_blank");
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner":
        return "bg-green-100 text-green-800 border-green-200";
      case "Intermediate":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Advanced":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPriceColor = (price: string) => {
    return price === "Free"
      ? "bg-green-100 text-green-800 border-green-200"
      : "bg-blue-100 text-blue-800 border-blue-200";
  };

  if (variant === "compact") {
    return (
      <Card className="h-full rounded-lg shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-1 border border-gray-200 group">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            {showLanguage && languageConfig && (
              <div className="text-2xl flex-shrink-0">
                {languageConfig.icon}
              </div>
            )}
            <div className="flex-1 min-w-0">
              <Typography
                variant="h6"
                className="text-gray-800 mb-1 font-semibold line-clamp-2 group-hover:text-blue-600 transition-colors"
              >
                {course.title}
              </Typography>
              <Typography
                variant="small"
                className="text-gray-600 mb-2 line-clamp-2"
              >
                {course.description}
              </Typography>
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <TimeIcon className="h-3 w-3" />
                <span>{course.duration}</span>
                {course.rating && (
                  <>
                    <StarIcon className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span>{course.rating}</span>
                  </>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className={cn(
        "h-full rounded-xl shadow-sm transition-all duration-300 cursor-pointer border border-gray-200 hover:shadow-xl hover:-translate-y-2 group",
        variant === "featured" && "ring-2 ring-blue-500 ring-opacity-20"
      )}
    >
      {/* Course Header */}
      <div
        className={cn(
          "h-48 flex items-center justify-center rounded-t-xl relative overflow-hidden",
          languageConfig
            ? `bg-gradient-to-br ${languageConfig.color}`
            : "bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600"
        )}
      >
        {/* Difficulty Badge */}
        <Badge
          variant="secondary"
          className={cn(
            "absolute top-4 left-4 font-medium text-xs shadow-sm border",
            getDifficultyColor(course.difficulty)
          )}
        >
          {course.difficulty === "Beginner"
            ? "初级"
            : course.difficulty === "Intermediate"
            ? "中级"
            : "高级"}
        </Badge>

        {/* Price Badge */}
        {course.price && (
          <Badge
            variant="secondary"
            className={cn(
              "absolute top-4 right-4 font-medium text-xs shadow-sm border",
              getPriceColor(course.price)
            )}
          >
            {course.price === "Free" ? "免费" : "付费"}
          </Badge>
        )}

        {/* Language Logo */}
        <div className="text-center">
          {languageConfig?.icon ? (
            <img
              src={languageConfig.icon}
              alt={languageConfig.name}
              className="w-16 h-16 mx-auto mb-2 object-contain"
            />
          ) : (
            <div className="text-6xl mb-2">💻</div>
          )}
          <Typography variant="small" className="text-white/90 font-medium">
            {languageConfig?.name || course.language}
          </Typography>
        </div>

        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
      </div>

      <CardContent className="p-6 h-[calc(100%-12rem)] flex flex-col">
        {/* Course Title */}
        <Typography
          variant="h6"
          className="font-bold text-gray-800 mb-3 leading-tight text-lg line-clamp-2 group-hover:text-blue-600 transition-colors"
        >
          {course.title}
        </Typography>

        {/* Course Description */}
        <Typography
          variant="small"
          className="text-gray-600 mb-6 leading-relaxed line-clamp-3 flex-1"
        >
          {course.description}
        </Typography>

        {/* Course Stats */}
        <div className="flex items-center gap-4 mb-4 flex-wrap">
          {course.duration && (
            <div className="flex items-center gap-1">
              <TimeIcon className="h-4 w-4 text-gray-500" />
              <Typography variant="muted" className="text-xs font-medium">
                {course.duration}
              </Typography>
            </div>
          )}
          {course.rating && (
            <div className="flex items-center gap-1">
              <StarIcon className="h-4 w-4 text-yellow-500 fill-current" />
              <Typography variant="muted" className="text-xs font-medium">
                {course.rating}
              </Typography>
            </div>
          )}
          {course.students && (
            <div className="flex items-center gap-1">
              <PeopleIcon className="h-4 w-4 text-gray-500" />
              <Typography variant="muted" className="text-xs font-medium">
                {course.students}
              </Typography>
            </div>
          )}
        </div>

        {/* Tags */}
        {course.tags && course.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {course.tags.slice(0, 2).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}

        {/* Action Buttons */}
        <div className="mt-auto">
          <Button
            onClick={handleEnroll}
            className={`w-full ${
              languageConfig
                ? `bg-gradient-to-r ${languageConfig.color} text-white hover:shadow-lg`
                : "bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:shadow-lg"
            } font-medium text-sm rounded-lg py-3 transition-all duration-200 group`}
          >
            <span className="flex items-center justify-center gap-2">
              {course.price === "Free" ? (
                <>
                  <PlayIcon className="h-4 w-4" />
                  免费开始学习
                </>
              ) : (
                <>查看课程详情</>
              )}
            </span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
