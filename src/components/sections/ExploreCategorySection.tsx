/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-21 11:07:44
 */
import React from "react";
import Link from "next/link";
import FluxIcon from "@/components/fluxIcon";
import { Typography } from "@/components/ui/typography";
import { Container } from "@/components/ui/container";
import Carousel from "@/components/ui/carousel";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import { PointerData } from "@/mockdata/maindata";
import { getCourseUrl } from "@/mockdata/maindata";
import { useRouter } from "next/navigation";

interface CategoryData {
  id: string;
  title: string;
  description: string;
  href: string;
  courses: PointerData[];
}

interface ExploreCategorySectionProps {
  category: CategoryData;
}

const ExploreCategorySection: React.FC<ExploreCategorySectionProps> = ({
  category,
}) => {
  const router = useRouter();
  return (
    <section className="py-12  border-gray-100">
      <Container size="lg-plus">
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <FluxIcon
                name="most"
                width={20}
                height={20}
                className="text-blue-600"
              />
              <Typography variant="h4" className="font-bold text-gray-900">
                {category.title}
              </Typography>
            </div>
            <Link
              href={category.href}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Explore {category.title}
            </Link>
          </div>
          <Typography className="text-gray-600 text-xs">
            {category.description}
          </Typography>
        </div>

        <Carousel itemsPerSlide={4} showArrows={true} showIndicators={true}>
          {category.courses.map((pointer) => (
            <SimpleCourseCard
              key={pointer.id}
              course={{
                id: pointer.id,
                title: pointer.title,
                description: pointer.description,
                duration: `${Math.round(pointer.duration / 3600)} h`, // 转换秒为小时
                difficulty: pointer.difficulty as
                  | "beginner"
                  | "intermediate"
                  | "advanced",
                bookmarked: false,
              }}
              onClick={() => {
                const courseUrl = getCourseUrl(pointer);
                router.push(courseUrl);
              }}
            />
          ))}
        </Carousel>
      </Container>
    </section>
  );
};

export default ExploreCategorySection;
