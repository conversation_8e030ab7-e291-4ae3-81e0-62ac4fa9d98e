"use client";

import React, { useState } from "react";
import { Typography } from "@/components/ui/typography";
import { ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  isOpen?: boolean;
}

interface FAQSectionProps {
  title?: string;
  subtitle?: string;
  faqs: FAQItem[];
  className?: string;
}

export default function FAQSection({
  title = "常见问题",
  subtitle = "找到您关于编程学习的常见问题答案",
  faqs,
  className,
}: FAQSectionProps) {
  const [openItems, setOpenItems] = useState<Set<string>>(
    new Set(faqs.filter((faq) => faq.isOpen).map((faq) => faq.id))
  );

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  return (
    <div className={cn("w-full", className)}>
      <div className="w-full overflow-hidden px-10 py-12 rounded-md border border-solid border-gray-200 bg-white shadow dark:bg-gray-800 dark:border-gray-700">
        {/* Header */}
        <div className="text-center mb-10">
          <Typography
            variant="h2"
            className="text-gray-800 dark:text-gray-100 mb-4"
          >
            {title}
          </Typography>
          {subtitle && (
            <Typography
              variant="p"
              className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"
            >
              {subtitle}
            </Typography>
          )}
        </div>

        {/* Divider */}
        <div className="border-0 border-b border-solid border-gray-200 dark:border-gray-600 mb-6"></div>

        {/* FAQ Items */}
        <div className="flex w-full flex-col space-y-0">
          {faqs.map((faq, index) => {
            const isOpen = openItems.has(faq.id);
            const isLast = index === faqs.length - 1;

            return (
              <div key={faq.id} className="w-full">
                <div className="flex w-full flex-shrink-0 flex-col py-8">
                  {/* Question */}
                  <div
                    className="flex cursor-pointer justify-between rounded hover:bg-gray-50 dark:hover:bg-gray-700 p-2 -m-2 transition-colors"
                    onClick={() => toggleItem(faq.id)}
                  >
                    <div className="flex space-x-4">
                      <div className="flex-1">
                        <Typography
                          variant="h6"
                          className="mb-1 mt-0 text-gray-700 dark:text-gray-200 font-semibold"
                        >
                          {faq.question}
                        </Typography>
                      </div>
                    </div>
                    <div className="flex-shrink-0 ml-4">
                      {isOpen ? (
                        <ChevronUp className="h-6 w-6 text-gray-500 dark:text-gray-400" />
                      ) : (
                        <ChevronDown className="h-6 w-6 text-gray-500 dark:text-gray-400" />
                      )}
                    </div>
                  </div>

                  {/* Answer */}
                  <div
                    className={cn(
                      "flex w-full flex-col overflow-hidden transition-all duration-300 ease-out",
                      isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
                    )}
                  >
                    <div className="pt-3 pr-16">
                      <Typography
                        variant="p"
                        className="text-gray-600 dark:text-gray-300 leading-relaxed whitespace-pre-line"
                      >
                        {faq.answer}
                      </Typography>
                    </div>
                  </div>
                </div>

                {/* Divider */}
                {!isLast && (
                  <div className="border-0 border-b border-solid border-gray-200 dark:border-gray-600"></div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
