"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Typography } from "@/components/ui/typography";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CourseFilter } from "@/types/catalog";
import { Search, Filter, X } from "lucide-react";

interface CourseFiltersProps {
  filters: CourseFilter;
  categories: string[];
  onFiltersChange: (filters: Partial<CourseFilter>) => void;
  resultsCount: number;
  language?: string;
  className?: string;
}

const difficultyOptions = [
  { value: "所有级别", label: "所有级别" },
  { value: "Beginner", label: "初级" },
  { value: "Intermediate", label: "中级" },
  { value: "Advanced", label: "高级" },
];

const priceOptions = [
  { value: "所有价格", label: "所有价格" },
  { value: "Free", label: "免费" },
  { value: "Premium", label: "付费" },
];

const durationOptions = [
  { value: "所有时长", label: "所有时长" },
  { value: "短期 (< 10小时)", label: "短期 (< 10小时)" },
  { value: "中期 (10-20小时)", label: "中期 (10-20小时)" },
  { value: "长期 (> 20小时)", label: "长期 (> 20小时)" },
];

export default function CourseFilters({
  filters,
  categories,
  onFiltersChange,
  resultsCount,
  language,
  className,
}: CourseFiltersProps) {
  const handleSearchChange = (value: string) => {
    onFiltersChange({ searchTerm: value });
  };

  const handleCategoryChange = (value: string) => {
    onFiltersChange({ category: value });
  };

  const handleDifficultyChange = (value: string) => {
    onFiltersChange({ difficulty: value });
  };

  const handlePriceChange = (value: string) => {
    onFiltersChange({ price: value });
  };

  const handleDurationChange = (value: string) => {
    onFiltersChange({ duration: value });
  };

  const clearFilters = () => {
    onFiltersChange({
      searchTerm: "",
      category: "所有分类",
      difficulty: "所有级别",
      duration: "所有时长",
      price: "所有价格",
    });
  };

  const hasActiveFilters =
    filters.searchTerm ||
    (filters.category && filters.category !== "所有分类") ||
    (filters.difficulty && filters.difficulty !== "所有级别") ||
    (filters.duration && filters.duration !== "所有时长") ||
    (filters.price && filters.price !== "所有价格");

  return (
    <div className={`bg-white rounded-xl border border-gray-200 p-6 mb-8 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Filter className="h-5 w-5 text-gray-600" />
          <Typography variant="h6" className="text-gray-800 font-semibold">
            筛选课程
          </Typography>
          {language && (
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              {language}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-4">
          <Typography variant="small" className="text-gray-600">
            找到 <span className="font-semibold text-blue-600">{resultsCount}</span> 门课程
          </Typography>
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearFilters}
              className="text-gray-600 hover:text-gray-800"
            >
              <X className="h-4 w-4 mr-1" />
              清除筛选
            </Button>
          )}
        </div>
      </div>

      {/* Search Bar */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder="搜索课程、技术或关键词..."
          value={filters.searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="pl-10 h-12 text-base border-gray-300 focus:border-blue-500 focus:ring-blue-500"
        />
      </div>

      {/* Filter Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Category Filter */}
        <div>
          <Typography variant="small" className="text-gray-700 font-medium mb-2">
            分类
          </Typography>
          <Select value={filters.category} onValueChange={handleCategoryChange}>
            <SelectTrigger className="h-10">
              <SelectValue placeholder="选择分类" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Difficulty Filter */}
        <div>
          <Typography variant="small" className="text-gray-700 font-medium mb-2">
            难度级别
          </Typography>
          <Select value={filters.difficulty} onValueChange={handleDifficultyChange}>
            <SelectTrigger className="h-10">
              <SelectValue placeholder="选择难度" />
            </SelectTrigger>
            <SelectContent>
              {difficultyOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Price Filter */}
        <div>
          <Typography variant="small" className="text-gray-700 font-medium mb-2">
            价格
          </Typography>
          <Select value={filters.price} onValueChange={handlePriceChange}>
            <SelectTrigger className="h-10">
              <SelectValue placeholder="选择价格" />
            </SelectTrigger>
            <SelectContent>
              {priceOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Duration Filter */}
        <div>
          <Typography variant="small" className="text-gray-700 font-medium mb-2">
            课程时长
          </Typography>
          <Select value={filters.duration} onValueChange={handleDurationChange}>
            <SelectTrigger className="h-10">
              <SelectValue placeholder="选择时长" />
            </SelectTrigger>
            <SelectContent>
              {durationOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Active Filters */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <Typography variant="small" className="text-gray-600 mb-2">
            当前筛选条件：
          </Typography>
          <div className="flex flex-wrap gap-2">
            {filters.searchTerm && (
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                搜索: {filters.searchTerm}
              </Badge>
            )}
            {filters.category && filters.category !== "所有分类" && (
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                分类: {filters.category}
              </Badge>
            )}
            {filters.difficulty && filters.difficulty !== "所有级别" && (
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                难度: {difficultyOptions.find(opt => opt.value === filters.difficulty)?.label}
              </Badge>
            )}
            {filters.price && filters.price !== "所有价格" && (
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                价格: {priceOptions.find(opt => opt.value === filters.price)?.label}
              </Badge>
            )}
            {filters.duration && filters.duration !== "所有时长" && (
              <Badge variant="secondary" className="bg-indigo-100 text-indigo-800">
                时长: {durationOptions.find(opt => opt.value === filters.duration)?.label}
              </Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
