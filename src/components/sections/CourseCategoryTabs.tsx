import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Typography } from "@/components/ui/typography";
import { cn } from "@/lib/utils";

interface CourseCategoryTabsProps {
  categories: Array<{
    id: string;
    label: string;
    count: number;
    active: boolean;
  }>;
  onCategoryChange: (categoryId: string) => void;
}

export default function CourseCategoryTabs({
  categories,
  onCategoryChange,
}: CourseCategoryTabsProps) {
  return (
    <div className="mb-8">
      <div className="flex flex-wrap gap-2 justify-center">
        {categories.map((category) => (
          <Button
            key={category.id}
            variant={category.active ? "default" : "outline"}
            onClick={() => onCategoryChange(category.id)}
            className={cn(
              "flex items-center gap-2 rounded-full px-6 py-2 transition-all duration-200",
              category.active
                ? "bg-blue-500 text-white hover:bg-blue-600 shadow-md"
                : "bg-white text-gray-700 border-gray-200 hover:border-gray-300 hover:bg-gray-50"
            )}
          >
            <Typography
              variant="small"
              className={cn(
                "font-medium",
                category.active ? "text-white" : "text-gray-700"
              )}
            >
              {category.label}
            </Typography>

            <Badge
              variant="secondary"
              className={cn(
                "text-xs font-medium",
                category.active
                  ? "bg-blue-400 text-white"
                  : "bg-gray-100 text-gray-600"
              )}
            >
              {category.count}
            </Badge>
          </Button>
        ))}
      </div>
    </div>
  );
}
