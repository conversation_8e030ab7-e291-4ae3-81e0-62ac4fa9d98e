/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-08-22 17:41:18
 */
"use client";

import React from "react";
import { Typography } from "@/components/ui/typography";
import { Container } from "@/components/ui/container";
import { cn } from "@/lib/utils";
import { CategoryInfoSection as CategoryInfoSectionType } from "@/types/course";

interface CategoryInfoSectionProps {
  sections?: CategoryInfoSectionType[];
  className?: string;
  containerSize?: "sm" | "md" | "lg" | "lg-plus" | "xl";
}

const CategoryInfoSection: React.FC<CategoryInfoSectionProps> = ({
  sections,
  className = "",
  containerSize = "lg-plus",
}) => {
  // 如果没有传入sections，则不渲染组件
  if (!sections || sections.length === 0) {
    return null;
  }

  return (
    <div className={cn("", className)}>
      <Container size={containerSize}>
        <div className="bg-white rounded-lg border border-gray-200 p-16">
          {/* 主要内容区域 */}
          <div className="space-y-8">
            {sections.map((section, index) => (
              <div key={index} className="space-y-4">
                <Typography
                  variant="h4"
                  className="text-gray-800 font-semibold text-xl"
                >
                  {section.title}
                </Typography>

                {section.features &&
                  section.features.length > 0 &&
                  index === 0 && (
                    <ul className="list-disc list-inside space-y-2 text-gray-700">
                      {section.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="text-sm ml-4">
                          {feature}
                        </li>
                      ))}
                    </ul>
                  )}

                <Typography variant="p" className=" leading-relaxed text-sm">
                  {section.description}
                </Typography>

                {section.features &&
                  section.features.length > 0 &&
                  index > 0 && (
                    <ul className="space-y-3 text-gray-700">
                      {section.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="text-sm ml-4">
                          <span className="text-sm">
                            <strong>{feature.split(":")[0]}:</strong>
                            {feature.includes(":")
                              ? feature.split(":").slice(1).join(":")
                              : feature}
                          </span>
                        </li>
                      ))}
                    </ul>
                  )}
              </div>
            ))}

            {/* 结尾段落 */}
            <Typography variant="p" className="leading-relaxed text-sm italic">
              Experience a learning journey where every lesson is interactive,
              every project is exciting, and every challenge is a chance to
              level up your skills. Get ready to code, create, and conquer the
              world of programming—because your future in tech starts now!
            </Typography>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default CategoryInfoSection;
