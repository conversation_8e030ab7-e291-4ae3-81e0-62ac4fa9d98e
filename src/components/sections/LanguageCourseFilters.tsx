import React from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Typography } from "@/components/ui/typography";
import { Search as SearchIcon } from "lucide-react";
import { CourseFilter } from "@/types/catalog";

interface LanguageCourseFiltersProps {
  filters: CourseFilter;
  categories: string[];
  onFiltersChange: (filters: Partial<CourseFilter>) => void;
  resultsCount: number;
  language?: string;
}

export default function LanguageCourseFilters({
  filters,
  categories,
  onFiltersChange,
  resultsCount,
  language = "courses",
}: LanguageCourseFiltersProps) {
  return (
    <div className="mb-8">
      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative max-w-2xl mx-auto">
          <SearchIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            placeholder={`Search ${language} courses, topics, or skills...`}
            value={filters.searchTerm}
            onChange={(e) => onFiltersChange({ searchTerm: e.target.value })}
            className="pl-12 pr-4 py-3 rounded-xl border-gray-200 bg-white hover:border-gray-300 focus:border-blue-500 shadow-sm text-base"
          />
        </div>
      </div>

      {/* Filter Controls */}
      <div className="flex gap-4 flex-wrap items-center justify-center mb-6">
        <Typography variant="small" className="text-gray-600 font-medium mr-2">
          Filter by:
        </Typography>

        {/* Category Filter */}
        <Select
          value={filters.category}
          onValueChange={(value) => onFiltersChange({ category: value })}
        >
          <SelectTrigger className="w-48 rounded-lg border-gray-200 bg-white">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Difficulty Filter */}
        <Select
          value={filters.difficulty}
          onValueChange={(value) => onFiltersChange({ difficulty: value })}
        >
          <SelectTrigger className="w-40 rounded-lg border-gray-200 bg-white">
            <SelectValue placeholder="All Levels" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All Levels">All Levels</SelectItem>
            <SelectItem value="Beginner">Beginner</SelectItem>
            <SelectItem value="Intermediate">Intermediate</SelectItem>
            <SelectItem value="Advanced">Advanced</SelectItem>
          </SelectContent>
        </Select>

        {/* Price Filter */}
        <Select
          value={filters.price}
          onValueChange={(value) => onFiltersChange({ price: value })}
        >
          <SelectTrigger className="w-36 rounded-lg border-gray-200 bg-white">
            <SelectValue placeholder="All Prices" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All Prices">All Prices</SelectItem>
            <SelectItem value="Free">Free</SelectItem>
            <SelectItem value="Premium">Premium</SelectItem>
          </SelectContent>
        </Select>

        {/* Duration Filter */}
        <Select
          value={filters.duration}
          onValueChange={(value) => onFiltersChange({ duration: value })}
        >
          <SelectTrigger className="w-40 rounded-lg border-gray-200 bg-white">
            <SelectValue placeholder="All Durations" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All Durations">All Durations</SelectItem>
            <SelectItem value="Short">Short (&lt; 10 hrs)</SelectItem>
            <SelectItem value="Medium">Medium (10-30 hrs)</SelectItem>
            <SelectItem value="Long">Long (&gt; 30 hrs)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Results Count */}
      <div className="text-center">
        <Typography variant="p" className="text-gray-600">
          Showing{" "}
          <span className="font-semibold text-gray-800">{resultsCount}</span>{" "}
          {language} courses
        </Typography>
      </div>
    </div>
  );
}
