import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import {
  BookOpen,
  Route,
  Cloud,
  ClipboardCheck,
  FolderOpen,
} from "lucide-react";

interface CourseStatsProps {
  stats: {
    courses: number;
    paths: number;
    cloudLabs: number;
    assessments: number;
    projects: number;
  };
}

const statItems = [
  {
    key: "courses",
    label: "课程",
    icon: BookOpen,
    color: "bg-blue-500",
    description: "交互式课程",
  },
  {
    key: "paths",
    label: "学习路径",
    icon: Route,
    color: "bg-green-500",
    description: "结构化学习路径",
  },
  {
    key: "cloudLabs",
    label: "云实验室",
    icon: Cloud,
    color: "bg-purple-500",
    description: "实践实验室",
  },
  {
    key: "assessments",
    label: "评估",
    icon: ClipboardCheck,
    color: "bg-orange-500",
    description: "技能评估",
  },
  {
    key: "projects",
    label: "项目",
    icon: FolderOpen,
    color: "bg-red-500",
    description: "真实项目",
  },
] as const;

export default function CourseStatsSection({ stats }: CourseStatsProps) {
  return (
    <div className="mb-12">
      <div className="text-center mb-8">
        <Typography variant="h3" className="text-gray-800 mb-4">
          Explore Everything Python
        </Typography>
        <Typography variant="p" className="text-gray-600 max-w-2xl mx-auto">
          Discover the power of Python with comprehensive learning resources
          designed to take you from beginner to expert
        </Typography>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {statItems.map((item) => {
          const IconComponent = item.icon;
          const count = stats[item.key];

          return (
            <Card
              key={item.key}
              className="hover:shadow-lg transition-all duration-200 cursor-pointer border border-gray-200 hover:border-gray-300"
            >
              <CardContent className="p-6 text-center">
                <div
                  className={`w-12 h-12 ${item.color} rounded-lg flex items-center justify-center mx-auto mb-4`}
                >
                  <IconComponent className="h-6 w-6 text-white" />
                </div>

                <Typography variant="h4" className="text-gray-800 mb-1">
                  {count}
                </Typography>

                <Typography
                  variant="small"
                  className="text-gray-600 font-medium mb-1"
                >
                  {item.label}
                </Typography>

                <Typography variant="muted" className="text-xs text-gray-500">
                  {item.description}
                </Typography>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
