"use client";

import React, { useEffect, useId, useRef, useState } from "react";
import type { AtomicContent } from "@/types/openapi";
import mermaid from "mermaid";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import rehypeHighlight from "rehype-highlight";
import ContentRenderer from "@/components/ui/content-renderer";

// 初始化 mermaid
mermaid.initialize({ startOnLoad: false, securityLevel: "strict" });

function MarkdownText({
  children,
  inline = false,
  className,
}: {
  children: string;
  inline?: boolean;
  className?: string;
}) {
  const content = (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      rehypePlugins={[rehypeRaw as any, rehypeHighlight as any]}
      components={
        inline ? ({ p: (props: any) => <span {...props} /> } as any) : undefined
      }
    >
      {children || ""}
    </ReactMarkdown>
  );
  return inline ? (
    <span className={className}>{content}</span>
  ) : (
    <div className={className}>{content}</div>
  );
}

function MermaidBlock({ definition }: { definition: string }) {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [error, setError] = useState<string | null>(null);
  const uniqueId = useId().replace(/:/g, "-");

  useEffect(() => {
    let mounted = true;
    async function render() {
      setError(null);
      try {
        const { svg } = await mermaid.render(`mermaid-${uniqueId}`, definition);
        if (mounted && containerRef.current) {
          containerRef.current.innerHTML = svg;
        }
      } catch (e: any) {
        console.error("Mermaid render error:", e);
        setError(e?.message || "渲染失败");
      }
    }
    if (definition?.trim()) render();
    return () => {
      mounted = false;
    };
  }, [definition, uniqueId]);

  if (error) {
    return (
      <div className="rounded-md border p-3 bg-red-50 text-red-700 text-sm">
        流程图渲染失败：{error}
      </div>
    );
  }

  return <div ref={containerRef} className="overflow-auto" />;
}

function CodeBlock({ language, code }: { language?: string; code: string }) {
  return (
    <pre className="rounded-md bg-slate-900 text-slate-100 p-3 overflow-auto text-sm">
      <code>{code}</code>
    </pre>
  );
}

function MCQBlock(props: {
  question: string;
  options: string[];
  answerIndex: number | number[];
}) {
  const { question, options, answerIndex } = props;
  const [picked, setPicked] = useState<number[]>([]);
  const isMulti = Array.isArray(answerIndex);
  const check = () => {
    const right = isMulti
      ? (answerIndex as number[]).slice().sort().join(",") ===
        picked.slice().sort().join(",")
      : picked[0] === (answerIndex as number);
    alert(right ? "回答正确" : "再想想看");
  };
  return (
    <div className="rounded-md border p-3">
      <div className="font-medium mb-2">
        <MarkdownText inline>{question}</MarkdownText>
      </div>
      <div className="space-y-2">
        {options.map((opt, i) => (
          <label key={i} className="flex items-center gap-2">
            <input
              type={isMulti ? "checkbox" : "radio"}
              name={`q-${question}`}
              checked={picked.includes(i)}
              onChange={(e) => {
                if (isMulti) {
                  setPicked((p) =>
                    e.target.checked ? [...p, i] : p.filter((x) => x !== i)
                  );
                } else {
                  setPicked([i]);
                }
              }}
            />
            <span>
              <MarkdownText inline>{opt}</MarkdownText>
            </span>
          </label>
        ))}
      </div>
      <Button size="sm" className="mt-3" onClick={check}>
        检查
      </Button>
    </div>
  );
}

function FillBlankBlock({
  question,
  answers,
}: {
  question: string;
  answers: string[];
}) {
  const [text, setText] = useState("");
  const check = () => {
    const right = answers?.some(
      (a) => a?.toLowerCase?.() === text.trim().toLowerCase()
    );
    alert(right ? "回答正确" : "不完全正确");
  };
  return (
    <div className="rounded-md border p-3">
      <div className="font-medium mb-2">
        <MarkdownText inline>{question}</MarkdownText>
      </div>
      <input
        className="border rounded px-2 py-1 w-full"
        value={text}
        onChange={(e) => setText(e.target.value)}
      />
      <Button size="sm" className="mt-3" onClick={check}>
        检查
      </Button>
    </div>
  );
}

// 使用 KaTeX 渲染公式
import { BlockMath } from "react-katex";
import { CodeExecInline } from "@/feature/playground";

export function AtomicContentRenderer({ block }: { block: AtomicContent }) {
  switch (block.type) {
    case "text_explanation": {
      const md = (block.data?.markdown ??
        block.data?.body ??
        block.data?.text ??
        block.data?.content ??
        "") as string;
      return (
        <div className="leading-7">
          <ContentRenderer type={"markdown"} mdContent={md} className="mb-10" />
        </div>
      );
    }
    case "code_snippet": {
      const lang = (block.data?.language ?? "javascript") as string;
      const code = (block.data?.code ?? block.data?.snippet ?? "") as string;
      // 使用可执行的内联组件渲染代码，并默认展示输出
      return (
        <div className="my-3">
          <CodeExecInline
            language={lang}
            code={code}
            height="320px"
            theme="dark"
          />
        </div>
      );
    }
    case "flowchart_description": {
      const mermaidDef = (block.data?.mermaid ??
        block.data?.diagram_syntax ??
        block.data?.description ??
        block.data?.text ??
        "") as string;
      if (!mermaidDef?.trim()) {
        return <div className="text-gray-500 text-sm">暂无流程图内容</div>;
      }
      return (
        <div className="rounded-md border p-3 bg-white">
          <MermaidBlock definition={mermaidDef} />
        </div>
      );
    }
    case "interactive_quiz": {
      // 后端可能返回的交互题，若为单选/多选，映射到 MCQ
      const options = (block.data?.options ?? []) as string[];
      const single = Boolean(
        block.data?.single_answer ?? block.data?.is_single
      );
      const idx = (block.data?.correct_answer_index ??
        block.data?.answer_index ??
        -1) as number;
      const indices = (block.data?.answer_indices ?? []) as number[];
      const answerIndex = single ? idx : indices.length ? indices : [idx];
      const question = (block.data?.question ?? "请选择正确答案") as string;
      return (
        <MCQBlock
          question={question}
          options={options}
          answerIndex={answerIndex as any}
        />
      );
    }
    case "multiple_choice_quiz": {
      const question = (block.data?.question ?? "请选择正确答案") as string;
      const options = (block.data?.options ?? []) as string[];
      const answerIndex = (block.data?.answer_index ??
        block.data?.answer_indices ??
        -1) as number | number[];
      return (
        <MCQBlock
          question={question}
          options={options}
          answerIndex={answerIndex}
        />
      );
    }
    case "fill_in_blank_quiz": {
      const question = (block.data?.question ?? "填写空白") as string;
      const answers = (block.data?.answers ?? []) as string[];
      return <FillBlankBlock question={question} answers={answers} />;
    }
    case "practice_exercise": {
      const prompt = (block.data?.prompt ??
        block.data?.task ??
        "练习") as string;
      const hints = (block.data?.hints ?? []) as string[];
      return (
        <div className="rounded-md border p-3 bg-white">
          <div className="font-medium">
            <MarkdownText>{prompt}</MarkdownText>
          </div>
          {hints?.length > 0 && (
            <ul className="list-disc pl-5 mt-2 text-sm text-gray-600">
              {hints.map((h, i) => (
                <li key={i}>
                  <MarkdownText inline>{h}</MarkdownText>
                </li>
              ))}
            </ul>
          )}
        </div>
      );
    }
    case "math_formula": {
      const latex = (block.data?.latex ?? block.data?.formula ?? "") as string;
      if (!latex) return <div className="text-gray-500 text-sm">暂无公式</div>;
      return (
        <div className="rounded-md border p-3 bg-slate-50 overflow-auto">
          <BlockMath>{latex}</BlockMath>
        </div>
      );
    }
    default:
      return (
        <div className="rounded-md border p-3">
          <div className="text-xs text-gray-500 mb-1">未支持的内容类型</div>
          <pre className="text-xs whitespace-pre-wrap">
            {JSON.stringify(block.data, null, 2)}
          </pre>
        </div>
      );
  }
}

export default AtomicContentRenderer;
