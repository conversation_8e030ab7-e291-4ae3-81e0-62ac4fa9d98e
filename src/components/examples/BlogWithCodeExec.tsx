/*
 * @Description: 博客文章中嵌入CodeExec组件的示例
 * @Author: Devin
 * @Date: 2025-08-07
 */
"use client";

import React from "react";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import { Card, CardContent } from "@/components/ui/card";
import { CodeExecInline } from "@/feature/playground";

export const BlogWithCodeExec: React.FC = () => {
  return (
    <Container size="lg" className="py-8">
      <article className="prose prose-lg max-w-none">
        {/* 文章标题 */}
        <Typography variant="h1" className="font-bold text-gray-900 mb-4">
          JavaScript 基础教程：函数与闭包
        </Typography>

        <Typography variant="h6" className="text-gray-600 mb-8">
          通过实际代码示例学习 JavaScript 中的函数定义和闭包概念
        </Typography>

        {/* 文章内容 */}
        <div className="space-y-8">
          <section>
            <Typography
              variant="h2"
              className="font-semibold text-gray-800 mb-4"
            >
              1. 函数基础
            </Typography>

            <Typography variant="p" className="text-gray-700 mb-4">
              在 JavaScript
              中，函数是一等公民，可以作为值传递、赋值给变量、作为参数传递等。
              让我们从最基本的函数定义开始：
            </Typography>

            <CodeExecInline
              language="javascript"
              code={`// 函数声明
function greet(name) {
  return "Hello, " + name + "!";
}

// 函数表达式
const greetArrow = (name) => {
  return \`Hello, \${name}!\`;
};

// 调用函数
console.log(greet("Alice"));
console.log(greetArrow("Bob"));

// 函数作为值
const sayHello = greet;
console.log(sayHello("Charlie"));`}
              height="300px"
              theme="dark"
            />
          </section>

          <section>
            <Typography
              variant="h2"
              className="font-semibold text-gray-800 mb-4"
            >
              2. 高阶函数
            </Typography>

            <Typography variant="p" className="text-gray-700 mb-4">
              高阶函数是接受函数作为参数或返回函数的函数。这是函数式编程的重要概念：
            </Typography>

            <CodeExecInline
              language="javascript"
              code={`// 高阶函数示例
function createMultiplier(factor) {
  return function(number) {
    return number * factor;
  };
}

// 创建不同的乘法器
const double = createMultiplier(2);
const triple = createMultiplier(3);

console.log("double(5):", double(5));
console.log("triple(4):", triple(4));

// 数组方法中的高阶函数
const numbers = [1, 2, 3, 4, 5];

const doubled = numbers.map(x => x * 2);
const evens = numbers.filter(x => x % 2 === 0);
const sum = numbers.reduce((acc, x) => acc + x, 0);

console.log("原数组:", numbers);
console.log("翻倍:", doubled);
console.log("偶数:", evens);
console.log("求和:", sum);`}
              height="350px"
              theme="dark"
            />
          </section>

          <section>
            <Typography
              variant="h2"
              className="font-semibold text-gray-800 mb-4"
            >
              3. 闭包（Closure）
            </Typography>

            <Typography variant="p" className="text-gray-700 mb-4">
              闭包是 JavaScript
              中最重要的概念之一。它允许内部函数访问外部函数的变量，
              即使外部函数已经执行完毕：
            </Typography>

            <CodeExecInline
              language="javascript"
              code={`// 闭包示例 1：计数器
function createCounter() {
  let count = 0;
  
  return function() {
    count++;
    return count;
  };
}

const counter1 = createCounter();
const counter2 = createCounter();

console.log("counter1:", counter1()); // 1
console.log("counter1:", counter1()); // 2
console.log("counter2:", counter2()); // 1
console.log("counter1:", counter1()); // 3

// 闭包示例 2：私有变量
function createBankAccount(initialBalance) {
  let balance = initialBalance;
  
  return {
    deposit: function(amount) {
      balance += amount;
      return balance;
    },
    withdraw: function(amount) {
      if (amount <= balance) {
        balance -= amount;
        return balance;
      } else {
        return "余额不足";
      }
    },
    getBalance: function() {
      return balance;
    }
  };
}

const account = createBankAccount(100);
console.log("初始余额:", account.getBalance());
console.log("存入50:", account.deposit(50));
console.log("取出30:", account.withdraw(30));
console.log("取出200:", account.withdraw(200));`}
              height="400px"
              theme="dark"
            />
          </section>

          <section>
            <Typography
              variant="h2"
              className="font-semibold text-gray-800 mb-4"
            >
              4. 实际应用：防抖函数
            </Typography>

            <Typography variant="p" className="text-gray-700 mb-4">
              让我们用闭包来实现一个实用的防抖函数，这在处理用户输入时非常有用：
            </Typography>

            <CodeExecInline
              language="javascript"
              code={`// 防抖函数实现
function debounce(func, delay) {
  let timeoutId;
  
  return function(...args) {
    // 清除之前的定时器
    clearTimeout(timeoutId);
    
    // 设置新的定时器
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
}

// 模拟搜索函数
function search(query) {
  console.log(\`搜索: "\${query}"\`);
}

// 创建防抖版本的搜索函数
const debouncedSearch = debounce(search, 300);

// 模拟快速输入
console.log("模拟用户快速输入...");
debouncedSearch("a");
debouncedSearch("ap");
debouncedSearch("app");
debouncedSearch("appl");
debouncedSearch("apple");

// 只有最后一次调用会在300ms后执行
setTimeout(() => {
  console.log("300ms后，只执行了最后一次搜索");
}, 350);`}
              height="350px"
              theme="dark"
            />
          </section>

          <section>
            <Typography
              variant="h2"
              className="font-semibold text-gray-800 mb-4"
            >
              总结
            </Typography>

            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-6">
                <Typography variant="p" className="text-gray-700">
                  通过这些示例，我们学习了：
                </Typography>
                <ul className="list-disc list-inside mt-4 space-y-2 text-gray-700">
                  <li>函数的不同定义方式和使用场景</li>
                  <li>高阶函数的概念和实际应用</li>
                  <li>闭包的工作原理和强大功能</li>
                  <li>如何使用闭包实现私有变量和实用工具函数</li>
                </ul>
                <Typography variant="p" className="text-gray-700 mt-4">
                  掌握这些概念对于编写高质量的 JavaScript 代码至关重要。
                  继续练习和探索，你会发现更多有趣的应用场景！
                </Typography>
              </CardContent>
            </Card>
          </section>
        </div>
      </article>
    </Container>
  );
};
