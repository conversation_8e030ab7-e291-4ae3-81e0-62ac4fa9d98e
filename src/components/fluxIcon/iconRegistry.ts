/*
 * @Description: FlowIcon 动态图标系统 - 兼容性导出
 * @Author: Devin
 * @Date: 2025-08-06 15:04:40
 */
import { FluxIconConfig } from "./types";

// 空的图标映射 - 现在使用动态导入，不需要预注册
export const iconMap: Record<string, React.ComponentType<any>> = {};
export const iconRegistry: FluxIconConfig[] = [];

// 兼容性函数
export function getAvailableIcons(): string[] {
  return [];
}

export function getIconsByCategory(_category: string): FluxIconConfig[] {
  return [];
}

export function getCategories(): string[] {
  return ["icon"];
}
