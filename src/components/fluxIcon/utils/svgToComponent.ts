/**
 * 将SVG字符串转换为React组件
 * @param svgContent SVG内容字符串
 * @param iconName 图标名称
 * @returns React组件代码字符串
 */
export function svgToComponent(svgContent: string, iconName: string): string {
  // 清理SVG内容
  const cleanedSvg = cleanSvgContent(svgContent);

  // 提取SVG属性
  const svgAttributes = extractSvgAttributes(cleanedSvg);

  // 提取SVG内部内容
  const svgInnerContent = extractSvgInnerContent(cleanedSvg);

  // 生成组件名称
  const componentName =
    iconName
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join("") + "Icon";

  // 生成React组件代码
  const componentCode = `import React from 'react';
import { IconProps } from '../types';

const ${componentName}: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = 'currentColor',
  fill = '${svgAttributes.fill || "none"}',
  stroke,
  strokeWidth = ${svgAttributes.strokeWidth || "2"},
  className,
  style,
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="${svgAttributes.viewBox || "0 0 24 24"}"
      fill={fill}
      stroke={stroke || color}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      style={style}
      {...props}
    >
${svgInnerContent}
    </svg>
  );
};

export default ${componentName};`;

  return componentCode;
}

/**
 * 清理SVG内容，移除不必要的属性和空白
 */
function cleanSvgContent(svgContent: string): string {
  return svgContent.replace(/\s+/g, " ").replace(/>\s+</g, "><").trim();
}

/**
 * 提取SVG标签的属性
 */
function extractSvgAttributes(svgContent: string): {
  viewBox?: string;
  fill?: string;
  stroke?: string;
  strokeWidth?: string;
} {
  const svgTagMatch = svgContent.match(/<svg[^>]*>/);
  if (!svgTagMatch) return {};

  const svgTag = svgTagMatch[0];

  const viewBoxMatch = svgTag.match(/viewBox="([^"]*)"/);
  const fillMatch = svgTag.match(/fill="([^"]*)"/);
  const strokeMatch = svgTag.match(/stroke="([^"]*)"/);
  const strokeWidthMatch =
    svgTag.match(/stroke-width="([^"]*)"/) ||
    svgTag.match(/strokeWidth="([^"]*)"/);

  return {
    viewBox: viewBoxMatch?.[1],
    fill: fillMatch?.[1],
    stroke: strokeMatch?.[1],
    strokeWidth: strokeWidthMatch?.[1],
  };
}

/**
 * 提取SVG内部内容（去除svg标签）
 */
function extractSvgInnerContent(svgContent: string): string {
  const match = svgContent.match(/<svg[^>]*>([\s\S]*)<\/svg>/);
  if (!match) return "";

  const innerContent = match[1].trim();

  // 格式化内部内容，添加适当的缩进
  return innerContent
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line.length > 0)
    .map((line) => `      ${line}`)
    .join("\n");
}

/**
 * 从SVG文件内容创建图标组件文件
 * @param svgContent SVG文件内容
 * @param iconName 图标名称
 * @param outputPath 输出文件路径
 */
export async function createIconComponentFromSvg(
  svgContent: string,
  iconName: string,
  outputPath?: string
): Promise<string> {
  const componentCode = svgToComponent(svgContent, iconName);

  if (outputPath) {
    // 如果提供了输出路径，可以在这里写入文件
    // 这里只返回代码，实际写入由调用者处理
  }

  return componentCode;
}

/**
 * 批量转换SVG到组件
 */
export function batchSvgToComponents(
  svgFiles: { name: string; content: string }[]
): { name: string; code: string }[] {
  return svgFiles.map(({ name, content }) => ({
    name,
    code: svgToComponent(content, name),
  }));
}
