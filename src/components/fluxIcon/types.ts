export interface IconProps {
  /** 图标宽度 */
  width?: number | string;
  /** 图标高度 */
  height?: number | string;
  /** 图标颜色 */
  color?: string;
  /** 填充颜色 */
  fill?: string;
  /** 描边颜色 */
  stroke?: string;
  /** 描边宽度 */
  strokeWidth?: number | string;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 其他SVG属性 */
  [key: string]: any;
}

export type IconName = string;

export interface FluxIconConfig {
  /** 图标名称 */
  name: IconName;
  /** 图标组件 */
  component: React.ComponentType<IconProps>;
  /** 图标描述 */
  description?: string;
  /** 图标分类 */
  category?: string;
}
