"use client";

/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-08-06 15:03:45
 */
import React from "react";
import { cn } from "@/lib/utils";
export interface FluxIconProps {
  /** 图标名称 */
  name: string;
  /** 图标大小，可以是数字(px)或字符串 */
  size?: number | string;
  /** 图标颜色 */
  color?: string;
  /** 填充颜色 */
  fill?: string;
  /** 描边颜色 */
  stroke?: string;
  /** 描边宽度 */
  strokeWidth?: number | string;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 其他SVG属性 */
  [key: string]: any;
}

const FluxIcon: React.FC<FluxIconProps> = ({
  name,
  width = 24,
  height = 24,
  color,
  fill,
  stroke,
  strokeWidth,
  className,
  style,
  ...props
}) => {
  // 如果没有明确指定 fill 或 stroke，则使用 color 作为默认值
  const finalFill = fill !== undefined ? fill : color;
  const finalStroke = stroke !== undefined ? stroke : color;

  // 动态导入SVG组件
  const [SvgComponent, setSvgComponent] =
    React.useState<React.ComponentType<any> | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(false);

  React.useEffect(() => {
    let isMounted = true;

    const loadSvgComponent = async () => {
      try {
        setLoading(true);
        setError(false);

        // 动态导入SVG文件作为React组件
        const svgModule = await import(`./svgs/${name}.svg`);
        const Component = svgModule.default;

        if (isMounted) {
          // 创建一个包装组件来处理属性继承和覆盖
          const WrappedComponent: React.FC<any> = (props) => {
            // 构建动态属性对象
            const svgProps: any = {
              width,
              height,
              className: cn("flux-icon", className),
              style,
              ...props,
            };

            // 只在有值时才添加 fill
            if (finalFill) {
              svgProps.fill = finalFill;
            }

            // 只在有值时才添加 stroke 相关属性
            if (finalStroke) {
              svgProps.stroke = finalStroke;
            }
            if (strokeWidth) {
              svgProps.strokeWidth = strokeWidth;
            }

            return <Component {...svgProps} />;
          };

          setSvgComponent(() => WrappedComponent);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          console.warn(`FluxIcon: Failed to load icon "${name}"`, err);
          setError(true);
          setLoading(false);
        }
      }
    };

    loadSvgComponent();

    return () => {
      isMounted = false;
    };
  }, [name]);

  // 加载中状态
  if (loading) {
    return (
      <div
        className={cn("flux-icon flux-icon-loading", className)}
        style={{
          width: width,
          height: height,
          display: "inline-block",
          ...style,
        }}
        {...props}
      />
    );
  }

  // 错误状态或组件未找到 - 显示默认图标
  if (error || !SvgComponent) {
    return (
      <svg
        width={width}
        height={height}
        viewBox="0 0 24 24"
        fill="none"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={cn("flux-icon Flux-icon-error", className)}
        style={style}
        {...props}
      >
        <circle cx="12" cy="12" r="10" />
        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
        <circle cx="12" cy="17" r="1" />
      </svg>
    );
  }

  // 渲染SVG组件
  return <SvgComponent {...props} />;
};

export default FluxIcon;
