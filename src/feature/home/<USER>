/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-18 17:05:00
 */
"use client";

// components/PracticeSection.tsx
import Image from "next/image";
import React from "react";
import { useTranslation } from "react-i18next";
import { Container } from "@/components/ui/container";
import { useRouter } from "next/navigation";
import FluxIcon from "@/components/fluxIcon";

const PracticeSection: React.FC = () => {
  const { t } = useTranslation();
  const router = useRouter();

  return (
    <div
      className="flex py-16 items-cente justify-center"
      style={{ backgroundColor: "#f8f8ff" }}
    >
      <Container size="lg" className="w-full">
        {/* Header Section */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            {t("home.practice.title")}
          </h2>
        </div>

        {/* Section 1 - Cloud Labs */}
        <div className="w-full flex flex-col lg:flex-row items-center ">
          {/* Text - Left */}
          <div className="flex-1">
            <p className="uppercase text-xs tracking-widest mb-2">
              {t("home.practice.cloudLabs.category")}
            </p>
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">
              <span className="text-indigo-600">
                {t("home.practice.cloudLabs.titleHighlight")}
              </span>{" "}
              {t("home.practice.cloudLabs.title")}
            </h3>
            <ul className="text-gray-600 space-y-2 mb-4 text-sm">
              {(
                t("home.practice.cloudLabs.features", {
                  returnObjects: true,
                }) as string[]
              ).map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
            <div className="flex items-center gap-4 mb-4">
              <FluxIcon name="aws" width={32} height={32} />
              <FluxIcon name="google" width={32} height={32} />
              <FluxIcon name="meta" width={32} height={32} />
            </div>
            <button
              className="mt-2 bg-indigo-600 text-white px-6 py-2 rounded-md text-sm font-semibold hover:bg-indigo-700 transition"
              onClick={() => router.push("/explore")}
            >
              {t("home.practice.cloudLabs.buttonText")}
            </button>
          </div>

          {/* Image - Right */}
          <div className="flex-1 relative w-full  lg:h-[400px]">
            <Image
              src="/images/home/<USER>"
              alt="Cloud Labs"
              fill
              className="object-contain"
            />
          </div>
        </div>

        {/* Section 2 - Projects */}
        <div className="w-full flex flex-col lg:flex-row items-center gap-16">
          {/* Image - Left */}
          <div className="flex-1 relative w-full h-[500px] lg:h-[600px]">
            <Image
              src="/images/home/<USER>"
              alt="Projects"
              fill
              className="object-contain"
            />
          </div>

          {/* Text - Right */}
          <div className="flex-1">
            <p className="uppercase text-sm text-gray-500 tracking-widest mb-2">
              {t("home.practice.handsonProjects.category")}
            </p>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              <span className="text-indigo-600">
                {t("home.practice.handsonProjects.titleHighlight")}
              </span>{" "}
              {t("home.practice.handsonProjects.title")}
            </h3>
            <ul className="text-gray-600 space-y-2 mb-4 text-sm">
              {(
                t("home.practice.handsonProjects.features", {
                  returnObjects: true,
                }) as string[]
              ).map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
            <button
              className="mt-2 bg-indigo-600 text-white px-6 py-2 rounded-md text-sm font-semibold hover:bg-indigo-700 transition"
              onClick={() => router.push("/explore")}
            >
              {t("home.practice.handsonProjects.buttonText")}
            </button>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default PracticeSection;
