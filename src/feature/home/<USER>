"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp, ArrowRight } from "lucide-react";
import { Container } from "@/components/ui/container";

const AILearning = () => {
  const { t } = useTranslation();
  const [activeIndex, setActiveIndex] = useState(0);

  const aiFeatures = t("home.aiLearning.features", {
    returnObjects: true,
  }) as Array<{
    title: string;
    description: string;
    buttonText: string;
    buttonVariant: string;
  }>;

  // 添加视频URL到特性数据
  const featuresWithVideo = aiFeatures.map((feature, index) => ({
    ...feature,
    videoUrl: [
      "https://www.educative.io/static/imgs/Personalized-recommendations-v2.webm",
      "https://www.educative.io/static/imgs/Mock-interviews-v2.webm",
      "https://www.educative.io/static/imgs/AI-prompt-v2.webm",
      "https://www.educative.io/static/imgs/Code-feedback-v2.webm",
      "https://www.educative.io/static/imgs/Explain-with-AI-v2.webm",
      "https://www.educative.io/static/imgs/AI-code-mentor-v2.webm",
    ][index],
  }));
  return (
    <section className="py-20 bg-gray-50">
      <Container size="lg-plus" className="mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t("home.aiLearning.title")}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t("home.aiLearning.subtitle")}
          </p>
        </div>

        {/* AI Features Accordion */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {/* Left Side - Accordion */}
          <div className="space-y-4">
            {featuresWithVideo.map((feature, index) => (
              <div
                key={index}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                <button
                  onClick={() =>
                    setActiveIndex(activeIndex === index ? -1 : index)
                  }
                  className={`w-full px-6 py-4 text-left flex items-center justify-between transition-colors ${
                    activeIndex === index
                      ? "bg-blue-600 text-white"
                      : "bg-white text-gray-900 hover:bg-gray-50"
                  }`}
                >
                  <span className="font-semibold text-lg">{feature.title}</span>
                  {activeIndex === index ? (
                    <ChevronUp className="w-5 h-5" />
                  ) : (
                    <ChevronDown className="w-5 h-5" />
                  )}
                </button>

                {activeIndex === index && (
                  <div className="px-6 py-4 bg-white border-t border-gray-200">
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {feature.description}
                    </p>
                    <Button
                      className={
                        feature.buttonVariant === "primary"
                          ? "bg-blue-600 hover:bg-blue-700 text-white"
                          : "border border-blue-600 text-blue-600 hover:bg-blue-50 bg-white"
                      }
                    >
                      {feature.buttonText}
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Right Side - Video */}
          <div className="flex items-center justify-center">
            <div className="w-full max-w-lg">
              <video
                key={activeIndex}
                autoPlay
                muted
                loop
                className="w-full h-auto rounded-lg shadow-lg"
                poster="/api/placeholder/600/400"
              >
                <source
                  src={
                    featuresWithVideo[activeIndex >= 0 ? activeIndex : 0]
                      ?.videoUrl
                  }
                  type="video/webm"
                />
                <source
                  src={
                    featuresWithVideo[activeIndex >= 0 ? activeIndex : 0]
                      ?.videoUrl
                  }
                  type="video/mp4"
                />
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        </div>

        {/* Bottom CTA Button */}
        <div className="text-center mb-16">
          <Button
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg"
          >
            {t("home.aiLearning.cta")}
          </Button>
        </div>
      </Container>
    </section>
  );
};

export default AILearning;
