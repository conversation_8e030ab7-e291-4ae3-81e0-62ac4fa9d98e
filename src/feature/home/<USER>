/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-07-18 21:22:05
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import SignupForm from "@/components/forms/SignupForm";
import { Container } from "@/components/ui/container";

const JoinDevelopersGlobally: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#3b3b98] to-[#5553ff] relative overflow-hidden">
      <Container size="lg-plus" className="pt-20 pb-16">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-12 min-h-[80vh]">
          {/* Left Content */}
          <div className="flex-1 text-center lg:text-left max-w-2xl">
            {/* Main Heading */}
            <h1 className="text-2xl lg:text-3xl xl:text-6xl font-bold text-white leading-tight mb-8">
              {t("home.joinDevelopersGlobally.title")}{" "}
              <span className="text-[#00d4aa]">
                {t("home.joinDevelopersGlobally.titleHighlight")}
              </span>{" "}
              {t("home.joinDevelopersGlobally.titleSuffix")}
            </h1>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 lg:gap-12 mb-12">
              {(
                t("home.joinDevelopersGlobally.stats", {
                  returnObjects: true,
                }) as Array<{ value: string; label: string }>
              ).map((stat, index) => (
                <div key={index} className="text-center lg:text-left">
                  <div className="text-3xl lg:text-4xl font-bold text-[#00d4aa] mb-2">
                    {stat.value}
                  </div>
                  <div className="text-sm lg:text-base text-white/80">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Content - Signup Form */}
          <div className="flex-shrink-0 w-full lg:w-auto">
            <SignupForm className="mx-auto lg:mx-0 shadow-2xl" />
          </div>
        </div>
      </Container>

      {/* Background decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-[#00d4aa]/10 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-white/3 rounded-full blur-lg"></div>
      </div>
    </div>
  );
};

export default JoinDevelopersGlobally;
