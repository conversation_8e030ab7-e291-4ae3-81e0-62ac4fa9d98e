"use client";

import React from "react";
import { FaPlayCircle } from "react-icons/fa";
import { useTranslation } from "react-i18next";
import LogoCarousel from "@/components/common/logo-carousel";
import { getCompanyLogos } from "@/mockdata/maindata";

const Introduce: React.FC = () => {
  const { t } = useTranslation();
  const companyLogos = getCompanyLogos();

  return (
    <div
      className="flex flex-col items-center justify-center pt-20 pb-10 bg-white relative overflow-x-hidden
    bg-gradient-to-b from-white to-[#eef] "
    >
      {/* Main Heading */}
      <h1
        className="text-[48px] font-extrabold leading-tight w-full text-center"
        style={{
          fontFamily: "'Inter', sans-serif",
          margin: 0,
          backgroundImage: "linear-gradient(90deg, #5553ff, #86198f)",
          WebkitBackgroundClip: "text",
          backgroundClip: "text",
          color: "transparent",
        }}
      >
        {t("home.hero.title")
          .split("\n")
          .map((line, index) => (
            <React.Fragment key={index}>
              {line}
              {index === 0 && <br />}
            </React.Fragment>
          ))}
      </h1>

      {/* Subheading */}
      <div className="mt-6 text-[18px] text-gray-700 text-center font-normal leading-snug max-w-3xl mx-auto">
        {t("home.hero.subtitle")
          .split("\n")
          .map((line, index) => (
            <React.Fragment key={index}>
              {line}
              {index === 0 && <br />}
            </React.Fragment>
          ))}
      </div>

      {/* Card */}
      <div className="relative mt-12 w-[70%] max-w-full">
        {/* New Badge */}
        <span className="absolute top-[-0.75rem] left-[-1rem] rotate-[-10deg] flex h-6 items-center justify-center rounded bg-[#86198f] px-2 py-1 text-sm leading-5 font-semibold text-white">
          {t("home.hero.badge")}
        </span>

        <div className=" border rounded-sm shadow-md px-8 py-6 flex flex-row items-center justify-between  border-[#cccbff] bg-white p-6">
          {/* Card Content */}
          <div>
            <div className="uppercase text-gray-500 text-xs leading-4 font-bold tracking-[2.5px]">
              {t("home.hero.card.category")}
            </div>
            <div
              className="text-[1.5rem] leading-8 mt-[0.625rem] font-semibold text-[#0c1322]"
              style={{ fontFamily: "'Inter', sans-serif" }}
            >
              {t("home.hero.card.title")}
            </div>
            <div className="text-gray-700 text-md leading-5 flex items-center gap-2 mt-2">
              {t("home.hero.card.description1")}
            </div>
            <div className="text-gray-700 text-md leading-5 flex items-center gap-2 mt-2">
              {t("home.hero.card.description2")}{" "}
              <a
                href="#"
                className="underline text-[#7c3aed] hover:text-[#5b21b6]"
              >
                {t("home.hero.card.aiMockInterviews")}
              </a>{" "}
              {t("home.hero.card.description3")}
            </div>
          </div>

          {/* Card Actions */}
          <div
            className="flex flex-col items-end min-w-[200px] "
            style={{ fontFamily: "'Inter', sans-serif" }}
          >
            <button className="w-full bg-[#6366f1] hover:bg-[#4f46e5] text-white text-base rounded-lg py-3 mb-4 transition-colors duration-150">
              {t("home.hero.cta.primary")}
            </button>
            <button className="w-full flex items-center justify-center border border-[#a5b4fc] text-[#6366f1] hover:bg-[#f5f3ff] text-sm font-medium rounded-lg py-3 transition-colors duration-150">
              <FaPlayCircle className="mr-2 text-base" />
              {t("home.hero.cta.secondary")}
            </button>
          </div>
        </div>
      </div>

      {/* Companies */}
      <div className="mt-16 flex flex-col items-center w-full">
        <div className="flex text-md text-gray-700 text-center gap-6">
          <div className="mb-4">
            {t("home.hero.companies.text")}{" "}
            <span className="text-[#6366f1] font-bold">
              {t("home.hero.companies.count")}
            </span>{" "}
            {t("home.hero.companies.suffix")}
          </div>
          <LogoCarousel
            logos={companyLogos}
            speed={20}
            className="max-w-sm mx-auto"
          />
        </div>
      </div>
    </div>
  );
};

export default Introduce;
