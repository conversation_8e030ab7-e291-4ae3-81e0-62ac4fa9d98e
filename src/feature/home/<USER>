"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Container } from "@/components/ui/container";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import Carousel from "@/components/ui/carousel";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import {
  getPointersByCategory,
  getMostPopularPointers,
  CATEGORY_TYPES,
  getCourseUrl,
} from "@/mockdata/maindata";
import { formatDuration } from "@/lib/utils";
import { useRouter } from "next/navigation";

// 课程分类标签 - 基于 CATEGORY_TYPES
const getCategoryTabs = (t: any) => [
  { id: "most-popular", label: t("home.courseCategories.tabs.mostPopular") },
  { id: "system-design", label: t("home.courseCategories.tabs.systemDesign") },
  { id: "interview", label: t("home.courseCategories.tabs.interview") },
  { id: "aws", label: t("home.courseCategories.tabs.aws") },
  { id: "generative-ai", label: t("home.courseCategories.tabs.generativeAi") },
  { id: "python", label: t("home.courseCategories.tabs.python") },
  { id: "javascript", label: t("home.courseCategories.tabs.javascript") },
];

// 获取课程数据的函数
const getCoursesByCategory = (categoryId: string) => {
  if (categoryId === "most-popular") {
    return getMostPopularPointers(5);
  }

  // 对于其他分类，从对应的分类中获取课程
  return getPointersByCategory(categoryId).slice(0, 5);
};

export default function CourseCategories() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("most-popular");
  const router = useRouter();

  // 获取当前标签页的课程
  const currentCourses = getCoursesByCategory(activeTab);
  const categoryTabs = getCategoryTabs(t);

  return (
    <section className="py-16 bg-white">
      <Container size="lg-plus">
        <div className="mb-8">
          <Tabs
            value={activeTab}
            onValueChange={(value) => {
              setActiveTab(value);
            }}
            className="w-full"
          >
            {/* 标签页导航 */}
            <div className="relative">
              <div className="w-full">
                <TabsList className="flex w-full justify-between p-1 bg-transparent border-b border-gray-200">
                  {categoryTabs.map((tab) => (
                    <TabsTrigger
                      key={tab.id}
                      value={tab.id}
                      className="flex-1 px-4 py-2 text-sm font-medium text-center border-b-2 border-transparent data-[state=active]:border-blue-600 data-[state=active]:text-blue-600 data-[state=active]:bg-transparent hover:text-blue-600 transition-colors"
                    >
                      {tab.label}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>
            </div>

            <div className="my-10 text-gray-600 text-md text-center">
              {t("home.courseCategories.subtitle")}
            </div>

            {/* 课程走马灯 */}
            {categoryTabs.map((tab) => (
              <TabsContent key={tab.id} value={tab.id} className="mt-6">
                <Carousel
                  itemsPerSlide={4}
                  showArrows={true}
                  showIndicators={true}
                >
                  {currentCourses.map((course) => (
                    <SimpleCourseCard
                      key={course.id}
                      course={{
                        id: course.id,
                        title: course.title,
                        description: course.description,
                        duration: formatDuration(course.duration),
                        level:
                          course.level.charAt(0).toUpperCase() +
                          course.level.slice(1),
                        bookmarked: false,
                      }}
                      onClick={() => {
                        const courseUrl = getCourseUrl(course);
                        router.push(courseUrl);
                      }}
                    />
                  ))}
                </Carousel>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </Container>
    </section>
  );
}
