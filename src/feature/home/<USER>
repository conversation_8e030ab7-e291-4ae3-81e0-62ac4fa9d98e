/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-07-25 15:32:51
 */
"use client";

import React from "react";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import Carousel from "@/components/ui/carousel";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import { allPointerData, getCourseUrl } from "@/mockdata/maindata";
import { formatDuration } from "@/lib/utils";
import { useRouter } from "next/navigation";

export default function FreeCourses() {
  const router = useRouter();

  // Get free courses from maindata (filter by price = "Free" or take first few courses)
  const freeCourses = allPointerData
    .filter((course) => course.price === "Free" || course.level === "beginner")
    .slice(0, 8); // Take first 8 courses

  return (
    <section className="py-16" style={{ backgroundColor: "#f8f8ff" }}>
      <Container size="lg-plus">
        {/* Header */}
        <div className="text-center mb-12">
          <Typography
            variant="h1"
            className="text-4xl font-bold text-gray-900 mb-4"
          >
            Check out our <span className="text-blue-600">free</span> courses
          </Typography>
        </div>

        {/* Course Carousel */}
        <Carousel itemsPerSlide={4} showArrows={true} showIndicators={true}>
          {freeCourses.map((course) => (
            <SimpleCourseCard
              key={course.id}
              course={{
                id: course.id,
                title: course.title,
                description: course.description,
                duration: formatDuration(course.duration),
                difficulty:
                  course.level.charAt(0).toUpperCase() + course.level.slice(1),
                bookmarked: false,
              }}
              onClick={() => {
                const courseUrl = getCourseUrl(course);
                router.push(courseUrl);
              }}
            />
          ))}
        </Carousel>

        {/* Explore All Button */}
        <div className="text-center mt-8">
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200"
            onClick={() => router.push("/explore")}
          >
            Explore All
          </button>
        </div>
      </Container>
    </section>
  );
}
