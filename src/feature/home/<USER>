/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-18 21:01:40
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import Image from "next/image";
import FluxIcon from "@/components/fluxIcon";
import { Container } from "@/components/ui/container";

const TeamSkillsPromotion: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="flex py-16 px-4 flex-col items-center w-full">
      <Container size="2xl" className="px-8 w-[80%] max-w-none">
        {/* Team Skills Section */}
        <div className="w-full flex flex-col lg:flex-row items-center">
          {/* Text - Left */}
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-6 h-6 bg-black rounded-full flex items-center justify-center">
                <FluxIcon name="chevron-down" className="w-3 h-3 text-white" />
              </div>
              <p className="uppercase text-sm text-gray-500 tracking-widest">
                {t("home.teamSkillsPromotion.badge")}
              </p>
            </div>
            <h3 className="text-3xl font-bold text-gray-900 my-8">
              {t("home.teamSkillsPromotion.title").replace(
                t("home.teamSkillsPromotion.titleHighlight"),
                ""
              )}{" "}
              <span className="text-indigo-600">
                {t("home.teamSkillsPromotion.titleHighlight")}
              </span>{" "}
              {
                t("home.teamSkillsPromotion.title").split(
                  t("home.teamSkillsPromotion.titleHighlight")
                )[1]
              }
            </h3>
            <p className="text-gray-600 mb-4 text-sm">
              {t("home.teamSkillsPromotion.description")}
            </p>
            <button className="mt-2 bg-indigo-600 text-white px-6 py-2 rounded-md text-sm font-semibold hover:bg-indigo-700 transition">
              {t("home.teamSkillsPromotion.buttonText")}
            </button>
          </div>

          {/* Image - Right */}
          <div className="flex-1 relative h-80 ml-8">
            <Image
              src="/images/home/<USER>"
              alt="Team Skills Dashboard"
              fill
              className="object-contain"
            />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default TeamSkillsPromotion;
