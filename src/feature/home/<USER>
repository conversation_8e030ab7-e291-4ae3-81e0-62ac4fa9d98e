"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronUp, ChevronDown } from "lucide-react";
import { Container } from "@/components/ui/container";
import { getTestimonials } from "@/mockdata/maindata";

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Get testimonials from maindata and transform to match component structure
  const rawTestimonials = getTestimonials();
  const testimonials = rawTestimonials.map((testimonial, index) => ({
    id: index + 1,
    name: testimonial.name,
    role: testimonial.role,
    company: testimonial.company,
    companyLogo: "", // Can be enhanced later with company logos
    avatar:
      testimonial.avatar ||
      `https://www.educative.io/static/imgs/FelipeMatheus.png`,
    content: testimonial.content,
  }));

  const visibleTestimonials = testimonials.slice(
    currentIndex,
    currentIndex + 3
  );

  const scrollUp = () => {
    setCurrentIndex((prev) => Math.max(0, prev - 1));
  };

  const scrollDown = () => {
    setCurrentIndex((prev) => Math.min(testimonials.length - 3, prev + 1));
  };

  return (
    <section className="py-20 bg-gray-50">
      <Container size="lg-plus" className="mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Side - Header and CTA */}
          <div className="flex flex-col justify-center space-y-8">
            <div>
              <p className="text-gary-600 text-sm  mb-4">
                2.7 million developers love learning with Pageflux AI
              </p>
              <h2 className="text-5xl md:text-5xl font-bold text-gray-900 mb-8 leading-tight">
                Recommended by the best in the industry
              </h2>
              <Button
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-4 text-sm font-semibold"
              >
                Sign Up for Free
              </Button>
            </div>
          </div>

          {/* Right Side - Testimonials Carousel */}
          <div className="relative bg-gray-100 rounded-lg p-6">
            {/* Scroll Buttons Container - Centered on the right */}
            <div className="absolute top-1/2 -translate-y-1/2 right-4 flex flex-col gap-2 z-10">
              <button
                onClick={scrollUp}
                disabled={currentIndex === 0}
                className="w-8 h-8 bg-white rounded-full shadow-sm flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed border border-gray-300"
              >
                <ChevronUp className="w-4 h-4 text-gray-600" />
              </button>
              <button
                onClick={scrollDown}
                disabled={currentIndex >= testimonials.length - 3}
                className="w-8 h-8 bg-white rounded-full shadow-sm flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed border border-gray-300"
              >
                <ChevronDown className="w-4 h-4 text-gray-600" />
              </button>
            </div>

            {/* Testimonials Container - Exactly 3 cards visible */}
            <div className="pr-12 overflow-hidden">
              <div className="space-y-4">
                {visibleTestimonials.map((testimonial) => (
                  <div
                    key={testimonial.id}
                    className="bg-white rounded-lg p-4 shadow-sm h-52 flex flex-col"
                  >
                    {/* Top: Avatar and Name */}
                    <div className="flex items-center gap-3 mb-3">
                      <img
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        className="w-10 h-10 rounded-full object-cover flex-shrink-0"
                      />
                      <div className="flex items-center gap-2">
                        {testimonial.companyLogo && (
                          <span className="text-sm">
                            {testimonial.companyLogo}
                          </span>
                        )}
                        <span className="font-semibold text-gray-900 text-sm truncate">
                          {testimonial.name}
                        </span>
                      </div>
                    </div>

                    {/* Middle: Content - 5 lines reserved */}
                    {testimonial.content && (
                      <div className="flex-1 mb-2">
                        <p
                          className="text-gray-700 text-sm leading-relaxed overflow-hidden"
                          style={{
                            display: "-webkit-box",
                            WebkitLineClamp: 3,
                            WebkitBoxOrient: "vertical",
                            minHeight: "4rem", // Reserve space for 5 lines
                          }}
                        >
                          {testimonial.content}
                        </p>
                      </div>
                    )}

                    {/* Bottom: Role/Identity */}
                    {testimonial.role && (
                      <div className="mt-auto">
                        <p className="text-xs my-5 text-blue-600">
                          {testimonial.role}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default Testimonials;
