"use client";

import React from "react";

const TeamSkills: React.FC = () => {
  return (
    <div className="bg-gray-50 font-inter">
      <div className="flex flex-col lg:flex-row items-center justify-between px-8 py-16 max-w-7xl mx-auto">
        {/* Left Section */}
        <div className="max-w-lg mb-12 lg:mb-0">
          <div className="flex items-center space-x-2 mb-4">
            <div className="w-6 h-6 bg-black rounded-full flex items-center justify-center">
              <i className="fas fa-caret-up text-white" />
            </div>
            <span className="text-sm font-semibold text-gray-800">DEVPATH</span>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 leading-snug">
            Level up your <span className="text-indigo-600">team’s</span> skills
          </h1>
          <p className="text-gray-600 mt-4">
            Leverage Pageflux AI courses and internal knowledge to onboard,
            upskill, and train your developers at scale with DevPath.
          </p>
          <a
            href="#"
            className="mt-6 inline-block bg-indigo-600 text-white text-sm font-semibold py-3 px-6 rounded-lg shadow hover:bg-indigo-700"
          >
            Visit Devpath
          </a>
        </div>

        {/* Right Section */}
        <div className="relative">
          <div className="w-[500px] h-[300px] bg-gray-100 rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm text-gray-500">
                pointer-ai.devpath.com
              </span>
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full" />
                <div className="w-2 h-2 bg-gray-400 rounded-full" />
                <div className="w-2 h-2 bg-gray-400 rounded-full" />
              </div>
            </div>

            <div className="bg-indigo-100 text-indigo-600 text-xs font-semibold px-3 py-1 rounded-full inline-block mb-4">
              Manager Dashboard
            </div>
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              Track Team Members Progress
            </h2>

            <div className="space-y-2">
              {/* Progress Bars */}
              <ProgressBar progress={70} color="bg-indigo-500" />
              <ProgressBar progress={50} color="bg-yellow-500" />
              <ProgressBar progress={30} color="bg-red-500" />
            </div>
          </div>

          {/* Pie Chart */}
          <div className="absolute bottom-0 right-0 transform translate-x-12 translate-y-12 w-40 h-40 bg-white rounded-lg shadow-md border border-gray-200 p-4">
            <div className="w-16 h-16 mx-auto">
              <img
                src="/images/home/<USER>"
                alt="Pie chart showing skill distribution"
                width={64}
                height={64}
              />
            </div>
            <p className="text-center text-sm text-gray-600 mt-2">
              Skill Distribution
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

interface ProgressBarProps {
  progress: number;
  color: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ progress, color }) => {
  return (
    <div className="flex items-center space-x-2">
      <div className="w-8 h-8 rounded-full bg-gray-300" />
      <div className="flex-1 bg-gray-200 h-2 rounded-full overflow-hidden">
        <div className={`${color} h-full`} style={{ width: `${progress}%` }} />
      </div>
    </div>
  );
};

export default TeamSkills;
