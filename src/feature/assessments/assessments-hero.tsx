"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Check as CheckIcon } from "lucide-react";
import FluxIcon from "@/components/fluxIcon";

const AssessmentsHero: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="w-full bg-blue-600 dark:bg-gray-D1400">
      <div className="w-full" style={{ zIndex: 1 }}>
        <div className="relative mx-auto flex w-full max-w-5xl justify-between  flex-col-reverse self-center px-6 lg:flex-row lg:items-center items-center">
          {/* Left Side - Content */}
          <div className="flex flex-col my-14 flex-shrink flex-grow sm:mr-16  max-w-lg text-white dark:text-gray-D200">
            {/* Icon and Badge */}
            <div className="m-0 flex flex-row items-center lg:mt-2">
              <FluxIcon
                name="assessments-skill"
                width={25}
                height={25}
                className="fill-current text-white dark:text-gray-D200"
              />
              <p className="eyebrow m-0 mt-2 whitespace-nowrap pl-2 uppercase not-italic text-gray-L50 dark:text-gray-D200">
                {t("assessments.hero.badge")}
              </p>
            </div>

            {/* Main Heading */}
            <h1
              className="text-5xl text-left mt-2 mb-0 mx-0 font-bold  leading-normal"
              style={{ maxWidth: "450px", letterSpacing: "0.15px" }}
            >
              {t("assessments.hero.title")}
            </h1>

            {/* Feature List */}
            <div className="mt-3 flex text-sm">
              <CheckIcon className="mt-1 h-5 w-5 flex-shrink-0 stroke-current text-white dark:text-gray-D200" />
              <div
                className="body-medium my-0 ml-4 not-italic"
                style={{ maxWidth: "400px", letterSpacing: "0.15px" }}
              >
                Take an Educative Assessment to benchmark your current coding
                skills
              </div>
            </div>

            <div className="mt-3 flex text-sm">
              <CheckIcon className="mt-1 h-5 w-5 flex-shrink-0 stroke-current text-white dark:text-gray-D200" />
              <div
                className="body-medium my-0 ml-4 not-italic"
                style={{ maxWidth: "400px", letterSpacing: "0.15px" }}
              >
                Understand your strengths and weaknesses, to improve your skills
              </div>
            </div>
          </div>

          {/* Right Side - Illustration */}
          <svg
            width="359"
            height="233"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="tailwind-hidden mb-24 mt-16 lg:block"
            style={{ flex: "0 0 auto", height: "234px" }}
          >
            <path
              opacity="0.1"
              d="M248.2 233c31.7 0 57.5-2.2 57.5-4.8 0-2.7-25.8-4.8-57.5-4.8s-57.4 2.1-57.4 4.8c0 2.6 25.7 4.8 57.4 4.8Z"
              fill="#000"
            ></path>
            <path
              d="M248.2 119.7a9 9 0 1 0 0-17.9 9 9 0 0 0 0 17.9Z"
              fill="#FD9E02"
            ></path>
            <path
              d="M248.2 74.6a36.2 36.2 0 1 0 0 72.4 36.2 36.2 0 0 0 0-72.4Zm0 59a23 23 0 1 1 0-45.8 23 23 0 0 1 0 45.9Z"
              fill="#50CAC3"
            ></path>
            <path
              d="M297.3 61.6a69.5 69.5 0 1 0-98.2 98.3 69.5 69.5 0 0 0 98.2-98.3Zm-49 102a52.9 52.9 0 1 1 0-105.8 52.9 52.9 0 0 1 0 105.7Z"
              fill="#50CAC3"
            ></path>
            <path
              d="M349.4 68a110.1 110.1 0 1 0-203 85.5 110.1 110.1 0 0 0 203-85.5ZM309 171.5A86 86 0 1 1 187.4 50 86 86 0 0 1 309 171.5Z"
              fill="#50CAC3"
            ></path>
            <path
              opacity="0.1"
              d="M358 110.7a109.7 109.7 0 0 1-109.8 109.8V1A109.7 109.7 0 0 1 358 110.7Z"
              fill="#000"
            ></path>
            <path
              d="m234.7 106.3 13.4 4.3-13.3-4.8-.1.5Z"
              fill="#231F20"
            ></path>
            <path
              d="M72.3 50.9a9 9 0 0 0-.4 3.5l.6 8.4a9 9 0 0 0 10.3 8.4l32.8-4.6 2.2-.3-2.2-2.3L93 41.1a9 9 0 0 0-13.3.3l-5.6 6.4c-.8.9-1.4 2-1.8 3Z"
              fill="#FD9E02"
            ></path>
            <path
              d="m104.6 60.5-.7 2 11.7 4.1 70 25a414.3 414.3 0 0 1 23 8.5l10.2 4c3.2 1.1 6.6 1.8 10 2l7.4.5-6.2-4.2a38.5 38.5 0 0 0-9-4.5l-10.6-3a366.4 366.4 0 0 1-23.4-7.4L115.5 64l-10.9-3.5Z"
              fill="#152E59"
            ></path>
            <path
              d="m198.3 91.3-1.4 4.3c4 1.4 7.9 3 11.8 4.5l10.1 4c3.2 1.1 6.6 1.8 10 2l7.4.5-6.2-4.2a38.5 38.5 0 0 0-9-4.5l-10.6-3-12-3.6Z"
              fill="#FFAC01"
            ></path>
            <path
              opacity="0.2"
              d="M72.3 50.9a9 9 0 0 0-.4 3.5l.6 8.4a9 9 0 0 0 10.3 8.4l32.8-4.6 70 25a415.1 415.1 0 0 1 23 8.5l10.2 4c3.2 1.1 6.6 1.8 10 2l7.4.5L72.3 50.9Z"
              fill="#fff"
            ></path>
            <path
              d="M248.1 111.5a.8.8 0 1 0 0-1.7.8.8 0 0 0 0 1.7Z"
              fill="#231F20"
            ></path>
            <path
              opacity="0.5"
              d="M71.5 23.5 44.9 13m67.1 3L96 9.5m22 79.8-18-6.1m38.8-31.8L85.2 29.9M56.3 66.8 2 47.4"
              stroke="#63AFE6"
              strokeWidth="4"
              strokeMiterlimit="10"
              strokeLinecap="round"
            ></path>
          </svg>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="flex justify-center">
        <div
          className="-mt-2 flex w-full flex-col"
          style={{ maxWidth: "1000px" }}
        ></div>
      </div>
    </div>
  );
};

export default AssessmentsHero;
