// components/assessments-content.tsx
"use client";

import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import { Container } from "@/components/ui/container";
import { Button } from "@/components/ui/button";
import FluxIcon from "@/components/fluxIcon";

type Assessment = {
  title: string;
  href: string;
  imageUrl: string;
};

const assessments: Assessment[] = [
  {
    title: "OOP in C++",
    href: "/assessments/oop-in-cpp-assessment",
    imageUrl: "/images/assessments/c++.png",
  },
  {
    title: "Go Programming Fundamentals",
    href: "/assessments/golang-fundamentals",
    imageUrl: "/images/assessments/golang.png",
  },
  {
    title: "Logic and Problem Solving",
    href: "/assessments/logic-problem-solving",
    imageUrl: "/images/assessments/logic.png",
  },
  {
    title: "React Development",
    href: "/assessments/react-development",
    imageUrl: "/images/assessments/react.png",
  },
  {
    title: "Relational Database Design",
    href: "/assessments/relational-database-design",
    imageUrl: "/images/assessments/relational-database-design.png",
  },
  {
    title: "Scikit-Learn Machine Learning",
    href: "/assessments/scikit-learn-ml",
    imageUrl: "/images/assessments/scikit-learn.png",
  },
  {
    title: "SQL Database Management",
    href: "/assessments/sql-database",
    imageUrl: "/images/assessments/sql.png",
  },
  {
    title: "R Basics",
    href: "/assessments/r-basics",
    imageUrl: "/images/assessments/r-basics.png",
  },
];

export default function AssessmentsContent() {
  const [search, setSearch] = useState("");
  const [visibleCount, setVisibleCount] = useState(6); // Initially show 6 assessments

  const filtered = assessments.filter((a) =>
    a.title.toLowerCase().includes(search.toLowerCase())
  );

  const visibleAssessments = filtered.slice(0, visibleCount);
  const hasMore = visibleCount < filtered.length;

  const handleLoadMore = () => {
    setVisibleCount((prev) => Math.min(prev + 6, filtered.length));
  };

  // Reset visible count when search changes
  useEffect(() => {
    setVisibleCount(6);
  }, [search]);

  return (
    <div className="py-16 bg-gray-50">
      <Container size="lg-plus">
        {/* Search */}
        <div className="mb-12 px-8">
          <div className="w-full mx-auto">
            <div className="relative">
              <input
                type="search"
                placeholder="Search Assessments"
                className="w-full h-10 pl-12 pr-4 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
              <FluxIcon
                name="search"
                className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"
              />
            </div>
          </div>
        </div>

        {/* Assessment Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 mb-12 px-12">
          {visibleAssessments.map((assessment, index) => (
            <AssessmentCard key={index} {...assessment} />
          ))}
        </div>

        {/* Show More Button */}
        {hasMore && (
          <div className="flex justify-center">
            <Button
              variant="outline"
              className="px-8 py-3 border-gray-300 text-gray-700 hover:bg-gray-50"
              onClick={handleLoadMore}
            >
              Show More
              <FluxIcon
                name="arrow-down"
                width={24}
                height={24}
                className="ml-1 h-6 w-6 stroke-indigo-500 dark:stroke-indigo-200"
              />
            </Button>
          </div>
        )}
      </Container>
    </div>
  );
}

function AssessmentCard({ title, href, imageUrl }: Assessment) {
  return (
    <div className="mt-6 flex h-80 max-h-80 w-full max-w-xs flex-row rounded-md text-base sm:h-72 sm:max-h-52 sm:max-w-lg sm:text-xl lg:mt-0 lg:max-w-none">
      <Link href={href} className="w-full">
        <div className="flex h-80 max-h-80 w-full flex-col rounded-md shadow dark:bg-gray-800 sm:h-56 sm:max-h-52 sm:flex-row">
          {/* Image */}
          <div className="flex h-24 w-full flex-shrink-0 items-center justify-center border-b border-gray-200 dark:border-gray-700 sm:h-full sm:w-32 sm:border-b-0 sm:border-r lg:w-[25%] lg:px-2">
            <div
              className="w-16 h-16 sm:h-24 sm:w-24 relative"
              style={{ height: 90, width: 90 }}
            >
              <Image
                src={imageUrl}
                alt={title}
                fill
                className="object-cover rounded"
              />
            </div>
          </div>

          {/* Content */}
          <div className="flex h-full w-full flex-col  lg:w-[75%] ">
            <div className="mb-4 ml-4 mr-4 h-36 sm:mb-0 sm:ml-8 sm:h-4/6 lg:ml-4">
              <p className="mb-2 mt-2 text-left uppercase text-gray-500 dark:text-gray-200 text-xs">
                Assessment
              </p>
              <p className="my-0 tracking-wide text-lg font-semibold">
                {title}
              </p>
            </div>
            <div className="flex items-center justify-end mx-4 mb-4">
              <button className="text-default w-28 rounded border border-solid border-gray-300 py-2 text-base flex items-center justify-center gap-2">
                <span className="text-base">Preview</span>
                <FluxIcon
                  name="arrow-right"
                  width={24}
                  height={24}
                  className="icon-right h-5 w-5 stroke-indigo-500 dark:text-indigo-200"
                />
              </button>
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
}
