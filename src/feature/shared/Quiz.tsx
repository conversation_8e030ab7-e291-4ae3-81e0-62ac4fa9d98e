// components/Quiz.tsx
"use client";

import React, { useState, useEffect } from "react";
import FluxIcon from "@/components/fluxIcon";
import { MockApiQuestion, MockApiQuestionOption } from "@/types/mockapi";

// 导出类型别名以保持向后兼容
export type QuestionOption = MockApiQuestionOption;
export type Question = MockApiQuestion;

interface QuizProps {
  questions: Question[];
}

const Quiz: React.FC<QuizProps> = ({ questions }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [answers, setAnswers] = useState<string[][]>(() =>
    Array.from({ length: questions.length }, () => [])
  );
  const [showSkippedDropdown, setShowSkippedDropdown] = useState(false);
  const [isAnswerSubmitted, setIsAnswerSubmitted] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);
  const [submittedQuestions, setSubmittedQuestions] = useState<Set<number>>(
    new Set()
  );

  // 安全检查：如果没有问题或当前问题不存在，显示提示信息
  if (!questions || questions.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <p className="text-gray-600">No quiz questions available.</p>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  if (!currentQuestion) {
    return (
      <div className="flex items-center justify-center p-8">
        <p className="text-gray-600">Question not found.</p>
      </div>
    );
  }

  const isMultipleChoice = currentQuestion.multipleAnswers === true;

  // 当 questions 变化时重置状态
  useEffect(() => {
    setCurrentQuestionIndex(0);
    setSelectedOptions([]);
    setAnswers(Array.from({ length: questions.length }, () => []));
    setIsAnswerSubmitted(false);
    setShowFeedback(false);
    setSubmittedQuestions(new Set());
  }, [questions.length]); // 只依赖长度，避免无限循环

  // 当当前问题索引变化时，检查该问题是否已经提交过答案
  useEffect(() => {
    const currentAnswers = answers[currentQuestionIndex] || [];
    const isQuestionSubmitted = submittedQuestions.has(currentQuestionIndex);

    setSelectedOptions(currentAnswers);
    setIsAnswerSubmitted(isQuestionSubmitted);
    setShowFeedback(isQuestionSubmitted);
  }, [currentQuestionIndex, answers, submittedQuestions]);

  // 计算跳过的问题 - 只包括当前问题之前的未提交问题
  const skippedQuestions = Array.from(
    { length: currentQuestionIndex + 1 },
    (_, index) => index
  ).filter((index) => !submittedQuestions.has(index));

  // 跳转到指定问题
  const goToQuestion = (index: number) => {
    // 保存当前答案到 answers 数组
    const updatedAnswers = [...answers];
    updatedAnswers[currentQuestionIndex] = selectedOptions;
    setAnswers(updatedAnswers);

    // 切换到新问题
    setCurrentQuestionIndex(index);
    setShowSkippedDropdown(false);
  };

  // 重置所有答案
  const resetAllAnswers = () => {
    setSelectedOptions([]);
    setAnswers(Array.from({ length: questions.length }, () => []));
    setCurrentQuestionIndex(0);
    setIsAnswerSubmitted(false);
    setShowFeedback(false);
    setSubmittedQuestions(new Set());
  };

  const handleOptionSelect = (optionId: string) => {
    // 如果已经提交答案，不允许再次选择
    if (isAnswerSubmitted) return;

    if (isMultipleChoice) {
      // 多选题逻辑
      setSelectedOptions((prev) =>
        prev.includes(optionId)
          ? prev.filter((id) => id !== optionId)
          : [...prev, optionId]
      );
    } else {
      // 单选题逻辑
      setSelectedOptions([optionId]);
    }
  };

  // 获取选项的样式类名
  const getOptionClassName = (option: QuestionOption) => {
    const baseClasses =
      "flex items-center space-x-3 cursor-pointer p-3 rounded-lg border transition-all duration-200";

    const isSelected = selectedOptions.includes(option.id);
    const isQuestionSubmitted = submittedQuestions.has(currentQuestionIndex);

    if (!isQuestionSubmitted) {
      // 未提交答案时的样式（预选状态）
      return `${baseClasses} ${
        isSelected
          ? "border bg-[#cccbff] dark:bg-pink-900/20"
          : "border-[#cccbff] hover:border-pink-200 hover:bg-gray-50 dark:hover:bg-gray-800"
      }`;
    }

    // 已提交答案后的反馈样式
    const isCorrect = option.correct;

    if (isCorrect) {
      // 正确答案显示绿色
      return `${baseClasses} border-green-500 bg-green-100 dark:bg-green-900/20`;
    } else if (isSelected && !isCorrect) {
      // 选择的错误答案显示红色
      return `${baseClasses} border-red-500 bg-red-100 dark:bg-red-900/20`;
    } else {
      // 未选择的选项保持默认样式
      return `${baseClasses} border-gray-300 bg-gray-50 dark:bg-gray-800`;
    }
  };

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      goToQuestion(currentQuestionIndex + 1);
    }
  };

  const handlePrev = () => {
    if (currentQuestionIndex > 0) {
      goToQuestion(currentQuestionIndex - 1);
    }
  };

  const handleSubmit = () => {
    // 如果还没有选择答案，提示用户
    if (selectedOptions.length === 0) {
      alert("Please select an answer before submitting.");
      return;
    }

    // 如果已经提交过答案，跳转到下一题
    if (isAnswerSubmitted) {
      if (currentQuestionIndex < questions.length - 1) {
        goToQuestion(currentQuestionIndex + 1);
      } else {
        // 最后一题，显示完成信息
        console.log("Quiz completed! Final answers:", answers);
        alert("Quiz completed successfully!");
      }
      return;
    }

    // 提交答案并显示反馈
    setIsAnswerSubmitted(true);
    setShowFeedback(true);

    // 标记当前问题为已提交
    setSubmittedQuestions((prev) => new Set(prev).add(currentQuestionIndex));

    // 保存答案
    const updatedAnswers = [...answers];
    updatedAnswers[currentQuestionIndex] = selectedOptions;
    setAnswers(updatedAnswers);
  };

  return (
    <div className="max-w-4xl mx-auto my-8 rounded-xl shadow-lg overflow-hidden">
      {/* Header - 粉红色 */}
      <div className="bg-[#f5f5ff]  border-3 border-[#f5f5ff] px-6 py-4 border-b ">
        <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100">
          Quiz
        </h2>
      </div>

      {/* Content - 白色背景 */}
      <div className="bg-white dark:bg-gray-900 p-6">
        <div className="mb-6">
          <div
            className="text-base font-medium text-gray-800 dark:text-gray-100 leading-relaxed"
            dangerouslySetInnerHTML={{
              __html: currentQuestion.questionTextHtml,
            }}
          />
        </div>

        <div className="space-y-3 mb-6">
          {currentQuestion.questionOptions.map((option, index) => (
            <label key={option.id} className={getOptionClassName(option)}>
              <input
                type={isMultipleChoice ? "checkbox" : "radio"}
                name={`question-${currentQuestionIndex}`}
                className="h-4 w-4 text-pink-600 border-gray-300 focus:ring-pink-500"
                checked={selectedOptions.includes(option.id)}
                onChange={() => handleOptionSelect(option.id)}
                disabled={isAnswerSubmitted}
              />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {String.fromCharCode(65 + index)}.
              </span>
              <span className="text-sm text-gray-700 dark:text-gray-300 flex-1">
                {option.text}
              </span>
            </label>
          ))}
        </div>

        <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
          {/* Left side - Reset and Skipped */}
          <div className="flex items-center space-x-3">
            {/* Reset all answers button */}
            <button
              className="p-2 text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
              onClick={resetAllAnswers}
              title="Reset all answers"
            >
              <FluxIcon name="refresh" className="w-4 h-4" />
            </button>

            {/* Skipped dropdown */}
            <div className="relative">
              {skippedQuestions.length > 0 && (
                <button
                  className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                  onClick={() => setShowSkippedDropdown(!showSkippedDropdown)}
                >
                  <span>Skipped ({skippedQuestions.length})</span>
                  <FluxIcon
                    name="chevron-down"
                    className={`w-4 h-4 transition-transform ${
                      showSkippedDropdown ? "rotate-180" : ""
                    }`}
                  />
                </button>
              )}

              {showSkippedDropdown && skippedQuestions.length > 0 && (
                <div className="absolute bottom-full left-0 mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg py-2 min-w-[120px]">
                  {skippedQuestions.map((questionIndex) => (
                    <button
                      key={questionIndex}
                      className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => goToQuestion(questionIndex)}
                    >
                      Q{questionIndex + 1}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Center - Navigation */}
          <div className="flex items-center space-x-4">
            <button
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handlePrev}
              disabled={currentQuestionIndex === 0}
            >
              <FluxIcon name="chevron-left" className="w-5 h-5" />
            </button>

            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {currentQuestionIndex + 1} / {questions.length}
            </span>

            <button
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleNext}
              disabled={currentQuestionIndex === questions.length - 1}
            >
              <FluxIcon name="chevron-right" className="w-5 h-5" />
            </button>
          </div>

          {/* Right side - Submit button */}
          <button
            className="px-4 py-2 bg-[#5553ff] text-white text-sm rounded-lg hover:bg-[#4f46e5] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            onClick={handleSubmit}
            disabled={selectedOptions.length === 0 && !isAnswerSubmitted}
          >
            {isAnswerSubmitted
              ? currentQuestionIndex === questions.length - 1
                ? "Finish Quiz"
                : "Next Question"
              : "Submit Answer"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Quiz;
