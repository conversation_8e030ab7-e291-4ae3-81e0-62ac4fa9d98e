"use client";

import React, { useState, useEffect } from "react";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import { Card } from "@/components/ui/card";
import { Box } from "@/components/ui/box";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface Testimonial {
  id?: number;
  name: string;
  role: string;
  company?: string;
  companyLogo?: string;
  avatar?: string;
  content: string;
}

interface TestimonialsCarouselProps {
  testimonials?: Testimonial[];
  title?: string;
  subtitle?: string;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  slidesToShow?: number;
  className?: string;
  containerSize?: "sm" | "md" | "lg" | "lg-plus" | "xl";
}

// 默认评价数据
const defaultTestimonials: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Software Engineer",
    company: "<PERSON><PERSON><PERSON>",
    companyLogo: "🌱",
    content:
      "I love the content on Pageflux AI and I feel as if I am definitely improving in my craft.",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Software Engineer",
    company: "Wayfair",
    companyLogo: "🏠",
    content:
      "Your method is simple, straight to the point and I can practice with it everywhere, even from my phone, that's something I have never had in other learning platforms.",
  },
  {
    id: 3,
    name: "Sarah Johnson",
    role: "Frontend Developer",
    company: "Microsoft",
    companyLogo: "🖥️",
    content:
      "The interactive coding environment is fantastic. I can practice algorithms and data structures without any setup.",
  },
  {
    id: 4,
    name: "David Kim",
    role: "Full Stack Developer",
    company: "Google",
    companyLogo: "🔍",
    content:
      "Pageflux AI's hands-on approach helped me land my dream job. The courses are well-structured and practical.",
  },
  {
    id: 5,
    name: "Maria Rodriguez",
    role: "DevOps Engineer",
    company: "Amazon",
    companyLogo: "📦",
    content:
      "I highly recommend Pageflux AI. The courses are well organized and easy to understand.",
  },
  {
    id: 6,
    name: "Alex Chen",
    role: "Senior Engineering Manager",
    company: "Netflix",
    companyLogo: "🎬",
    content:
      "As a manager, I recommend Pageflux AI to my team. The hands-on approach accelerates learning and skill development.",
  },
];

const TestimonialsCarousel: React.FC<TestimonialsCarouselProps> = ({
  testimonials = defaultTestimonials,
  title,
  subtitle,
  autoPlay = true,
  autoPlayInterval = 4000,
  slidesToShow = 2,
  className = "",
  containerSize = "lg",
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  // 计算总页数
  const totalSlides = Math.ceil(testimonials.length / slidesToShow);

  // 自动播放功能
  useEffect(() => {
    if (!autoPlay || totalSlides <= 1) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % totalSlides);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, totalSlides]);

  // 处理上一页
  const handlePrevSlide = () => {
    setCurrentSlide((prev) => (prev > 0 ? prev - 1 : totalSlides - 1));
  };

  // 处理下一页
  const handleNextSlide = () => {
    setCurrentSlide((prev) => (prev < totalSlides - 1 ? prev + 1 : 0));
  };

  // 获取当前页的评价
  const visibleTestimonials = testimonials.slice(
    currentSlide * slidesToShow,
    (currentSlide + 1) * slidesToShow
  );

  return (
    <section className={cn("py-16 bg-white", className)}>
      <Container size={containerSize}>
        {/* 标题部分 */}
        {(title || subtitle) && (
          <div className="text-center mb-12">
            {title && (
              <Typography variant="h2" className="font-bold text-gray-900 mb-4">
                {title}
              </Typography>
            )}
            {subtitle && (
              <Typography
                variant="p"
                className="text-gray-600 max-w-2xl mx-auto"
              >
                {subtitle}
              </Typography>
            )}
          </div>
        )}

        {/* 走马灯容器 */}
        <div className="relative">
          {/* 左右箭头导航 */}
          {totalSlides > 1 && (
            <>
              <button
                onClick={handlePrevSlide}
                className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full shadow-lg border border-gray-200 p-3 hover:shadow-xl transition-all duration-200 hover:scale-105"
                aria-label="Previous testimonials"
              >
                <ChevronLeft className="h-5 w-5 text-gray-600" />
              </button>
              <button
                onClick={handleNextSlide}
                className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full shadow-lg border border-gray-200 p-3 hover:shadow-xl transition-all duration-200 hover:scale-105"
                aria-label="Next testimonials"
              >
                <ChevronRight className="h-5 w-5 text-gray-600" />
              </button>
            </>
          )}

          {/* 评价卡片 */}
          <div className="px-12">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {visibleTestimonials.map((testimonial, index) => (
                <Card
                  key={testimonial.id || index}
                  className="p-6 bg-white h-64 flex flex-col rounded-0 border-0 shadow-none"
                >
                  <div className="flex flex-col h-full">
                    {/* 头部：头像和用户信息 */}
                    <div className="flex items-start gap-4 mb-4">
                      {/* 头像 */}
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex-shrink-0 flex items-center justify-center">
                        {testimonial.avatar ? (
                          <img
                            src={testimonial.avatar}
                            alt={testimonial.name}
                            className="w-12 h-12 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-blue-600 font-semibold text-lg">
                            {testimonial.name.charAt(0)}
                          </span>
                        )}
                      </div>

                      {/* 用户信息 */}
                      <div className="mt-2">
                        <Typography
                          variant="small"
                          className="font-semibold text-gray-900"
                        >
                          {testimonial.name}
                        </Typography>
                      </div>
                    </div>

                    {/* 评价内容 - 限制4行 */}
                    <div className="flex-1 mb-4">
                      <Typography
                        variant="small"
                        className="text-gray-700 leading-relaxed overflow-hidden"
                        style={{
                          display: "-webkit-box",
                          WebkitLineClamp: 4,
                          WebkitBoxOrient: "vertical",
                          minHeight: "4.5rem", // 预留4行的空间
                        }}
                      >
                        "{testimonial.content}"
                      </Typography>
                    </div>

                    {/* 底部：用户身份信息 */}
                    <div className="mt-auto flex gap-2">
                      <Typography
                        variant="small"
                        className="font-semibold text-gray-900"
                      >
                        {testimonial.name}
                      </Typography>
                      <Typography variant="small" className="text-gray-600">
                        {testimonial.role}
                      </Typography>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* 指示器 */}
          {totalSlides > 1 && (
            <div className="flex justify-center mt-8 space-x-2">
              {Array.from({ length: totalSlides }).map((_, index) => (
                <button
                  key={index}
                  className={cn(
                    "w-2 h-2 rounded-full transition-all duration-300",
                    currentSlide === index
                      ? "bg-blue-600 scale-110"
                      : "bg-gray-300 hover:bg-gray-400"
                  )}
                  onClick={() => setCurrentSlide(index)}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          )}
        </div>
      </Container>
    </section>
  );
};

export default TestimonialsCarousel;
