"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface FAQItem {
  question: string;
  answer: string;
}

interface FAQSectionProps {
  title?: string;
  subtitle?: string;
  faqs?: FAQItem[];
  className?: string;
  containerSize?: "sm" | "md" | "lg" | "lg-plus" | "xl";
}

// 获取默认FAQ数据的函数
const getDefaultFAQs = (t: any): FAQItem[] => [
  {
    question: t("common.faq.whatIsPageflux"),
    answer: t("common.faq.whatIsPagefluxAnswer"),
  },
  {
    question: t("common.faq.doesPagefluxHaveFree"),
    answer: t("common.faq.doesPagefluxHaveFreeAnswer"),
  },
  {
    question: t("common.faq.canPayInstallments"),
    answer: t("common.faq.canPayInstallmentsAnswer"),
  },
];

const FAQSection: React.FC<FAQSectionProps> = ({
  title,
  subtitle,
  faqs,
  className = "",
  containerSize = "lg",
}) => {
  const { t } = useTranslation();
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());

  // 使用传入的props或翻译作为默认值
  const faqTitle = title || t("shared.faq.title");
  const faqSubtitle = subtitle;
  const faqItems = faqs || getDefaultFAQs(t);

  const toggleItem = (index: number) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index);
    } else {
      newOpenItems.add(index);
    }
    setOpenItems(newOpenItems);
  };

  return (
    <section className={cn("py-12", className)}>
      <Container size={containerSize}>
        <div className="mb-16">
          <div className="max-w-10xl mx-auto">
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden px-8 py-8">
              <div className="p-6  border-gray-200">
                <Typography
                  variant="h3"
                  className="font-bold text-gray-900 text-3xl"
                >
                  {faqTitle}
                </Typography>
                {faqSubtitle && (
                  <Typography variant="p" className="text-gray-600 mt-2">
                    {faqSubtitle}
                  </Typography>
                )}
              </div>

              {faqItems.map((faq, index) => {
                const isOpen = openItems.has(index);
                const isLast = index === faqItems.length - 1;

                return (
                  <div
                    key={index}
                    className={`py-4 mx-6 ${isLast ? "" : "border-b"}`}
                  >
                    <button
                      className="w-full pt-4 pb-4 text-left flex items-center justify-between px-1 transition-all duration-200 ease-in-out"
                      onClick={() => toggleItem(index)}
                    >
                      <Typography
                        variant="h6"
                        className="font-semibold text-gray-900 pr-4"
                      >
                        {faq.question}
                      </Typography>
                      <div className="flex-shrink-0">
                        <ChevronDown
                          className={cn(
                            "w-5 h-5 text-gray-500 transition-transform duration-300 ease-in-out",
                            isOpen ? "rotate-180" : "rotate-0"
                          )}
                        />
                      </div>
                    </button>

                    <div
                      className={cn(
                        "overflow-hidden transition-all duration-500 ease-in-out",
                        isOpen
                          ? "max-h-[500px] opacity-100 pb-0"
                          : "max-h-0 opacity-0 pb-0"
                      )}
                    >
                      <div className="px-1 pb-4 pt-2">
                        <Typography
                          variant="small"
                          className="text-gray-600 leading-relaxed"
                        >
                          {faq.answer}
                        </Typography>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default FAQSection;
