"use client";

import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Container } from "@/components/ui/container";
import { ChevronDown, ChevronUp } from "lucide-react";

// AI功能数据类型
interface AIFeature {
  title: string;
  description: string;
  buttonText: string;
  buttonVariant: "primary" | "outline";
  videoUrl?: string;
}

// 组件属性类型
interface AILearningSectionProps {
  title?: string;
  subtitle?: string;
  features?: AIFeature[];
  showCTAButton?: boolean;
  ctaButtonText?: string;
  className?: string;
}

// 获取默认AI功能数据的函数
const getDefaultAIFeatures = (t: any): AIFeature[] => [
  {
    title: t("shared.aiLearning.features.0.title"),
    description: t("shared.aiLearning.features.0.description"),
    buttonText: t("shared.aiLearning.features.0.buttonText"),
    buttonVariant: t("shared.aiLearning.features.0.buttonVariant") as
      | "outline"
      | "primary",
    videoUrl:
      "https://www.pointer-ai.com/static/imgs/Personalized-recommendations-v2.webm",
  },
  {
    title: t("shared.aiLearning.features.1.title"),
    description: t("shared.aiLearning.features.1.description"),
    buttonText: t("shared.aiLearning.features.1.buttonText"),
    buttonVariant: t("shared.aiLearning.features.1.buttonVariant") as
      | "outline"
      | "primary",
    videoUrl:
      "https://www.pointer-ai.com/static/imgs/Personalized-recommendations-v2.webm",
  },
];

const AILearningSection: React.FC<AILearningSectionProps> = ({
  title,
  subtitle,
  features,
  showCTAButton = true,
  ctaButtonText,
  className = "",
}) => {
  const { t } = useTranslation();
  const [activeIndex, setActiveIndex] = useState(0);

  // 使用传入的props或翻译作为默认值
  const sectionTitle = title || t("shared.aiLearning.title");
  const sectionSubtitle = subtitle || t("shared.aiLearning.subtitle");
  const sectionFeatures = features || getDefaultAIFeatures(t);
  const sectionCtaButtonText =
    ctaButtonText || t("shared.aiLearning.ctaButton");

  return (
    <section className={`py-20 bg-gray-50 ${className}`}>
      <Container size="lg">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-5xl md:text-5xl font-bold text-gray-900 mb-6">
            {sectionTitle}
          </h2>
          <p className="text-sm text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {sectionSubtitle}
          </p>
        </div>

        {/* AI Features Accordion */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {/* Left Side - Accordion */}
          <div className="space-y-4">
            {sectionFeatures.map((feature, index) => (
              <div
                key={index}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                <button
                  onClick={() =>
                    setActiveIndex(activeIndex === index ? -1 : index)
                  }
                  className={`w-full px-6 py-4 text-left flex items-center justify-between transition-colors ${
                    activeIndex === index
                      ? "bg-blue-600 text-white"
                      : "bg-white text-gray-900 hover:bg-gray-50"
                  }`}
                >
                  <span className="font-semibold text-lg">{feature.title}</span>
                  {activeIndex === index ? (
                    <ChevronUp className="w-5 h-5" />
                  ) : (
                    <ChevronDown className="w-5 h-5" />
                  )}
                </button>

                {activeIndex === index && (
                  <div className="px-6 py-4 bg-white border-t border-gray-200">
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {feature.description}
                    </p>
                    <Button
                      className={
                        feature.buttonVariant === "primary"
                          ? "bg-blue-600 hover:bg-blue-700 text-white"
                          : "border border-blue-600 text-blue-600 hover:bg-blue-50 bg-white"
                      }
                    >
                      {feature.buttonText}
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Right Side - Video */}
          <div className="flex items-center justify-center">
            <div className="w-full max-w-lg">
              <video
                key={activeIndex}
                autoPlay
                muted
                loop
                className="w-full h-auto rounded-lg shadow-lg"
                poster="/api/placeholder/600/400"
              >
                <source
                  src={
                    sectionFeatures[activeIndex >= 0 ? activeIndex : 0]
                      ?.videoUrl
                  }
                  type="video/webm"
                />
                <source
                  src={
                    sectionFeatures[activeIndex >= 0 ? activeIndex : 0]
                      ?.videoUrl
                  }
                  type="video/mp4"
                />
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        </div>

        {/* Bottom CTA Button */}
        {showCTAButton && (
          <div className="text-center mb-16">
            <Button
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg"
            >
              {sectionCtaButtonText}
            </Button>
          </div>
        )}
      </Container>
    </section>
  );
};

export default AILearningSection;
