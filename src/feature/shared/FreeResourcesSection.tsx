"use client";
/*
 * @Description: Free Resources Section Component
 * @Author: Devin
 * @Date: 2025-07-23
 */
import React from "react";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import FluxIcon from "@/components/fluxIcon";
import { cn } from "@/lib/utils";

interface ResourceItem {
  id: string;
  type: "Blog" | "Cheatsheet";
  title: string;
  url: string;
}

interface FreeResourcesSectionProps {
  title?: string;
  resources?: ResourceItem[];
  className?: string;
  containerSize?: "sm" | "md" | "lg" | "lg-plus" | "xl";
}

// Default System Design Free Resources
const defaultSystemDesignResources: ResourceItem[] = [
  {
    id: "1",
    type: "Blog",
    title: "Components of System Design",
    url: "#",
  },
  {
    id: "2",
    type: "Blog",
    title: "System Design Interview Questions",
    url: "#",
  },
  {
    id: "3",
    type: "Cheatsheet",
    title: "What is Availability in System Design?",
    url: "#",
  },
  {
    id: "4",
    type: "Blog",
    title: "System Design fundamentals",
    url: "#",
  },
  {
    id: "5",
    type: "Blog",
    title: "Amazon System design Interview Questions",
    url: "#",
  },
  {
    id: "6",
    type: "Cheatsheet",
    title: "System Design Building Blocks",
    url: "#",
  },
  {
    id: "7",
    type: "Cheatsheet",
    title: "What is Performance in System Design?",
    url: "#",
  },
  {
    id: "8",
    type: "Blog",
    title: "Distributed Systems",
    url: "#",
  },
  {
    id: "9",
    type: "Blog",
    title: "Scalable Systems",
    url: "#",
  },
  {
    id: "10",
    type: "Blog",
    title: "FAANG/MAANG System Design Interview Questions",
    url: "#",
  },
  {
    id: "11",
    type: "Blog",
    title: "Facebook System Design Interview Questions",
    url: "#",
  },
  {
    id: "12",
    type: "Cheatsheet",
    title: "Solve Any System Design Interview Question",
    url: "#",
  },
  {
    id: "13",
    type: "Blog",
    title: "Microsoft System Design Interview Questions",
    url: "#",
  },
  {
    id: "14",
    type: "Blog",
    title: "Uber Backend System Design",
    url: "#",
  },
  {
    id: "15",
    type: "Blog",
    title: "Google System Design Interview Questions",
    url: "#",
  },
];

const FreeResourcesSection: React.FC<FreeResourcesSectionProps> = ({
  title = "Free Resources",
  resources = defaultSystemDesignResources,
  className = "",
  containerSize = "lg",
}) => {
  const getTypeIcon = (type: "Blog" | "Cheatsheet") => {
    if (type === "Blog") {
      return <FluxIcon name="blog" className="w-4 h-4 mr-2" />;
    } else {
      return <FluxIcon name="cheatsheet" className="w-4 h-4 mr-2" />;
    }
  };

  return (
    <section className={cn("py-16 bg-gray-50", className)}>
      <Container size={containerSize}>
        <div className="text-center mb-12">
          <Typography variant="h2" className="text-3xl font-bold text-gray-900">
            {title}
          </Typography>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {resources.map((resource) => (
            <a
              key={resource.id}
              href={resource.url}
              className="flex items-center p-4 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-200 group"
            >
              <div className="flex items-center text-blue-600 group-hover:text-blue-700">
                {getTypeIcon(resource.type)}
                <span className="text-xs font-medium text-gray-500 mr-3">
                  {resource.type}
                </span>
              </div>
              <Typography
                variant="small"
                className="text-gray-900 group-hover:text-blue-700 font-medium flex-1"
              >
                {resource.title}
              </Typography>
            </a>
          ))}
        </div>
      </Container>
    </section>
  );
};

export default FreeResourcesSection;
