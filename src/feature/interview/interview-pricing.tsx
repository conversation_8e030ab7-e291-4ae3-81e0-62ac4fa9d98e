/*
 * @Description: Interview Pricing Section Component
 * @Author: <PERSON>
 * @Date: 2025-07-30
 */
"use client";

import React from "react";
import { Container } from "@/components/ui/container";
import PricingPlans from "@/feature/price/PricingPlans";

const InterviewPricing: React.FC = () => {
  return (
    <div className="py-16 bg-gray-100">
      <Container size="lg-plus">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Get the job you want. Keep growing once you're there.
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Access 1,450+ Courses, Projects & Cloud Labs to level up your career
          </p>
        </div>

        {/* Pricing Plans */}
        <PricingPlans />
      </Container>
    </div>
  );
};

export default InterviewPricing;
