/*
 * @Description: Interview Hero Section Component
 * @Author: <PERSON>
 * @Date: 2025-07-30
 */
"use client";

import React, { useState } from "react";
import FluxIcon from "@/components/fluxIcon";
import { Container } from "@/components/ui/container";

const InterviewHero: React.FC = () => {
  const [formData, setFormData] = useState({
    role: "Entry-Level Engineer",
    company: "Google",
    interviewDate: "I don't have an interview date",
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="py-16 lg:py-24 relative overflow-hidden">
      {/* SVG Background */}
      <div className="absolute inset-0  w-screen lg:-mt-1">
        <div className="w-full" style={{ height: "561px" }}>
          <svg
            viewBox="0 0 1440 561"
            fill="none"
            preserveAspectRatio="none"
            className="h-full w-full"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0 0H1440V328.046L0 525V0Z"
              style={{ fill: "#e0e0ff" }}
            ></path>
            <g>
              <path
                opacity="0.78"
                d="M1262.59 311.398C1262.59 310.414 1263.31 309.576 1264.28 309.422L1492.77 273.361C1493.95 273.175 1495.03 274.054 1495.08 275.245L1496.9 315.213C1496.95 316.236 1496.21 317.128 1495.2 317.281L1264.89 352.164C1263.68 352.347 1262.59 351.41 1262.59 350.186L1262.59 311.398Z"
                style={{ fill: "#06b6d4" }}
              ></path>
              <path
                opacity="0.78"
                d="M1297.07 342.796C1297.02 341.78 1297.74 340.89 1298.74 340.726L1475.74 311.878C1476.93 311.684 1478.02 312.578 1478.06 313.782L1479.39 351.233C1479.42 352.247 1478.69 353.128 1477.69 353.281L1301.06 380.162C1299.89 380.34 1298.82 379.465 1298.77 378.28L1297.07 342.796Z"
                style={{ fill: "#7775ff" }}
              ></path>
              <path
                opacity="0.78"
                d="M-313.051 578.702C-313.116 577.68 -312.398 576.774 -311.388 576.603L145.84 499.383C147.006 499.186 148.087 500.037 148.169 501.216L150.452 534.187C150.523 535.215 149.803 536.129 148.787 536.298L-308.681 612.713C-309.85 612.908 -310.931 612.05 -311.006 610.867L-313.051 578.702Z"
                style={{ fill: "#06b6d4" }}
              ></path>
              <path
                opacity="0.78"
                d="M-175.036 512.694C-175.036 511.716 -174.329 510.881 -173.364 510.721L172.213 453.372C173.405 453.174 174.499 454.071 174.539 455.279L176.02 499.992C176.053 500.992 175.343 501.862 174.357 502.03L-172.701 561.101C-173.922 561.309 -175.036 560.369 -175.036 559.13L-175.036 512.694Z"
                style={{ fill: "#7775ff" }}
              ></path>
            </g>
          </svg>
        </div>
      </div>

      <Container size="lg">
        <div className="text-center relative z-10">
          {/* Main Heading */}
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            <span className="text-indigo-600">Personalized</span>
            <span className="relative">
              <span className="absolute -top-2 -right-2 text-orange-500 text-2xl">
                ✦
              </span>
            </span>{" "}
            Interview Prep
          </h1>

          {/* Subtitle */}
          <p className="text-md  mb-4 max-w-4xl mx-auto">
            Skip the LeetCode grind with a custom roadmap that{" "}
            <span className="text-indigo-600 font-semibold">adapts</span> to
            your goals.
          </p>

          <p className="text-md mb-12 max-w-3xl mx-auto">
            Hands-on practice for{" "}
            <span className="font-semibold">Coding Interviews</span>,{" "}
            <span className="font-semibold">System Design</span>, and more.
          </p>

          {/* Form */}
          <div className="flex w-full justify-center px-2">
            <div
              className="relative w-full rounded-lg bg-white border border-gray-200 px-4 pb-6 pt-4 lg:px-8 lg:pb-10 lg:pt-6"
              style={{
                maxWidth: "425px",
                maxHeight: "430px",
                boxShadow: "0 0 4px rgba(0, 0, 0, .15), 8px 8px 0 #cccbff",
              }}
            >
              {/* Star decoration */}
              <FluxIcon
                name="star-decoration"
                width={18}
                height={19}
                className="absolute -top-2 right-8 h-4 w-4 fill-current text-orange-400 sm:right-12 lg:right-16"
              />

              <div className="flex w-full flex-col items-start justify-start gap-6 rounded-lg text-start">
                <div className="flex w-full flex-shrink-0 flex-col items-start space-y-4">
                  {/* Role Selection */}
                  <div className="w-full">
                    <div className="text-sm mb-2 font-bold">
                      Role of Interest
                    </div>
                    <div className="relative inline-block w-full">
                      <div className="relative flex">
                        <div className="w-full h-9 bg-white border border-gray-200 rounded px-2.5 py-2 min-w-32 flex cursor-pointer flex-row items-center justify-between text-gray-600">
                          <span className="text-gray-900">
                            Entry-Level Engineer
                          </span>
                          <FluxIcon
                            name="chevron-down"
                            width={24}
                            height={24}
                            className="h-6 w-6 cursor-pointer text-gray-400"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Company Selection */}
                  <div className="w-full">
                    <div className="text-sm mb-2 flex justify-between font-bold">
                      Companies
                    </div>
                    <div className="relative inline-block w-full">
                      <div className="w-full">
                        <div className="relative flex">
                          <div className="min-w-32 flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded bg-white pr-2.5 py-2 border border-gray-200">
                            <div className="flex h-full w-full items-center">
                              <span className="mx-4 mt-1">
                                <FluxIcon
                                  name="google"
                                  width={20}
                                  height={20}
                                />
                              </span>
                              <span className="text-sm text-gray-900 m-0 mt-0.5">
                                Google
                              </span>
                            </div>
                            <FluxIcon
                              name="chevron-down"
                              width={24}
                              height={24}
                              className="h-6 w-6 cursor-pointer text-gray-400"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Interview Date */}
                  <div className="w-full">
                    <div className="text-sm mb-2 font-bold">Interview Date</div>
                    <div className="react-datepicker-wrapper">
                      <div className="react-datepicker__input-container">
                        <div className="relative cursor-pointer bg-white border border-gray-200 m-0 flex h-9 w-full items-center rounded px-2.5 py-2">
                          <input
                            className="text-sm text-gray-900 m-0 w-full cursor-pointer border-none font-normal tracking-normal focus:outline-none bg-transparent"
                            placeholder="Select Interview Date"
                            readOnly
                            value="August 27, 2025"
                          />
                          <span className="mt-1">
                            <FluxIcon
                              name="calendar"
                              width={18}
                              height={20}
                              className="text-gray-400 stroke-transparent"
                            />
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="mt-2 flex w-fit cursor-pointer items-center gap-x-2">
                      <input
                        type="checkbox"
                        className="border-gray-300 h-4 w-4 rounded accent-indigo-500"
                        readOnly
                        aria-label="I don't have an interview date"
                      />
                      <span className="mt-1 text-sm">
                        I don't have an interview date
                      </span>
                    </div>
                  </div>
                </div>

                {/* Generate Button */}
                <div className="flex w-full flex-col gap-y-4">
                  <div className="mt-2 w-full">
                    <button className="bg-indigo-600 hover:bg-indigo-700 text-white h-9 w-full rounded py-1 flex items-center justify-center transition-colors">
                      <FluxIcon
                        name="generate-stars"
                        width={13}
                        height={14}
                        className="mr-2 h-4 w-4 fill-white"
                      />
                      <span>Generate my roadmap</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default InterviewHero;
