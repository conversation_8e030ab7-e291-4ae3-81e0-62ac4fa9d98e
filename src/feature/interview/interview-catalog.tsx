/*
 * @Description: Interview Catalog Section Component
 * @Author: <PERSON>
 * @Date: 2025-07-30
 */
"use client";

import React, { useState } from "react";
import { Container } from "@/components/ui/container";
import { Tabs, Ta<PERSON><PERSON>ist, Ta<PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";

// Mock data for interview prep courses organized by tabs
const interviewCourses = {
  "coding-data-structures": [
    {
      id: "html-frontend-interviews",
      title: "HTML for Front-end Interviews",
      description:
        "The ultimate guide to HTML front-end interviews. Developed by FAANG engineers, practice with real-world interview questions and coding challenges. Get interview-ready in just a few hours.",
      duration: "10 h",
      difficulty: "Intermediate",
    },
    {
      id: "javascript-interview-handbook",
      title: "The JavaScript Interview Handbook: 100+ Interview Questions",
      description:
        "The ultimate guide to JavaScript interviews. Developed by FAANG engineers, practice with real-world interview questions, gain confidence, and get interview-ready in just a few hours.",
      duration: "10 h",
      difficulty: "Intermediate",
    },
    {
      id: "css-frontend-interviews",
      title: "CSS for Front-end Interviews",
      description:
        "The ultimate guide to CSS interviews, developed by FAANG engineers. Prep faster and get interview-ready in hours with battle-tested, real-world questions and a structured approach to styling and advanced concepts.",
      duration: "7 h",
      difficulty: "Intermediate",
    },
  ],
  "system-api-design": [
    {
      id: "system-design-fundamentals",
      title: "System Design Fundamentals",
      description:
        "Master the fundamentals of system design with real-world examples. Learn scalability, reliability, and performance optimization techniques used by top tech companies.",
      duration: "15 h",
      difficulty: "Advanced",
    },
    {
      id: "api-design-patterns",
      title: "API Design Patterns",
      description:
        "Learn to design robust, scalable APIs. Master REST, GraphQL, and modern API design principles with hands-on examples and best practices.",
      duration: "12 h",
      difficulty: "Intermediate",
    },
  ],
  frontend: [
    {
      id: "react-interview-prep",
      title: "React Interview Preparation",
      description:
        "Comprehensive React interview preparation covering hooks, state management, performance optimization, and advanced patterns used in production applications.",
      duration: "14 h",
      difficulty: "Intermediate",
    },
    {
      id: "frontend-system-design",
      title: "Frontend System Design",
      description:
        "Learn to design scalable frontend applications. Cover component architecture, state management, performance optimization, and modern frontend patterns.",
      duration: "16 h",
      difficulty: "Advanced",
    },
  ],
};

const tabConfig = [
  {
    id: "coding-data-structures",
    label: "Coding & Data Structures",
  },
  {
    id: "system-api-design",
    label: "System & API design",
  },
  {
    id: "frontend",
    label: "Frontend",
  },
];

const InterviewCatalog: React.FC = () => {
  const [activeTab, setActiveTab] = useState("coding-data-structures");

  const handleCourseClick = (courseId: string) => {
    console.log("Course clicked:", courseId);
    // Navigate to course page
  };

  return (
    <div className="py-16 bg-gray-50">
      <Container size="lg-plus">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Explore Interview Prep Catalog
          </h2>
          <p className="text-xl text-gray-600">
            100+ courses & skill paths for every specialization.
          </p>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="flex w-full justify-start mb-8 bg-transparent border-b border-gray-200 rounded-none p-0 h-auto">
            {tabConfig.map((tab) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className="data-[state=active]:text-blue-600 data-[state=active]:border-blue-600 text-gray-500 font-medium py-3 px-6 rounded-none border-b-2 border-transparent hover:text-gray-700 transition-colors bg-transparent"
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Tab Content */}
          {tabConfig.map((tab) => (
            <TabsContent key={tab.id} value={tab.id} className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-24">
                {interviewCourses[tab.id as keyof typeof interviewCourses].map(
                  (course) => (
                    <SimpleCourseCard
                      key={course.id}
                      course={course}
                      onClick={() => handleCourseClick(course.id)}
                      className="hover:shadow-xl transition-all duration-300"
                    />
                  )
                )}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </Container>
    </div>
  );
};

export default InterviewCatalog;
