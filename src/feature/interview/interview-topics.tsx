/*
 * @Description: Interview Topics Section Component with Tabs
 * @Author: <PERSON>
 * @Date: 2025-07-30
 */
"use client";

import React, { useState } from "react";
import Image from "next/image";
import FluxIcon from "@/components/fluxIcon";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Container } from "@/components/ui/container";

// Interview topic tabs
const interviewTabs = [
  { id: "coding-interviews", label: "Coding Interviews" },
  { id: "system-design", label: "System Design" },
  { id: "api-design", label: "API Design" },
  { id: "ood-principles", label: "OOD Principles" },
  { id: "machine-learning", label: "Machine Learning" },
  { id: "engineering-manager", label: "Engineering Manager" },
  { id: "behavioral-interview", label: "Behavioral Interview" },
];

// Content for each tab
const tabContent = {
  "coding-interviews": {
    badge: "AI POWERED",
    title: "Coding Interview Patterns",
    description:
      "Learn 26 patterns. Unlock thousands of common coding interview questions — without getting lost in a maze of Leetcode practice problems. Developed by FAANG hiring managers to help you prepare for interviews at major tech companies.",
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=1200,quality=75/static/imgs/InterviewPrepLandingPage/codingInterviews.gif",
    primaryButton: {
      text: "Start Learning in Python",
      href: "/courses/grokking-coding-interview-in-python",
    },
    languages: [
      {
        name: "JavaScript",
        href: "/courses/grokking-coding-interview-in-javascript",
        icon: "/images/common/javascript.png",
      },
      {
        name: "C++",
        href: "/courses/grokking-coding-interview-in-cpp",
        icon: "/images/common/c.png",
      },
      {
        name: "Java",
        href: "/courses/grokking-coding-interview",
        icon: "/images/common/java.png",
      },
      {
        name: "Golang",
        href: "/courses/grokking-coding-interview-in-go",
        icon: "/images/common/golang.svg",
      },
    ],
  },
  "system-design": {
    badge: "AI POWERED",
    title: "Modern System Design Interviews",
    description:
      "System Design Interviews play a key role in determining your starting level and salary. Learn how to build distributed web-scale services with 13 real-world systems (e.g. YouTube, Uber, and more) and confidently approach design questions for interviews with this course developed by FAANG systems engineers.",
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=1200,quality=75/static/imgs/InterviewPrepLandingPage/systemDesign.gif",
    primaryButton: {
      text: "Start Learning System Design",
      href: "/courses/grokking-system-design",
    },
    languages: [],
  },
  "api-design": {
    badge: "AI POWERED",
    title: "API Design Interview",
    description:
      "Confidently tackle API Design and Product Design interviews with this hands-on course to master API design concepts and apply to real-world examples such as YouTube, Stripe, and Zoom. This is the first course anywhere of its kind, designed to give you a leg up on this popular new design interview format.",
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=1200,quality=75/static/imgs/InterviewPrepLandingPage/grokkingAPIDesign.svg",
    primaryButton: {
      text: "Preview Course",
      href: "/courses/grokking-api-design",
    },
    languages: [],
  },
  "ood-principles": {
    badge: "AI POWERED",
    title: "Low Level Design Interview Using OOD Principles",
    description:
      "Master design principles and patterns to ace the object-oriented design interview. Learn a bottom-up approach to break any design problem - with 20+ real-world systems (e.g. Amazon Locker Service, StackOverflow and more - into its fundamental parts).",
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=1200,quality=75/static/imgs/InterviewPrepLandingPage/grokkingLowLevelDesign.svg",
    primaryButton: {
      text: "Preview Course",
      href: "/courses/grokking-ood",
    },
    languages: [],
  },
  "machine-learning": {
    badge: "AI POWERED",
    title: "Machine Learning Interview",
    description:
      "Leverage System Design principles to efficiently approach ML interviews. Through this hands-on course, you will learn a 4-step process to confidently answer ML interview questions. Practice your skills through 5+ real-world applications.",
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=1200,quality=75/static/imgs/InterviewPrepLandingPage/grokkingMachineLearning.svg",
    primaryButton: {
      text: "Preview Course",
      href: "/courses/grokking-ml-design",
    },
    languages: [],
  },
  "engineering-manager": {
    badge: "AI POWERED",
    title: "Engineering Management & Leadership Interviews",
    description:
      "Through this course developed by an EM with experience leading dev teams at Lyft and Meta, you will learn to confidently manage people, projects, and cross-collaboration across organizations.",
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=1200,quality=75/static/imgs/InterviewPrepLandingPage/grokkingEngineeringManagement.svg",
    primaryButton: {
      text: "Preview Course",
      href: "/courses/grokking-eng-management",
    },
    languages: [],
  },
  "behavioral-interview": {
    badge: "AI POWERED",
    title: "Behavioral Interview",
    description:
      "The ultimate guide to behavioral interviews. Developed by FAANG engineers. Use real-world questions, video recording, and structured strategies to get interview-ready in just a few hours.",
    image:
      "https://www.educative.io/cdn-cgi/image/format=auto,width=1200,quality=75/static/imgs/InterviewPrepLandingPage/grokkingEngineeringManagement.svg",
    primaryButton: {
      text: "Preview Course",
      href: "/courses/grokking-behavioral-interview",
    },
    languages: [],
  },
};

const InterviewTopics: React.FC = () => {
  const [activeTab, setActiveTab] = useState("coding-interviews");

  const currentContent = tabContent[activeTab as keyof typeof tabContent];

  return (
    <div className="bg-white dark:bg-gray-900 flex items-center justify-center py-12">
      <Container size="lg-plus" className="py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-gray-D50">
            Not ready for a Personalized Interview Prep Plan? Explore interview
            topics
          </h1>
        </div>

        <div
          className="flex flex-col items-center justify-center"
          style={{ columnGap: "10%" }}
        >
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value)}
            className="w-full"
          >
            {/* Tab Navigation */}
            <div className=" flex h-20 w-full items-end justify-start  text-center lg:w-full">
              <TabsList
                className="grid w-full bg-transparent border-0 p-0 h-auto"
                style={{
                  gridTemplateColumns: `repeat(${interviewTabs.length}, 1fr)`,
                }}
              >
                {interviewTabs.map((tab) => (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="flex-1 cursor-pointer items-center justify-center border-0 border-b-2 border-solid pb-2.5 px-2 data-[state=active]:border-indigo-500  flex bg-transparent hover:bg-transparent data-[state=active]:bg-transparent "
                  >
                    <div className="flex items-center justify-center w-full">
                      <p className="navigation-text m-0 data-[state=active]:text-black data-[state=active]:dark:text-indigo-400 text-gray-D700 dark:text-gray-D50 text-center text-sm lg:text-base">
                        {tab.label}
                      </p>
                    </div>
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>

            {/* Tab Content */}
            <div className="mt-6 flex h-fit flex-col justify-center rounded-2xl border border-solid border-gray-D50 bg-white text-start text-gray-D1300 dark:border-blue-1000 dark:bg-indigo-1200 dark:bg-transparent dark:text-gray-D100 sm:mx-0 lg:w-full">
              {interviewTabs.map((tab) => (
                <TabsContent
                  key={tab.id}
                  value={tab.id}
                  className="transition-opacity duration-700 ease-in-out opacity-100 m-0 px-8 py-8"
                >
                  <div className="relative mx-4 flex flex-col py-6 lg:flex-row">
                    {/* Image Section - Mobile (Top) / Desktop (Right) */}
                    <div className="flex w-full flex-col justify-center lg:hidden">
                      <div className="my-6 flex justify-center">
                        <div style={{ zIndex: 1 }}>
                          <Image
                            alt={currentContent.title}
                            title={currentContent.title}
                            src={currentContent.image}
                            width={400}
                            height={300}
                            className="rounded-2xl w-full max-w-md"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Content Section - Left Side (60%) */}
                    <div className="flex w-full flex-col justify-start  lg:w-[60%] lg:pr-8">
                      {/* Badge */}
                      <div className="flex w-fit flex-row justify-center gap-x-3 rounded-2xl pt-1 text-center font-normal text-gray-400">
                        <FluxIcon
                          name="magic-wand"
                          className="mt-0.5 h-4 w-4"
                        />
                        <label>{currentContent.badge}</label>
                      </div>

                      {/* Title */}
                      <label className="text-4xl my-2 font-bold">
                        {currentContent.title}
                      </label>

                      {/* Description */}
                      <div className="body-medium mt-3 text-left ">
                        {currentContent.description}
                      </div>

                      {/* Primary Button */}
                      <div className="mt-8 flex items-center gap-x-7">
                        <a href={currentContent.primaryButton.href}>
                          <Button className="bg-blue-600 hover:bg-blue-700 text-white w-fit py-2.5 px-6 rounded-lg font-medium">
                            {currentContent.primaryButton.text}
                            <FluxIcon
                              name="arrow-right"
                              width={24}
                              height={24}
                              className="ml-2 h-6 w-6 fill-current"
                            />
                          </Button>
                        </a>
                      </div>

                      {/* Language Options */}
                      {currentContent.languages.length > 0 && (
                        <div className="mt-6">
                          <div className="flex flex-col flex-wrap gap-x-2 gap-y-4 lg:flex-row">
                            <div className="body-medium content-default-default mt-1">
                              Also available in
                            </div>
                            <div className="flex flex-row flex-wrap gap-2 lg:flex-nowrap">
                              {currentContent.languages.map((lang) => (
                                <a key={lang.name} href={lang.href}>
                                  <Button
                                    variant="outline"
                                    className="border-gray-300 bg-white hover:bg-gray-50 text-gray-700 items-center gap-x-2.5 rounded-3xl px-2.5 py-1.5 text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                                  >
                                    {lang.icon.startsWith("/images/") ? (
                                      lang.icon.includes("golang.svg") ? (
                                        <FluxIcon
                                          name="golang"
                                          width={20}
                                          height={20}
                                          className="w-5 h-5"
                                        />
                                      ) : (
                                        <img
                                          src={lang.icon}
                                          alt={lang.name}
                                          className="w-5 h-5"
                                        />
                                      )
                                    ) : (
                                      <span className="text-sm font-medium">
                                        {lang.icon}
                                      </span>
                                    )}
                                    <span>{lang.name}</span>
                                  </Button>
                                </a>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Image Section - Right Side (40%) */}
                    <div className="hidden lg:flex lg:w-[40%] justify-center items-center">
                      <div className="flex justify-center w-full">
                        <Image
                          alt={currentContent.title}
                          title={currentContent.title}
                          src={currentContent.image}
                          width={500}
                          height={400}
                          className="rounded-2xl w-full h-auto object-cover"
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>
              ))}
            </div>
          </Tabs>
        </div>
      </Container>
    </div>
  );
};

export default InterviewTopics;
