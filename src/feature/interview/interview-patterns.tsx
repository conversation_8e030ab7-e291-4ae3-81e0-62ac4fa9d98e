/*
 * @Description: Interview Patterns Hero Component (Based on <PERSON><PERSON> Interview Hero)
 * @Author: <PERSON>
 * @Date: 2025-07-30
 */
"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check as CheckIcon } from "lucide-react";
import FluxIcon from "@/components/fluxIcon";
import { Container } from "@/components/ui/container";

const InterviewPatterns: React.FC = () => {
  const handleStartLearning = () => {
    // Navigate to coding patterns course
    console.log("Start Learning clicked");
  };

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Main Hero Section - Left/Right Layout */}
      <div className="bg-fill-default  justify-center p-4  sm:h-full sm:px-8 sm:py-8 bg-[#f5f5ff]">
        <Container
          size="lg-plus"
          className=" grid grid-cols-1 lg:grid-cols-2 gap-24 items-center"
        >
          {/* Left Side - Content */}
          <div className="text-left  py-12">
            {/* AI-Powered Badge */}
            <div className="tailwind-hidden items-center gap-x-2 text-gray-L600 dark:text-gray-D50 sm:flex ">
              <FluxIcon
                name="ai-stars"
                width={14}
                height={14}
                className="h-4 w-4 rotate-180 fill-yellow-400 dark:fill-yellow-400"
              />
              <span
                className="mt-0.5 text-xs text-gray-L500"
                style={{
                  letterSpacing: "2.5px",
                  fontWeight: 700,
                }}
              >
                AI-POWERED
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="text-2xl md:text-3xl font-extrabold text-gray-900 my-4 ">
              <span
                className="bg-gradient-to-r from-indigo-500 to-fuchsia-700 mr-2 dark:from-indigo-400 dark:to-indigo-500"
                style={{
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                }}
              >
                Coding Interview
              </span>
              <span>for Software Engineers</span>
            </h1>

            {/* Subheading */}
            <p className="text-xl  mb-4 leading-relaxed">
              On-demand, personalized practice for every technical interview
              loop.
            </p>

            {/* Pattern Features with Checkmarks */}
            <div className="space-y-3 mb-8 ">
              {["Coding Interviews", "System Design", "MAANG+ Interviews"].map(
                (feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckIcon className="w-4 h-4 text-[#5553ff]" />
                    <span className="text-sm text-gray-700">{feature}</span>
                  </div>
                )
              )}
            </div>

            {/* CTA Button */}
            <Button
              size="lg"
              className="px-8 py-4 text-lg  bg-[#5553ff] hover:bg-[#4f46e5] shadow-lg"
              onClick={handleStartLearning}
            >
              Try For Free
            </Button>
          </div>

          {/* Right Side - Video */}
          <div className="relative">
            <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden">
              <video
                src="https://www.educative.io//static/imgs/MIHeroSection.webm"
                className="w-full h-64 md:h-80 lg:h-96 border-0"
                title="Coding Interview Patterns Demo"
                autoPlay
                playsInline
                loop
                muted
              />
            </div>
          </div>
        </Container>
      </div>
    </div>
  );
};

export default InterviewPatterns;
