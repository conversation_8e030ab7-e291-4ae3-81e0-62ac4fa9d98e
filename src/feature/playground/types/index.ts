// Playground相关的类型定义

export interface Language {
  value: string;
  label: string;
  icon: string;
}

export interface CodeTemplate {
  id: string;
  name: string;
  language: string;
  code: string;
}

export interface ExecutionResult {
  success: boolean;
  output: string;
  error?: string;
  executionTime?: number;
}

export interface PlaygroundState {
  selectedLanguage: string;
  code: string;
  output: string;
  isRunning: boolean;
  fileName: string;
  pythonReady: boolean;
  pythonLoading: boolean;
}

// CodeExec组件的配置接口
export interface CodeExecProps {
  // 基础配置
  defaultLanguage?: string;
  initialCode?: string;
  height?: string | number;
  width?: string | number;

  // 功能配置
  showLanguageSelector?: boolean;
  showFileName?: boolean;
  showToolbar?: boolean;
  enabledLanguages?: string[];

  // 样式配置
  theme?: "light" | "dark";
  className?: string;
  editorClassName?: string;
  outputClassName?: string;

  // 回调函数
  onCodeChange?: (code: string) => void;
  onLanguageChange?: (language: string) => void;
  onExecute?: (code: string, language: string) => void;
  onOutput?: (output: string) => void;

  // 布局配置
  layout?: "horizontal" | "vertical";
  outputPosition?: "right" | "bottom";
  showOutput?: boolean;
}
