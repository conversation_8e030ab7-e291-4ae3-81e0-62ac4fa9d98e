// 代码执行器工具函数

// JavaScript执行函数
export const executeJavaScript = async (code: string): Promise<string> => {
  try {
    // 创建一个安全的执行环境
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    let output = "";

    // 重写console方法来捕获输出
    console.log = (...args) => {
      output +=
        args
          .map((arg) =>
            typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg)
          )
          .join(" ") + "\n";
    };

    console.error = (...args) => {
      output +=
        "ERROR: " +
        args
          .map((arg) =>
            typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg)
          )
          .join(" ") +
        "\n";
    };

    console.warn = (...args) => {
      output +=
        "WARNING: " +
        args
          .map((arg) =>
            typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg)
          )
          .join(" ") +
        "\n";
    };

    const startTime = Date.now();

    try {
      // 使用Function构造器创建一个相对安全的执行环境
      const func = new Function(code);
      const result = func();

      // 如果函数有返回值，也显示出来
      if (result !== undefined) {
        output += `返回值: ${
          typeof result === "object"
            ? JSON.stringify(result, null, 2)
            : String(result)
        }\n`;
      }
    } catch (error) {
      output += `运行时错误: ${
        error instanceof Error ? error.message : String(error)
      }\n`;
    }

    const executionTime = Date.now() - startTime;
    output += `\n执行完成，耗时: ${executionTime}ms`;

    // 恢复原始console方法
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;

    return output || "代码执行完成，无输出";
  } catch (error) {
    return `执行错误: ${
      error instanceof Error ? error.message : String(error)
    }`;
  }
};

// Python执行函数（使用Pyodide）
export const executePython = async (
  code: string,
  pythonReady: boolean,
  pythonLoading: boolean
): Promise<string> => {
  try {
    // 检查Python环境状态
    if (pythonLoading) {
      return "Python环境正在加载中，请稍后再试...";
    }

    if (
      !pythonReady ||
      typeof window === "undefined" ||
      !(window as any).pyodide
    ) {
      return "Python环境未就绪，请等待加载完成或刷新页面重试";
    }

    const pyodide = (window as any).pyodide;
    const startTime = Date.now();

    // 重定向Python的stdout
    pyodide.runPython(`
import sys
from io import StringIO
sys.stdout = StringIO()
sys.stderr = StringIO()
    `);

    try {
      // 执行用户代码
      pyodide.runPython(code);

      // 获取输出
      const stdout = pyodide.runPython("sys.stdout.getvalue()");
      const stderr = pyodide.runPython("sys.stderr.getvalue()");

      const executionTime = Date.now() - startTime;

      let output = "";
      if (stdout) output += stdout;
      if (stderr) output += "错误: " + stderr;

      output += `\n执行完成，耗时: ${executionTime}ms`;

      return output || "代码执行完成，无输出";
    } catch (error) {
      const executionTime = Date.now() - startTime;
      return `Python执行错误: ${
        error instanceof Error ? error.message : String(error)
      }\n执行时间: ${executionTime}ms`;
    }
  } catch (error) {
    return `Python环境错误: ${
      error instanceof Error ? error.message : String(error)
    }`;
  }
};

// Go 在线编译服务
const executeGoOnline = async (code: string): Promise<string> => {
  try {
    const response = await fetch("https://play.golang.org/compile", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: `version=2&body=${encodeURIComponent(code)}&withVet=true`,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.Errors) {
      return `编译错误:\n${result.Errors}`;
    }

    if (result.Events) {
      let output = "";
      for (const event of result.Events) {
        if (event.Kind === "stdout" || event.Kind === "stderr") {
          output += event.Message;
        }
      }
      return output || "程序执行完成，无输出";
    }

    return "程序执行完成";
  } catch (error) {
    return `Go 在线编译服务暂时不可用\n错误信息: ${
      error instanceof Error ? error.message : String(error)
    }`;
  }
};

// C++ 在线编译服务（使用免费服务）
const executeCppOnline = async (code: string): Promise<string> => {
  try {
    // 尝试使用 Wandbox API（免费的在线编译服务）
    const wandboxResponse = await fetch(
      "https://wandbox.org/api/compile.json",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          compiler: "gcc-head",
          code: code,
          options: "warning,gnu++1z",
          stdin: "",
        }),
      }
    );

    if (wandboxResponse.ok) {
      const result = await wandboxResponse.json();
      console.log("Wandbox 响应:", result);

      if (result.compiler_error) {
        return `编译错误:\n${result.compiler_error}`;
      }

      if (result.program_error) {
        return `运行时错误:\n${result.program_error}`;
      }

      if (result.program_output) {
        return result.program_output;
      }

      return "程序执行完成，无输出";
    } else {
      throw new Error(`Wandbox API 错误: ${wandboxResponse.status}`);
    }
  } catch (error) {
    console.log("C++ 在线编译服务错误:", error);
    // 如果在线服务不可用，直接返回错误信息
    return `编译服务不可用\n错误信息: ${
      error instanceof Error ? error.message : String(error)
    }\n\n请检查网络连接或稍后重试。\n\n注意：由于网络限制，C++ 在线编译服务可能无法正常工作。`;
  }
};

// C++执行函数
export const executeCpp = async (code: string): Promise<string> => {
  return executeCppOnline(code);
};

// Go执行函数
export const executeGo = async (code: string): Promise<string> => {
  return executeGoOnline(code);
};

// Node.js执行函数（与JavaScript相同，但可以有不同的环境提示）
export const executeNodejs = async (code: string): Promise<string> => {
  try {
    const result = await executeJavaScript(code);
    return `Node.js 环境执行结果:\n${result}\n\n注意: 在浏览器环境中模拟 Node.js 执行，某些 Node.js 特定功能可能不可用。`;
  } catch (error) {
    return `Node.js 执行错误: ${
      error instanceof Error ? error.message : String(error)
    }`;
  }
};

// Java在线编译服务
const executeJavaOnline = async (code: string): Promise<string> => {
  // 尝试多个可能的编译器名称
  const compilers = ["jdk-22+36"];

  for (const compiler of compilers) {
    try {
      const wandboxResponse = await fetch(
        "https://wandbox.org/api/compile.json",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            compiler: compiler,
            code: code,
            options: "",
            stdin: "",
          }),
        }
      );

      if (wandboxResponse.ok) {
        const result = await wandboxResponse.json();
        console.log(`Java Wandbox 响应 (${compiler}):`, result);

        if (result.compiler_error) {
          return `编译错误:\n${result.compiler_error}`;
        }

        if (result.program_error) {
          return `运行时错误:\n${result.program_error}`;
        }

        if (result.program_output) {
          return result.program_output;
        }

        return "程序执行完成，无输出";
      }
    } catch (error) {
      console.log(`Java 编译器 ${compiler} 失败:`, error);
      continue; // 尝试下一个编译器
    }
  }

  return `Java 编译服务暂时不可用\n\n已尝试的编译器: ${compilers.join(
    ", "
  )}\n\n请稍后重试或检查网络连接。`;
};

// Java执行函数
export const executeJava = async (code: string): Promise<string> => {
  return executeJavaOnline(code);
};

// Rust在线编译服务
const executeRustOnline = async (code: string): Promise<string> => {
  try {
    const wandboxResponse = await fetch(
      "https://wandbox.org/api/compile.json",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          compiler: "1.82.0",
          code: code,
          options: "",
          stdin: "",
        }),
      }
    );

    if (wandboxResponse.ok) {
      const result = await wandboxResponse.json();
      console.log("Rust Wandbox 响应:", result);

      if (result.compiler_error) {
        return `编译错误:\n${result.compiler_error}`;
      }

      if (result.program_error) {
        return `运行时错误:\n${result.program_error}`;
      }

      if (result.program_output) {
        return result.program_output;
      }

      return "程序执行完成，无输出";
    } else {
      throw new Error(`Wandbox API 错误: ${wandboxResponse.status}`);
    }
  } catch (error) {
    console.log("Rust 在线编译服务错误:", error);
    return `Rust 编译服务不可用\n错误信息: ${
      error instanceof Error ? error.message : String(error)
    }\n\n请检查网络连接或稍后重试。`;
  }
};

// Rust执行函数
export const executeRust = async (code: string): Promise<string> => {
  return executeRustOnline(code);
};

// Swift在线编译服务
const executeSwiftOnline = async (code: string): Promise<string> => {
  try {
    const wandboxResponse = await fetch(
      "https://wandbox.org/api/compile.json",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          compiler: "swift-5.9",
          code: code,
          options: "",
          stdin: "",
        }),
      }
    );

    if (wandboxResponse.ok) {
      const result = await wandboxResponse.json();
      console.log("Swift Wandbox 响应:", result);

      if (result.compiler_error) {
        return `编译错误:\n${result.compiler_error}`;
      }

      if (result.program_error) {
        return `运行时错误:\n${result.program_error}`;
      }

      if (result.program_output) {
        return result.program_output;
      }

      return "程序执行完成，无输出";
    } else {
      throw new Error(`Wandbox API 错误: ${wandboxResponse.status}`);
    }
  } catch (error) {
    console.log("Swift 在线编译服务错误:", error);
    return `Swift 编译服务不可用\n错误信息: ${
      error instanceof Error ? error.message : String(error)
    }\n\n请检查网络连接或稍后重试。`;
  }
};

// Swift执行函数
export const executeSwift = async (code: string): Promise<string> => {
  return executeSwiftOnline(code);
};

// Kotlin在线编译服务
const executeKotlinOnline = async (code: string): Promise<string> => {
  try {
    const wandboxResponse = await fetch(
      "https://wandbox.org/api/compile.json",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          compiler: "6.0.1",
          code: code,
          options: "",
          stdin: "",
        }),
      }
    );

    if (wandboxResponse.ok) {
      const result = await wandboxResponse.json();
      console.log("Kotlin Wandbox 响应:", result);

      if (result.compiler_error) {
        return `编译错误:\n${result.compiler_error}`;
      }

      if (result.program_error) {
        return `运行时错误:\n${result.program_error}`;
      }

      if (result.program_output) {
        return result.program_output;
      }

      return "程序执行完成，无输出";
    } else {
      throw new Error(`Wandbox API 错误: ${wandboxResponse.status}`);
    }
  } catch (error) {
    console.log("Kotlin 在线编译服务错误:", error);
    return `Kotlin 编译服务不可用\n错误信息: ${
      error instanceof Error ? error.message : String(error)
    }\n\n请检查网络连接或稍后重试。`;
  }
};

// Kotlin执行函数
export const executeKotlin = async (code: string): Promise<string> => {
  return executeKotlinOnline(code);
};

// 主执行函数
export const executeCode = async (
  code: string,
  language: string,
  pythonReady: boolean,
  pythonLoading: boolean
): Promise<string> => {
  switch (language) {
    case "javascript":
      return executeJavaScript(code);
    case "cpp":
      return executeCpp(code);
    case "python":
      return executePython(code, pythonReady, pythonLoading);
    // case "java":
    //   return executeJava(code);
    // case "go":
    //   return executeGo(code);
    // case "rust":
    //   return executeRust(code);
    // case "swift":
    //   return executeSwift(code);
    // case "kotlin":
    //   return executeKotlin(code);
    // case "nodejs":
    //   return executeNodejs(code);
    default:
      return "不支持的编程语言";
  }
};
