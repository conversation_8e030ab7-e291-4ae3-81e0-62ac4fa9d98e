import { useState, useEffect } from 'react';

export const usePythonEnvironment = () => {
  const [pythonReady, setPythonReady] = useState(false);
  const [pythonLoading, setPythonLoading] = useState(false);

  useEffect(() => {
    const initPython = async () => {
      if (
        typeof window !== "undefined" &&
        !(window as any).pyodide &&
        !pythonLoading
      ) {
        setPythonLoading(true);
        try {
          // 动态加载Pyodide
          const script = document.createElement("script");
          script.src = "https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js";
          script.onload = async () => {
            try {
              const pyodide = await (window as any).loadPyodide({
                indexURL: "https://cdn.jsdelivr.net/pyodide/v0.24.1/full/",
              });
              (window as any).pyodide = pyodide;
              setPythonReady(true);
              setPythonLoading(false);
            } catch (error) {
              console.error("Failed to load Pyodide:", error);
              setPythonLoading(false);
            }
          };
          script.onerror = () => {
            console.error("Failed to load Pyodide script");
            setPythonLoading(false);
          };
          document.head.appendChild(script);
        } catch (error) {
          console.error("Error initializing Python:", error);
          setPythonLoading(false);
        }
      } else if ((window as any).pyodide) {
        setPythonReady(true);
      }
    };

    initPython();
  }, [pythonLoading]);

  return { pythonReady, pythonLoading };
};
