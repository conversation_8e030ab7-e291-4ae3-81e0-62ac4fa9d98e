/*
 * @Description: CodeExec组件的状态管理hook
 * @Author: Devin
 * @Date: 2025-08-07
 */
import { useState, useCallback, useEffect } from "react";
import { CodeExecProps } from "../types";
import { executeCode } from "../utils/codeExecutors";
import { usePythonEnvironment } from "./usePythonEnvironment";
import { FILE_EXTENSIONS } from "../constants/languages";

export const useCodeExec = (props: CodeExecProps = {}) => {
  const {
    defaultLanguage = "javascript",
    initialCode,
    enabledLanguages,
    onCodeChange,
    onLanguageChange,
    onExecute,
    onOutput,
  } = props;

  // 获取初始代码
  const getInitialCode = () => {
    return initialCode || "";
  };

  // 状态管理
  const [selectedLanguage, setSelectedLanguage] = useState(defaultLanguage);
  const [code, setCode] = useState(getInitialCode());
  const [output, setOutput] = useState("");
  const [isRunning, setIsRunning] = useState(false);
  // 获取文件名
  const getFileName = (lang: string) => {
    const extension = FILE_EXTENSIONS[lang] || "txt";
    if (lang === "javascript") {
      return `index.${extension}`;
    }
    return `main.${extension}`;
  };

  const [fileName, setFileName] = useState(getFileName(defaultLanguage));

  const { pythonReady, pythonLoading } = usePythonEnvironment();

  // 处理语言切换
  const handleLanguageChange = useCallback(
    (language: string) => {
      setSelectedLanguage(language);

      // 更新文件名
      setFileName(getFileName(language));

      // 触发回调
      onLanguageChange?.(language);
    },
    [onLanguageChange]
  );

  // 处理代码变更
  const handleCodeChange = useCallback(
    (newCode: string) => {
      setCode(newCode);
      onCodeChange?.(newCode);
    },
    [onCodeChange]
  );

  // 执行代码
  const handleRunCode = useCallback(async () => {
    setIsRunning(true);
    setOutput("Running code...");

    try {
      // 触发执行前回调
      onExecute?.(code, selectedLanguage);

      const result = await executeCode(
        code,
        selectedLanguage,
        pythonReady,
        pythonLoading
      );

      setOutput(result);
      onOutput?.(result);
    } catch (error) {
      const errorMessage = `执行错误: ${
        error instanceof Error ? error.message : String(error)
      }`;
      setOutput(errorMessage);
      onOutput?.(errorMessage);
    } finally {
      setIsRunning(false);
    }
  }, [code, selectedLanguage, pythonReady, pythonLoading, onExecute, onOutput]);

  // 停止执行
  const handleStopExecution = useCallback(() => {
    setIsRunning(false);
    const stopMessage = "Execution stopped by user.";
    setOutput(stopMessage);
    onOutput?.(stopMessage);
  }, [onOutput]);

  // 重置代码
  const handleReset = useCallback(() => {
    const resetCode = initialCode || "";
    setCode(resetCode);
    setOutput("");
    onCodeChange?.(resetCode);
  }, [initialCode, onCodeChange]);

  // 清空输出
  const clearOutput = useCallback(() => {
    setOutput("");
    onOutput?.("");
  }, [onOutput]);

  // 当默认语言或初始代码改变时更新状态
  useEffect(() => {
    if (defaultLanguage !== selectedLanguage) {
      handleLanguageChange(defaultLanguage);
    }
  }, [defaultLanguage, selectedLanguage, handleLanguageChange]);

  // 只在初始化时设置代码，不持续监听 initialCode 变化
  useEffect(() => {
    if (initialCode) {
      setCode(initialCode);
    }
  }, [initialCode]); // 移除 code 依赖，避免循环重置

  return {
    // 状态
    selectedLanguage,
    code,
    output,
    isRunning,
    fileName,
    pythonReady,
    pythonLoading,

    // 操作函数
    handleLanguageChange,
    handleCodeChange,
    handleRunCode,
    handleStopExecution,
    handleReset,
    clearOutput,
    setFileName,

    // 工具函数
    getAvailableLanguages: () => {
      if (enabledLanguages) {
        return enabledLanguages;
      }
      return ["javascript", "python", "cpp"]; // 默认支持的语言
    },
  };
};
