/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-31 17:28:36
 */
import { useState } from "react";
import { CODE_TEMPLATES } from "../constants/templates";
import { FILE_EXTENSIONS } from "../constants/languages";
import { executeCode } from "../utils/codeExecutors";
import { usePythonEnvironment } from "./usePythonEnvironment";

export const usePlayground = () => {
  const [selectedLanguage, setSelectedLanguage] = useState("javascript");
  const [code, setCode] = useState(CODE_TEMPLATES[0].code);
  const [output, setOutput] = useState("");
  const [isRunning, setIsRunning] = useState(false);
  const [fileName, setFileName] = useState("main.js");

  const { pythonReady, pythonLoading } = usePythonEnvironment();

  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language);
    const template =
      CODE_TEMPLATES.find((t) => t.language === language) || CODE_TEMPLATES[0];
    setCode(template.code);

    // Update file extension based on language
    setFileName(`main.${FILE_EXTENSIONS[language] || "txt"}`);
  };

  const handleRunCode = async () => {
    setIsRunning(true);
    setOutput("Running code...");

    try {
      const result = await executeCode(
        code,
        selectedLanguage,
        pythonReady,
        pythonLoading
      );
      setOutput(result);
    } catch (error) {
      setOutput(
        `执行错误: ${error instanceof Error ? error.message : String(error)}`
      );
    } finally {
      setIsRunning(false);
    }
  };

  const handleStopExecution = () => {
    setIsRunning(false);
    setOutput("Execution stopped by user.");
  };

  const handleReset = () => {
    const template =
      CODE_TEMPLATES.find((t) => t.language === selectedLanguage) ||
      CODE_TEMPLATES[0];
    setCode(template.code);
    setOutput("");
  };

  const loadTemplate = (template: any) => {
    setSelectedLanguage(template.language);
    setCode(template.code);
    setOutput("");
    handleLanguageChange(template.language);
  };

  return {
    // State
    selectedLanguage,
    code,
    output,
    isRunning,
    fileName,
    pythonReady,
    pythonLoading,

    // Actions
    setCode,
    setFileName,
    handleLanguageChange,
    handleRunCode,
    handleStopExecution,
    handleReset,
    loadTemplate,
  };
};
