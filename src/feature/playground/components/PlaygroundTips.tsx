import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Box } from "@/components/ui/box";

export const PlaygroundTips: React.FC = () => {
  return (
    <Card className="mt-3 bg-[#2d2d2d] rounded-[8px] border border-[#404040]">
      <CardContent className="p-4">
        <Typography variant="h6" className="text-white font-semibold mb-2">
          💡 Playground Tips
        </Typography>
        <Box className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <Typography variant="small" className="text-gray-400">
            • Use Ctrl+Enter (Cmd+Enter on Mac) to run your code quickly
          </Typography>
          <Typography variant="small" className="text-gray-400">
            • Save your work frequently using the save button
          </Typography>
          <Typography variant="small" className="text-gray-400">
            • Try different templates to explore various programming concepts
          </Typography>
          <Typography variant="small" className="text-gray-400">
            • Share your code with others using the share button
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};
