import React from "react";
import { Card } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Box } from "@/components/ui/box";

interface OutputPanelProps {
  output: string;
  isRunning: boolean;
}

export const OutputPanel: React.FC<OutputPanelProps> = ({
  output,
  isRunning,
}) => {
  return (
    <Card className="flex-1 min-h-[200px] lg:h-[350px] bg-gray-700 rounded-lg border border-gray-600 flex flex-col">
      <Box className="p-2 border-b border-gray-600 flex items-center gap-2">
        <Typography variant="h6" className="text-white font-semibold">
          Output
        </Typography>
        {isRunning && (
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        )}
      </Box>
      <Box className="flex-1 p-2 font-mono text-sm leading-relaxed text-gray-300 bg-gray-900 overflow-auto whitespace-pre-wrap">
        {output || "Click 'Run' to execute your code..."}
      </Box>
    </Card>
  );
};
