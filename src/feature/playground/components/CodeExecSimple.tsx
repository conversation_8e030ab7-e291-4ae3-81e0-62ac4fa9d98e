/*
 * @Description: 简化的代码执行组件 - 按照用户要求设计
 * @Author: Devin
 * @Date: 2025-08-07
 */
"use client";

import React, { useState, useRef, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Button } from "@/components/ui/button";
import { Editor } from "@monaco-editor/react";
import { cn } from "@/lib/utils";
import FluxIcon from "@/components/fluxIcon";

import { useCodeExec } from "../hooks/useCodeExec";
import { SUPPORTED_LANGUAGES, getMonacoLanguage } from "../constants/languages";

interface CodeExecSimpleProps {
  language?: string;
  code?: string;
  height?: string | number;
  theme?: "light" | "dark";
  className?: string;
  onCodeChange?: (code: string) => void;
  onOutput?: (output: string) => void;
}

export const CodeExecSimple: React.FC<CodeExecSimpleProps> = ({
  language = "javascript",
  code: initialCodeProp = "",
  height = "400px",
  theme = "dark",
  className,
  onCodeChange,
  onOutput,
}) => {
  const [hasExecuted, setHasExecuted] = useState(false);
  const [executionTime, setExecutionTime] = useState<number>(0);
  const [isOutputCollapsed, setIsOutputCollapsed] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const outputPanelRef = useRef<HTMLDivElement>(null);

  const {
    selectedLanguage,
    code,
    output,
    isRunning,
    fileName,
    handleCodeChange,
    handleRunCode: originalHandleRunCode,
  } = useCodeExec({
    defaultLanguage: language,
    initialCode: initialCodeProp,
    onCodeChange,
    onOutput,
    enabledLanguages: [language], // 只支持指定的语言
  });

  const currentLanguage = SUPPORTED_LANGUAGES.find(
    (l) => l.value === selectedLanguage
  );

  // 包装代码变化处理函数，确保回调被触发
  const handleCodeChangeWrapper = (newCode: string) => {
    handleCodeChange(newCode);
    // 确保外部回调被触发
    if (onCodeChange) {
      onCodeChange(newCode);
    }
  };

  // 复制代码到剪贴板
  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
    } catch (error) {
      console.error("Failed to copy code:", error);
    }
  };

  // 重置代码到初始状态
  const handleResetCode = () => {
    handleCodeChangeWrapper(initialCodeProp);
    setHasExecuted(false);
    setExecutionTime(0);
    setIsOutputCollapsed(false);
  };

  // 切换全屏模式
  const handleToggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 处理代码执行
  const handleRunCode = async () => {
    const startTime = Date.now();
    setHasExecuted(true);

    await originalHandleRunCode();

    const endTime = Date.now();
    setExecutionTime(endTime - startTime);
  };

  // 当输出面板显示时，滚动到顶部
  useEffect(() => {
    if (hasExecuted && outputPanelRef.current) {
      // 使用 setTimeout 确保 DOM 更新完成后再滚动
      setTimeout(() => {
        if (outputPanelRef.current) {
          // 滚动到输出面板，确保完全可见
          outputPanelRef.current.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
            inline: "nearest",
          });

          // 额外向上滚动一些像素，确保输出面板顶部完全可见
          setTimeout(() => {
            window.scrollBy(0, -50);
          }, 300);
        }
      }, 100);
    }
  }, [hasExecuted]);

  const containerStyle = {
    height: typeof height === "number" ? `${height}px` : height,
  };

  return (
    <div
      className={cn(
        "w-full",
        isFullscreen ? "fixed inset-0 z-50 bg-white" : "",
        className
      )}
      style={isFullscreen ? { height: "100vh" } : containerStyle}
    >
      <Card className={cn("border-[#d1d5db] h-full flex flex-col border pb-2")}>
        {/* 头部 - 语言标题和文件名 */}
        <div
          className={cn(
            "rounded-t-lg flex h-auto  w-full flex-wrap items-center gap-2 px-4 py-2"
          )}
        >
          <div className="flex items-center">
            <div className="mr-1 flex">
              {/* JavaScript 图标 */}
              {selectedLanguage === "javascript" && (
                <FluxIcon name="javascript" width={20} height={20} />
              )}
              {/* Python 图标 */}
              {selectedLanguage === "python" && (
                <FluxIcon
                  name="python"
                  width={20}
                  height={20}
                  color="#3776ab"
                />
              )}
              {/* C++ 图标 */}
              {selectedLanguage === "cpp" && (
                <FluxIcon name="cpp" width={20} height={20} color="#00599c" />
              )}
            </div>
            <span className={cn("text-[14px] font-['codicon']")}>
              {currentLanguage?.label || language}
            </span>
          </div>

          <div className="ml-auto flex justify-between space-x-1">
            <div className="flex items-center">
              <Button
                size="sm"
                variant="ghost"
                onClick={handleResetCode}
                className={cn(
                  "hover:bg-gray-200 h-6 w-6 p-0 rounded hover:cursor-pointer "
                )}
                title="重置代码"
              >
                <FluxIcon
                  name="reset"
                  width={24}
                  height={24}
                  color="currentColor"
                />
              </Button>
            </div>
            <div className="flex items-center">
              <Button
                size="sm"
                variant="ghost"
                onClick={handleToggleFullscreen}
                className={cn(
                  "hover:bg-gray-200 h-5 w-5 p-0 rounded  hover:cursor-pointer"
                )}
                title={isFullscreen ? "退出全屏" : "全屏"}
              >
                <FluxIcon
                  name="fullscreen"
                  width={24}
                  height={24}
                  color="currentColor"
                />
              </Button>
            </div>
          </div>
        </div>

        {/* 主体内容区域 - 包含文件名卡片、编辑器和输出面板 */}
        <div className="mx-4 flex-1 flex flex-col border rounded-lg overflow-hidden relative">
          {/* 文件名卡片 */}
          <div
            className={cn(
              "bg-[#f3f4f6] px-3 py-3 border-b flex items-center gap-2"
            )}
          >
            <span className="text-sm">
              <FluxIcon
                name="code"
                width={20}
                height={12}
                className="h-4 w-4 text-green-700 dark:text-green-500"
              />
            </span>
            <Typography variant="small" className={cn("font-mono text-sm")}>
              {fileName}
            </Typography>
          </div>

          {/* 代码编辑器 */}
          <div className="flex-1 relative">
            <Editor
              height="100%"
              language={getMonacoLanguage(selectedLanguage)}
              value={code}
              onChange={(value) => handleCodeChangeWrapper(value || "")}
              theme={theme === "dark" ? "vs-dark" : "light"}
              options={{
                fontSize: 14,
                fontFamily:
                  "'JetBrains Mono', 'Fira Code', 'Consolas', monospace",
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                wordWrap: "on",
                automaticLayout: true,
                tabSize: 2,
                insertSpaces: true,
                lineNumbers: "on",
                folding: true,
                glyphMargin: false,
                lineDecorationsWidth: 0,
                lineNumbersMinChars: 3,
                readOnly: false,
              }}
            />
          </div>

          {/* 右下角的运行和复制按钮 - 相对于主体内容区域定位 */}
          <div
            className={cn(
              "absolute right-4 flex items-center gap-2 z-[5]",
              hasExecuted && !isOutputCollapsed
                ? "bottom-6" // 输出面板展开时：281px(输出面板) + 16px(间距)
                : hasExecuted && isOutputCollapsed
                ? "bottom-6" // 输出面板折叠时：40px(头部) + 16px(间距)
                : "bottom-4" // 无输出面板时：正常位置
            )}
          >
            <Button
              size="sm"
              variant="ghost"
              onClick={handleCopyCode}
              className={cn(
                "h-8 px-2 backdrop-blur-sm",
                theme === "dark"
                  ? "bg-gray-800/80 hover:bg-gray-700/80 text-gray-300"
                  : "bg-white/80 hover:bg-gray-100/80 text-gray-700"
              )}
            >
              <FluxIcon name="copy" width={12} height={12} />
            </Button>
            {isRunning ? (
              <Button
                size="sm"
                disabled
                className="h-8 px-3 backdrop-blur-sm bg-gray-600/80"
              >
                Running...
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={handleRunCode}
                className="h-8 px-3 backdrop-blur-sm bg-green-600/90 hover:bg-green-700/90 text-white"
              >
                <FluxIcon name="play" width={12} height={12} className="mr-1" />
                Run
              </Button>
            )}
          </div>
        </div>
        {/* 输出面板 - 只在执行后显示 */}
        {hasExecuted && (
          <div
            ref={outputPanelRef}
            className={cn(
              "mt-2 border rounded-md mx-4 flex flex-col transition-all duration-300 ease-in-out overflow-hidden ",
              isOutputCollapsed ? "max-h-[40px]" : "max-h-[281px]"
            )}
          >
            {/* 输出头部 */}
            <div
              className={cn(
                "bg-[#f3f4f6] px-3 py-2 border-b flex items-center justify-between"
              )}
            >
              <div className="flex items-center gap-2">
                <Typography
                  variant="small"
                  className={cn("font-medium text-gray-700")}
                >
                  Output
                </Typography>
                {isRunning && (
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                )}
              </div>
              <div className="flex items-center gap-2">
                {executionTime > 0 && (
                  <Typography
                    variant="small"
                    className={cn("font-mono text-gray-700")}
                  >
                    {executionTime}ms
                  </Typography>
                )}
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setIsOutputCollapsed(!isOutputCollapsed)}
                  className={cn("h-6 w-6 p-0 hover:bg-gray-200")}
                >
                  {isOutputCollapsed ? (
                    <FluxIcon name="chevron-up" width={16} height={16} />
                  ) : (
                    <FluxIcon name="chevron-down" width={16} height={16} />
                  )}
                </Button>
              </div>
            </div>

            {/* 输出内容 */}
            {!isOutputCollapsed && (
              <div
                className={cn(
                  "flex-1 p-3 font-mono text-sm leading-relaxed overflow-auto"
                )}
              >
                <pre className="whitespace-pre-wrap">
                  {output || "No output"}
                </pre>
              </div>
            )}
          </div>
        )}
      </Card>
    </div>
  );
};
