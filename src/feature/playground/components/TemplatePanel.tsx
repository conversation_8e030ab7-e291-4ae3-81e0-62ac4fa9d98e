/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-07-31 17:29:29
 */
import React from "react";
import { Card } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import { Box } from "@/components/ui/box";
import { CODE_TEMPLATES } from "../constants/templates";
import { SUPPORTED_LANGUAGES } from "../constants/languages";
import { CodeTemplate } from "../types";

interface TemplatePanelProps {
  onLoadTemplate: (template: CodeTemplate) => void;
}

export const TemplatePanel: React.FC<TemplatePanelProps> = ({
  onLoadTemplate,
}) => {
  return (
    <Card className="bg-gray-700 rounded-lg border border-gray-600 flex-1 min-h-[200px] lg:h-[230px] overflow-hidden flex flex-col">
      <Box className="p-2 border-b border-gray-600">
        <Typography variant="h6" className="text-white font-semibold">
          Code Templates
        </Typography>
      </Box>
      <Box className="flex-1 overflow-auto">
        {CODE_TEMPLATES.map((template) => (
          <Box
            key={template.id}
            className="p-2 border-b border-gray-600 cursor-pointer transition-colors hover:bg-gray-600 last:border-b-0"
            onClick={() => onLoadTemplate(template)}
          >
            <Box className="flex items-center gap-2 mb-1">
              <Typography
                variant="h6"
                className="text-white font-semibold text-sm"
              >
                {template.name}
              </Typography>
              <Badge
                variant="outline"
                className="bg-gray-600 text-gray-400 text-xs"
              >
                {
                  SUPPORTED_LANGUAGES.find((l) => l.value === template.language)
                    ?.label
                }
              </Badge>
            </Box>
            <Typography
              variant="small"
              className="text-gray-400 text-xs font-mono truncate"
            >
              {template.code.split("\n")[0]}
            </Typography>
          </Box>
        ))}
      </Box>
    </Card>
  );
};
