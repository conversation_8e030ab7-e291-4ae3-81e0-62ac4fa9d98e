/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-31 17:30:05
 */
"use client";

import React from "react";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import { Box } from "@/components/ui/box";
import { Code as CodeIcon } from "lucide-react";

import { usePlayground } from "../hooks/usePlayground";
import { PlaygroundToolbar } from "./PlaygroundToolbar";
import { CodeEditor } from "./CodeEditor";
import { OutputPanel } from "./OutputPanel";
import { TemplatePanel } from "./TemplatePanel";
import { PlaygroundTips } from "./PlaygroundTips";

export const Playground: React.FC = () => {
  const {
    selectedLanguage,
    code,
    output,
    isRunning,
    fileName,
    pythonReady,
    pythonLoading,
    setCode,
    setFileName,
    handleLanguageChange,
    handleRunCode,
    handleStopExecution,
    handleReset,
    loadTemplate,
  } = usePlayground();

  return (
    <div className="bg-gray-900 min-h-screen pb-20 pt-4 text-white">
      <Container size="xl">
        {/* Header */}
        <div className="mb-6">
          <Typography
            variant="h2"
            className="font-bold text-white mb-2 flex items-center gap-2 border-0"
          >
            <CodeIcon className="h-8 w-8 text-blue-500" />
            Code Playground
          </Typography>
          <Typography variant="h6" className="text-gray-400 text-base">
            Write, run, and experiment with code in your browser
          </Typography>
        </div>

        {/* Toolbar */}
        <PlaygroundToolbar
          selectedLanguage={selectedLanguage}
          fileName={fileName}
          code={code}
          isRunning={isRunning}
          onLanguageChange={handleLanguageChange}
          onFileNameChange={setFileName}
          onRunCode={handleRunCode}
          onStopExecution={handleStopExecution}
          onReset={handleReset}
        />

        {/* Main Content */}
        <Box className="flex flex-col lg:flex-row gap-3 min-h-[600px] lg:h-[600px]">
          {/* Code Editor */}
          <Box className="flex-[1_1_100%] lg:flex-[1_1_60%] min-h-[400px] lg:h-full">
            <CodeEditor
              code={code}
              selectedLanguage={selectedLanguage}
              onCodeChange={setCode}
            />
          </Box>

          {/* Right Panel */}
          <Box className="flex flex-col gap-3 flex-[1_1_100%] lg:flex-[1_1_40%] min-h-[400px] lg:h-full">
            {/* Output */}
            <OutputPanel output={output} isRunning={isRunning} />

            {/* Templates */}
            <TemplatePanel onLoadTemplate={loadTemplate} />
          </Box>
        </Box>

        {/* Tips */}
        <PlaygroundTips />
      </Container>
    </div>
  );
};
