/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-31 17:29:11
 */
import React from "react";
import { useTranslation } from "react-i18next";
import { Card } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";
import { Badge } from "@/components/ui/badge";
import { Box } from "@/components/ui/box";
import { Editor } from "@monaco-editor/react";
import { SUPPORTED_LANGUAGES, getMonacoLanguage } from "../constants/languages";

interface CodeEditorProps {
  code: string;
  selectedLanguage: string;
  onCodeChange: (code: string) => void;
}

export const CodeEditor: React.FC<CodeEditorProps> = ({
  code,
  selectedLanguage,
  onCodeChange,
}) => {
  const { t } = useTranslation();
  const currentLanguage = SUPPORTED_LANGUAGES.find(
    (l) => l.value === selectedLanguage
  );

  return (
    <Card className="flex flex-col h-full min-h-[400px] bg-[#2d2d2d] border border-[#404040] rounded-md overflow-hidden">
      <Box className="p-2 border-b border-[#404040] flex items-center justify-between">
        <Typography variant="h6" className="text-white font-semibold">
          {t("playground.codeEditor.title")}
        </Typography>
        <Badge
          variant="default"
          className="bg-blue-500/20 text-blue-500 font-medium"
        >
          {currentLanguage?.label}
        </Badge>
      </Box>
      <Box className="flex-1 p-0 h-full">
        <Editor
          height="100%"
          language={getMonacoLanguage(selectedLanguage)}
          value={code}
          onChange={(value) => onCodeChange(value || "")}
          theme="vs-dark"
          options={{
            fontSize: 14,
            fontFamily: "'JetBrains Mono', 'Fira Code', 'Consolas', monospace",
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 2,
            insertSpaces: true,
            wordWrap: "on",
            lineNumbers: "on",
            renderLineHighlight: "line",
            selectOnLineNumbers: true,
            roundedSelection: false,
            readOnly: false,
            cursorStyle: "line",
            scrollbar: {
              vertical: "auto",
              horizontal: "auto",
              verticalScrollbarSize: 8,
              horizontalScrollbarSize: 8,
              alwaysConsumeMouseWheel: false,
            },
          }}
          className="min-h-[400px]"
        />
      </Box>
    </Card>
  );
};
