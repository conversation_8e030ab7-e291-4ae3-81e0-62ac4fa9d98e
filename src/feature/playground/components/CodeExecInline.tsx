/*
 * @Description: 内联代码执行组件 - 简化版本
 * @Author: Devin
 * @Date: 2025-08-07
 */
"use client";

import React from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Editor } from "@monaco-editor/react";
import { Play, Square, Copy, RotateCcw } from "lucide-react";
import { cn } from "@/lib/utils";

import { useCodeExec } from "../hooks/useCodeExec";
import { SUPPORTED_LANGUAGES, getMonacoLanguage } from "../constants/languages";

interface CodeExecInlineProps {
  language?: string;
  code?: string;
  height?: string | number;
  showOutput?: boolean;
  theme?: "light" | "dark";
  className?: string;
  onCodeChange?: (code: string) => void;
  onOutput?: (output: string) => void;
}

export const CodeExecInline: React.FC<CodeExecInlineProps> = ({
  language = "javascript",
  code: initialCode = "",
  height = "300px",
  showOutput = true,
  theme = "dark",
  className,
  onCodeChange,
  onOutput,
}) => {
  const {
    selectedLanguage,
    code,
    output,
    isRunning,
    handleCodeChange,
    handleRunCode,
    handleStopExecution,
    handleReset,
  } = useCodeExec({
    defaultLanguage: language,
    initialCode,
    onCodeChange,
    onOutput,
    enabledLanguages: [language], // 只支持指定的语言
  });

  const currentLanguage = SUPPORTED_LANGUAGES.find(l => l.value === selectedLanguage);

  // 复制代码到剪贴板
  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
    } catch (error) {
      console.error("Failed to copy code:", error);
    }
  };

  const containerStyle = {
    height: typeof height === "number" ? `${height}px` : height,
  };

  return (
    <div className={cn("w-full", className)} style={containerStyle}>
      <Card className={cn(
        "h-full flex flex-col overflow-hidden border",
        theme === "dark" ? "bg-[#2d2d2d] border-[#404040]" : "bg-white border-gray-200"
      )}>
        {/* 头部工具栏 */}
        <div className={cn(
          "p-2 border-b flex items-center justify-between",
          theme === "dark" ? "border-[#404040] bg-gray-800" : "border-gray-200 bg-gray-50"
        )}>
          <div className="flex items-center gap-2">
            <Badge 
              variant="default" 
              className="bg-blue-500/20 text-blue-500 font-medium text-xs"
            >
              {currentLanguage?.label}
            </Badge>
            <span className={cn(
              "text-xs",
              theme === "dark" ? "text-gray-400" : "text-gray-600"
            )}>
              {currentLanguage?.value}
            </span>
          </div>

          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={handleCopyCode}
              className="h-7 px-2"
            >
              <Copy className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleReset}
              className="h-7 px-2"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>
            {isRunning ? (
              <Button
                size="sm"
                variant="destructive"
                onClick={handleStopExecution}
                className="h-7 px-2"
              >
                <Square className="h-3 w-3 mr-1" />
                Stop
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={handleRunCode}
                className="h-7 px-2 bg-green-600 hover:bg-green-700"
              >
                <Play className="h-3 w-3 mr-1" />
                Run
              </Button>
            )}
          </div>
        </div>

        {/* 代码编辑器和输出区域 */}
        <div className="flex-1 flex flex-col">
          {/* 代码编辑器 */}
          <div className={cn(
            "flex-1",
            showOutput ? "min-h-[150px]" : ""
          )}>
            <Editor
              height="100%"
              language={getMonacoLanguage(selectedLanguage)}
              value={code}
              onChange={(value) => handleCodeChange(value || "")}
              theme={theme === "dark" ? "vs-dark" : "light"}
              options={{
                fontSize: 13,
                fontFamily: "'JetBrains Mono', 'Fira Code', 'Consolas', monospace",
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                wordWrap: "on",
                automaticLayout: true,
                tabSize: 2,
                insertSpaces: true,
                lineNumbers: "on",
                folding: false,
                glyphMargin: false,
                lineDecorationsWidth: 0,
                lineNumbersMinChars: 3,
              }}
            />
          </div>

          {/* 输出面板 */}
          {showOutput && (
            <>
              <div className={cn(
                "border-t px-2 py-1 flex items-center gap-2",
                theme === "dark" ? "border-[#404040] bg-gray-800" : "border-gray-200 bg-gray-50"
              )}>
                <span className={cn(
                  "text-xs font-medium",
                  theme === "dark" ? "text-gray-300" : "text-gray-700"
                )}>
                  Output
                </span>
                {isRunning && (
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                )}
              </div>
              <div className={cn(
                "min-h-[100px] max-h-[200px] p-2 font-mono text-xs leading-relaxed overflow-auto whitespace-pre-wrap border-t",
                theme === "dark" 
                  ? "text-gray-300 bg-gray-900 border-[#404040]" 
                  : "text-gray-700 bg-white border-gray-200"
              )}>
                {output || "Click 'Run' to execute your code..."}
              </div>
            </>
          )}
        </div>
      </Card>
    </div>
  );
};
