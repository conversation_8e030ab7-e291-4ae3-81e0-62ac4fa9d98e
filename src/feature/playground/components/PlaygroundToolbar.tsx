import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { TextField } from "@/components/ui/text-field";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { IconButton } from "@/components/ui/icon-button";
import { Box } from "@/components/ui/box";
import {
  Play as PlayIcon,
  Square as StopIcon,
  RotateCcw as RefreshIcon,
  Save as SaveIcon,
  Share as ShareIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  Maximize as FullscreenIcon,
} from "lucide-react";
import { SUPPORTED_LANGUAGES } from "../constants/languages";

interface PlaygroundToolbarProps {
  selectedLanguage: string;
  fileName: string;
  code: string;
  isRunning: boolean;
  onLanguageChange: (language: string) => void;
  onFileNameChange: (fileName: string) => void;
  onRunCode: () => void;
  onStopExecution: () => void;
  onReset: () => void;
}

export const PlaygroundToolbar: React.FC<PlaygroundToolbarProps> = ({
  selectedLanguage,
  fileName,
  code,
  isRunning,
  onLanguageChange,
  onFileNameChange,
  onRunCode,
  onStopExecution,
  onReset,
}) => {
  return (
    <Card className="mb-4 bg-gray-800 rounded-lg border border-gray-600">
      <CardContent className="p-3">
        <Box className="flex flex-col md:flex-row items-stretch md:items-center gap-2 justify-between">
          <Box className="flex items-center gap-2 flex-wrap">
            <div className="min-w-[150px]">
              <Select value={selectedLanguage} onValueChange={onLanguageChange}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {SUPPORTED_LANGUAGES.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      <Box className="flex items-center gap-1">
                        {lang.icon.startsWith("/images/") ? (
                          <img
                            src={lang.icon}
                            alt={lang.label}
                            className="w-4 h-4 object-contain"
                          />
                        ) : (
                          <span>{lang.icon}</span>
                        )}
                        {lang.label}
                      </Box>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <TextField
              value={fileName}
              onChange={(e) => onFileNameChange(e.target.value)}
              className="min-w-[120px] bg-gray-700 border-gray-600 text-white"
              placeholder="filename.js"
            />
          </Box>

          <Box className="flex items-center gap-1">
            <Button
              variant={isRunning ? "destructive" : "default"}
              onClick={isRunning ? onStopExecution : onRunCode}
              disabled={!code.trim()}
              className={`font-semibold ${
                isRunning
                  ? "bg-red-500 hover:bg-red-600 text-white"
                  : "bg-green-500 hover:bg-green-600 text-white"
              } disabled:bg-gray-700 disabled:text-gray-400`}
            >
              {isRunning ? (
                <StopIcon className="mr-2 h-4 w-4" />
              ) : (
                <PlayIcon className="mr-2 h-4 w-4" />
              )}
              {isRunning ? "Stop" : "Run"}
            </Button>

            <IconButton
              onClick={onReset}
              className="text-gray-400 hover:text-white"
              title="Reset to template"
            >
              <RefreshIcon />
            </IconButton>

            <IconButton
              className="text-gray-400 hover:text-white"
              title="Save code"
            >
              <SaveIcon />
            </IconButton>

            <IconButton
              className="text-gray-400 hover:text-white"
              title="Share code"
            >
              <ShareIcon />
            </IconButton>

            <IconButton
              className="text-gray-400 hover:text-white"
              title="Download code"
            >
              <DownloadIcon />
            </IconButton>

            <IconButton
              className="text-gray-400 hover:text-white"
              title="Settings"
            >
              <SettingsIcon />
            </IconButton>

            <IconButton
              className="text-gray-400 hover:text-white"
              title="Fullscreen"
            >
              <FullscreenIcon />
            </IconButton>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};
