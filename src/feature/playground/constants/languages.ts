/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-07-31 17:26:45
 */
import { Language } from "../types";

export const SUPPORTED_LANGUAGES: Language[] = [
  {
    value: "javascript",
    label: "JavaScript",
    icon: "/images/common/javascript.png",
  },
  {
    value: "python",
    label: "Python",
    icon: "/images/common/python.png",
  },
  {
    value: "cpp",
    label: "C++",
    icon: "⚡",
  },
  // {
  //   value: "nodejs",
  //   label: "Node.js",
  //   icon: "🟢",
  // },

  // {
  //   value: "java",
  //   label: "Java",
  //   icon: "☕",
  // },
  // {
  //   value: "go",
  //   label: "Go",
  //   icon: "🔵",
  // },
  // {
  //   value: "rust",
  //   label: "Rust",
  //   icon: "🦀",
  // },
  // {
  //   value: "swift",
  //   label: "Swift",
  //   icon: "🍎",
  // },
  // {
  //   value: "kotlin",
  //   label: "Kotlin",
  //   icon: "🟣",
  // },
];

export const FILE_EXTENSIONS: { [key: string]: string } = {
  javascript: "js",
  python: "py",
  cpp: "cpp",
  // nodejs: "js",
  // java: "java",
  // go: "go",
  // rust: "rs",
  // swift: "swift",
  // kotlin: "kt",
};

// Monaco Editor 语言映射
export const MONACO_LANGUAGE_MAP: { [key: string]: string } = {
  javascript: "javascript",
  python: "python",
  cpp: "cpp",
  // nodejs: "javascript",
  // java: "java",
  // go: "go",
  // rust: "rust",
  // swift: "swift",
  // kotlin: "kotlin",
};

// 获取 Monaco Editor 支持的语言标识符
export const getMonacoLanguage = (language: string): string => {
  return MONACO_LANGUAGE_MAP[language] || "plaintext";
};
