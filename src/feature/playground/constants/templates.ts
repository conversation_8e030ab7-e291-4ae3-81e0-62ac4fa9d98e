import { CodeTemplate } from "../types";

export const CODE_TEMPLATES: CodeTemplate[] = [
  {
    id: "hello-world-js",
    name: "Hello World (JS)",
    language: "javascript",
    code: `// Welcome to the Pageflux AI Playground!
// Write your JavaScript code here and click Run to execute

console.log("Hello, World!");

// Try modifying this code:
const name = "Developer";
console.log(\`Hello, \${name}!\`);

// You can also return values
function add(a, b) {
  return a + b;
}

console.log("5 + 3 =", add(5, 3));`,
  },
  {
    id: "js-advanced",
    name: "JavaScript Advanced",
    language: "javascript",
    code: `// Advanced JavaScript Examples
console.log("=== JavaScript Advanced Examples ===");

// 1. Array methods
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
const evens = numbers.filter(n => n % 2 === 0);
console.log("Original:", numbers);
console.log("Doubled:", doubled);
console.log("Evens:", evens);

// 2. Object manipulation
const person = { name: "<PERSON>", age: 30 };
const updatedPerson = { ...person, city: "New York" };
console.log("Person:", updatedPerson);

// 3. Async/Promise simulation
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

console.log("Starting async operation...");
// Note: setTimeout won't work in this environment, but the code structure is correct`,
  },
  {
    id: "python-basics",
    name: "Python Basics",
    language: "python",
    code: `# Welcome to Python Playground!
# Write your Python code here

def greet(name):
    return f"Hello, {name}!"

# Try running this code
print(greet("World"))
print(greet("Python"))

# Practice with lists and loops
numbers = [1, 2, 3, 4, 5]
print("\\nNumbers and their squares:")
for num in numbers:
    print(f"Number: {num}, Square: {num**2}")

# Dictionary example
person = {"name": "Alice", "age": 30, "city": "New York"}
print(f"\\nPerson info: {person['name']} is {person['age']} years old")`,
  },
  {
    id: "python-advanced",
    name: "Python Advanced",
    language: "python",
    code: `# Advanced Python Examples
print("=== Python Advanced Examples ===")

# 1. List comprehensions
numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
squares = [n**2 for n in numbers]
evens = [n for n in numbers if n % 2 == 0]
print(f"Squares: {squares}")
print(f"Evens: {evens}")

# 2. Classes and objects
class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def get_history(self):
        return self.history

calc = Calculator()
print(f"\\n5 + 3 = {calc.add(5, 3)}")
print(f"10 + 7 = {calc.add(10, 7)}")
print(f"History: {calc.get_history()}")

# 3. Lambda functions and map
nums = [1, 2, 3, 4, 5]
doubled = list(map(lambda x: x * 2, nums))
print(f"\\nDoubled: {doubled}")`,
  },
  {
    id: "cpp-hello",
    name: "C++ Hello World",
    language: "cpp",
    code: `#include <iostream>
#include <string>
#include <vector>

using namespace std;

int main() {
    // Basic output
    cout << "Hello, World!" << endl;
    cout << "Welcome to C++ Playground!" << endl;

    // Variables and basic operations
    int a = 10, b = 20;
    cout << "a = " << a << ", b = " << b << endl;
    cout << "a + b = " << (a + b) << endl;

    // String manipulation
    string name = "C++";
    cout << "Programming in " << name << " is fun!" << endl;

    // Vector example
    vector<int> numbers = {1, 2, 3, 4, 5};
    cout << "Numbers: ";
    for(int num : numbers) {
        cout << num << " ";
    }
    cout << endl;

    return 0;
}`,
  },
  {
    id: "cpp-advanced",
    name: "C++ Advanced",
    language: "cpp",
    code: `#include <iostream>
#include <vector>
#include <algorithm>
#include <string>

using namespace std;

// Class example
class Rectangle {
private:
    double width, height;
public:
    Rectangle(double w, double h) : width(w), height(h) {}

    double area() const {
        return width * height;
    }

    void display() const {
        cout << "Rectangle: " << width << "x" << height
             << ", Area: " << area() << endl;
    }
};

int main() {
    cout << "=== C++ Advanced Examples ===" << endl;

    // Object-oriented programming
    Rectangle rect(5.0, 3.0);
    rect.display();

    // STL containers and algorithms
    vector<int> numbers = {5, 2, 8, 1, 9, 3};
    cout << "\\nOriginal: ";
    for(int n : numbers) cout << n << " ";

    sort(numbers.begin(), numbers.end());
    cout << "\\nSorted: ";
    for(int n : numbers) cout << n << " ";
    cout << endl;

    // Lambda function
    auto square = [](int x) { return x * x; };
    cout << "\\nSquare of 7: " << square(7) << endl;

    return 0;
}`,
  },
  //   {
  //     id: "go-hello",
  //     name: "Go Hello World",
  //     language: "go",
  //     code: `package main

  // import (
  //     "fmt"
  //     "strings"
  // )

  // func main() {
  //     // Basic output
  //     fmt.Println("Hello, World!")
  //     fmt.Println("Welcome to Go Playground!")

  //     // Variables and basic operations
  //     a, b := 10, 20
  //     fmt.Printf("a = %d, b = %d\\n", a, b)
  //     fmt.Printf("a + b = %d\\n", a + b)

  //     // String manipulation
  //     name := "Go"
  //     fmt.Printf("Programming in %s is fun!\\n", name)

  //     // Slice example
  //     numbers := []int{1, 2, 3, 4, 5}
  //     fmt.Print("Numbers: ")
  //     for _, num := range numbers {
  //         fmt.Printf("%d ", num)
  //     }
  //     fmt.Println()

  //     // String operations
  //     text := "hello world"
  //     fmt.Printf("Uppercase: %s\\n", strings.ToUpper(text))
  // }`,
  //   },
  //   {
  //     id: "go-advanced",
  //     name: "Go Advanced",
  //     language: "go",
  //     code: `package main

  // import (
  //     "fmt"
  //     "sort"
  //     "strings"
  // )

  // // Struct example
  // type Person struct {
  //     Name string
  //     Age  int
  //     City string
  // }

  // // Method for Person
  // func (p Person) Introduce() string {
  //     return fmt.Sprintf("Hi, I'm %s, %d years old, from %s", p.Name, p.Age, p.City)
  // }

  // // Interface example
  // type Shape interface {
  //     Area() float64
  // }

  // type Rectangle struct {
  //     Width, Height float64
  // }

  // func (r Rectangle) Area() float64 {
  //     return r.Width * r.Height
  // }

  // func main() {
  //     fmt.Println("=== Go Advanced Examples ===")

  //     // Struct and methods
  //     person := Person{Name: "Alice", Age: 30, City: "New York"}
  //     fmt.Println(person.Introduce())

  //     // Interface usage
  //     var shape Shape = Rectangle{Width: 5.0, Height: 3.0}
  //     fmt.Printf("\\nRectangle area: %.2f\\n", shape.Area())

  //     // Slice operations
  //     numbers := []int{5, 2, 8, 1, 9, 3}
  //     fmt.Printf("\\nOriginal: %v\\n", numbers)

  //     sort.Ints(numbers)
  //     fmt.Printf("Sorted: %v\\n", numbers)

  //     // Map example
  //     colors := map[string]string{
  //         "red":   "#FF0000",
  //         "green": "#00FF00",
  //         "blue":  "#0000FF",
  //     }

  //     fmt.Println("\\nColors:")
  //     for name, hex := range colors {
  //         fmt.Printf("%s: %s\\n", strings.Title(name), hex)
  //     }

  //     // Goroutine simulation (won't actually run concurrently in this environment)
  //     fmt.Println("\\nNote: This is a simulation - real Go would support goroutines!")
  // }`,
  //   },
  //   {
  //     id: "nodejs-hello",
  //     name: "Node.js Hello World",
  //     language: "nodejs",
  //     code: `// Welcome to Node.js Playground!
  // // This runs JavaScript with Node.js runtime features

  // console.log("Hello from Node.js!");

  // // File system operations (simulated)
  // const fs = require('fs');
  // console.log("Node.js version:", process.version);
  // console.log("Platform:", process.platform);

  // // Working with modules
  // const path = require('path');
  // console.log("Current directory:", process.cwd());

  // // Environment variables
  // console.log("Environment:", process.env.NODE_ENV || 'development');

  // // Async operations
  // setTimeout(() => {
  //     console.log("Async operation completed!");
  // }, 100);

  // // Export example
  // module.exports = {
  //     greet: (name) => \`Hello, \${name} from Node.js!\`
  // };`,
  //   },
  //   {
  //     id: "java-hello",
  //     name: "Java Hello World",
  //     language: "java",
  //     code: `public class Main {
  //     public static void main(String[] args) {
  //         // Basic output
  //         System.out.println("Hello, World!");
  //         System.out.println("Welcome to Java Playground!");

  //         // Variables and basic operations
  //         int a = 10, b = 20;
  //         System.out.println("a = " + a + ", b = " + b);
  //         System.out.println("a + b = " + (a + b));

  //         // String manipulation
  //         String name = "Java";
  //         System.out.println("Programming in " + name + " is fun!");

  //         // Array example
  //         int[] numbers = {1, 2, 3, 4, 5};
  //         System.out.print("Numbers: ");
  //         for (int num : numbers) {
  //             System.out.print(num + " ");
  //         }
  //         System.out.println();

  //         // Method call
  //         greetUser("Developer");
  //     }

  //     public static void greetUser(String name) {
  //         System.out.println("Hello, " + name + "!");
  //     }
  // }`,
  //   },
  //   {
  //     id: "rust-hello",
  //     name: "Rust Hello World",
  //     language: "rust",
  //     code: `fn main() {
  //     // Basic output
  //     println!("Hello, World!");
  //     println!("Welcome to Rust Playground!");

  //     // Variables and basic operations
  //     let a = 10;
  //     let b = 20;
  //     println!("a = {}, b = {}", a, b);
  //     println!("a + b = {}", a + b);

  //     // String manipulation
  //     let name = "Rust";
  //     println!("Programming in {} is fun!", name);

  //     // Vector example
  //     let numbers = vec![1, 2, 3, 4, 5];
  //     print!("Numbers: ");
  //     for num in &numbers {
  //         print!("{} ", num);
  //     }
  //     println!();

  //     // Function call
  //     let result = add_numbers(5, 3);
  //     println!("5 + 3 = {}", result);

  //     // Pattern matching
  //     let value = Some(42);
  //     match value {
  //         Some(n) => println!("Found number: {}", n),
  //         None => println!("No number found"),
  //     }
  // }

  // fn add_numbers(x: i32, y: i32) -> i32 {
  //     x + y
  // }`,
  //   },
  //   {
  //     id: "swift-hello",
  //     name: "Swift Hello World",
  //     language: "swift",
  //     code: `import Foundation

  // // Basic output
  // print("Hello, World!")
  // print("Welcome to Swift Playground!")

  // // Variables and basic operations
  // let a = 10
  // let b = 20
  // print("a = \\(a), b = \\(b)")
  // print("a + b = \\(a + b)")

  // // String manipulation
  // let name = "Swift"
  // print("Programming in \\(name) is fun!")

  // // Array example
  // let numbers = [1, 2, 3, 4, 5]
  // print("Numbers: \\(numbers)")

  // // Dictionary example
  // let person = ["name": "Alice", "age": "30"]
  // print("Person: \\(person)")

  // // Function example
  // func greet(name: String) -> String {
  //     return "Hello, \\(name)!"
  // }

  // print(greet(name: "Developer"))

  // // Optional example
  // var optionalName: String? = "Swift"
  // if let name = optionalName {
  //     print("Unwrapped name: \\(name)")
  // }

  // // Class example
  // class Calculator {
  //     func add(_ a: Int, _ b: Int) -> Int {
  //         return a + b
  //     }
  // }

  // let calc = Calculator()
  // print("Calculator result: \\(calc.add(5, 3))")`,
  //   },
  //   {
  //     id: "kotlin-hello",
  //     name: "Kotlin Hello World",
  //     language: "kotlin",
  //     code: `fun main() {
  //     // Basic output
  //     println("Hello, World!")
  //     println("Welcome to Kotlin Playground!")

  //     // Variables and basic operations
  //     val a = 10
  //     val b = 20
  //     println("a = $a, b = $b")
  //     println("a + b = \${a + b}")

  //     // String manipulation
  //     val name = "Kotlin"
  //     println("Programming in $name is fun!")

  //     // List example
  //     val numbers = listOf(1, 2, 3, 4, 5)
  //     println("Numbers: $numbers")

  //     // Map example
  //     val person = mapOf("name" to "Alice", "age" to 30)
  //     println("Person: \${person["name"]} is \${person["age"]} years old")

  //     // Function call
  //     val result = addNumbers(5, 3)
  //     println("5 + 3 = $result")

  //     // Class usage
  //     val calc = Calculator()
  //     println("Calculator multiply: \${calc.multiply(4, 7)}")

  //     // Lambda expression
  //     val doubled = numbers.map { it * 2 }
  //     println("Doubled numbers: $doubled")

  //     // Null safety
  //     val nullableName: String? = "Kotlin"
  //     nullableName?.let {
  //         println("Safe call: $it")
  //     }
  // }

  // fun addNumbers(x: Int, y: Int): Int {
  //     return x + y
  // }

  // class Calculator {
  //     fun multiply(a: Int, b: Int): Int = a * b
  // }`,
  //   },
];
