"use client";

import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import FluxIcon from "@/components/fluxIcon";
import {
  Sheet,
  <PERSON>et<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Sheet<PERSON>itle,
  SheetDescription,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { usePreferences } from "@/hooks/usePreferences";

interface PreferencesDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function PreferencesDrawer({
  open,
  onOpenChange,
}: PreferencesDrawerProps) {
  const { t } = useTranslation();
  const {
    formData,
    setFormData,
    isLoading,
    isSaving,
    loadPreferences,
    savePreferences,
    isFormValid,
  } = usePreferences();

  const GENDER_OPTIONS_TRANSLATED = [
    { value: "male", label: t("preferences.genderOptions.male") },
    { value: "female", label: t("preferences.genderOptions.female") },
  ];

  const LANGUAGE_OPTIONS = [
    { value: "zh-CN", label: "中文" },
    { value: "en", label: "English" },
  ];

  const LEARNING_STYLE_OPTIONS = [
    { value: "visual", label: t("preferences.options.format.visual") },
    { value: "auditory", label: t("preferences.options.format.auditory") },
    {
      value: "kinesthetic",
      label: t("preferences.options.format.kinesthetic"),
    },
    { value: "reading", label: t("preferences.options.format.reading") },
  ];

  const STUDY_TIME_OPTIONS = [
    {
      value: "morning",
      label: t(
        "preferences.onboarding.steps.learningPreferences.preferredTimeOptions.morning"
      ),
    },
    {
      value: "afternoon",
      label: t(
        "preferences.onboarding.steps.learningPreferences.preferredTimeOptions.afternoon"
      ),
    },
    {
      value: "evening",
      label: t(
        "preferences.onboarding.steps.learningPreferences.preferredTimeOptions.evening"
      ),
    },
    {
      value: "night",
      label: t(
        "preferences.onboarding.steps.learningPreferences.preferredTimeOptions.night"
      ),
    },
    {
      value: "flexible",
      label: t(
        "preferences.onboarding.steps.learningPreferences.preferredTimeOptions.flexible"
      ),
    },
  ];

  const LEARNING_PACE_OPTIONS = [
    {
      value: "slow",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.slow"
      ),
    },
    {
      value: "moderate",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.moderate"
      ),
    },
    {
      value: "fast",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.fast"
      ),
    },
    {
      value: "intensive",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.intensive"
      ),
    },
  ];

  // Load existing preferences when drawer opens
  useEffect(() => {
    if (open) {
      loadPreferences();
    }
  }, [open, loadPreferences]);

  const handleSave = async () => {
    const success = await savePreferences();
    if (success) {
      onOpenChange(false);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="overflow-y-auto">
        <SheetHeader className="pb-6">
          <SheetTitle asChild>
            <div className="flex items-center gap-8 mb-2">
              <FluxIcon
                name="slider"
                className="w-6 h-6 text-blue-600 cursor-pointer"
                onClick={() => onOpenChange(false)}
              />
              <span className=" text-gray-900">{t("preferences.title")}</span>
            </div>
          </SheetTitle>
          <SheetDescription className="text-gray-600">
            {t("preferences.description")}
          </SheetDescription>
        </SheetHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Basic Information Section */}
            <div className="space-y-4">
              <Typography
                variant="h5"
                className="font-semibold text-gray-900 border-b pb-2"
              >
                {t("preferences.basicInfo.title")}
              </Typography>

              {/* Age */}
              <div>
                <Label
                  htmlFor="age"
                  className="text-sm font-medium text-gray-700"
                >
                  {t("preferences.basicInfo.age")} *
                </Label>
                <Input
                  id="age"
                  type="number"
                  value={formData.age}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, age: e.target.value }))
                  }
                  placeholder={t("preferences.placeholders.age")}
                  className="mt-1"
                />
              </div>

              {/* Gender */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t("preferences.basicInfo.gender")} *
                </Label>
                <Select
                  value={formData.gender}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, gender: value }))
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue
                      placeholder={t("preferences.placeholders.gender")}
                    />
                  </SelectTrigger>
                  <SelectContent className="w-[var(--radix-select-trigger-width)] max-w-none">
                    {GENDER_OPTIONS_TRANSLATED.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Preferred Language */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t("preferences.learningPreferences.language")} *
                </Label>
                <Select
                  value={formData.preferredLanguage}
                  onValueChange={(value) =>
                    setFormData((prev) => ({
                      ...prev,
                      preferredLanguage: value,
                    }))
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue
                      placeholder={t(
                        "preferences.placeholders.preferredLanguage"
                      )}
                    />
                  </SelectTrigger>
                  <SelectContent className="w-[var(--radix-select-trigger-width)] max-w-none">
                    {LANGUAGE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Educational Background Section */}
            <div className="space-y-4">
              <Typography
                variant="h5"
                className="font-semibold text-gray-900 border-b pb-2"
              >
                {t("preferences.onboarding.steps.education.title")}
              </Typography>

              {/* Education Experience */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t("preferences.onboarding.steps.education.educationLevel")} *
                </Label>
                <Select
                  value={formData.educationExperience}
                  onValueChange={(value) =>
                    setFormData((prev) => ({
                      ...prev,
                      educationExperience: value,
                    }))
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue
                      placeholder={t(
                        "preferences.placeholders.educationExperience"
                      )}
                    />
                  </SelectTrigger>
                  <SelectContent className="w-[var(--radix-select-trigger-width)] max-w-none">
                    <SelectItem value="high_school">
                      {t(
                        "preferences.onboarding.steps.education.educationOptions.highSchool"
                      )}
                    </SelectItem>
                    <SelectItem value="bachelor">
                      {t(
                        "preferences.onboarding.steps.education.educationOptions.bachelor"
                      )}
                    </SelectItem>
                    <SelectItem value="master">
                      {t(
                        "preferences.onboarding.steps.education.educationOptions.master"
                      )}
                    </SelectItem>
                    <SelectItem value="other">
                      {t(
                        "preferences.onboarding.steps.education.educationOptions.other"
                      )}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Major */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t("preferences.onboarding.steps.education.major")} *
                </Label>
                <Input
                  value={formData.major}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, major: e.target.value }))
                  }
                  placeholder={t("preferences.placeholders.major")}
                  className="mt-1"
                />
              </div>

              {/* Graduation Year */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t("preferences.onboarding.steps.education.graduationYear")} *
                </Label>
                <Input
                  type="number"
                  value={formData.graduationYear}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      graduationYear: e.target.value,
                    }))
                  }
                  placeholder={t("preferences.placeholders.graduationYear")}
                  className="mt-1"
                />
              </div>
            </div>

            {/* Professional Background Section */}
            <div className="space-y-4">
              <Typography
                variant="h5"
                className="font-semibold text-gray-900 border-b pb-2"
              >
                {t("preferences.onboarding.steps.career.title")}
              </Typography>

              {/* Current Role */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t("preferences.onboarding.steps.career.currentRole")} *
                </Label>
                <Input
                  value={formData.currentRole}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      currentRole: e.target.value,
                    }))
                  }
                  placeholder={t("preferences.placeholders.currentRole")}
                  className="mt-1"
                />
              </div>

              {/* Industry */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t("preferences.onboarding.steps.career.industry")} *
                </Label>
                <Input
                  value={formData.industry}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      industry: e.target.value,
                    }))
                  }
                  placeholder={t("preferences.placeholders.industry")}
                  className="mt-1"
                />
              </div>

              {/* Work Experience */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t("preferences.onboarding.steps.career.workExperience")} *
                </Label>
                <Input
                  type="number"
                  value={formData.workExperience}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      workExperience: e.target.value,
                    }))
                  }
                  placeholder={t("preferences.placeholders.workExperience")}
                  className="mt-1"
                />
              </div>
            </div>

            {/* Learning Preferences Section */}
            <div className="space-y-4">
              <Typography
                variant="h5"
                className="font-semibold text-gray-900 border-b pb-2"
              >
                {t("preferences.learningPreferences.title")}
              </Typography>

              {/* Learning Style */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t("preferences.learningPreferences.format")} *
                </Label>
                <Select
                  value={formData.learningStyle}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, learningStyle: value }))
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue
                      placeholder={t("preferences.placeholders.learningStyle")}
                    />
                  </SelectTrigger>
                  <SelectContent className="w-[var(--radix-select-trigger-width)] max-w-none">
                    {LEARNING_STYLE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Study Time Per Week */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t(
                    "preferences.onboarding.steps.learningPreferences.studyTime"
                  )}{" "}
                  *
                </Label>
                <Input
                  type="number"
                  value={formData.studyTimePerWeek}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      studyTimePerWeek: e.target.value,
                    }))
                  }
                  placeholder={t("preferences.placeholders.studyTimePerWeek")}
                  className="mt-1"
                />
              </div>

              {/* Preferred Study Time */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t("preferences.learningPreferences.duration")} *
                </Label>
                <Select
                  value={formData.preferredStudyTime}
                  onValueChange={(value) =>
                    setFormData((prev) => ({
                      ...prev,
                      preferredStudyTime: value,
                    }))
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue
                      placeholder={t(
                        "preferences.placeholders.preferredStudyTime"
                      )}
                    />
                  </SelectTrigger>
                  <SelectContent className="w-[var(--radix-select-trigger-width)] max-w-none">
                    {STUDY_TIME_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Learning Pace */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  {t("preferences.learningPreferences.pace")} *
                </Label>
                <Select
                  value={formData.learningPace}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, learningPace: value }))
                  }
                >
                  <SelectTrigger className="w-full">
                    <SelectValue
                      placeholder={t("preferences.placeholders.learningPace")}
                    />
                  </SelectTrigger>
                  <SelectContent className="w-[var(--radix-select-trigger-width)] max-w-none">
                    {LEARNING_PACE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Save Button */}
            <div className="pt-6 border-t">
              <Button
                onClick={handleSave}
                disabled={!isFormValid || isSaving}
                className="w-full"
              >
                {isSaving
                  ? t("preferences.messages.saving")
                  : t("preferences.buttons.save")}
              </Button>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
