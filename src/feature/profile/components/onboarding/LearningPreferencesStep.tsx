"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Label } from "@/components/ui/label";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Typography } from "@/components/ui/typography";
import { Card, CardContent } from "@/components/ui/card";

interface LearningPreferencesStepProps {
  formData: {
    learningStyle: string;
    studyTimePerWeek: string;
    preferredStudyTime: string;
    learningPace: string;
    learningGoals: string;
  };
  onFormDataChange: (
    data: Partial<LearningPreferencesStepProps["formData"]>
  ) => void;
}

export default function LearningPreferencesStep({
  formData,
  onFormDataChange,
}: LearningPreferencesStepProps) {
  const { t } = useTranslation();

  const LEARNING_STYLE_OPTIONS = [
    {
      value: "visual",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningStyleOptions.visual"
      ),
      description: t(
        "preferences.onboarding.steps.learningPreferences.learningStyleOptions.visualDesc"
      ),
      icon: "👁️",
    },
    {
      value: "auditory",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningStyleOptions.auditory"
      ),
      description: t(
        "preferences.onboarding.steps.learningPreferences.learningStyleOptions.auditoryDesc"
      ),
      icon: "👂",
    },
    {
      value: "kinesthetic",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningStyleOptions.kinesthetic"
      ),
      description: t(
        "preferences.onboarding.steps.learningPreferences.learningStyleOptions.kinestheticDesc"
      ),
      icon: "✋",
    },
    {
      value: "reading",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningStyleOptions.reading"
      ),
      description: t(
        "preferences.onboarding.steps.learningPreferences.learningStyleOptions.readingDesc"
      ),
      icon: "📚",
    },
  ];

  const STUDY_TIME_OPTIONS = [
    {
      value: "1-3",
      label: t(
        "preferences.onboarding.steps.learningPreferences.studyTimeOptions.1to3"
      ),
    },
    {
      value: "4-6",
      label: t(
        "preferences.onboarding.steps.learningPreferences.studyTimeOptions.4to6"
      ),
    },
    {
      value: "7-10",
      label: t(
        "preferences.onboarding.steps.learningPreferences.studyTimeOptions.7to10"
      ),
    },
    {
      value: "11-15",
      label: t(
        "preferences.onboarding.steps.learningPreferences.studyTimeOptions.11to15"
      ),
    },
    {
      value: "16+",
      label: t(
        "preferences.onboarding.steps.learningPreferences.studyTimeOptions.16plus"
      ),
    },
  ];

  const PREFERRED_TIME_OPTIONS = [
    {
      value: "morning",
      label: t(
        "preferences.onboarding.steps.learningPreferences.preferredTimeOptions.morning"
      ),
    },
    {
      value: "afternoon",
      label: t(
        "preferences.onboarding.steps.learningPreferences.preferredTimeOptions.afternoon"
      ),
    },
    {
      value: "evening",
      label: t(
        "preferences.onboarding.steps.learningPreferences.preferredTimeOptions.evening"
      ),
    },
    {
      value: "night",
      label: t(
        "preferences.onboarding.steps.learningPreferences.preferredTimeOptions.night"
      ),
    },
    {
      value: "flexible",
      label: t(
        "preferences.onboarding.steps.learningPreferences.preferredTimeOptions.flexible"
      ),
    },
  ];

  const LEARNING_PACE_OPTIONS = [
    {
      value: "slow",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.slow"
      ),
      description: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.slowDesc"
      ),
    },
    {
      value: "moderate",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.moderate"
      ),
      description: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.moderateDesc"
      ),
    },
    {
      value: "fast",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.fast"
      ),
      description: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.fastDesc"
      ),
    },
    {
      value: "intensive",
      label: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.intensive"
      ),
      description: t(
        "preferences.onboarding.steps.learningPreferences.learningPaceOptions.intensiveDesc"
      ),
    },
  ];

  const handleInputChange = (
    field: keyof LearningPreferencesStepProps["formData"],
    value: string
  ) => {
    onFormDataChange({ [field]: value });
  };

  return (
    <div className="max-w-xl mx-auto space-y-6">
      <div className="space-y-6">
        {/* Learning Style */}
        <div className="space-y-4">
          <Label className="text-sm font-medium text-gray-700">
            {t(
              "preferences.onboarding.steps.learningPreferences.learningStyle"
            )}{" "}
            *
          </Label>
          <div className="space-y-3">
            {LEARNING_STYLE_OPTIONS.map((option) => (
              <Card
                key={option.value}
                className={`cursor-pointer transition-all hover:shadow-sm ${
                  formData.learningStyle === option.value
                    ? "ring-2 ring-blue-500 bg-blue-50"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => handleInputChange("learningStyle", option.value)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Typography
                        variant="h6"
                        className="font-medium text-gray-900 mb-1"
                      >
                        {option.label}
                      </Typography>
                      <Typography variant="small" className="text-gray-600">
                        {option.description}
                      </Typography>
                    </div>
                    <div
                      className={`w-4 h-4 rounded-full border-2 ${
                        formData.learningStyle === option.value
                          ? "bg-blue-500 border-blue-500"
                          : "border-gray-300"
                      }`}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Study Time Per Week */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">
            {t("preferences.onboarding.steps.learningPreferences.studyTime")} *
          </Label>
          <Select
            value={formData.studyTimePerWeek}
            onValueChange={(value) =>
              handleInputChange("studyTimePerWeek", value)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue
                placeholder={t("preferences.placeholders.studyTimePerWeek")}
              />
            </SelectTrigger>
            <SelectContent>
              {STUDY_TIME_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Preferred Study Time */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">
            {t(
              "preferences.onboarding.steps.learningPreferences.preferredTime"
            )}{" "}
            *
          </Label>
          <Select
            value={formData.preferredStudyTime}
            onValueChange={(value) =>
              handleInputChange("preferredStudyTime", value)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue
                placeholder={t("preferences.placeholders.preferredStudyTime")}
              />
            </SelectTrigger>
            <SelectContent>
              {PREFERRED_TIME_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Learning Pace */}
        <div className="space-y-4">
          <Label className="text-sm font-medium text-gray-700">
            {t("preferences.onboarding.steps.learningPreferences.learningPace")}{" "}
            *
          </Label>
          <div className="space-y-3">
            {LEARNING_PACE_OPTIONS.map((option) => (
              <Card
                key={option.value}
                className={`cursor-pointer transition-all hover:shadow-sm ${
                  formData.learningPace === option.value
                    ? "ring-2 ring-blue-500 bg-blue-50"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => handleInputChange("learningPace", option.value)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Typography
                        variant="h6"
                        className="font-medium text-gray-900 mb-1"
                      >
                        {option.label}
                      </Typography>
                      <Typography variant="small" className="text-gray-600">
                        {option.description}
                      </Typography>
                    </div>
                    <div
                      className={`w-4 h-4 rounded-full border-2 ${
                        formData.learningPace === option.value
                          ? "bg-blue-500 border-blue-500"
                          : "border-gray-300"
                      }`}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
