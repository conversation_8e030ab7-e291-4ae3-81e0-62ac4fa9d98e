"use client";

import React, { useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import MultiStepForm, { StepConfig } from "@/components/ui/multi-step-form";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import BasicInfoStep from "./BasicInfoStep";
import EducationStep from "./EducationStep";
import CareerStep from "./CareerStep";
import LearningPreferencesStep from "./LearningPreferencesStep";
import GoalSettingStep from "./GoalSettingStep";
import { profileService } from "@/service/profile.service";
import { toast } from "sonner";

interface OnboardingFormProps {
  open: boolean;
  onComplete: () => void;
  onCancel?: () => void;
}

interface OnboardingFormData {
  // Basic Information
  age: string;
  gender: string;
  preferred_language: string;

  // Educational Background
  education_experience: string;
  major: string;
  graduation_year: string;

  // Professional Background
  current_role: string;
  industry: string;
  work_experience: string;

  // Learning Preferences
  learning_style: string;
  study_time_per_week: string;
  preferred_study_time: string;
  learning_pace: string;
  learning_goals: string;
}

const initialFormData: OnboardingFormData = {
  // Basic Information
  age: "",
  gender: "",
  preferred_language: "zh-CN",

  // Educational Background
  education_experience: "",
  major: "",
  graduation_year: "",

  // Professional Background
  current_role: "",
  industry: "",
  work_experience: "",

  // Learning Preferences
  learning_style: "",
  study_time_per_week: "",
  preferred_study_time: "",
  learning_pace: "",
  learning_goals: "",
};

export default function OnboardingForm({
  open,
  onComplete,
  onCancel,
}: OnboardingFormProps) {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<OnboardingFormData>(initialFormData);

  const handleFormDataChange = useCallback((stepData: any) => {
    // Map component field names to backend field names
    const mappedData: Partial<OnboardingFormData> = {};

    Object.entries(stepData).forEach(([key, value]) => {
      switch (key) {
        case "preferredLanguage":
          mappedData.preferred_language = value as string;
          break;
        case "educationExperience":
          mappedData.education_experience = value as string;
          break;
        case "graduationYear":
          mappedData.graduation_year = value as string;
          break;
        case "currentRole":
          mappedData.current_role = value as string;
          break;
        case "workExperience":
          mappedData.work_experience = value as string;
          break;
        case "learningStyle":
          mappedData.learning_style = value as string;
          break;
        case "studyTimePerWeek":
          mappedData.study_time_per_week = value as string;
          break;
        case "preferredStudyTime":
          mappedData.preferred_study_time = value as string;
          break;
        case "learningPace":
          mappedData.learning_pace = value as string;
          break;
        case "learningGoals":
          mappedData.learning_goals = value as string;
          break;
        default:
          (mappedData as any)[key] = value;
          break;
      }
    });

    setFormData((prev) => ({ ...prev, ...mappedData }));
  }, []);

  // Validation functions for each step
  const isStep1Valid = (): boolean => {
    return !!(
      formData.age &&
      parseInt(formData.age) >= 13 &&
      formData.gender &&
      formData.preferred_language
    );
  };

  const isStep2Valid = (): boolean => {
    return !!(
      formData.education_experience &&
      formData.major &&
      formData.graduation_year
    );
  };

  const isStep3Valid = (): boolean => {
    return !!(
      formData.current_role &&
      formData.industry &&
      formData.work_experience
    );
  };

  const isStep4Valid = (): boolean => {
    return !!(
      formData.learning_style &&
      formData.study_time_per_week &&
      formData.preferred_study_time &&
      formData.learning_pace
    );
  };

  const isStep5Valid = (): boolean => {
    return !!formData.learning_goals.trim();
  };

  const handleComplete = async () => {
    try {
      // Prepare profile data for API
      const profileData = {
        // Basic Information
        age: formData.age ? parseInt(formData.age) : undefined,
        gender: formData.gender || undefined,
        preferred_language: formData.preferred_language || undefined,

        // Educational Background
        education_experience: formData.education_experience || undefined,
        major: formData.major || undefined,
        graduation_year: formData.graduation_year
          ? parseInt(formData.graduation_year)
          : undefined,

        // Professional Background
        current_role: formData.current_role || undefined,
        industry: formData.industry || undefined,
        work_experience: formData.work_experience
          ? parseInt(formData.work_experience)
          : undefined,

        // Learning Preferences
        learning_style: formData.learning_style || undefined,
        study_time_per_week: formData.study_time_per_week
          ? parseInt(formData.study_time_per_week.split("-")[0])
          : undefined,
        preferred_study_time: formData.preferred_study_time || undefined,
        learning_pace: formData.learning_pace || undefined,
      };

      // Try to update first, if it fails, create new profile
      try {
        await profileService.updateStaticProfile(profileData);
      } catch (updateError) {
        // If update fails, try to create
        await profileService.createStaticProfile(profileData);
      }

      toast.success(t("preferences.onboarding.success.message"));
      onComplete();
    } catch (error) {
      console.error("Error saving onboarding data:", error);
      toast.error(t("preferences.messages.saveError"));
    }
  };

  const steps: StepConfig[] = [
    {
      id: "basic-info",
      title: t("preferences.onboarding.steps.basicInfo.title"),
      description: t("preferences.onboarding.steps.basicInfo.description"),
      component: (
        <BasicInfoStep
          formData={{
            age: formData.age,
            gender: formData.gender,
            preferredLanguage: formData.preferred_language,
          }}
          onFormDataChange={handleFormDataChange}
        />
      ),
      isValid: isStep1Valid(),
      canSkip: false,
    },
    {
      id: "education",
      title: t("preferences.onboarding.steps.education.title"),
      description: t("preferences.onboarding.steps.education.description"),
      component: (
        <EducationStep
          formData={{
            educationExperience: formData.education_experience,
            major: formData.major,
            graduationYear: formData.graduation_year,
          }}
          onFormDataChange={handleFormDataChange}
        />
      ),
      isValid: isStep2Valid(),
      canSkip: false,
    },
    {
      id: "career",
      title: t("preferences.onboarding.steps.career.title"),
      description: t("preferences.onboarding.steps.career.description"),
      component: (
        <CareerStep
          formData={{
            currentRole: formData.current_role,
            industry: formData.industry,
            workExperience: formData.work_experience,
          }}
          onFormDataChange={handleFormDataChange}
        />
      ),
      isValid: isStep3Valid(),
      canSkip: false,
    },
    {
      id: "learning-preferences",
      title: t("preferences.onboarding.steps.learningPreferences.title"),
      description: t(
        "preferences.onboarding.steps.learningPreferences.description"
      ),
      component: (
        <LearningPreferencesStep
          formData={{
            learningStyle: formData.learning_style,
            studyTimePerWeek: formData.study_time_per_week,
            preferredStudyTime: formData.preferred_study_time,
            learningPace: formData.learning_pace,
            learningGoals: formData.learning_goals,
          }}
          onFormDataChange={handleFormDataChange}
        />
      ),
      isValid: isStep4Valid(),
      canSkip: false,
    },
    {
      id: "goal-setting",
      title: t("preferences.onboarding.steps.goals.title"),
      description: t("preferences.onboarding.steps.goals.description"),
      component: (
        <GoalSettingStep
          formData={{
            learningGoals: formData.learning_goals,
          }}
          onFormDataChange={handleFormDataChange}
        />
      ),
      isValid: isStep5Valid(),
      canSkip: false,
    },
  ];

  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent className="max-w-[80vw] h-[80vh] overflow-y-auto p-0">
        <div
          className="p-8 h-full bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "url('/pre-assessment-modal-bg.svg')",
          }}
        >
          <MultiStepForm
            steps={steps}
            currentStep={currentStep}
            onStepChange={setCurrentStep}
            onComplete={handleComplete}
            onCancel={onCancel}
            allowSkipSteps={true}
            completeButtonText={t("preferences.onboarding.navigation.complete")}
            nextButtonText={t("preferences.onboarding.navigation.next")}
            previousButtonText={t("preferences.onboarding.navigation.previous")}
            className="pt-12"
            title={t("preferences.onboarding.title")}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
