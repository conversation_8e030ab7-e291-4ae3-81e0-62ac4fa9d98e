"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface BasicInfoStepProps {
  formData: {
    age: string;
    gender: string;
    preferredLanguage: string;
  };
  onFormDataChange: (data: Partial<BasicInfoStepProps["formData"]>) => void;
}

export default function BasicInfoStep({
  formData,
  onFormDataChange,
}: BasicInfoStepProps) {
  const { t } = useTranslation();

  const GENDER_OPTIONS = [
    { value: "male", label: t("preferences.genderOptions.male") },
    { value: "female", label: t("preferences.genderOptions.female") },
  ];

  const LANGUAGE_OPTIONS = [
    { value: "zh-CN", label: "中文" },
    { value: "en", label: "English" },
  ];

  const handleInputChange = (
    field: keyof BasicInfoStepProps["formData"],
    value: string
  ) => {
    onFormDataChange({ [field]: value });
  };

  return (
    <div className="max-w-xl mx-auto space-y-6">
      <div className="space-y-6">
        {/* Age */}
        <div className="space-y-2">
          <Label htmlFor="age" className="text-sm font-medium text-gray-700">
            {t("preferences.onboarding.steps.basicInfo.age")} *
          </Label>
          <Input
            id="age"
            type="number"
            min="13"
            max="100"
            value={formData.age}
            onChange={(e) => handleInputChange("age", e.target.value)}
            placeholder={t("preferences.placeholders.age")}
            className="w-full"
          />
        </div>

        {/* Gender */}
        <div className="space-y-2">
          <Label htmlFor="gender" className="text-sm font-medium text-gray-700">
            {t("preferences.onboarding.steps.basicInfo.gender")} *
          </Label>
          <Select
            value={formData.gender}
            onValueChange={(value) => handleInputChange("gender", value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder={t("preferences.placeholders.gender")} />
            </SelectTrigger>
            <SelectContent>
              {GENDER_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Preferred Language */}
        <div className="space-y-2">
          <Label
            htmlFor="language"
            className="text-sm font-medium text-gray-700"
          >
            {t("preferences.onboarding.steps.basicInfo.preferredLanguage")} *
          </Label>
          <Select
            value={formData.preferredLanguage}
            onValueChange={(value) =>
              handleInputChange("preferredLanguage", value)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue
                placeholder={t("preferences.placeholders.preferredLanguage")}
              />
            </SelectTrigger>
            <SelectContent>
              {LANGUAGE_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
