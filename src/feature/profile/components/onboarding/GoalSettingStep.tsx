"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Typography } from "@/components/ui/typography";

interface GoalSettingStepProps {
  formData: {
    learningGoals: string;
  };
  onFormDataChange: (data: Partial<GoalSettingStepProps["formData"]>) => void;
}

export default function GoalSettingStep({
  formData,
  onFormDataChange,
}: GoalSettingStepProps) {
  const { t } = useTranslation();

  const handleInputChange = (
    field: keyof GoalSettingStepProps["formData"],
    value: string
  ) => {
    onFormDataChange({ [field]: value });
  };

  return (
    <div className="max-w-xl mx-auto space-y-6">
      {/* Learning Goals */}
      <div className="space-y-2">
        <Label
          htmlFor="learningGoals"
          className="text-sm font-medium text-gray-700"
        >
          {t("preferences.onboarding.steps.goals.learningGoals")} *
        </Label>
        <Textarea
          id="learningGoals"
          value={formData.learningGoals}
          onChange={(e) => handleInputChange("learningGoals", e.target.value)}
          placeholder={t("preferences.onboarding.steps.goals.placeholder")}
          className="w-full min-h-[120px]"
          maxLength={500}
        />
        <div className="flex justify-between text-sm text-gray-500">
          <span>{t("preferences.onboarding.steps.goals.description")}</span>
          <span>{formData.learningGoals.length}/500</span>
        </div>
      </div>

      {/* Example Goals */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <Typography variant="h6" className="font-medium text-blue-900 mb-2">
          {t("preferences.onboarding.steps.goals.examples.title")}
        </Typography>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>{t("preferences.onboarding.steps.goals.examples.frontend")}</li>
          <li>{t("preferences.onboarding.steps.goals.examples.python")}</li>
          <li>{t("preferences.onboarding.steps.goals.examples.transition")}</li>
          <li>{t("preferences.onboarding.steps.goals.examples.promotion")}</li>
          <li>{t("preferences.onboarding.steps.goals.examples.startup")}</li>
        </ul>
      </div>
    </div>
  );
}
