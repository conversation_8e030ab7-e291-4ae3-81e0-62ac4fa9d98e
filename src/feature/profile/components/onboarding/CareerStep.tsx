"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CareerStepProps {
  formData: {
    currentRole: string;
    industry: string;
    workExperience: string;
  };
  onFormDataChange: (data: Partial<CareerStepProps["formData"]>) => void;
}

export default function CareerStep({
  formData,
  onFormDataChange,
}: CareerStepProps) {
  const { t } = useTranslation();

  const WORK_EXPERIENCE_OPTIONS = [
    {
      value: "0",
      label: t(
        "preferences.onboarding.steps.career.workExperienceOptions.noExperience"
      ),
    },
    {
      value: "1",
      label: t(
        "preferences.onboarding.steps.career.workExperienceOptions.lessThan1"
      ),
    },
    {
      value: "2",
      label: t(
        "preferences.onboarding.steps.career.workExperienceOptions.1to2"
      ),
    },
    {
      value: "5",
      label: t(
        "preferences.onboarding.steps.career.workExperienceOptions.3to5"
      ),
    },
    {
      value: "10",
      label: t(
        "preferences.onboarding.steps.career.workExperienceOptions.6to10"
      ),
    },
    {
      value: "15",
      label: t(
        "preferences.onboarding.steps.career.workExperienceOptions.11to15"
      ),
    },
    {
      value: "20",
      label: t(
        "preferences.onboarding.steps.career.workExperienceOptions.16plus"
      ),
    },
  ];

  const handleInputChange = (
    field: keyof CareerStepProps["formData"],
    value: string
  ) => {
    onFormDataChange({ [field]: value });
  };

  return (
    <div className="max-w-xl mx-auto space-y-6">
      <div className="space-y-6">
        {/* Current Role */}
        <div className="space-y-2">
          <Label
            htmlFor="currentRole"
            className="text-sm font-medium text-gray-700"
          >
            {t("preferences.onboarding.steps.career.currentRole")} *
          </Label>
          <Input
            id="currentRole"
            value={formData.currentRole}
            onChange={(e) => handleInputChange("currentRole", e.target.value)}
            placeholder={t("preferences.placeholders.currentRole")}
            className="w-full"
          />
        </div>

        {/* Industry */}
        <div className="space-y-2">
          <Label
            htmlFor="industry"
            className="text-sm font-medium text-gray-700"
          >
            {t("preferences.onboarding.steps.career.industry")} *
          </Label>
          <Input
            id="industry"
            value={formData.industry}
            onChange={(e) => handleInputChange("industry", e.target.value)}
            placeholder={t("preferences.placeholders.industry")}
            className="w-full"
          />
        </div>

        {/* Work Experience */}
        <div className="space-y-2">
          <Label
            htmlFor="workExperience"
            className="text-sm font-medium text-gray-700"
          >
            {t("preferences.onboarding.steps.career.workExperience")} *
          </Label>
          <Select
            value={formData.workExperience}
            onValueChange={(value) =>
              handleInputChange("workExperience", value)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue
                placeholder={t("preferences.placeholders.workExperience")}
              />
            </SelectTrigger>
            <SelectContent>
              {WORK_EXPERIENCE_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
