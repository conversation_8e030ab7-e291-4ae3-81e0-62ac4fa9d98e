"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface EducationStepProps {
  formData: {
    educationExperience: string;
    major: string;
    graduationYear: string;
  };
  onFormDataChange: (data: Partial<EducationStepProps["formData"]>) => void;
}

export default function EducationStep({
  formData,
  onFormDataChange,
}: EducationStepProps) {
  const { t } = useTranslation();

  const EDUCATION_OPTIONS = [
    {
      value: "high_school",
      label: t(
        "preferences.onboarding.steps.education.educationOptions.highSchool"
      ),
    },
    {
      value: "bachelor",
      label: t(
        "preferences.onboarding.steps.education.educationOptions.bachelor"
      ),
    },
    {
      value: "master",
      label: t(
        "preferences.onboarding.steps.education.educationOptions.master"
      ),
    },
    {
      value: "other",
      label: t("preferences.onboarding.steps.education.educationOptions.other"),
    },
  ];

  const handleInputChange = (
    field: keyof EducationStepProps["formData"],
    value: string
  ) => {
    onFormDataChange({ [field]: value });
  };

  const currentYear = new Date().getFullYear();

  return (
    <div className="max-w-xl mx-auto space-y-6">
      <div className="space-y-6">
        {/* Education Level */}
        <div className="space-y-2">
          <Label
            htmlFor="education"
            className="text-sm font-medium text-gray-700"
          >
            {t("preferences.onboarding.steps.education.educationLevel")} *
          </Label>
          <Select
            value={formData.educationExperience}
            onValueChange={(value) =>
              handleInputChange("educationExperience", value)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue
                placeholder={t("preferences.placeholders.educationExperience")}
              />
            </SelectTrigger>
            <SelectContent>
              {EDUCATION_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Major/Field of Study */}
        <div className="space-y-2">
          <Label htmlFor="major" className="text-sm font-medium text-gray-700">
            {t("preferences.onboarding.steps.education.major")} *
          </Label>
          <Input
            id="major"
            value={formData.major}
            onChange={(e) => handleInputChange("major", e.target.value)}
            placeholder={t("preferences.placeholders.major")}
            className="w-full"
          />
        </div>

        {/* Graduation Year */}
        <div className="space-y-2">
          <Label
            htmlFor="graduationYear"
            className="text-sm font-medium text-gray-700"
          >
            {t("preferences.onboarding.steps.education.graduationYear")} *
          </Label>
          <Input
            id="graduationYear"
            type="number"
            value={formData.graduationYear}
            onChange={(e) =>
              handleInputChange("graduationYear", e.target.value)
            }
            placeholder={t("preferences.placeholders.graduationYear")}
            min="1950"
            max={currentYear.toString()}
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
}
