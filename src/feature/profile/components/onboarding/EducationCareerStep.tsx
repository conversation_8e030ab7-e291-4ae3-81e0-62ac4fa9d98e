"use client";

import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Typography } from "@/components/ui/typography";

interface EducationCareerStepProps {
  formData: {
    educationExperience: string;
    major: string;
    graduationYear: string;
    currentRole: string;
    industry: string;
    workExperience: string;
  };
  onFormDataChange: (
    data: Partial<EducationCareerStepProps["formData"]>
  ) => void;
}

const EDUCATION_OPTIONS = [
  { value: "high_school", label: "High School" },
  { value: "bachelor", label: "Bachelor's Degree" },
  { value: "master", label: "Master's Degree" },
  { value: "other", label: "Other" },
];

const INDUSTRY_OPTIONS = [
  { value: "technology", label: "Technology" },
  { value: "finance", label: "Finance" },
  { value: "healthcare", label: "Healthcare" },
  { value: "education", label: "Education" },
  { value: "retail", label: "Retail" },
  { value: "manufacturing", label: "Manufacturing" },
  { value: "consulting", label: "Consulting" },
  { value: "government", label: "Government" },
  { value: "nonprofit", label: "Non-profit" },
  { value: "student", label: "Student" },
  { value: "unemployed", label: "Currently Unemployed" },
  { value: "other", label: "Other" },
];

const WORK_EXPERIENCE_OPTIONS = [
  { value: "0", label: "No experience" },
  { value: "1", label: "Less than 1 year" },
  { value: "2", label: "1-2 years" },
  { value: "5", label: "3-5 years" },
  { value: "10", label: "6-10 years" },
  { value: "15", label: "11-15 years" },
  { value: "20", label: "16+ years" },
];

export default function EducationCareerStep({
  formData,
  onFormDataChange,
}: EducationCareerStepProps) {
  const handleInputChange = (
    field: keyof EducationCareerStepProps["formData"],
    value: string
  ) => {
    onFormDataChange({ [field]: value });
  };

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 50 }, (_, i) => currentYear - i);

  return (
    <div className="max-w-xl mx-auto space-y-6">
      <div className="text-center mb-8">
        <Typography variant="h5" className="font-semibold text-gray-900 mb-2">
          Educational & Professional Background
        </Typography>
        <Typography variant="p" className="text-gray-600">
          Help us understand your background to personalize your learning experience
        </Typography>
      </div>

      <div className="space-y-6">
        {/* Education Level */}
        <div className="space-y-2">
          <Label
            htmlFor="education"
            className="text-sm font-medium text-gray-700"
          >
            Highest Education Level
          </Label>
          <Select
            value={formData.educationExperience}
            onValueChange={(value) =>
              handleInputChange("educationExperience", value)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select education level" />
            </SelectTrigger>
            <SelectContent>
              {EDUCATION_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Major/Field of Study */}
        <div className="space-y-2">
          <Label htmlFor="major" className="text-sm font-medium text-gray-700">
            Major / Field of Study
          </Label>
          <Input
            id="major"
            value={formData.major}
            onChange={(e) => handleInputChange("major", e.target.value)}
            placeholder="e.g., Computer Science, Business, Engineering"
            className="w-full"
          />
        </div>

        {/* Graduation Year */}
        <div className="space-y-2">
          <Label
            htmlFor="graduationYear"
            className="text-sm font-medium text-gray-700"
          >
            Graduation Year
          </Label>
          <Select
            value={formData.graduationYear}
            onValueChange={(value) =>
              handleInputChange("graduationYear", value)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select year" />
            </SelectTrigger>
            <SelectContent>
              {years.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Current Role */}
        <div className="space-y-2">
          <Label
            htmlFor="currentRole"
            className="text-sm font-medium text-gray-700"
          >
            Current Role
          </Label>
          <Input
            id="currentRole"
            value={formData.currentRole}
            onChange={(e) => handleInputChange("currentRole", e.target.value)}
            placeholder="e.g., Software Developer, Student, Manager"
            className="w-full"
          />
        </div>

        {/* Industry */}
        <div className="space-y-2">
          <Label
            htmlFor="industry"
            className="text-sm font-medium text-gray-700"
          >
            Industry
          </Label>
          <Select
            value={formData.industry}
            onValueChange={(value) => handleInputChange("industry", value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select industry" />
            </SelectTrigger>
            <SelectContent>
              {INDUSTRY_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Work Experience */}
        <div className="space-y-2">
          <Label
            htmlFor="workExperience"
            className="text-sm font-medium text-gray-700"
          >
            Years of Work Experience
          </Label>
          <Select
            value={formData.workExperience}
            onValueChange={(value) =>
              handleInputChange("workExperience", value)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select experience level" />
            </SelectTrigger>
            <SelectContent>
              {WORK_EXPERIENCE_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Help Text */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mt-6">
        <Typography variant="small" className="text-green-800">
          <strong>Why we ask:</strong> Understanding your educational and professional 
          background helps us recommend courses that align with your experience level 
          and career goals. This information allows us to create a more personalized 
          learning path for you.
        </Typography>
      </div>
    </div>
  );
}
