/*
 * @Description: Learning Plans Hero Section Component
 * @Author: <PERSON>
 * @Date: 2025-07-21
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import FluxIcon from "@/components/fluxIcon";
import { Container } from "@/components/ui/container";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import { Clock } from "lucide-react";

const LearningPlansHero: React.FC = () => {
  const { t } = useTranslation();

  return (
    <section
      className="pt-16 pb-8 lg:pt-24 lg:pb-16"
      style={{
        background:
          "linear-gradient(121.05deg, rgb(243, 252, 246), rgb(239, 246, 255))",
      }}
    >
      <Container size="lg">
        <div className="text-center max-w-4xl mx-auto">
          {/* Main Heading */}
          <Typography
            variant="h1"
            className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight"
            style={{ letterSpacing: "0.25px" }}
          >
            {t("learningPlans.hero.title")}
          </Typography>

          {/* Subtitle */}
          <Typography
            variant="p"
            className="text-lg lg:text-sm mb-8 leading-relaxed max-w-2xl mx-auto"
          >
            {t("learningPlans.hero.subtitle")}
          </Typography>

          {/* CTA Button and Time Estimate Row */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
            <Button
              size="lg"
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-2 text-sm font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
            >
              {t("learningPlans.hero.cta")}
            </Button>

            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-gray-500" />
              <div className="text-sm text-gray-500 font-medium">
                {t("learningPlans.hero.timeEstimate")}
              </div>
            </div>
          </div>

          {/* Hero Illustration */}
          <div className="relative max-w-2xl mx-auto">
            <div className="relative w-full h-40 lg:h-64 flex items-center justify-center">
              <FluxIcon
                name="leaning-plans"
                width="100%"
                height="100%"
                className="object-contain max-w-full max-h-full"
              />
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default LearningPlansHero;
