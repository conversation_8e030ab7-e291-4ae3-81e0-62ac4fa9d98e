/*
 * @Description: Learning Plans How It Works Section Component
 * @Author: Devin
 * @Date: 2025-07-30
 */
"use client";

import React from "react";
import FluxIcon from "@/components/fluxIcon";
import { Container } from "@/components/ui/container";

const steps = [
  {
    title: "Share your Learning Objectives",
    description:
      "Answer a few questions to share your learning goals and preferences.",
    iconName: "objectives",
    marginTop: "lg:mt-11", // First card has top margin
  },
  {
    title: "Get a Personalized Path",
    description:
      "Based on your responses, we recommend a personalized path tailor-made to achieve your learning objectives.",
    iconName: "personalized-path",
    marginTop: "", // Middle card has no margin
  },
  {
    title: "Start Learning",
    description:
      "Make the most out of the Educative platform and achieve your learning goals.",
    iconName: "start-learning",
    marginTop: "lg:mt-14", // Third card has different top margin
  },
];

const LearningPlansHowItWorks: React.FC = () => {
  return (
    <div
      className="w-full py-16 lg:py-24"
      style={{ backgroundColor: "#f9fafb" }}
    >
      <Container size="lg">
        <div className="flex w-full flex-col items-center justify-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-10 mt-0 flex items-center justify-center lg:mb-16">
            How it works
          </h2>
          <div className="flex w-full flex-col items-center justify-around text-gray-900 lg:h-96 lg:flex-row lg:space-x-8">
            {steps.map((step, index) => (
              <div
                key={index}
                className={`mb-8 rounded-lg sm:mb-10 lg:mb-0 ${step.marginTop} flex h-full w-full max-w-[716px] flex-col bg-white shadow-md lg:max-w-none`}
              >
                {/* Image Section */}
                <div className="mb-8 flex h-[191px] justify-center items-center rounded-t-lg bg-indigo-50 pt-6 lg:h-[136px]">
                  <div className="flex items-center justify-center w-full h-full">
                    <FluxIcon
                      name={step.iconName}
                      width={160}
                      height={160}
                      className="w-auto h-auto object-contain max-h-28"
                    />
                  </div>
                </div>

                {/* Content Section */}
                <div className="px-8">
                  <span className="text-lg font-semibold mb-2 flex">
                    {step.title}
                  </span>
                  <div className="text-sm mb-14 text-gray-600 leading-relaxed">
                    {step.description}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default LearningPlansHowItWorks;
