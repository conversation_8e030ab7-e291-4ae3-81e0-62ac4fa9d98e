/*
 * @Description: Learning Plans Features Section Component
 * @Author: Devin
 * @Date: 2025-07-30
 */
"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import FluxIcon from "@/components/fluxIcon";

const getFeatures = (t: any) => [
  {
    title: t("learningPlans.featuresSection.0.title"),
    description: t("learningPlans.featuresSection.0.description"),
    iconName: "experts",
  },
  {
    title: t("learningPlans.featuresSection.1.title"),
    description: t("learningPlans.featuresSection.1.description"),
    iconName: "start-learning",
  },
  {
    title: t("learningPlans.featuresSection.2.title"),
    description: t("learningPlans.featuresSection.2.description"),
    iconName: "curriculum",
  },
  {
    title: t("learningPlans.featuresSection.3.title"),
    description: t("learningPlans.featuresSection.3.description"),
    iconName: "analytics",
  },
];

const LearningPlansFeaturesSection: React.FC = () => {
  const { t } = useTranslation();
  const features = getFeatures(t);

  return (
    <div className="flex w-full flex-col items-center justify-center pb-20 pt-16 sm:pt-20 lg:py-32">
      <div
        className="space-y-20 px-4 text-gray-900 lg:space-y-32 lg:px-6"
        style={{ maxWidth: "1172px" }}
      >
        {features.map((feature, index) => {
          // Alternate layout: even index (0,2,4...) = image left, odd index (1,3,5...) = image right
          const isImageLeft = index % 2 === 0;

          return (
            <div
              key={index}
              className={`flex items-center ${
                isImageLeft
                  ? "flex-col lg:flex-row lg:space-x-20"
                  : "flex-col-reverse lg:flex-row-reverse lg:space-x-reverse lg:space-x-20"
              }`}
            >
              {/* Image Section */}
              <div className="mb-4 flex h-full w-full max-w-[716px] items-center justify-center rounded-2xl bg-gray-50 lg:mb-0 lg:w-1/2 lg:max-w-[468px]">
                <div className="flex w-full justify-center py-4 lg:px-8 lg:py-6">
                  <div className="flex w-full max-w-xs lg:h-60 lg:w-80">
                    <FluxIcon
                      name={feature.iconName}
                      width={320}
                      height={240}
                      className="w-full h-auto object-contain"
                    />
                  </div>
                </div>
              </div>

              {/* Content Section */}
              <div className="flex max-w-[716px] flex-col text-left lg:max-w-none">
                <div className="text-2xl lg:text-3xl font-bold my-2 flex tracking-wide">
                  {feature.title}
                </div>
                <div className="text-sm mb-0 flex leading-relaxed">
                  {feature.description}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default LearningPlansFeaturesSection;
