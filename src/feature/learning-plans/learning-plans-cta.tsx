/*
 * @Description: Learning Plans CTA Section Component
 * @Author: Devin
 * @Date: 2025-07-30
 */
"use client";

import React from "react";
import { Container } from "@/components/ui/container";
import FluxIcon from "@/components/fluxIcon";

const LearningPlansCTA: React.FC = () => {
  return (
    <div className="flex w-full flex-col items-center justify-center px-4 py-32">
      <Container size="lg">
        <div className="flex w-full flex-col items-center justify-center">
          <span className="text-xl lg:text-2xl font-semibold mb-4 mt-0 flex text-center">
            Start your own personalized path now.
          </span>
          <div className="flex flex-col items-center sm:flex-row">
            <button className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium mb-3 flex h-11 w-56 items-center justify-center rounded-md px-4 py-2 text-sm transition-colors sm:mb-0 sm:mr-5 sm:w-auto">
              Try Personalized Paths
            </button>
            <div className="flex items-center">
              <FluxIcon
                name="clock-estimate"
                width={24}
                height={24}
                className="my-auto mr-2 flex fill-current text-indigo-500"
              />
              <p className="text-sm my-auto flex text-gray-600">
                Est. 3 mins (Approx. 6 Qs)
              </p>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default LearningPlansCTA;
