"use client";

import React, { useState } from "react";
import {
  Play,
  CheckCircle,
  Lock,
  FileText,
  HelpCircle,
  Zap,
  ChevronUp,
  ChevronDown,
} from "lucide-react";
import FluxIcon from "@/components/fluxIcon";

interface SubLesson {
  id: string;
  title: string;
  description?: string;
}

interface Lesson {
  id: string;
  title: string;
  description?: string;
  duration?: string;
  isCompleted: boolean;
  isLocked?: boolean;
  type?: "lesson" | "quiz" | "challenge" | "exercise" | "project";
  subLessons?: SubLesson[];
}

interface RoadmapLessonProps {
  lesson: Lesson;
  onClick: () => void;
}

export const RoadmapLesson: React.FC<RoadmapLessonProps> = ({
  lesson,
  onClick,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const getIcon = () => {
    if (lesson.isLocked) {
      return <Lock className="w-4 h-4 text-gray-400" />;
    }

    if (lesson.isCompleted) {
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    }

    switch (lesson.type) {
      case "quiz":
        return <HelpCircle className="w-4 h-4 text-blue-600" />;
      case "challenge":
      case "exercise":
        return <Zap className="w-4 h-4 text-orange-600" />;
      case "project":
        return <FileText className="w-4 h-4 text-purple-600" />;
      default:
        return <Play className="w-4 h-4 text-blue-600" />;
    }
  };

  const handleClick = () => {
    if (!lesson.isLocked) {
      onClick();
    }
  };

  return (
    <div
      className={`flex items-center justify-between p-4 rounded-lg border transition-colors ${
        lesson.isLocked
          ? "bg-gray-50 border-gray-200"
          : "bg-white border-gray-200 hover:border-blue-300"
      }`}
    >
      {/* Left side - Icon and Info */}
      <div className="flex items-center gap-3 flex-1">
        {/* Lesson Icon */}
        <div className="flex-shrink-0">{getIcon()}</div>

        {/* Lesson Info */}
        <div className="flex-1 min-w-0">
          <h4
            className={`font-medium ${
              lesson.isLocked ? "text-gray-400" : "text-gray-900"
            }`}
          >
            {lesson.title}
          </h4>
          {lesson.description && (
            <p
              className={`text-sm mt-1 ${
                lesson.isLocked ? "text-gray-300" : "text-gray-600"
              }`}
            >
              {lesson.description}
            </p>
          )}
        </div>
      </div>

      {/* Right side - Action Button */}
      {!lesson.isLocked && (
        <button
          onClick={handleClick}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2 ${
            lesson.isCompleted
              ? "bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300"
              : "bg-blue-600 hover:bg-blue-700 text-white"
          }`}
        >
          {lesson.isCompleted ? "Revisit" : "Start Learning"}
        </button>
      )}
    </div>
  );
};
