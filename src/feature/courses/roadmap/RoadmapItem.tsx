/*
 * @Description: Roadmap Section Item Component
 * @Author: Devin
 * @Date: 2025-07-24 11:03:31
 */
import React, { useState } from "react";
import { RoadmapItem } from "@/types";
import { ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import FluxIcon from "@/components/fluxIcon";

interface LessonData {
  id: string;
  title: string;
  description?: string;
  duration?: string;
  isCompleted?: boolean;
  isLocked?: boolean;
  type?: string;
  lessonId?: string; // 添加 lessonId 属性
  contentFlowId?: string; // 添加 contentFlowId 属性
}

interface Props {
  item: RoadmapItem & {
    id?: string;
    isCompleted?: boolean;
    isLocked?: boolean;
    duration?: string;
    type?: string;
  };
  lessons?: LessonData[];
  isFirst?: boolean;
  onClick?: (itemId?: string) => void;
  onLessonClick?: (
    lessonData: string | { lessonId: string; contentFlowId?: string }
  ) => void;
}

const RoadmapItemComp: React.FC<Props> = ({
  item,
  lessons = [],
  isFirst,
  onClick,
  onLessonClick,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const hasLessons = lessons.length > 0;

  const handleSectionClick = () => {
    if (onClick) {
      onClick(item.id);
    }
  };

  const handleExpandClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    console.log("Expanding section:", item.title, "Current state:", isExpanded);
    setIsExpanded(!isExpanded);
  };

  const handleLessonClick = (lesson: LessonData, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onLessonClick) {
      // 传递包含 lessonId 和 contentFlowId 的对象
      if (lesson.lessonId && lesson.contentFlowId) {
        onLessonClick({
          lessonId: lesson.lessonId,
          contentFlowId: lesson.contentFlowId,
        });
      } else {
        // 兼容旧格式
        onLessonClick(lesson.lessonId || lesson.id);
      }
    }
  };

  const getSectionIcon = (hasProgress: boolean) => {
    if (hasProgress) {
      return (
        <div className="w-5 h-5 bg-[#2563eb] rounded-full flex items-center justify-center">
          <FluxIcon
            name="inprogress"
            width={24}
            height={24}
            strokeWidth={2}
            className="text-white"
          />
        </div>
      );
    }
    return (
      <div className="w-5 h-5 rounded-full flex items-center justify-center">
        <FluxIcon name="tree" width={16} height={18} color="text-[#303640]" />
      </div>
    );
  };

  return (
    <div className="mb-4 ml-3">
      {/* Section Header */}
      <div
        className={cn(
          "bg-gray-50 border-gray-200 hover:bg-gray-100 rounded-lg border p-4 transition-all cursor-pointer",
          {
            "mt-4": !isFirst,
          }
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleExpandClick} // 点击整个卡片展开
      >
        <div
          className={cn("flex items-center justify-between", {
            "mb-6": isExpanded,
          })}
        >
          <div className="flex items-center gap-3 flex-1">
            <div className="flex-1">
              <div className="flex items-center gap-3">
                {getSectionIcon(item.hasProgress as boolean)}
                <h4 className="font-semibold text-gray-900">{item.title}</h4>
              </div>

              <p className="text-sm text-gray-600 mt-1 ml-8">
                {item.description}
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {/* Show learning button on hover or if in progress */}
            {!isExpanded && isHovered && (
              <Button
                variant="default"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation(); // 阻止展开事件
                  handleSectionClick(); // 导航到课程
                }}
                className="flex items-center gap-2"
              >
                {item.hasProgress ? "Continue Learning" : "Start Learning"}
              </Button>
            )}

            {/* Show expand button on hover or when expanded */}
            {hasLessons && (isHovered || isExpanded) && (
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  isExpanded
                    ? "text-gray-700 hover:text-gray-900 bg-gray-100"
                    : "text-gray-400 hover:text-gray-600"
                )}
                onClick={(e) => {
                  e.stopPropagation(); // 阻止卡片点击事件
                  handleExpandClick(e);
                }}
              >
                {isExpanded ? (
                  <ChevronUp className="w-5 h-5" />
                ) : (
                  <ChevronDown className="w-5 h-5" />
                )}
              </Button>
            )}
          </div>
        </div>
        {/* Expanded Lessons */}
        {isExpanded && hasLessons && (
          <div className="mt-2 space-y-2">
            {lessons.map((lesson) => (
              <div
                key={lesson.id}
                className={cn(
                  "flex items-center justify-between p-4 rounded-lg transition-colors cursor-pointer hover:shadow-lesson"
                )}
                style={{ backgroundColor: "#ffffff" }}
                onClick={(e) => handleLessonClick(lesson, e)}
              >
                <div className="flex items-center gap-3 flex-1">
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900">
                      {lesson.title}
                    </h5>
                  </div>
                </div>

                {/* Start Learning Button */}
                <Button
                  variant="outline"
                  className="border border-[#5553ff80] text-[#5553ff] hover:border-[#5553ff] hover:text-[#5553ff]"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLessonClick(lesson, e);
                  }}
                >
                  {lesson.isCompleted ? "Revisit" : "Start Learning"}
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RoadmapItemComp;
