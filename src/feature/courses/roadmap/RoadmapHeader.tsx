/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-24 10:18:29
 */
"use client";

import React, { useState, useRef, useEffect } from "react";
import { MoreHorizontal, RotateCcw } from "lucide-react";

interface RoadmapHeaderProps {
  title: string;
  progress: number;
  onStartLearning: () => void;
  onResetProgress?: () => void;
}

export const RoadmapHeader: React.FC<RoadmapHeaderProps> = ({
  title,
  progress,
  onStartLearning,
  onResetProgress,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleMoreClick = () => {
    setShowDropdown(!showDropdown);
  };

  const handleResetProgress = () => {
    if (progress > 0 && onResetProgress) {
      onResetProgress();
    }
    setShowDropdown(false);
  };

  return (
    <div className=" border-gray-200 px-6 pt-12 pb-6">
      <div className="max-w-6xl mx-auto">
        {/* Title and Actions Row */}
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-lg font-semibold text-gray-900">
            {title} Roadmap
          </h1>

          <div className="flex items-center gap-3">
            <button
              onClick={onStartLearning}
              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded transition-colors"
            >
              {progress > 0 ? "Continue Learning" : "Start Learning"}
            </button>

            <div className="relative" ref={dropdownRef}>
              <button
                onClick={handleMoreClick}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <MoreHorizontal className="w-5 h-5" />
              </button>

              {/* Dropdown Menu */}
              {showDropdown && (
                <div className="absolute right-0 top-full mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                  <button
                    onClick={handleResetProgress}
                    disabled={progress === 0}
                    className={`w-full px-4 py-3 text-left text-sm flex items-center gap-3 rounded-lg transition-colors ${
                      progress === 0
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    <RotateCcw
                      className={`w-4 h-4 ${
                        progress === 0 ? "text-gray-300" : "text-gray-400"
                      }`}
                    />
                    Reset Progress
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Progress Bar Row */}
        <div className="flex items-center gap-3">
          <div className="flex-1 max-w-[12rem]">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
          <span className="text-sm text-gray-600 font-medium min-w-[30px]">
            {progress}%
          </span>
        </div>
      </div>
    </div>
  );
};
