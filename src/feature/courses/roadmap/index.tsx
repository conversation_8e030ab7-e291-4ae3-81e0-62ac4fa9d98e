/*
 * @Description: Roadmap Page Component
 * @Author: Devin
 * @Date: 2025-08-18
 */
"use client";

import React, { useState, useEffect } from "react";
import { useRouter, notFound } from "next/navigation";
import { RoadmapContainer } from "./RoadmapContainer";
import { learningNodeService } from "@/service/learning-nodes.service";
import { handleApiResponse, handleApiError } from "@/lib/api-error-handler";
import type { NodeLesson, LearningNode } from "@/types/openapi";

interface RoadmapPageProps {
  courseId: string;
}

export default function RoadmapPage({ courseId }: RoadmapPageProps) {
  const router = useRouter();
  const [progressData, setProgressData] = useState<Record<string, boolean>>({});
  const [nodeInfo, setNodeInfo] = useState<LearningNode | null>(null);
  const [nodeLessons, setNodeLessons] = useState<NodeLesson[]>([]);
  const [loading, setLoading] = useState(true);

  // 获取课程数据
  useEffect(() => {
    const fetchCourseData = async () => {
      try {
        setLoading(true);
        const [nodeResp, lessonsResp] = await Promise.all([
          learningNodeService.get(courseId),
          learningNodeService.listLessons(courseId),
        ]);
        const node = handleApiResponse(nodeResp, undefined, {
          showToast: false,
        });
        const lessons =
          handleApiResponse(lessonsResp, undefined, { showToast: false }) || [];

        if (!node) {
          notFound();
        }

        setNodeInfo(node);
        setNodeLessons(lessons);
      } catch (error) {
        handleApiError(error, {
          showToast: true,
          defaultMessage: "加载课程失败",
        });
        notFound();
      } finally {
        setLoading(false);
      }
    };

    fetchCourseData();
  }, [courseId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">Loading...</div>
    );
  }

  if (!nodeInfo) {
    notFound();
  }

  // 处理课程点击事件
  const handleLessonClick = (
    lessonData: string | { lessonId: string; contentFlowId?: string }
  ) => {
    // 处理不同的参数格式
    let lessonId: string;
    let contentFlowId: string | undefined;

    if (typeof lessonData === "string") {
      // 兼容旧的字符串格式
      lessonId = lessonData;
    } else {
      // 新的对象格式
      lessonId = lessonData.lessonId;
      contentFlowId = lessonData.contentFlowId;
    }

    console.log(
      "Navigating to lesson:",
      lessonId,
      "contentFlowId:",
      contentFlowId
    );

    // 构建 URL，如果有 contentFlowId 则添加查询参数
    let url = `/courses/${courseId}/${lessonId}`;
    if (contentFlowId) {
      url += `?content_flow_id=${contentFlowId}`;
    }
    router.push(url);
  };

  // 处理进度更新
  const handleProgressUpdate = (
    courseId: string,
    lessonId: string,
    completed: boolean
  ) => {
    console.log("Progress update:", { courseId, lessonId, completed });
    setProgressData((prev) => ({
      ...prev,
      [`${courseId}-${lessonId}`]: completed,
    }));

    // 这里可以调用 API 来保存进度
    // await updateLessonProgress(courseId, lessonId, completed);
  };

  return (
    <div className="bg-white">
      <RoadmapContainer
        courseData={{ nodeInfo, nodeLessons }}
        showProgress={true}
        enableLessonNavigation={true}
        onLessonClick={handleLessonClick}
        onProgressUpdate={handleProgressUpdate}
      />
    </div>
  );
}
