"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { RoadmapHeader } from "./RoadmapHeader";
import { RoadmapWeek } from "./RoadmapWeek";
import { RoadmapWeekRead } from "./RoadmapWeekRead";
import type {
  NodeLesson,
  LearningNode,
  UserLessonProgressMap,
} from "@/types/openapi";
import { RoadmapWeek as RoadmapWeekType, RoadmapItem } from "@/types/roadmap";
import { Container } from "@/components/ui/container";
import { useUserLessonProgress } from "@/hooks/useUserLessonProgress";

interface RoadmapContainerProps {
  courseData: {
    nodeInfo: LearningNode;
    nodeLessons: NodeLesson[];
  };
  customWeeks?: RoadmapWeekType[];
  showProgress?: boolean;
  enableLessonNavigation?: boolean;
  onLessonClick?: (
    lessonData: string | { lessonId: string; contentFlowId?: string }
  ) => void;
  onProgressUpdate?: (
    courseId: string,
    lessonId: string,
    completed: boolean
  ) => void;
}

export const RoadmapContainer: React.FC<RoadmapContainerProps> = ({
  courseData,
  customWeeks,
  showProgress = true,
  enableLessonNavigation = true,
  onLessonClick,
  onProgressUpdate,
}) => {
  const router = useRouter();
  const { fetchProgressByNodeOrPath } = useUserLessonProgress();

  // 配置变量：每周显示的课程数量
  const LESSONS_PER_WEEK = 4;

  // 进度状态
  const [allLessonsProgress, setAllLessonsProgress] =
    useState<UserLessonProgressMap>({});

  // 获取用户学习进度
  useEffect(() => {
    const fetchUserProgress = async () => {
      if (!courseData.nodeInfo.id) return;

      try {
        const progressData = await fetchProgressByNodeOrPath(
          courseData.nodeInfo.id
        );
        setAllLessonsProgress(progressData);
      } catch (error) {
        console.error("Failed to fetch user progress:", error);
      }
    };

    fetchUserProgress();
  }, [courseData.nodeInfo.id, fetchProgressByNodeOrPath]);

  // 计算整体进度百分比
  const calculateOverallProgress = (): number => {
    const { nodeLessons } = courseData;

    if (!nodeLessons || nodeLessons.length === 0) {
      return 0;
    }

    let totalContentFlows = 0;
    let completedContentFlows = 0;

    nodeLessons.forEach((lesson) => {
      const lessonId = lesson.lesson_id;

      // 跳过没有 lesson_id 的课程
      if (!lessonId) {
        console.warn("Lesson missing lesson_id:", lesson);
        return;
      }

      const lessonProgress = allLessonsProgress[lessonId];
      const completedContentFlowIds =
        lessonProgress?.completed_content_flow_ids || [];

      // 计算该课程的总内容流数量
      if (lesson.content_flow && lesson.content_flow.length > 0) {
        totalContentFlows += lesson.content_flow.length;

        // 计算该课程中已完成的内容流数量
        lesson.content_flow.forEach((contentFlow) => {
          if (
            contentFlow.id &&
            completedContentFlowIds.includes(contentFlow.id)
          ) {
            completedContentFlows++;
          }
        });
      } else {
        // 如果没有 content_flow，则按课程级别计算
        totalContentFlows += 1;
        if (lessonProgress?.is_completed) {
          completedContentFlows += 1;
        }
      }
    });

    if (totalContentFlows === 0) {
      return 0;
    }

    const percentage = Math.round(
      (completedContentFlows / totalContentFlows) * 100
    );
    return Math.max(0, Math.min(100, percentage));
  };

  // 按 order_key 排序课程的辅助函数
  const sortLessonsByOrderKey = (lessons: NodeLesson[]): NodeLesson[] => {
    return [...lessons].sort((a, b) => {
      if (a.order_key && b.order_key) {
        // 按 order_key 的长度排序，长度短的在前
        const lengthDiff = a.order_key.length - b.order_key.length;
        if (lengthDiff !== 0) {
          return lengthDiff;
        }
        // 如果长度相同，按字典序排序
        return a.order_key.localeCompare(b.order_key);
      }
      // 如果没有 order_key，保持原有顺序
      return 0;
    });
  };

  // 将 NodeLessons 转换为周的格式
  const convertLessonsToWeeks = (): (RoadmapWeekType & {
    lessons: any[];
  })[] => {
    const { nodeLessons } = courseData;

    if (!nodeLessons || nodeLessons.length === 0) {
      // 如果没有课程，返回一个空的周结构，显示课程信息
      return [
        {
          weekTitle: "Course Overview",
          items: [
            {
              title: courseData.nodeInfo.title || "Course Title",
              description:
                courseData.nodeInfo.description || "No lessons available yet",
              status: "default" as const,
            },
          ],
          lessons: [],
        },
      ];
    }

    // 按 order_key 排序课程
    const sortedLessons = sortLessonsByOrderKey(nodeLessons);

    const weeks: (RoadmapWeekType & { lessons: any[] })[] = [];

    // 按照每周 LESSONS_PER_WEEK 个课程的方式分组
    for (let i = 0; i < sortedLessons.length; i += LESSONS_PER_WEEK) {
      const weekNumber = Math.floor(i / LESSONS_PER_WEEK) + 1;
      const weekLessons = sortedLessons.slice(i, i + LESSONS_PER_WEEK);

      // 确定当前周的状态 - 第一周默认为当前周
      const isCurrentWeek = weekNumber === 1;

      // 转换为 RoadmapItem 格式（每个课程作为一个 item）
      const items: RoadmapItem[] = weekLessons
        .filter((lesson) => lesson.lesson_id) // 只包含有 lesson_id 的课程
        .map((lesson) => {
          const lessonId = lesson.lesson_id!; // 使用非空断言，因为已经过滤了
          const lessonProgress = allLessonsProgress[lessonId];
          const completedContentFlowIds =
            lessonProgress?.completed_content_flow_ids || [];

          // 检查是否有任何小节完成
          let hasAnyCompleted = false;
          if (lesson.content_flow && lesson.content_flow.length > 0) {
            hasAnyCompleted = lesson.content_flow.some(
              (content) =>
                content.id && completedContentFlowIds.includes(content.id)
            );
          } else {
            hasAnyCompleted = lessonProgress?.is_completed || false;
          }

          // 根据完成情况设置状态
          let status: "default" | "inProgress" | "completed" = "default";

          // 如果有任何进度，或者是当前周，则设置为 inProgress
          if (hasAnyCompleted || isCurrentWeek) {
            status = "inProgress";
          }

          return {
            id: lessonId, // 添加 lesson ID 用于导航
            title: lesson.lesson_title || "Untitled Lesson",
            description:
              lesson.lesson_description || "No description available",
            status,
            hasProgress: hasAnyCompleted, // 添加进度标识
          };
        });

      // 转换课程数据用于导航 - 从每个课程的 content_flow 中读取小节
      const lessons = weekLessons
        .filter((lesson) => lesson.lesson_id) // 只包含有 lesson_id 的课程
        .flatMap((lesson) => {
          const lessonTitle = lesson.lesson_title || "Untitled Lesson";
          const lessonId = lesson.lesson_id!;
          const lessonProgress = allLessonsProgress[lessonId];
          const completedContentFlowIds =
            lessonProgress?.completed_content_flow_ids || [];

          // 如果有 content_flow，将每个内容作为小节
          if (lesson.content_flow && lesson.content_flow.length > 0) {
            // 按 order 排序 content_flow
            const sortedContent = [...lesson.content_flow].sort(
              (a, b) => a.order - b.order
            );

            return sortedContent.map((content, index) => ({
              id: content.id || `${lessonId}-${index}`,
              title: content.title || `Section ${index + 1}`,
              description: content.description || `${content.type} content`,
              duration: "5 min", // 默认时长
              isCompleted: content.id
                ? completedContentFlowIds.includes(content.id)
                : false,
              isLocked: false, // 可以根据实际权限调整
              type: content.type as any, // 临时使用 any 来避免类型错误
              categoryTitle: lessonTitle, // 使用课程标题作为分类标题
              lessonId: lessonId, // 添加 lessonId 用于导航
              contentFlowId: content.id, // 添加 contentFlowId 用于导航
            }));
          } else {
            // 如果没有 content_flow，将课程本身作为一个小节
            return [
              {
                id: lessonId,
                title: lessonTitle,
                description:
                  lesson.lesson_description || "No description available",
                duration: "5 min",
                isCompleted: lessonProgress?.is_completed || false,
                isLocked: false,
                type: "lesson" as any,
                categoryTitle: lessonTitle,
                lessonId: lessonId,
              },
            ];
          }
        });

      weeks.push({
        weekTitle: `Week ${weekNumber}`,
        items,
        lessons,
      });
    }

    return weeks;
  };

  // 使用自定义周数据或转换后的数据
  const weeks = customWeeks || convertLessonsToWeeks();

  const handleStartLearning = () => {
    if (!enableLessonNavigation) return;

    // 找到第一个可用的课程（按 order_key 排序后的第一个）
    const { nodeLessons, nodeInfo } = courseData;

    if (nodeLessons && nodeLessons.length > 0) {
      // 按 order_key 排序课程
      const sortedLessons = sortLessonsByOrderKey(nodeLessons);

      const firstLesson =
        sortedLessons.find((lesson) => lesson.lesson_id) || sortedLessons[0];
      const lessonId = firstLesson?.lesson_id;

      if (lessonId) {
        if (onLessonClick) {
          onLessonClick(lessonId);
        } else {
          router.push(`/courses/${nodeInfo.id}/${lessonId}`);
        }
      } else {
        console.warn("No valid lesson found for navigation");
      }
    }
  };

  const handleLessonClick = (
    lessonData: string | { lessonId: string; contentFlowId?: string }
  ) => {
    if (!enableLessonNavigation) return;

    const { nodeInfo } = courseData;

    // 处理不同的参数格式
    let lessonId: string;
    let contentFlowId: string | undefined;

    if (typeof lessonData === "string") {
      // 兼容旧的字符串格式，假设是 lesson ID
      lessonId = lessonData;
    } else {
      // 新的对象格式
      lessonId = lessonData.lessonId;
      contentFlowId = lessonData.contentFlowId;
    }

    if (onLessonClick) {
      onLessonClick({
        lessonId,
        contentFlowId,
      });
    } else {
      // 构建 URL，如果有 contentFlowId 则添加查询参数
      let url = `/courses/${nodeInfo.id || "unknown"}/${lessonId}`;
      if (contentFlowId) {
        url += `?content_flow_id=${contentFlowId}`;
      }
      router.push(url);
    }

    // 触发进度更新回调
    if (onProgressUpdate && nodeInfo.id) {
      onProgressUpdate(nodeInfo.id, lessonId, false);
    }
  };

  const handleResetProgress = () => {
    const { nodeInfo, nodeLessons } = courseData;

    // 这里可以实现重置进度的逻辑
    // 例如调用 API 或更新本地状态
    console.log("Reset progress for course:", nodeInfo.id);
    // 可以添加确认对话框
    if (
      window.confirm(
        "Are you sure you want to reset your progress? This action cannot be undone."
      )
    ) {
      // 实际的重置逻辑
      // resetCourseProgress(nodeInfo.id);

      // 触发所有课程的进度重置
      if (onProgressUpdate && nodeInfo.id && nodeLessons.length > 0) {
        // 按 order_key 排序课程
        const sortedLessons = sortLessonsByOrderKey(nodeLessons);

        sortedLessons.forEach((lesson) => {
          const lessonId = lesson.lesson_id || lesson.id || "";
          onProgressUpdate(nodeInfo.id!, lessonId, false);
        });
      }
    }
  };

  // 计算当前进度
  const currentProgress = calculateOverallProgress();

  return (
    <Container size="lg-plus" className="min-h-screen bg-white">
      {showProgress && (
        <RoadmapHeader
          title={courseData.nodeInfo.title || "Course Roadmap"}
          progress={currentProgress}
          onStartLearning={handleStartLearning}
          onResetProgress={handleResetProgress}
        />
      )}

      <div className="max-w-6xl mx-auto px-6 py-8">
        {weeks.map((week) => (
          <RoadmapWeek
            key={week.weekTitle}
            week={week}
            onLessonClick={
              enableLessonNavigation ? handleLessonClick : undefined
            }
          />
        ))}
        <RoadmapWeekRead />
      </div>
    </Container>
  );
};
