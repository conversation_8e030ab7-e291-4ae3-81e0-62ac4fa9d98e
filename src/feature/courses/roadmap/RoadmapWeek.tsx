/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-24 20:06:21
 */
import React from "react";
import { RoadmapWeek as RoadmapWeekType } from "@/types/roadmap";
import RoadmapItem from "./RoadmapItem";

interface Props {
  week: RoadmapWeekType & {
    lessons?: Array<{
      id: string;
      title: string;
      description?: string;
      duration?: string;
      isCompleted?: boolean;
      isLocked?: boolean;
      type?: string;
      categoryTitle?: string; // 添加 categoryTitle 属性
      lessonId?: string; // 添加 lessonId 属性
      contentFlowId?: string; // 添加 contentFlowId 属性
    }>;
  };
  onLessonClick?: (
    lessonData: string | { lessonId: string; contentFlowId?: string }
  ) => void;
}

const RoadmapWeek: React.FC<Props> = ({ week, onLessonClick }) => {
  const handleItemClick = (item: any, itemLessons?: any[]) => {
    // 当点击 item 时，导航到该 item 对应的第一个课程
    if (itemLessons && itemLessons.length > 0 && onLessonClick) {
      const firstLesson =
        itemLessons.find((l) => !l.isLocked) || itemLessons[0];

      // 传递包含 lessonId 和 contentFlowId 的对象
      if (firstLesson.lessonId && firstLesson.contentFlowId) {
        onLessonClick({
          lessonId: firstLesson.lessonId,
          contentFlowId: firstLesson.contentFlowId,
        });
      } else {
        // 兼容旧格式
        onLessonClick(firstLesson.lessonId || firstLesson.id);
      }
    }
  };

  return (
    <div className="mb-1">
      <div className="flex flex-row items-center gap-2">
        <div className="text-gray-500 font-bold text-[2rem] leading-[1] flex items-center mb-1">
          •
        </div>
        <div className="text-xs text-gray-500  leading-5 tracking-widest font-semibold">
          {week.weekTitle.toUpperCase()}
        </div>
      </div>

      <div className="ml-2 border-l border-gray-200 pl-2">
        {week.items.map((item, index) => {
          // 为每个 item 找到对应的 lessons
          const itemLessons =
            week.lessons?.filter(
              (lesson) => lesson.categoryTitle === item.title
            ) || [];

          return (
            <RoadmapItem
              key={`${week.weekTitle}-${item.title}-${index}`}
              item={item}
              lessons={itemLessons}
              isFirst={index === 0}
              onClick={() => handleItemClick(item, itemLessons)}
              onLessonClick={onLessonClick}
            />
          );
        })}
      </div>
    </div>
  );
};

export { RoadmapWeek };
