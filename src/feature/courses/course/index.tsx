/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-24 20:06:21
 */
"use client";

import React, { useState, useEffect } from "react";
import { notFound } from "next/navigation";
import { useTranslation } from "react-i18next";
import { Header } from "@/feature/courses/course/Header";
import { Tabs } from "@/feature/courses/course/Tabs";
import { Overview } from "@/feature/courses/course/Overview";
import { Content } from "@/feature/courses/course/Content";
import { Related } from "@/feature/courses/course/Related";
import { AdditionalContent } from "@/feature/courses/course/CourseHomepageAdditionalContent";
import { Container } from "@/components/ui/container";
import { learningNodeService } from "@/service/learning-nodes.service";
import { handleApiResponse, handleApiError } from "@/lib/api-error-handler";
import { formatDetailedDuration } from "@/lib/utils";
import type { NodeLesson, LearningNode } from "@/types/openapi";

interface CoursePageProps {
  courseId: string;
}

export default function CoursePage({ courseId }: CoursePageProps) {
  const { t } = useTranslation();

  const [nodeInfo, setNodeInfo] = useState<LearningNode | null>(null);
  const [nodeLessons, setNodeLessons] = useState<NodeLesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 异步获取课程数据
  useEffect(() => {
    const fetchCourseData = async () => {
      try {
        setLoading(true);
        const [nodeResp, lessonsResp] = await Promise.all([
          learningNodeService.get(courseId),
          learningNodeService.listLessons(courseId),
        ]);
        const node = handleApiResponse(nodeResp, undefined, {
          showToast: false,
        });
        const lessons =
          handleApiResponse(lessonsResp, undefined, { showToast: false }) || [];
        setNodeInfo(node || null);
        setNodeLessons(lessons);
      } catch (err) {
        handleApiError(err, {
          showToast: true,
          defaultMessage: t("errors.business.courseNotFound") || "加载课程失败",
        });
        setError(t("errors.common.operationFailed"));
      } finally {
        setLoading(false);
      }
    };

    fetchCourseData();
  }, [courseId, t]);

  // 加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t("common.status.loadingCourse")}</p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error || !nodeInfo) {
    notFound();
  }

  // 计算课程统计信息
  const calculateStats = (lessons: NodeLesson[]) => {
    let totalContentItems = 0;
    let totalQuizzes = 0;
    let totalChallenges = 0;

    lessons.forEach((lesson) => {
      if (lesson.content_flow) {
        totalContentItems += lesson.content_flow.length;

        // 统计不同类型的内容
        lesson.content_flow.forEach((content) => {
          switch (content.type) {
            case "multiple_choice_quiz":
            case "fill_in_blank_quiz":
            case "interactive_quiz":
              totalQuizzes++;
              break;
            case "practice_exercise":
            case "code_snippet":
              totalChallenges++;
              break;
          }
        });
      }
    });

    return {
      totalLessons: totalContentItems, // 总的内容项数量
      totalQuizzes,
      totalChallenges,
      totalCategories: lessons.length, // lesson 数量作为分类数
      previewPages: 0, // 暂时设为0
    };
  };

  const stats = calculateStats(nodeLessons);

  // 使用课程难度
  const courseDifficulty = nodeInfo.difficulty
    ? nodeInfo.difficulty <= 3
      ? "beginner"
      : nodeInfo.difficulty <= 7
      ? "intermediate"
      : "advanced"
    : "beginner";

  // 获取课程目标
  const courseObjectives = nodeInfo.what_you_will_learn || [];

  return (
    <div>
      <div className="bg-[#eef]">
        <Container size="lg-plus">
          <Header
            title={nodeInfo.title || "Course"}
            rating={4.5}
            level={courseDifficulty}
            duration={formatDetailedDuration(nodeInfo.estimated_times || 0)}
            description={nodeInfo.description || ""}
            courseId={nodeInfo.id || courseId}
            lessonsCount={stats.totalLessons}
            quizzesCount={stats.totalQuizzes}
            challengesCount={stats.totalChallenges}
          />
        </Container>
      </div>
      <Tabs
        courseTitle={nodeInfo.title || "Course"}
        courseId={nodeInfo.id || courseId}
      />
      <Overview
        summary={nodeInfo.description || ""}
        objectives={courseObjectives}
        takeawaySkills={nodeInfo.skills || []} // 使用 nodeInfo.skills
      />
      <Content courseData={{ nodeInfo, nodeLessons }} />
      {/* <AdditionalContent />
      <Related
        currentCourseId={nodeInfo.id || courseId}
        currentCourseCategory="programming"
      /> */}
    </div>
  );
}
