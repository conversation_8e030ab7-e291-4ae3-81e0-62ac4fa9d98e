/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-23 16:05:22
 */
// components/Course/Header.tsx
import React from "react";
import Image from "next/image";
import FluxIcon from "@/components/fluxIcon";
import { useRouter } from "next/navigation";
import DifficultyIcon from "@/components/icons/DifficultyIcon";

type HeaderProps = {
  title: string;
  rating: number;
  level: string;
  duration: string;
  description: string;
  courseId: string;
  lessonsCount?: number;
  quizzesCount?: number;
  challengesCount?: number;
};

export const Header = ({
  title,
  rating,
  level,
  duration,
  description,
  courseId,
  lessonsCount = 161,
  quizzesCount = 23,
  challengesCount = 4,
}: HeaderProps) => {
  const router = useRouter();

  const handleRoadmapClick = () => {
    router.push(`/courses/${courseId}/roadmap`);
  };

  // 课程统计数据配置
  const courseStats = [
    {
      count: lessonsCount,
      label: "Lessons",
      iconName: "lessons-icon",
    },
    {
      count: quizzesCount,
      label: "Quizzes",
      iconName: "quizzes-icon",
    },
    {
      count: challengesCount,
      label: "Challenges",
      iconName: "challenges-icon",
    },
  ].filter((stat) => stat.count > 0); // 只显示数量大于0的项目

  return (
    <section className="relative py-10">
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none overflow-hidden">
        {/* Background SVG */}
        <svg
          className="absolute left-0 top-0 w-[223px] h-[291px] text-gray-200"
          viewBox="0 0 223 291"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="208.5"
            cy="208.5"
            r="208.333"
            transform="matrix(-1 -.003 -.003 1 418.25 -126.748)"
            stroke="currentColor"
            strokeWidth="0.334918"
          />
        </svg>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Content - 2/3 width */}
          <div className="lg:col-span-2">
            {/* Breadcrumb Navigation */}
            <nav className="text-xs text-gray-500 mb-4 flex items-center space-x-2">
              <a href="/learn/home" className="hover:underline">
                Home
              </a>
              <span>›</span>
              <a href="/search?tab=courses" className="hover:underline">
                Courses
              </a>
              <span>›</span>
              <span className="text-gray-700">{title}</span>
            </nav>

            {/* Rating, Level, Duration */}
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center gap-1">
                <div className="flex text-yellow-400">
                  {"★".repeat(Math.floor(rating))}
                  {"☆".repeat(5 - Math.floor(rating))}
                </div>
                <span className="font-medium">{rating}</span>
              </div>
              <div className="flex items-center gap-1">
                <DifficultyIcon
                  difficulty={
                    level.toLowerCase() as
                      | "beginner"
                      | "intermediate"
                      | "advanced"
                  }
                  className="text-gray-600"
                  size={16}
                />
                <span>{level}</span>
              </div>
              <div className="flex items-center gap-1">
                <FluxIcon name="clock-circle" className="w-4 h-4" />
                <span>{duration}</span>
              </div>
            </div>

            {/* Course Title */}
            <h1 className="text-4xl font-bold text-gray-900 mb-4 leading-tight">
              {title}
              <button className="ml-3 text-gray-400 hover:text-gray-600">
                <FluxIcon name="bookmark" className="w-6 h-6" />
              </button>
            </h1>

            {/* Description */}
            <p className="text-gray-700 mb-8  leading-relaxed text-sm">
              {description}
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <button
                onClick={handleRoadmapClick}
                className="bg-[#5553ff] hover:bg-[#504ed2] text-white px-4 py-2 rounded-lg font-semibold transition-colors"
              >
                Continue Personalized Roadmap
              </button>
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <span>Join 2.7M developers at</span>
                <div className="flex items-center gap-2">
                  <Image
                    src="/images/home/<USER>"
                    alt="Meta"
                    width={40}
                    height={40}
                  />
                  <Image
                    src="/images/home/<USER>"
                    alt="Meta"
                    width={40}
                    height={40}
                  />
                  <Image
                    src="/images/home/<USER>"
                    alt="Netflix"
                    width={40}
                    height={40}
                  />
                  <Image
                    src="/images/home/<USER>"
                    alt="Apple"
                    width={20}
                    height={20}
                  />
                  <Image
                    src="/images/home/<USER>"
                    alt="Amazon"
                    width={40}
                    height={40}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Right Sidebar - 1/3 width */}
          <div className="lg:col-span-1 px-6">
            <div
              className="relative z-[60]"
              style={{
                borderRadius: "0.5rem",
                border: "0.0625rem solid #cccbff",
                background: "linear-gradient(150deg, #fff, #eef)",
                boxShadow: "0.5rem 0.5rem 0 #e0e0ff",
              }}
            >
              {/* Top Section - GitHub Student Pack */}
              <div className="bg-[#e0e0ff] p-4 border-b border-purple-200">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-800">
                    Included in GitHub Student Pack
                  </span>
                  <FluxIcon name="github" className="w-5 h-5 text-gray-800" />
                </div>
              </div>

              {/* Middle Section - Content */}
              <div className="p-4 space-y-4">
                {/* Personalized Learning */}
                <div className="bg-[#f4f4ff] rounded-lg p-3 border border-purple-200">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-purple-700">
                      Personalized Learning
                    </span>
                    <button className="text-purple-400 hover:text-purple-600">
                      <FluxIcon name="help-circle" className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-gray-600">
                    <FluxIcon name="lightbulb" className="w-3 h-3" />
                    <span>Powered by AI</span>
                  </div>
                </div>

                {/* Course Stats */}
                <div className="space-y-3 ml-2">
                  {courseStats.map((stat, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 text-sm"
                    >
                      <FluxIcon
                        name={stat.iconName}
                        width={16}
                        height={16}
                        className="text-gray-500"
                      />
                      <span className="text-gray-700 text-xs pt-1">
                        {stat.count} {stat.label}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Certificate Button */}
                <button className="w-full bg-purple-50 hover:bg-purple-100 text-purple-600 border border-purple-200 px-4 py-3 rounded-lg font-medium transition-colors">
                  Add a certificate for $19
                </button>
              </div>

              {/* Bottom Section - Student Pack Link */}
              <div className="p-4 border-t border-purple-200">
                <a
                  href="#"
                  className="block text-center text-purple-600 hover:text-purple-700 text-sm font-medium underline "
                >
                  View Courses in Student Pack
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
