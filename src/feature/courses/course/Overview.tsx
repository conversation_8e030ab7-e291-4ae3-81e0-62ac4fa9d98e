"use client";
// components/Course/Overview.tsx
import React, { useState } from "react";
import FluxIcon from "@/components/fluxIcon";

interface OverviewProps {
  summary?: string;
  objectives?: string[];
  takeawaySkills?: string[];
}

export const Overview = ({
  summary = "The primary goal of this course is to give you an excellent introduction to React and its ecosystem, tackle advanced features, and teach you as many best practices as possible. Furthermore, this course digs deeper into the most popular, latest, and straight forward ways to use React.",
  objectives = [],
  takeawaySkills = ["JavaScript", "Front-end Development", "React"],
}: OverviewProps) => {
  const [showFullSummary, setShowFullSummary] = useState(false);
  const [showAllObjectives, setShowAllObjectives] = useState(false);

  // Truncate summary for mobile view
  const truncatedSummary =
    summary.length > 150 ? summary.substring(0, 150) + "..." : summary;

  // Show only first objective on mobile
  const visibleObjectives = showAllObjectives
    ? objectives
    : objectives.slice(0, 1);

  return (
    <div id="overview" className="bg-white">
      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Course Summary - Desktop */}
        <div className="hidden md:block mb-8">
          <div className="text-gray-700 leading-relaxed">{summary}</div>
        </div>

        {/* Course Summary - Mobile */}
        <div className="md:hidden mb-8">
          <div className="text-gray-700 leading-relaxed">
            {showFullSummary ? summary : truncatedSummary}
            {summary.length > 150 && (
              <button
                onClick={() => setShowFullSummary(!showFullSummary)}
                className="text-blue-600 hover:text-blue-700 ml-1 font-medium"
              >
                {showFullSummary ? "Show Less" : "Show More"}
              </button>
            )}
          </div>
        </div>

        {/* What You'll Learn Section - 只有当有 objectives 时才显示 */}
        {objectives.length > 0 && (
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-6 uppercase tracking-wide">
              WHAT YOU'LL LEARN
            </h2>

            {/* Desktop Objectives */}
            <div className="hidden md:block">
              <div className="space-y-4">
                {objectives.map((objective, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <FluxIcon
                      name="check-mark"
                      width={13}
                      height={10}
                      className="text-green-600 flex-shrink-0"
                    />
                    <span className="text-gray-700 leading-relaxed">
                      {objective}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Mobile Objectives */}
            <div className="md:hidden">
              <div className="space-y-4">
                {visibleObjectives.map((objective, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <FluxIcon
                      name="check-mark"
                      width={13}
                      height={10}
                      className="text-green-600 flex-shrink-0"
                    />
                    <span className="text-gray-700 leading-relaxed">
                      {objective}
                    </span>
                  </div>
                ))}
                {objectives.length > 1 && (
                  <button
                    onClick={() => setShowAllObjectives(!showAllObjectives)}
                    className="text-blue-600 hover:text-blue-700 font-medium text-sm"
                  >
                    {showAllObjectives ? "Show less" : "Show more"}
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Takeaway Skills Section */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 uppercase tracking-wide">
            TAKEAWAY SKILLS
          </h3>
          <div className="flex flex-wrap gap-3">
            {takeawaySkills.map((skill, index) => (
              <div
                key={index}
                className="bg-gray-100 hover:bg-gray-200 transition-colors duration-200 rounded-lg px-4 py-2"
              >
                <span className="text-gray-800 font-medium text-sm">
                  {skill}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
