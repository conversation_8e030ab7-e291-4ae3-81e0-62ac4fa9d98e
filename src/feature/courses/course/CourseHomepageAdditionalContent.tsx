"use client";
/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-23 16:05:55
 */
// components/Course/AdditionalContent.tsx
import React from "react";
import Carousel from "@/components/ui/carousel";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import AILearningSection from "@/feature/shared/AILearningSection";
// Removed unused imports

// Mock review data formatted as course-like objects for SimpleCourseCard
const mockReviewCourses = [
  {
    id: "review-1",
    title: "<PERSON><PERSON>",
    description:
      "I found the course incredibly helpful in breaking down complex System Design concepts into digestible steps. The visual format — starting from requirements, moving through high-level architecture, and diving into trade-offs — made it much easier to internalize key design principles.",
    duration: "Software Engineer",
    level: "Microsoft",
    bookmarked: false,
  },
  {
    id: "review-2",
    title: "<PERSON>",
    description:
      "You've provided a very good, well-structured overview on modern system design which helped reinforce existing knowledge and add more. The AI-driven questions allowed me to think through various scenarios and made it an interactive experience. I'm looking forward to more Pageflux AI content.",
    duration: "Learner",
    level: "Pageflux AI",
    bookmarked: false,
  },
  {
    id: "review-3",
    title: "<PERSON><PERSON><PERSON><PERSON>",
    description:
      "I was looking for a course on System Design which will cover everything and help me clear interviews at the best companies, and this Pageflux AI course was by far the best and the most comprehensive. Additionally, features like mock-interviews and cloud labs are a great boost for learning.",
    duration: "Architectural Technologies",
    level: "Expert",
    bookmarked: false,
  },
  {
    id: "review-4",
    title: "Sarah Wilson",
    description:
      "Perfect for beginners and advanced developers alike. The course covers everything from basic concepts to advanced patterns. Highly recommended!",
    duration: "Senior Developer",
    level: "Google",
    bookmarked: false,
  },
  {
    id: "review-5",
    title: "Alex Chen",
    description:
      "Great course with practical examples. The Redux section was particularly helpful for my current project. Would love to see more advanced topics covered.",
    duration: "Tech Lead",
    level: "Meta",
    bookmarked: false,
  },
];

const companyLogos = [
  { name: "Netflix", src: "/images/home/<USER>" },
  { name: "Apple", src: "/images/home/<USER>" },
  { name: "Meta", src: "/images/home/<USER>" },
  { name: "Google", src: "/images/home/<USER>" },
  { name: "Amazon", src: "/images/home/<USER>" },
  { name: "Coinbase", src: "/images/home/<USER>" },
];

export const AdditionalContent = () => {
  return (
    <>
      {/* Trusted by Section */}
      <section className="bg-white pt-16">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <h2 className="text-2xl font-semibold mb-8 text-gray-900">
            Trusted by <span className="text-blue-600">2.7 million</span>{" "}
            developers working at
          </h2>
          <div className="flex justify-center items-center gap-8 flex-wrap">
            {companyLogos.map((logo) => (
              <div
                key={logo.name}
                className="flex items-center justify-center h-8"
              >
                <img
                  src={logo.src}
                  alt={logo.name}
                  className="h-4  opacity-60 hover:opacity-100 transition-opacity duration-200"
                />
              </div>
            ))}
          </div>
        </div>
      </section>
      <section id="reviews" className="bg-white py-8 pb-12">
        <div className="max-w-6xl mx-auto px-6">
          <Carousel
            itemsPerSlide={3}
            showArrows={true}
            showIndicators={true}
            className="px-12"
          >
            {mockReviewCourses.map((reviewCourse) => (
              <SimpleCourseCard
                key={reviewCourse.id}
                course={reviewCourse}
                className="h-64"
                onClick={() => {
                  // For reviews, we could navigate to a reviews page or do nothing
                  console.log("Review clicked:", reviewCourse.id);
                }}
              />
            ))}
          </Carousel>
        </div>
      </section>
      {/* Hands-on Learning Powered by AI */}
      <AILearningSection />
    </>
  );
};
