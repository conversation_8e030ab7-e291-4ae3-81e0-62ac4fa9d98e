"use client";
/*
 * @Description: Related Courses Component
 * @Author: Devin
 * @Date: 2025-07-23
 */
import React from "react";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import { getCourseUrl } from "@/mockdata/maindata";
import { useRouter } from "next/navigation";
import FAQSection from "@/feature/shared/FAQSection";
import FreeResourcesSection from "@/feature/shared/FreeResourcesSection";
import { allCourseData } from "@/mockdata/maindata";
import { formatDuration } from "@/lib/utils";

interface RelatedProps {
  currentCourseId?: string;
  currentCourseCategory?: string;
}

export const Related = ({
  currentCourseId,
  currentCourseCategory,
}: RelatedProps) => {
  const router = useRouter();
  // 获取相关课程数据
  const getRelatedCourses = () => {
    const allCourses = allCourseData;

    // 过滤掉当前课程
    const otherCourses = allCourses.filter(
      (course) => course.id !== currentCourseId
    );

    // 优先显示同类别的课程
    const sameCategoryCourses = otherCourses.filter(
      (course) => course.category === currentCourseCategory
    );

    const differentCategoryCourses = otherCourses.filter(
      (course) => course.category !== currentCourseCategory
    );

    // 合并并限制为4个课程
    return [...sameCategoryCourses, ...differentCategoryCourses].slice(0, 4);
  };

  const relatedCoursesData = getRelatedCourses();

  // 通用FAQ数据
  const generalFAQs = [
    {
      question: "How do I access course materials?",
      answer:
        "Once enrolled, you can access all course materials through your dashboard. Materials include video lessons, exercises, quizzes, and downloadable resources.",
    },
    {
      question: "Can I learn at my own pace?",
      answer:
        "Yes! Our courses are designed for self-paced learning. You can progress through the material at a speed that works for you, with no strict deadlines.",
    },
    {
      question: "Do I get a certificate upon completion?",
      answer:
        "Yes, you'll receive a certificate of completion when you finish all course requirements. This certificate can be shared on your professional profiles.",
    },
    {
      question: "Is there support available if I get stuck?",
      answer:
        "Absolutely! We provide community forums, Q&A sections, and direct instructor support to help you when you encounter challenges.",
    },
  ];
  return (
    <section id="related" className="bg-white py-16">
      <div className="max-w-6xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Related Courses and Skill Paths
          </h2>
        </div>

        {/* Related Courses Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {relatedCoursesData.slice(0, 4).map((course) => (
            <SimpleCourseCard
              key={course.id}
              course={{
                id: course.id,
                title: course.title,
                description: course.description,
                duration: formatDuration(course.duration),
                difficulty:
                  course.level.charAt(0).toUpperCase() + course.level.slice(1),
                bookmarked: false,
              }}
              className="h-64"
              onClick={() => {
                const courseUrl = getCourseUrl(course);
                router.push(courseUrl);
              }}
            />
          ))}
        </div>

        {/* Explore Full Catalog Button */}
        <div className="text-center mb-16">
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors">
            Explore Full Catalog
          </button>
        </div>
        {/* Free Resources Section */}
        <FreeResourcesSection
          title="Free Resources"
          className="py-16"
          containerSize="lg"
        />
        {/* FAQ Section */}
        <FAQSection
          title="Frequently Asked Questions"
          faqs={generalFAQs}
          className="py-0"
          containerSize="lg"
        />
      </div>
    </section>
  );
};
