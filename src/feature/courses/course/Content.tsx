"use client";
// components/Course/Content.tsx
import React, { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import FluxIcon from "@/components/fluxIcon";
import type {
  NodeLesson,
  LearningNode,
  UserLessonProgressMap,
} from "@/types/openapi";
import { useUserLessonProgress } from "@/hooks/useUserLessonProgress";

interface Lesson {
  id: string;
  title: string;
  type: "lesson" | "quiz" | "challenge" | "exercise" | "project";
  completed?: boolean;
  isLocked?: boolean;
  duration?: string;
  contentFlowId?: string; // 添加 content flow ID
  lessonId?: string; // 添加 lesson ID
}

interface Section {
  id: string;
  title: string;
  description?: string;
  lessons: Lesson[];
  isExpanded?: boolean;
}

interface ContentProps {
  courseData: {
    nodeInfo: LearningNode;
    nodeLessons: NodeLesson[];
  };
}

export const Content = ({ courseData }: ContentProps) => {
  const router = useRouter();
  const { fetchProgressByNodeOrPath } = useUserLessonProgress();

  // 进度状态
  const [allLessonsProgress, setAllLessonsProgress] =
    useState<UserLessonProgressMap>({});

  // 获取用户学习进度
  useEffect(() => {
    const fetchUserProgress = async () => {
      if (!courseData.nodeInfo.id) return;

      try {
        const progressData = await fetchProgressByNodeOrPath(
          courseData.nodeInfo.id
        );
        setAllLessonsProgress(progressData);
      } catch (error) {
        console.error("Failed to fetch user progress:", error);
      }
    };

    fetchUserProgress();
  }, [courseData.nodeInfo.id, fetchProgressByNodeOrPath]);

  const [sections, setSections] = useState<Section[]>([]);
  const [expandAll, setExpandAll] = useState(false);

  // 使用 useMemo 来避免无限循环
  const convertedSections = useMemo(() => {
    // 使用新的 API 数据
    if (courseData && courseData.nodeLessons.length > 0) {
      // 每个 lesson 作为一个 section，content_flow 作为该 section 下的 lessons
      return courseData.nodeLessons.map((lesson, index) => {
        const lessonId = lesson.lesson_id || lesson.id || `lesson-${index}`;
        const lessonProgress = allLessonsProgress[lessonId];
        const completedContentFlowIds =
          lessonProgress?.completed_content_flow_ids || [];

        return {
          id: lessonId,
          title: lesson.lesson_title || `Lesson ${index + 1}`,
          description: lesson.lesson_description || "",
          isExpanded: index === 0, // 第一个 lesson 默认展开
          lessons: (lesson.content_flow || []).map((content, contentIndex) => {
            // 根据 content_flow 的 type 判断类型
            let type: "lesson" | "quiz" | "challenge" | "exercise" | "project" =
              "lesson";

            switch (content.type) {
              case "multiple_choice_quiz":
              case "fill_in_blank_quiz":
              case "interactive_quiz":
                type = "quiz";
                break;
              case "practice_exercise":
                type = "exercise";
                break;
              case "code_snippet":
                type = "challenge";
                break;
              default:
                type = "lesson";
            }

            return {
              id: content.id || `content-${contentIndex}`,
              title:
                content.title ||
                `${content.type.replace(/_/g, " ")} ${contentIndex + 1}`,
              type,
              completed: content.id
                ? completedContentFlowIds.includes(content.id)
                : false,
              duration: "5 min", // 默认时长
              contentFlowId: content.id,
              lessonId: lessonId,
            };
          }),
        };
      });
    }

    // 如果没有数据，返回空数组
    return [];
  }, [courseData, allLessonsProgress]);

  // 当 convertedSections 变化时更新 sections
  useEffect(() => {
    setSections(convertedSections);
  }, [convertedSections]);

  const totalLessons = sections.reduce(
    (acc, section) => acc + section.lessons.length,
    0
  );
  const totalQuizzes = sections.reduce(
    (acc, section) =>
      acc + section.lessons.filter((lesson) => lesson.type === "quiz").length,
    0
  );
  const totalChallenges = sections.reduce(
    (acc, section) =>
      acc +
      section.lessons.filter((lesson) => lesson.type === "challenge").length,
    0
  );

  const toggleExpandAll = () => {
    const newExpandState = !expandAll;
    setExpandAll(newExpandState);
    setSections(
      sections.map((section) => ({ ...section, isExpanded: newExpandState }))
    );
  };

  const toggleSection = (sectionId: string) => {
    setSections(
      sections.map((section) =>
        section.id === sectionId
          ? { ...section, isExpanded: !section.isExpanded }
          : section
      )
    );
  };

  // 处理小节点击，跳转到课程页面
  const handleLessonClick = (lesson: Lesson) => {
    if (lesson.isLocked || !lesson.lessonId) return;

    // 跳转到 /courses/[courseId]/[lessonId] 页面，并添加 content_flow_id 参数
    const courseId = courseData.nodeInfo.id;
    const url = lesson.contentFlowId
      ? `/courses/${courseId}/${lesson.lessonId}?content_flow_id=${lesson.contentFlowId}`
      : `/courses/${courseId}/${lesson.lessonId}`;

    router.push(url);
  };

  return (
    <section id="content" className="bg-white py-10">
      <div className="max-w-4xl mx-auto px-6 border py-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Content</h2>
            <p className="text-gray-600 text-sm">
              {totalLessons} Lessons • {totalQuizzes} Quizzes •{" "}
              {totalChallenges} Challenges
            </p>
          </div>
          <button
            onClick={toggleExpandAll}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800 font-medium"
          >
            <span>Expand All</span>
            <FluxIcon
              name="chevron-down"
              className={`w-4 h-4 transition-transform ${
                expandAll ? "rotate-180" : ""
              }`}
            />
          </button>
        </div>

        {/* Content Sections */}
        <div className="space-y-0 ">
          {sections.map((section, index) => (
            <SectionCard
              key={section.id}
              section={section}
              index={index + 1}
              onToggle={() => toggleSection(section.id)}
              onLessonClick={handleLessonClick}
            />
          ))}
        </div>

        {/* Certificate of Completion */}
        <div className="bg-[#f9fafb]  border-gray-200 p-6 my-4">
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0">
              <FluxIcon name="certificate" className="w-6 h-6 text-gray-600" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Certificate of Completion
              </h3>
              <p className="text-gray-600 text-sm mb-4">
                Showcase your accomplishment by sharing your certificate of
                completion.
              </p>
            </div>
          </div>
        </div>
        <div className="text-sm text-gray-500 p-6 pt-0 pl-0">
          <span className="mr-2">Course Author:</span>
          <span className="font-medium text-gray-700 underline">
            Manuel Bieh
          </span>
          <span className="mx-2">and</span>
          <span className="font-medium text-gray-700 underline">
            Sibylle Lancaster
          </span>
        </div>
      </div>

      {/* Developed by MAANG Engineers */}
      <div className=" max-w-4xl  mx-auto  my-4 mt-12">
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
              <FluxIcon
                name="check-simple"
                className="w-4 h-4 text-green-600"
              />
            </div>
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              Developed by MAANG Engineers
            </h3>
          </div>
        </div>
        <p className="text-gray-600 text-md leading-relaxed">
          Every Pageflux AI lesson is designed by our in-house team of ex-MAANG
          software engineers and PhD computer science educators, and developed
          in consultation with developers and data scientists working at Meta,
          Google, and more. Our mission is to get you hands-on with the
          necessary skills to stay ahead in a constantly changing industry. No
          video, no fluff. Just interactive, project-based learning with
          personalized feedback that adapts to your goals and experience.
        </p>
      </div>
    </section>
  );
};

interface SectionCardProps {
  section: Section;
  index: number;
  className?: string;
  onToggle: () => void;
  onLessonClick: (lesson: Lesson) => void;
}

const SectionCard = ({
  section,
  index,
  onToggle,
  onLessonClick,
}: SectionCardProps) => {
  return (
    <div className="bg-white border-b border-gray-200">
      {/* Section Header */}
      <button
        onClick={onToggle}
        className="w-full p-6 text-left flex justify-between items-start hover:bg-gray-50 transition-colors"
      >
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <span className="text-lg font-semibold text-gray-900">
              {index}. {section.title}
            </span>
          </div>
          <p className="text-gray-600 text-sm leading-relaxed">
            {section.description}
          </p>
        </div>
        <div className="ml-4 flex-shrink-0 flex gap-6 ">
          {/* section nums */}
          {!section.isExpanded && (
            <span className="text-sm text-[#6b7280]">
              {section.lessons.length} lessons
            </span>
          )}
          <FluxIcon
            name="chevron-down"
            className={`w-5 h-5 text-gray-400 transition-transform ${
              section.isExpanded ? "rotate-180" : ""
            }`}
          />
        </div>
      </button>

      {/* Section Content */}
      {section.isExpanded && (
        <div className="px-6 pb-6">
          <div className="space-y-3">
            {section.lessons.map((lesson) => (
              <LessonItem
                key={lesson.id}
                lesson={lesson}
                onClick={() => onLessonClick(lesson)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

interface LessonItemProps {
  lesson: Lesson;
  onClick: () => void;
}

const LessonItem = ({ lesson, onClick }: LessonItemProps) => {
  const getStatusIcon = () => {
    if (lesson.isLocked) {
      return (
        <div className="w-6 h-6 rounded-full flex items-center justify-center">
          <FluxIcon name="lock" className="w-4 h-4 text-gary" />
        </div>
      );
    }

    if (lesson.completed) {
      return (
        <div className="w-5 h-5 bg-green-600 rounded-full flex items-center justify-center">
          <div className="text-white">
            <FluxIcon name="check-simple" className="w-4 h-4" />
          </div>
        </div>
      );
    }

    return (
      <div className="w-5 h-5 border-2 border-gray-300 rounded-full"></div>
    );
  };

  return (
    <div
      className={`flex items-center gap-4 p-3 hover:bg-gray-50 rounded-lg transition-colors ${
        lesson.isLocked ? "cursor-not-allowed" : "cursor-pointer"
      }`}
      onClick={lesson.isLocked ? undefined : onClick}
    >
      {getStatusIcon()}
      <div className="flex items-center gap-2 flex-1">
        <span
          className={`text-sm ${
            lesson.isLocked ? "text-gray-400" : "text-gray-700"
          }`}
        >
          {lesson.title}
        </span>
      </div>
      {lesson.type === "quiz" && (
        <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
          Quiz
        </div>
      )}
      {lesson.type === "challenge" && (
        <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
          Challenge
        </div>
      )}
    </div>
  );
};
