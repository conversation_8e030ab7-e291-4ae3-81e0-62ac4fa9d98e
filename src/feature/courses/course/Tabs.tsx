/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-23 16:05:31
 */
// components/Course/Tabs.tsx
"use client"; // if using Next.js app router
import FluxIcon from "@/components/fluxIcon";
import React, { useEffect, useCallback, useState } from "react";
import { useRouter } from "next/navigation";

// Custom hook for smooth scrolling to sections
const useScrollToSection = () => {
  const scrollToSection = useCallback((sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80; // Account for sticky header
      window.scrollTo({
        top: offsetTop,
        behavior: "smooth",
      });
    }
  }, []);

  return scrollToSection;
};

// Custom hook for detecting current section in viewport
const useActiveSection = (sectionIds: string[]) => {
  const [activeSection, setActiveSection] = useState(sectionIds[0]);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 100; // Offset for sticky header

      for (let i = sectionIds.length - 1; i >= 0; i--) {
        const element = document.getElementById(sectionIds[i]);
        if (element && element.offsetTop <= scrollPosition) {
          setActiveSection(sectionIds[i]);
          break;
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Check initial position

    return () => window.removeEventListener("scroll", handleScroll);
  }, [sectionIds]);

  return activeSection;
};

interface TabsProps {
  courseTitle?: string;
  courseId: string;
}

export const Tabs = ({
  courseTitle = "React Deep Dive: From Beginner to Advanced",
  courseId,
}: TabsProps) => {
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);

  const tabs = [
    { id: "overview", label: "Overview", iconName: "overview" },
    { id: "content", label: "Content", iconName: "content" },
    // { id: "reviews", label: "Reviews", iconName: "reviews" },
    // { id: "related", label: "Related", iconName: "related" },
  ];

  const sectionIds = tabs.map((tab) => tab.id);
  const scrollToSection = useScrollToSection();
  const activeTab = useActiveSection(sectionIds);

  const handleTabClick = (tabId: string) => {
    scrollToSection(tabId);
  };

  // Detect when to show the sticky tabs
  useEffect(() => {
    const handleScroll = () => {
      // Get the tabs element position
      const tabsElement = document.getElementById("tabs-anchor");
      if (tabsElement) {
        const rect = tabsElement.getBoundingClientRect();
        // Show sticky tabs when the original tabs are 50px above the viewport
        setIsVisible(rect.top < -50);
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Check initial position

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleRoadmapClick = () => {
    router.push(`/courses/${courseId}/roadmap`);
  };

  return (
    <>
      {/* Sticky Tabs - Always rendered but conditionally visible */}
      <div
        className={`fixed top-0 left-0 right-0 z-50 bg-white shadow-lg transition-transform duration-300 ${
          isVisible ? "translate-y-0" : "-translate-y-full"
        }`}
      >
        {/* Course Info Bar */}
        <div className="bg-[#e8e8ff] px-6 py-3 border-b border-purple-200">
          <div className="max-w-4xl mx-auto flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded text-xs font-medium uppercase tracking-wide">
                COURSE
              </span>
              <span className="text-gray-800 font-medium text-sm">
                {courseTitle}
              </span>
            </div>
            <button
              className="bg-[#5553ff] hover:bg-[#504ed2] text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              onClick={handleRoadmapClick}
            >
              Continue Personalized Roadmap
            </button>
          </div>
        </div>

        {/* Tabs Navigation */}
        <div className="bg-[#f8f8ff] border-b px-6">
          <div className="max-w-4xl mx-auto px-4 flex space-x-8 text-sm font-medium">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabClick(tab.id)}
                className={`flex items-center gap-2 p-6 pl-0 text-[1rem] font-medium transition-colors ${
                  activeTab === tab.id ? "text-blue-600" : "text-gray-600"
                }`}
              >
                <FluxIcon name={tab.iconName} width={18} height={18} />
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Static Tabs - Always rendered, maintains layout space */}
      <div id="tabs-anchor" className="bg-white border-b">
        {/* Tabs Navigation */}
        <div className="bg-[#f8f8ff] border-b px-6">
          <div className="max-w-4xl mx-auto px-4 flex space-x-8 text-sm font-medium">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabClick(tab.id)}
                className={`flex items-center gap-2 p-6 pl-0 text-[1rem] font-medium transition-colors ${
                  activeTab === tab.id ? "text-blue-600" : "text-gray-600"
                }`}
              >
                <FluxIcon name={tab.iconName} width={18} height={18} />
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};
