/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-24 20:06:21
 */
"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import SimpleCourseCard from "@/components/cards/SimpleCourseCard";
import { MockApiCourseData } from "@/types/mockapi";
import { getCourseUrl } from "@/mockdata/maindata";
import { formatDuration } from "@/lib/utils";
import { useRouter } from "next/navigation";
import {
  getDifficultyStandard,
  getCourseTitle,
  getCourseDescription,
  getCourseWhatYouWillLearn,
} from "@/utils/course.utils";

interface CourseGridProps {
  courses: MockApiCourseData[];
  itemsPerPage?: number;
}

export default function CourseGrid({
  courses,
  itemsPerPage = 12,
}: CourseGridProps) {
  const [displayedItems, setDisplayedItems] = useState(itemsPerPage);
  const router = useRouter();

  const visibleCourses = courses.slice(0, displayedItems);
  const hasMore = displayedItems < courses.length;

  const handleLoadMore = () => {
    setDisplayedItems((prev) => Math.min(prev + itemsPerPage, courses.length));
  };

  console.log(courses, "courses");

  return (
    <div className="space-y-6">
      {/* Course Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {visibleCourses.map((course) => {
          return (
            <SimpleCourseCard
              key={course.id || "unknown"}
              course={{
                id: course.id || "unknown",
                title: getCourseTitle(course),
                description: getCourseDescription(course),
                difficulty: getDifficultyStandard(course),
                duration: formatDuration(course.read_time),
                bookmarked: false,
              }}
              onClick={() => {
                // 创建一个兼容的对象用于 getCourseUrl
                const compatibleCourse = {
                  id: course.id || "unknown",
                  title: getCourseTitle(course),
                  description: getCourseDescription(course),
                  level: getDifficultyStandard(course).toLowerCase(),
                  type: "course",
                  category: "programming",
                  tags: [...course.tags, ...course.skills],
                  duration: course.read_time,
                  price: `$${course.price}`,
                  rating: 4.5,
                  students: "1k+",
                  image: "/images/course-placeholder.jpg",
                  playgrounds: 0,
                  quizzes: course.aggregated_widget_stats.Quiz || 0,
                  createAt: new Date(course.first_published_time).getTime(),
                  authors: [],
                  skills: course.skills,
                  whatYouWillLearn: getCourseWhatYouWillLearn(course),
                  developedByMAANG: false,
                  instructor: "Expert Instructor",
                  difficulty: getDifficultyStandard(course),
                  language: "en",
                  content: {
                    period: "week" as const,
                    sections: [],
                  },
                  currentProgress: {
                    completedLessons: [],
                    totalLessons: 0,
                    totalQuizzes: 0,
                    totalChallenges: 0,
                    percentage: 0,
                  },
                };
                const courseUrl = getCourseUrl(compatibleCourse);
                router.push(courseUrl);
              }}
            />
          );
        })}
      </div>

      {/* Load More Button */}
      {hasMore && (
        <div className="flex justify-center pt-4">
          <Button
            onClick={handleLoadMore}
            variant="outline"
            className="px-8 py-2 border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Load More
          </Button>
        </div>
      )}
    </div>
  );
}
