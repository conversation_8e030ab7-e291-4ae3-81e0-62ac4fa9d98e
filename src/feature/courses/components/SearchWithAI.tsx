"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>rk<PERSON>, Send, Maximize2 } from "lucide-react";

interface SearchWithAIProps {
  className?: string;
}

export default function SearchWithAI({ className }: SearchWithAIProps) {
  const [query, setQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSearch = async () => {
    if (!query.trim()) return;

    setIsLoading(true);
    // Simulate AI search
    setTimeout(() => {
      setIsLoading(false);
      console.log("AI Search:", query);
    }, 2000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSearch();
    }
  };

  return (
    <Card className={`w-full ${className} `}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg font-semibold text-purple-600">
            <Sparkles className="h-5 w-5" />
            Search with AI
          </CardTitle>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Maximize2 className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-3">
          <Textarea
            placeholder="Hi! What do you want to learn today? Let me know your interests, and I'll find the best courses for you!"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            className="min-h-[120px] resize-none border-gray-200 focus:border-purple-300 focus:ring-purple-200"
            disabled={isLoading}
          />

          <Button
            onClick={handleSearch}
            disabled={!query.trim() || isLoading}
            className="w-full bg-purple-600 hover:bg-purple-700 text-white"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Searching...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Send className="h-4 w-4" />
                Search
              </div>
            )}
          </Button>
        </div>

        {/* Suggested Prompts */}
        <div className="space-y-2">
          <p className="text-sm text-gray-600 font-medium">Try asking:</p>
          <div className="space-y-1">
            {[
              "Find beginner-friendly React courses",
              "I want to learn system design",
              "Show me Python interview prep courses",
              "Advanced JavaScript concepts",
            ].map((suggestion, index) => (
              <button
                key={index}
                onClick={() => setQuery(suggestion)}
                className="block w-full text-left text-sm text-gray-500 hover:text-purple-600 hover:bg-purple-50 p-2 rounded transition-colors"
                disabled={isLoading}
              >
                "{suggestion}"
              </button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
