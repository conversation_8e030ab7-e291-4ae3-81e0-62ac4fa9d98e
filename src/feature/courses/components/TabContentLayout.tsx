"use client";

import React from "react";
import { Container } from "@/components/ui/container";
import Search<PERSON>ithAI from "./SearchWithAI";
import CourseGrid from "./CourseGrid";
import FluxIcon from "@/components/fluxIcon";
import { MockApiCourseData } from "@/types/mockapi";

interface TabConfig {
  title: string;
  description: string;
  icon?: React.ReactNode;
  buttonText?: string;
  buttonColor?: string;
  showCourseGrid?: boolean;
  emptyStateMessage?: string;
}

interface TabContentLayoutProps {
  activeTab: string;
  filteredCourses?: MockApiCourseData[];
  tabConfig?: TabConfig;
  showSearchAI?: boolean;
  containerSize?: "sm" | "md" | "lg" | "lg-plus" | "xl" | "2xl" | "full";
  className?: string;
}

const defaultTabConfigs: Record<string, TabConfig> = {
  all: {
    title: "",
    description: "",
    showCourseGrid: true,
  },
  courses: {
    title: "Courses",
    description:
      "Comprehensive courses to master new skills and advance your career.",
    showCourseGrid: true,
    icon: <FluxIcon name="book-open" className="w-8 h-8 text-blue-600" />,
  },
  "cloud-labs": {
    title: "Cloud Labs",
    description:
      "Hands-on cloud computing labs to practice real-world scenarios in AWS, Azure, and Google Cloud.",
    showCourseGrid: true,
    icon: <FluxIcon name="cloud-simple" className="w-8 h-8 text-blue-600" />,
  },
  projects: {
    title: "Projects",
    description:
      "Build real-world projects to showcase your skills and create an impressive portfolio.",
    showCourseGrid: true,
    icon: <FluxIcon name="briefcase" className="w-8 h-8 text-green-600" />,
  },
  paths: {
    title: "Learning Paths",
    description:
      "Structured learning journeys to guide you from beginner to expert in your chosen field.",
    showCourseGrid: true,
    icon: <FluxIcon name="map" className="w-8 h-8 text-purple-600" />,
  },
  assessments: {
    title: "Assessments",
    description:
      "Test your knowledge and skills with comprehensive assessments and get certified.",
    showCourseGrid: true,
    icon: <FluxIcon name="check-circle" className="w-8 h-8 text-orange-600" />,
  },
  "mock-interviews": {
    title: "Mock Interviews",
    description:
      "Practice with AI-powered mock interviews to ace your next technical interview.",
    showCourseGrid: true,
    icon: <FluxIcon name="chat" className="w-8 h-8 text-red-600" />,
  },
};

export default function TabContentLayout({
  activeTab,
  filteredCourses = [],
  tabConfig,
  showSearchAI = true,
  containerSize = "xl",
  className = "",
}: TabContentLayoutProps) {
  const config =
    tabConfig || defaultTabConfigs[activeTab] || defaultTabConfigs.all;

  const renderContent = () => {
    // For "all" tab or tabs that should show course grid
    if (config.showCourseGrid) {
      return (
        <div className="space-y-6">
          <CourseGrid courses={filteredCourses} itemsPerPage={12} />
        </div>
      );
    }
  };

  return (
    <div className={`py-6 ${className} bg-[#f9fafb]`}>
      <Container size={containerSize}>
        <div className="flex gap-4 min-h-screen items-start">
          {/* Left Side - Main Content */}
          <div className="flex-1 bg-white border border-gray-200 rounded-lg p-6">
            {renderContent()}
          </div>

          {/* Right Side - Search with AI (Sticky) - Only show if enabled */}
          {showSearchAI && (
            <div className="w-80 flex-shrink-0 sticky top-56 h-[34rem]">
              <SearchWithAI className="h-full" />
            </div>
          )}
        </div>
      </Container>
    </div>
  );
}
