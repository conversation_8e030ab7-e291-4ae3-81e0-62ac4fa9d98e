/*
 * @Description: Course Actions Component - Certificate and Completion
 * @Author: Devin
 * @Date: 2025-07-23
 */
"use client";

import React from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import FluxIcon from "@/components/fluxIcon";

interface CourseActionsProps {
  courseId?: string;
  courseName?: string;
  isCompleted?: boolean;
  onMarkComplete?: () => void;
  className?: string;
}

export default function CourseActions({
  courseId = "react-beginner-to-advanced",
  courseName = "course",
  isCompleted = false,
  onMarkComplete,
  className = "",
}: CourseActionsProps) {
  const handleMarkComplete = () => {
    if (onMarkComplete) {
      onMarkComplete();
    } else {
      console.log("Mark course as completed");
    }
  };

  return (
    <div className={`mt-8 ${className}`}>
      {/* Course Certificate Link */}
      <Link
        href={`/courses/${courseId}/certificate`}
        className="mb-8 ml-6 mr-6 mt-5 flex flex-grow-0 cursor-pointer flex-nowrap items-center justify-center space-x-4 text-ellipsis break-words p-4 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
      >
        <FluxIcon
          name="certificate-detailed"
          width={28}
          height={24}
          className="text-black dark:text-gray-200 fill-current flex-shrink-0"
        />
        <h5 className="text-base font-semibold m-0 text-gray-800 dark:text-gray-200">
          Course Certificate
        </h5>
      </Link>

      {/* Mark Course as Completed Button */}
      <div className="mx-6 mb-6">
        <Button
          variant="outline"
          onClick={handleMarkComplete}
          disabled={isCompleted}
          className={`w-full py-3 px-4 text-sm font-medium border transition-all duration-200 ${
            isCompleted
              ? "bg-green-50 border-green-200 text-green-700 cursor-not-allowed"
              : "border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:border-gray-500 dark:hover:bg-gray-800"
          }`}
          title={
            isCompleted ? "Course already completed" : "Set course completion"
          }
        >
          {isCompleted ? (
            <>
              <FluxIcon name="check-simple" className="w-4 h-4 mr-2" />
              Course Completed
            </>
          ) : (
            <>Mark course as Completed</>
          )}
        </Button>
      </div>
    </div>
  );
}
