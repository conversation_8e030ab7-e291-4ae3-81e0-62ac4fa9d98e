// components/SidebarCollectionCategories.tsx
"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ChevronUp, ChevronDown } from "lucide-react";

interface Lesson {
  title: string;
  href: string;
  type?: string;
  completed?: boolean;
  current?: boolean;
  onClick?: () => void; // 添加点击处理函数
}

interface Category {
  id: string;
  title: string;
  lessons: Lesson[];
}

interface SidebarCollectionCategoriesProps {
  categories: Category[];
  openSections?: string[];
  onToggleSection?: (id: string) => void;
}

export default function SidebarCollectionCategories({
  categories,
  openSections = [],
  onToggleSection,
}: SidebarCollectionCategoriesProps) {
  const router = useRouter();
  const [openMap, setOpenMap] = useState<Record<string, boolean>>(
    Object.fromEntries(
      categories.map((cat) => [cat.id, openSections.includes(cat.id)])
    )
  );

  const toggleCategory = (id: string) => {
    if (onToggleSection) {
      onToggleSection(id);
    } else {
      setOpenMap((prev) => ({ ...prev, [id]: !prev[id] }));
    }
  };

  // Update openMap when openSections prop changes
  const currentOpenMap = onToggleSection
    ? Object.fromEntries(
        categories.map((cat) => [cat.id, openSections.includes(cat.id)])
      )
    : openMap;

  return (
    <div id="sidebar-collection-categories" className="flex flex-col py-2 ">
      {categories.map((category) => (
        <div key={category.id} className="mb-4 flex flex-col">
          {/* 分类标题 */}
          <div
            className="flex items-center justify-between py-2 pb-4  cursor-pointe  px-4"
            onClick={() => toggleCategory(category.id)}
          >
            <h5 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              {category.title}
            </h5>
            <button aria-label="Toggle category">
              {currentOpenMap[category.id] ? (
                <ChevronUp className="w-5 h-5 text-gray-500 dark:text-gray-300" />
              ) : (
                <ChevronDown className="w-5 h-5 text-gray-500 dark:text-gray-300" />
              )}
            </button>
          </div>

          {/* 分类下的课程列表 */}
          {currentOpenMap[category.id] && (
            <div className="relative w-full">
              {category.lessons.map((lesson, idx) => {
                const isLast = idx === category.lessons.length - 1;
                const isSelected = lesson.current || false;
                const isCompleted = lesson.completed || false;

                return (
                  <div
                    key={idx}
                    className={`relative pl-6 pr-2 ${
                      !isSelected
                        ? "hover:bg-gray-50 dark:hover:bg-gray-800"
                        : ""
                    } transition-colors duration-200 cursor-pointer`}
                    onClick={(e) => {
                      e.preventDefault();
                      if (lesson.onClick) {
                        lesson.onClick();
                      } else {
                        router.push(lesson.href);
                      }
                    }}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        if (lesson.onClick) {
                          lesson.onClick();
                        } else {
                          router.push(lesson.href);
                        }
                      }
                    }}
                  >
                    {/* 选中背景 - 最底层 */}
                    {isSelected && (
                      <div
                        className="absolute inset-0 bg-gray-200 dark:bg-gray-700"
                        style={{ zIndex: 1 }}
                      />
                    )}

                    {/* 连接线 - 中间层 */}
                    <div
                      className={`ml-2 mr-1 flex flex-nowrap items-start justify-start overflow-visible relative pb-3 pt-2 top-2 ${
                        !isLast
                          ? "border-l border-solid border-black dark:border-gray-600"
                          : ""
                      }`}
                      style={{ zIndex: 2 }}
                    >
                      <div
                        className="flex w-full relative"
                        style={{ zIndex: 3 }}
                      >
                        {/* 圆点 - 最上层 */}
                        <div
                          className="-ml-[8px] flex-shrink-0 flex-grow-0 relative"
                          style={{
                            marginTop: "-8px",
                            width: "16px",
                            zIndex: 4,
                          }}
                        >
                          <div
                            className={`flex-shrink-0 rounded-full ${
                              isCompleted
                                ? "bg-green-500" // 已完成：绿色（优先级最高）
                                : isSelected
                                ? "bg-black dark:bg-black" // 选中但未完成：黑色
                                : "bg-white border border-black dark:bg-white dark:border-black" // 默认：白色带边框
                            }`}
                            style={{ height: "16px", width: "16px" }}
                          />
                        </div>

                        {/* 课程标题 - 最上层 */}
                        <Link
                          href={lesson.href}
                          className={`mb-auto mr-1 flex-grow cursor-pointer overflow-hidden duration-none block relative`}
                          style={{
                            marginLeft: "20px",
                            marginTop: "-12px",
                            textDecoration: "none",
                            zIndex: 3,
                          }}
                          onClick={(e) => {
                            e.preventDefault();
                            if (lesson.onClick) {
                              lesson.onClick();
                            } else {
                              router.push(lesson.href);
                            }
                          }}
                        >
                          <div className="flex w-full justify-between">
                            <span className="text-[14px] leading-[26px]">
                              {lesson.title}
                            </span>
                          </div>
                        </Link>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
