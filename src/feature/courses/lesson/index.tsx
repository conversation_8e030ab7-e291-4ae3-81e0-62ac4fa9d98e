/*
 * @Description: 课程课时组件 - 只传入 courseId 和 lessonId
 * @Author: Devin
 * @Date: 2025-08-16
 */
"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import CourseSidebar from "@/feature/courses/lesson/CourseSidebar";
import CourseContent from "@/feature/courses/lesson/CourseContent";
import {
  LearningLesson,
  NodeLesson,
  AtomicContent,
  UserLessonProgressMap,
} from "@/types/openapi";
import { ApiQuestion } from "@/types/course";
import { learningNodeService } from "@/service/learning-nodes.service";
import { learningLessonService } from "@/service/learning-lessons.service";
import { useUserLessonProgress } from "@/hooks/useUserLessonProgress";
import { progressCache } from "@/lib/api-cache";

interface LessonComponentProps {
  courseId: string;
  lessonId: string;
}

// 定义课程节点数据结构
interface CourseNodeData {
  id: string;
  title: string;
  description?: string;
  lessons: NodeLesson[];
}

// 扩展的课程数据结构，包含完整的课程详细信息
interface EnhancedCourseNodeData extends CourseNodeData {
  lessonsDetails: Map<string, LearningLesson>; // lesson_id -> LearningLesson
}

// 处理单个小节内容的函数
const processSectionContent = (sectionContent?: AtomicContent) => {
  if (!sectionContent) {
    return {
      htmlContent: "",
      mdContent: "",
      contentType: "markdown" as const,
      questions: [],
    };
  }

  let htmlContent = "";
  let mdContent = "";
  let contentType: "markdown" | "quiz" = "markdown";
  const questions: ApiQuestion[] = [];

  switch (sectionContent.type) {
    case "text_explanation":
      const textData = sectionContent.data as {
        content?: string;
        html?: string;
        body?: string;
        title?: string;
      };
      // 优先使用 body，然后是 content，最后是 html
      const explanationContent =
        textData.body || textData.content || textData.html;
      if (explanationContent) {
        mdContent += explanationContent + "\n\n";
        htmlContent += explanationContent + "\n\n";
      }
      break;

    case "code_snippet":
      const codeData = sectionContent.data as {
        code?: string;
        language?: string;
      };
      if (codeData.code) {
        const codeBlock = `\`\`\`${codeData.language || ""}\n${
          codeData.code
        }\n\`\`\`\n\n`;
        mdContent += codeBlock;
        htmlContent += `<pre><code class="language-${
          codeData.language || ""
        }">${codeData.code}</code></pre>\n\n`;
      }
      break;

    case "multiple_choice_quiz":
    case "fill_in_blank_quiz":
    case "interactive_quiz":
      contentType = "quiz";
      // 根据新的数据结构处理测验
      const quizData = sectionContent.data as {
        questions?: Array<{
          question?: string;
          options?: string[];
          correct_answer_index?: number;
          correct_answer_indices?: number[];
          single_answer?: boolean;
          difficulty?: number;
        }>;
        // 兼容旧格式
        question?: string;
        options?: string[];
        correct_answer_index?: number;
        correct_answer_indices?: number[];
        single_answer?: boolean;
        difficulty?: number;
      };

      // 处理新格式（包含 questions 数组）
      if (quizData.questions && Array.isArray(quizData.questions)) {
        quizData.questions.forEach((questionData) => {
          const formattedQuestion = {
            questionText: questionData.question || "",
            questionTextHtml: questionData.question || "",
            questionOptions: (questionData.options || []).map(
              (option: string, index: number) => ({
                text: option,
                id: `option-${index}`,
                correct:
                  questionData.correct_answer_indices?.includes(index) ||
                  questionData.correct_answer_index === index ||
                  false,
                explanation: {
                  mdText: "",
                  mdHtml: "",
                },
                mdHtml: "",
              })
            ),
            multipleAnswers: !questionData.single_answer,
          };
          questions.push(formattedQuestion);
        });
      } else {
        // 兼容旧格式（单个问题）
        const formattedQuestion = {
          questionText: quizData.question || "",
          questionTextHtml: quizData.question || "",
          questionOptions: (quizData.options || []).map(
            (option: string, index: number) => ({
              text: option,
              id: `option-${index}`,
              correct:
                quizData.correct_answer_indices?.includes(index) ||
                quizData.correct_answer_index === index ||
                false,
              explanation: {
                mdText: "",
                mdHtml: "",
              },
              mdHtml: "",
            })
          ),
          multipleAnswers: !quizData.single_answer,
        };
        questions.push(formattedQuestion);
      }
      break;

    case "diagram_description":
    case "flowchart_description":
      const diagramData = sectionContent.data as { description?: string };
      if (diagramData.description) {
        mdContent += diagramData.description + "\n\n";
        htmlContent += `<p>${diagramData.description}</p>\n\n`;
      }
      break;

    case "math_formula":
      const mathData = sectionContent.data as {
        formula?: string;
        explanation?: string;
      };
      if (mathData.formula) {
        mdContent += `$$${mathData.formula}$$\n\n`;
        htmlContent += `<div class="math-formula">${mathData.formula}</div>\n\n`;
      }
      if (mathData.explanation) {
        mdContent += mathData.explanation + "\n\n";
        htmlContent += `<p>${mathData.explanation}</p>\n\n`;
      }
      break;

    case "practice_exercise":
      const exerciseData = sectionContent.data as {
        description?: string;
        instructions?: string;
      };
      if (exerciseData.description) {
        mdContent += `### Practice Exercise\n${exerciseData.description}\n\n`;
        htmlContent += `<h3>Practice Exercise</h3><p>${exerciseData.description}</p>\n\n`;
      }
      if (exerciseData.instructions) {
        mdContent += exerciseData.instructions + "\n\n";
        htmlContent += `<p>${exerciseData.instructions}</p>\n\n`;
      }
      break;

    default:
      // 处理未知类型，尝试从 data 中提取文本内容
      const unknownData = sectionContent.data as {
        content?: string;
        text?: string;
        description?: string;
      };
      const textContent =
        unknownData.content || unknownData.text || unknownData.description;
      if (textContent) {
        mdContent += textContent + "\n\n";
        htmlContent += `<p>${textContent}</p>\n\n`;
      }
      break;
  }

  return {
    htmlContent: htmlContent.trim(),
    mdContent: mdContent.trim(),
    contentType,
    questions,
  };
};

export default function LessonComponent({
  courseId,
  lessonId,
}: LessonComponentProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // 用户学习进度 hook
  const {
    fetchProgressByNodeOrPath,
    markContentFlowCompleted,
    removeCompletedContentFlow,
  } = useUserLessonProgress();

  const [courseData, setCourseData] = useState<EnhancedCourseNodeData | null>(
    null
  );
  const [currentLesson, setCurrentLesson] = useState<LearningLesson | null>(
    null
  );
  const [completedContentFlowIds, setCompletedContentFlowIds] = useState<
    string[]
  >([]);
  const [allLessonsProgress, setAllLessonsProgress] =
    useState<UserLessonProgressMap>({});

  // 从 URL 参数中获取初始状态 - 现在使用 content_flow_id 而不是 section 索引
  const initialContentFlowId = searchParams.get("content_flow_id") || "";
  const initialSectionIndex = parseInt(searchParams.get("section") || "0");
  const [selectedLessonId, setSelectedLessonId] = useState<string>(lessonId);
  const [selectedSectionIndex, setSelectedSectionIndex] =
    useState(initialSectionIndex);
  const [selectedContentFlowId, setSelectedContentFlowId] =
    useState<string>(initialContentFlowId);
  const [loading, setLoading] = useState(true);

  // 按需加载课程详情的函数
  const loadLessonDetails = useCallback(
    async (targetLessonId: string) => {
      if (!courseData?.lessonsDetails.has(targetLessonId)) {
        try {
          const lessonResponse = await learningLessonService.get(
            targetLessonId
          );
          if (lessonResponse.success && lessonResponse.data) {
            const lessonData = lessonResponse.data;
            const lessonId = lessonData.id || lessonData.lesson_id;
            if (lessonId) {
              setCourseData((prev) => {
                if (!prev) return prev;
                const newLessonsDetails = new Map(prev.lessonsDetails);
                newLessonsDetails.set(lessonId, lessonData);
                return {
                  ...prev,
                  lessonsDetails: newLessonsDetails,
                };
              });
              return lessonData;
            }
          }
        } catch (error) {
          console.error(
            `Failed to load lesson details for ${targetLessonId}:`,
            error
          );
        }
      }
      return courseData?.lessonsDetails.get(targetLessonId) || null;
    },
    [courseData?.lessonsDetails]
  );

  // 更新 URL 参数的函数 - 使用 useCallback 稳定引用
  const updateUrlParams = useCallback(
    (
      newLessonId: string,
      newContentFlowId: string,
      newSectionIndex?: number
    ) => {
      const params = new URLSearchParams(searchParams.toString());

      if (newContentFlowId) {
        params.set("content_flow_id", newContentFlowId);
        // 如果有 content_flow_id，就不需要 section 参数了
        params.delete("section");
      } else if (newSectionIndex !== undefined) {
        params.set("section", newSectionIndex.toString());
        params.delete("content_flow_id");
      }

      // 如果课程ID改变了，也需要更新路由
      if (newLessonId !== lessonId) {
        router.replace(
          `/courses/${courseId}/${newLessonId}?${params.toString()}`
        );
      } else {
        router.replace(`/courses/${courseId}/${lessonId}?${params.toString()}`);
      }
    },
    [router, searchParams, courseId, lessonId]
  );

  // 监听 URL 参数变化 - 优化避免循环更新
  useEffect(() => {
    const contentFlowIdParam = searchParams.get("content_flow_id");
    const sectionParam = searchParams.get("section");

    if (contentFlowIdParam && contentFlowIdParam !== selectedContentFlowId) {
      // 使用 content_flow_id 优先
      React.startTransition(() => {
        setSelectedContentFlowId(contentFlowIdParam);
        // 根据 content_flow_id 找到对应的 section index
        // 这个逻辑稍后在 processedContent 中实现
      });
    } else if (sectionParam !== null) {
      const newSectionIndex = parseInt(sectionParam);
      if (!isNaN(newSectionIndex) && newSectionIndex !== selectedSectionIndex) {
        // 使用 startTransition 来降低优先级，避免阻塞 UI
        React.startTransition(() => {
          setSelectedSectionIndex(newSectionIndex);
          setSelectedContentFlowId(""); // 清空 content_flow_id
        });
      }
    }
  }, [searchParams]); // 移除 selectedSectionIndex 依赖避免循环

  // 获取用户学习进度 - 获取整个课程的所有课程进度
  useEffect(() => {
    const fetchUserProgress = async () => {
      try {
        const progressData = await fetchProgressByNodeOrPath(courseId); // courseId 就是 node_id

        // 保存所有课程的进度
        setAllLessonsProgress(progressData);

        // 设置当前课程的进度
        const lessonProgress = progressData[lessonId];
        if (lessonProgress?.completed_content_flow_ids) {
          setCompletedContentFlowIds(lessonProgress.completed_content_flow_ids);
        }
      } catch (error) {
        console.error("Failed to fetch user progress:", error);
      }
    };

    if (courseId && lessonId) {
      fetchUserProgress();
    }
  }, [courseId, lessonId, fetchProgressByNodeOrPath]);

  // 计算大节进度的辅助函数
  const calculateLessonProgress = useCallback(
    (targetLessonId: string, newCompletedIds: string[]) => {
      const lessonDetails = courseData?.lessonsDetails.get(targetLessonId);
      if (!lessonDetails?.content_flow) return 0;

      const totalContentFlows = lessonDetails.content_flow.length;
      if (totalContentFlows === 0) return 0;

      const completedCount = lessonDetails.content_flow.filter(
        (contentFlow) =>
          contentFlow.id && newCompletedIds.includes(contentFlow.id)
      ).length;

      const progress = completedCount / totalContentFlows;

      // 确保进度在 0-1 之间，并保留2位小数
      const finalProgress =
        Math.round(Math.max(0, Math.min(1, progress)) * 100) / 100;

      console.log(
        `Lesson ${targetLessonId} progress: ${completedCount}/${totalContentFlows} = ${finalProgress}`
      );

      return finalProgress;
    },
    [courseData]
  );

  // 缓存处理后的内容 - 基于选中的小章节
  const processedContent = useMemo(() => {
    // 找到选中的大章节对应的课程数据
    const selectedLesson = courseData?.lessonsDetails.get(selectedLessonId);
    if (!selectedLesson?.content_flow) {
      return {
        htmlContent: "",
        mdContent: "",
        contentType: "markdown" as const,
        questions: [],
        sections: [],
        currentSectionIndex: 0,
        currentContentFlowId: "",
        block: undefined as AtomicContent | undefined,
      };
    }

    let selectedSection: AtomicContent | undefined;
    let actualSectionIndex = selectedSectionIndex;

    // 优先使用 content_flow_id 来查找内容
    if (selectedContentFlowId) {
      const foundIndex = selectedLesson.content_flow.findIndex(
        (section) => section.id === selectedContentFlowId
      );
      if (foundIndex !== -1) {
        selectedSection = selectedLesson.content_flow[foundIndex];
        actualSectionIndex = foundIndex;
      }
    } else {
      // 回退到使用 section index
      selectedSection = selectedLesson.content_flow[selectedSectionIndex];
    }

    if (!selectedSection) {
      return {
        htmlContent: "",
        mdContent: "",
        contentType: "markdown" as const,
        questions: [],
        sections: [],
        currentSectionIndex: 0,
        currentContentFlowId: "",
        block: undefined as AtomicContent | undefined,
      };
    }

    // 处理单个小章节的内容
    const processed = processSectionContent(selectedSection);
    return {
      ...processed,
      currentSectionIndex: actualSectionIndex,
      currentContentFlowId: selectedSection.id || "",
      block: selectedSection,
    };
  }, [
    courseData?.lessonsDetails,
    selectedLessonId,
    selectedSectionIndex,
    selectedContentFlowId,
  ]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // 获取课程的所有章节
        const lessonsResponse = await learningNodeService.listLessons(courseId);

        if (lessonsResponse.success && lessonsResponse.data) {
          const lessons = lessonsResponse.data;

          // 只获取当前课程的详细信息，其他课程按需加载
          let currentLessonData: LearningLesson | null = null;
          const lessonsDetails = new Map<string, LearningLesson>();

          // 获取当前课程的详细信息
          const currentLessonResponse = await learningLessonService.get(
            lessonId
          );
          if (currentLessonResponse.success && currentLessonResponse.data) {
            currentLessonData = currentLessonResponse.data;
            const currentLessonId =
              currentLessonData.id || currentLessonData.lesson_id;
            if (currentLessonId) {
              lessonsDetails.set(currentLessonId, currentLessonData);
            }
          }

          // 构建增强的课程数据
          const enhancedCourseData: EnhancedCourseNodeData = {
            id: courseId,
            title: currentLessonData?.title || "Course",
            description: currentLessonData?.description,
            lessons: lessons,
            lessonsDetails: lessonsDetails,
          };

          setCourseData(enhancedCourseData);
          setCurrentLesson(currentLessonData);

          // 如果 URL 中有 content_flow_id，根据它查找对应的课程和章节
          if (initialContentFlowId) {
            let foundLesson = false;
            const lessonsArray = Array.from(lessonsDetails.entries());
            for (const [lessonKey, lessonDetails] of lessonsArray) {
              if (lessonDetails.content_flow) {
                const sectionIndex = lessonDetails.content_flow.findIndex(
                  (contentFlow: AtomicContent) =>
                    contentFlow.id === initialContentFlowId
                );
                if (sectionIndex !== -1) {
                  // 找到了对应的课程和章节
                  setSelectedLessonId(lessonKey);
                  setSelectedSectionIndex(sectionIndex);
                  foundLesson = true;
                  console.log(
                    `Found content_flow_id ${initialContentFlowId} in lesson ${lessonKey}, section ${sectionIndex}`
                  );
                  break;
                }
              }
            }
            if (!foundLesson) {
              console.warn(
                `Content flow ID ${initialContentFlowId} not found in any lesson`
              );
            }
          }

          // 保持从 URL 参数读取的小节索引，不重置
        }
      } catch (err) {
        console.error("Error fetching course data:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [courseId, lessonId]);

  const handleMarkComplete = async () => {
    if (!currentLesson || !processedContent.currentContentFlowId) return;

    try {
      // 计算新的完成状态
      const newCompletedIds = completedContentFlowIds.includes(
        processedContent.currentContentFlowId
      )
        ? completedContentFlowIds
        : [...completedContentFlowIds, processedContent.currentContentFlowId];

      // 计算该大节的进度
      const progress = calculateLessonProgress(lessonId, newCompletedIds);

      await markContentFlowCompleted({
        node_id: courseId, // courseId 就是 node_id
        lesson_id: lessonId,
        content_flow_id: processedContent.currentContentFlowId,
        progress,
      });

      // 更新本地状态
      setCompletedContentFlowIds(newCompletedIds);

      // 更新所有课程进度状态
      setAllLessonsProgress((prev) => ({
        ...prev,
        [lessonId]: {
          ...prev[lessonId],
          completed_content_flow_ids: newCompletedIds,
        },
      }));

      console.log(
        `Content flow ${processedContent.currentContentFlowId} marked as completed`
      );
    } catch (error) {
      console.error("Failed to mark content flow as completed:", error);
    }
  };

  const handleMarkIncomplete = async () => {
    if (!currentLesson || !processedContent.currentContentFlowId) return;

    try {
      // 计算新的完成状态
      const newCompletedIds = completedContentFlowIds.filter(
        (id) => id !== processedContent.currentContentFlowId
      );

      // 计算该大节的进度
      const progress = calculateLessonProgress(lessonId, newCompletedIds);

      await removeCompletedContentFlow({
        node_id: courseId, // courseId 就是 node_id
        lesson_id: lessonId,
        content_flow_id: processedContent.currentContentFlowId,
        progress,
      });

      // 更新本地状态
      setCompletedContentFlowIds(newCompletedIds);

      // 更新所有课程进度状态
      setAllLessonsProgress((prev) => ({
        ...prev,
        [lessonId]: {
          ...prev[lessonId],
          completed_content_flow_ids: newCompletedIds,
        },
      }));

      console.log(
        `Content flow ${processedContent.currentContentFlowId} marked as incomplete`
      );
    } catch (error) {
      console.error("Failed to mark content flow as incomplete:", error);
    }
  };

  const handleNext = async () => {
    // 先标记当前内容流为已完成
    if (processedContent.currentContentFlowId) {
      try {
        // 计算新的完成状态
        const newCompletedIds = completedContentFlowIds.includes(
          processedContent.currentContentFlowId
        )
          ? completedContentFlowIds
          : [...completedContentFlowIds, processedContent.currentContentFlowId];

        // 计算该大节的进度
        const progress = calculateLessonProgress(lessonId, newCompletedIds);

        await markContentFlowCompleted({
          node_id: courseId,
          lesson_id: lessonId,
          content_flow_id: processedContent.currentContentFlowId,
          progress,
        });

        // 更新本地状态
        setCompletedContentFlowIds(newCompletedIds);

        // 更新所有课程进度状态
        setAllLessonsProgress((prev) => ({
          ...prev,
          [lessonId]: {
            ...prev[lessonId],
            completed_content_flow_ids: newCompletedIds,
          },
        }));
      } catch (error) {
        console.error(
          "Failed to mark current content flow as completed:",
          error
        );
      }
    }

    if (navigation.nextLesson) {
      // 解析下一个小章节的ID
      const parts = navigation.nextLesson.id.split("-section-");
      if (parts.length === 2) {
        const newLessonId = parts[0];
        const newSectionIndex = parseInt(parts[1]);

        // 获取下一个内容流的 ID
        const nextLesson = courseData?.lessonsDetails.get(newLessonId);
        const nextContentFlow = nextLesson?.content_flow?.[newSectionIndex];
        const nextContentFlowId = nextContentFlow?.id || "";

        React.startTransition(() => {
          setSelectedLessonId(newLessonId);
          setSelectedSectionIndex(newSectionIndex);
          setSelectedContentFlowId(nextContentFlowId);
          updateUrlParams(newLessonId, nextContentFlowId, newSectionIndex);
        });
      }
    }
  };

  const handlePrevious = () => {
    if (navigation.previousLesson) {
      // 解析上一个小章节的ID
      const parts = navigation.previousLesson.id.split("-section-");
      if (parts.length === 2) {
        const newLessonId = parts[0];
        const newSectionIndex = parseInt(parts[1]);

        // 获取上一个内容流的 ID
        const prevLesson = courseData?.lessonsDetails.get(newLessonId);
        const prevContentFlow = prevLesson?.content_flow?.[newSectionIndex];
        const prevContentFlowId = prevContentFlow?.id || "";

        React.startTransition(() => {
          setSelectedLessonId(newLessonId);
          setSelectedSectionIndex(newSectionIndex);
          setSelectedContentFlowId(prevContentFlowId);
          updateUrlParams(newLessonId, prevContentFlowId, newSectionIndex);
        });
      }
    }
  };

  if (loading || !courseData || !currentLesson) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading course...</p>
        </div>
      </div>
    );
  }

  // 构建侧边栏数据 - 大章节作为分组，小章节作为子项
  const adaptedCourseData = {
    id: courseId,
    title: courseData.title,
    description: courseData.description || "",
    content: {
      period: "week" as const,
      sections: courseData.lessons
        .sort((a, b) => {
          // 按 order_key 的长度排序
          const aLength = (a.order_key || "").length;
          const bLength = (b.order_key || "").length;
          return aLength - bLength;
        })
        .map((lesson) => {
          // 直接使用 NodeLesson 的 content_flow 数据，不需要获取详细信息
          const lessonSections = lesson.content_flow || [];

          return {
            id: lesson.lesson_id || "",
            title: lesson.lesson_title || "",
            lessons: lessonSections
              .sort((a, b) => a.order - b.order)
              .map((section, index) => {
                // 直接使用 ContentFlowBrief 的 title 和 description
                const sectionTitle = section.title || `Section ${index + 1}`;
                const sectionDescription = section.description || "";

                // 获取该课程的进度数据
                const lessonProgress =
                  allLessonsProgress[lesson.lesson_id || ""];
                const lessonCompletedIds =
                  lessonProgress?.completed_content_flow_ids || [];

                return {
                  id: `${lesson.lesson_id}-section-${index}`,
                  title: sectionTitle,
                  description: sectionDescription,
                  href: "#",
                  duration: 60,
                  isLocked: false,
                  isCompleted: section.id
                    ? lessonCompletedIds.includes(section.id)
                    : false,
                  type: "lesson" as const,
                  htmlContent: "",
                };
              }),
          };
        }),
    },
    whatYouWillLearn: currentLesson.learning_objectives || [],
    skills: [],
    authors: [],
    developedByMAANG: false,
    instructor: "",
    duration: `${currentLesson.estimated_minutes || 0} min`,
    difficulty: "Beginner" as const,
    language: "English",
    category: "",
    tags: [],
    currentProgress: (() => {
      // 计算所有小节的总进度
      let totalContentFlows = 0;
      let completedContentFlows = 0;

      courseData.lessons.forEach((lesson) => {
        const lessonProgress = allLessonsProgress[lesson.lesson_id || ""];
        const lessonCompletedIds =
          lessonProgress?.completed_content_flow_ids || [];

        // 直接使用 NodeLesson 的 content_flow 数据
        const lessonContentFlows = lesson.content_flow || [];
        totalContentFlows += lessonContentFlows.length;

        // 计算该课程中已完成的 content flow 数量
        lessonContentFlows.forEach((contentFlow) => {
          if (contentFlow.id && lessonCompletedIds.includes(contentFlow.id)) {
            completedContentFlows++;
          }
        });
      });

      const percentage =
        totalContentFlows > 0
          ? Math.round((completedContentFlows / totalContentFlows) * 100)
          : 0;

      return {
        completedLessons: Array(completedContentFlows).fill(""), // 用长度表示已完成数量
        totalLessons: totalContentFlows, // 所有小节的总 content flow 数量
        totalQuizzes: 0,
        totalChallenges: 0,
        percentage,
      };
    })(),
    faq: [],
  };

  const navigation = (() => {
    // 构建所有小章节的扁平列表
    const allSections: Array<{
      lessonId: string;
      sectionIndex: number;
      title: string;
      lessonTitle: string;
    }> = [];

    courseData.lessons
      .sort((a, b) => {
        const aLength = (a.order_key || "").length;
        const bLength = (b.order_key || "").length;
        return aLength - bLength;
      })
      .forEach((lesson) => {
        // 直接使用 NodeLesson 的 content_flow 数据
        const lessonSections = lesson.content_flow || [];

        lessonSections
          .sort((a, b) => a.order - b.order)
          .forEach((section, index) => {
            // 直接使用 ContentFlowBrief 的 title
            const sectionTitle = section.title || `Section ${index + 1}`;

            allSections.push({
              lessonId: lesson.lesson_id || "",
              sectionIndex: index,
              title: sectionTitle,
              lessonTitle: lesson.lesson_title || "",
            });
          });
      });

    // 找到当前小章节在扁平列表中的位置
    const currentSectionIndex = allSections.findIndex(
      (section) =>
        section.lessonId === selectedLessonId &&
        section.sectionIndex === selectedSectionIndex
    );

    return {
      previousLesson:
        currentSectionIndex > 0
          ? {
              id: `${allSections[currentSectionIndex - 1].lessonId}-section-${
                allSections[currentSectionIndex - 1].sectionIndex
              }`,
              title:
                allSections[currentSectionIndex - 1].title ||
                `Section ${
                  allSections[currentSectionIndex - 1].sectionIndex + 1
                }`,
              href: "#",
            }
          : undefined,
      nextLesson:
        currentSectionIndex !== -1 &&
        currentSectionIndex < allSections.length - 1
          ? {
              id: `${allSections[currentSectionIndex + 1].lessonId}-section-${
                allSections[currentSectionIndex + 1].sectionIndex
              }`,
              title:
                allSections[currentSectionIndex + 1].title ||
                `Section ${
                  allSections[currentSectionIndex + 1].sectionIndex + 1
                }`,
              href: "#",
            }
          : undefined,
      currentLesson: {
        id: `${selectedLessonId}-section-${selectedSectionIndex}`,
        title: (() => {
          // 优先从 NodeLesson 的 content_flow 获取标题
          const currentLessonData = courseData.lessons.find(
            (lesson) => lesson.lesson_id === selectedLessonId
          );
          const selectedSection =
            currentLessonData?.content_flow?.[selectedSectionIndex];

          if (selectedSection?.title) {
            return selectedSection.title;
          }

          // 如果没有标题，回退到默认标题
          return `Section ${selectedSectionIndex + 1}`;
        })(),
        isCompleted: processedContent.currentContentFlowId
          ? completedContentFlowIds.includes(
              processedContent.currentContentFlowId
            )
          : false,
      },
    };
  })();

  return (
    <div className="flex bg-white dark:bg-gray-950">
      <div className="fixed left-0 top-16 h-screen z-10">
        <CourseSidebar
          courseData={adaptedCourseData}
          currentLessonId={`${selectedLessonId}-section-${selectedSectionIndex}`}
          onLessonSelect={async (selectedId) => {
            // 解析选中的ID，格式为 lessonId-section-sectionIndex
            const parts = selectedId.split("-section-");
            if (parts.length === 2) {
              const newLessonId = parts[0];
              const newSectionIndex = parseInt(parts[1]);

              // 按需加载课程详情
              await loadLessonDetails(newLessonId);

              // 获取选中内容流的 ID - 优先从 NodeLesson 获取
              const currentLessonData = courseData.lessons.find(
                (lesson) => lesson.lesson_id === newLessonId
              );
              const selectedContentFlow =
                currentLessonData?.content_flow?.[newSectionIndex];
              const selectedContentFlowId = selectedContentFlow?.id || "";

              // 使用 React 18 的批量更新来减少重新渲染
              React.startTransition(() => {
                setSelectedLessonId(newLessonId);
                setSelectedSectionIndex(newSectionIndex);
                setSelectedContentFlowId(selectedContentFlowId);

                // 更新当前课程的完成状态
                const newLessonProgress = allLessonsProgress[newLessonId];
                if (newLessonProgress?.completed_content_flow_ids) {
                  setCompletedContentFlowIds(
                    newLessonProgress.completed_content_flow_ids
                  );
                } else {
                  setCompletedContentFlowIds([]);
                }

                // 更新 URL 参数
                updateUrlParams(
                  newLessonId,
                  selectedContentFlowId,
                  newSectionIndex
                );
              });
            }
          }}
        />
      </div>
      <div className="flex-1 ml-80">
        <CourseContent
          courseTitle={courseData.title}
          coursePath="#"
          lessonTitle={(() => {
            // 优先从 NodeLesson 的 content_flow 获取标题
            const currentLessonData = courseData.lessons.find(
              (lesson) => lesson.lesson_id === selectedLessonId
            );
            const selectedSection =
              currentLessonData?.content_flow?.[selectedSectionIndex];

            if (selectedSection?.title) {
              return selectedSection.title;
            }

            // 如果 NodeLesson 的 content_flow 没有标题，回退到默认标题
            return `Section ${selectedSectionIndex + 1}`;
          })()}
          lessonDescription={(() => {
            // 优先从 NodeLesson 的 content_flow 获取描述
            const currentLessonData = courseData.lessons.find(
              (lesson) => lesson.lesson_id === selectedLessonId
            );
            const selectedSection =
              currentLessonData?.content_flow?.[selectedSectionIndex];

            return selectedSection?.description || "";
          })()}
          contentType={processedContent.contentType}
          htmlContent={processedContent.htmlContent}
          mdContent={processedContent.mdContent}
          questions={processedContent.questions}
          block={processedContent.block}
          navigation={navigation}
          onMarkComplete={handleMarkComplete}
          onMarkIncomplete={handleMarkIncomplete}
          onNext={handleNext}
          onPrevious={handlePrevious}
        />
      </div>
    </div>
  );
}
