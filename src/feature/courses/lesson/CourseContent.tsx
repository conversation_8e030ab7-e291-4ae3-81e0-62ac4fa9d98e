"use client";

import React, {
  useMemo,
  useEffect,
  useState,
  useRef,
  useCallback,
} from "react";
import CourseBreadcrumb from "@/components/ui/pt-breadcrumb";
import TableOfContents, { TOCItem } from "@/components/ui/table-of-contents";
import CourseNavigation, { useCourseNavigation } from "./CourseNavigation";
import ContentRenderer from "@/components/ui/content-renderer";
import AtomicContentRenderer from "@/components/lessons/AtomicContentRenderer";
import Quiz, { Question } from "@/feature/shared/Quiz";
import { CourseNavigation as CourseNavigationType } from "@/types/course";
import { validateTOCItems } from "@/utils/htmlParser";
import { DifyChat } from "@/components/dify-chat";
import { useAuth } from "@/lib/auth-context";
import { Sparkles } from "lucide-react";

// 面包屑项接口
interface BreadcrumbItem {
  key: string;
  value: string;
  path?: string;
  isEllipsis?: boolean;
}

// CourseContent组件的Props接口
interface CourseContentProps {
  courseTitle: string;
  coursePath: string;
  lessonTitle: string;
  lessonDescription?: string;
  // 内容渲染相关
  contentType?: "markdown" | "html" | "quiz";
  htmlContent?: string;
  mdContent?: string;
  questions?: Question[];
  // 当传入原子内容块时，优先使用 AtomicContentRenderer（支持 mermaid 与代码执行）
  block?: any;
  // 其他属性
  tableOfContents?: TOCItem[];
  navigation?: CourseNavigationType;
  estimatedReadTime?: string;
  lastUpdated?: string;
  tags?: string[];
  learningObjectives?: string[];
  onMarkComplete?: () => void;
  onMarkIncomplete?: () => void;
  onNext?: () => void;
  onPrevious?: () => void;
}

const CourseContent: React.FC<CourseContentProps> = ({
  courseTitle,
  coursePath,
  lessonTitle,
  lessonDescription,
  contentType = "markdown",
  htmlContent,
  mdContent,
  questions,
  block,
  tableOfContents = [],
  navigation,
  onMarkComplete,
  onMarkIncomplete,
  onNext,
  onPrevious,
}) => {
  const { isCompleted, markAsComplete, markAsIncomplete } =
    useCourseNavigation();
  const { userInfo } = useAuth();

  // 用于引用内容容器的 ref
  const contentRef = useRef<HTMLDivElement>(null);

  // 状态管理动态生成的目录
  const [dynamicTOC, setDynamicTOC] = useState<TOCItem[]>([]);

  // 从渲染后的 DOM 中提取目录
  const extractTOCFromDOM = useCallback(() => {
    if (!contentRef.current) return [];

    // 只选择真正的 h1, h2 标签，排除其他元素
    const headings = contentRef.current.querySelectorAll("h1, h2");
    const tocItems: TOCItem[] = [];
    const usedIds = new Set<string>(); // 跟踪已使用的 id

    headings.forEach((heading, index) => {
      // 确保是真正的 h1 或 h2 标签
      const tagName = heading.tagName.toLowerCase();
      if (tagName !== "h1" && tagName !== "h2") return;

      // 排除那些只是有样式类但不是真正标题的元素
      const classList = heading.classList;

      // 如果元素有 text-h2 等样式类但不是真正的 h2 标签，跳过
      if (classList.contains("text-h2") && tagName !== "h2") return;
      if (classList.contains("text-h1") && tagName !== "h1") return;

      const level = parseInt(tagName.charAt(1));
      let title = heading.textContent || "";

      // 清理标题文本
      title = title.replace(/\s*#\s*$/, "").trim();

      // 跳过空标题或过短的标题
      if (!title || title.length < 2) return;

      // 排除一些明显不是标题的元素
      if (
        classList.contains("code") ||
        classList.contains("inline") ||
        heading.closest("code") ||
        heading.closest("pre")
      ) {
        return;
      }

      // 检查是否是纯代码标题（整个标题都是代码）
      const codeElements = heading.querySelectorAll("code");
      const hasOnlyCode =
        codeElements.length > 0 &&
        codeElements.length === 1 &&
        heading.children.length === 1 &&
        codeElements[0].textContent?.trim() === title.trim();

      // 排除纯代码标题或明显的代码方法标题
      if (
        hasOnlyCode ||
        // 排除文本内容看起来像代码方法的标题
        /^[A-Z][a-z]*\.[a-z]+\(.*\)$/.test(title.trim())
      ) {
        return;
      }

      // 生成唯一的 id
      let id = heading.getAttribute("id");

      // 如果没有 id，生成新的唯一 id
      if (!id && title) {
        // 生成基础 id
        const baseId = title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/^-+|-+$/g, "");

        // 确保 id 唯一
        id = baseId;
        let counter = 1;
        while (usedIds.has(id)) {
          id = `${baseId}-${counter}`;
          counter++;
        }

        heading.setAttribute("id", id);
      } else if (id && usedIds.has(id)) {
        // 如果已有的 id 重复了，也要生成新的唯一 id
        const originalId = id;
        let counter = 1;
        while (usedIds.has(id)) {
          id = `${originalId}-${counter}`;
          counter++;
        }

        heading.setAttribute("id", id);
      }

      // 记录已使用的 id
      if (id) {
        usedIds.add(id);
      }

      if (id && title) {
        tocItems.push({
          id,
          title,
          level,
        });
      }
    });

    return validateTOCItems(tocItems);
  }, []);

  // 自动检测内容类型 - 使用 useMemo 避免不必要的重新计算
  const questionsLength = useMemo(
    () => questions?.length || 0,
    [questions?.length]
  );
  const actualContentType = useMemo(
    () => (questionsLength > 0 ? "quiz" : contentType),
    [questionsLength, contentType]
  );

  // 创建稳定的依赖项
  const tableOfContentsLength = tableOfContents?.length || 0;
  const hasTableOfContents = tableOfContentsLength > 0;

  // 监听内容变化，重新提取目录
  useEffect(() => {
    // Quiz 模式不需要生成目录
    if (actualContentType === "quiz") {
      setDynamicTOC([]);
      return;
    }

    // 延迟执行，确保内容已经渲染完成
    const timer = setTimeout(() => {
      if (hasTableOfContents && tableOfContents) {
        // 如果手动提供了目录，直接使用
        setDynamicTOC(tableOfContents);
      } else {
        // 从 DOM 中提取目录
        const extractedTOC = extractTOCFromDOM();
        setDynamicTOC(extractedTOC);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [actualContentType, hasTableOfContents, htmlContent, mdContent]);

  // 使用动态目录
  const finalTOC = dynamicTOC;

  // 动态生成面包屑数据
  const breadcrumbItems: BreadcrumbItem[] = [
    {
      key: "course",
      value: courseTitle,
      path: coursePath,
      isEllipsis: true,
    },
    {
      key: "current",
      value: lessonTitle,
    },
  ];

  // 使用传入的导航数据或默认值
  const navigationData = navigation || {
    currentLesson: {
      id: "current",
      title: lessonTitle,
      isCompleted: isCompleted,
    },
  };

  return (
    <>
      {/* Table of Contents - Fixed Right Side */}
      <TableOfContents items={finalTOC} showProgress={true} />

      <div className="max-w-4xl mx-auto px-4 sm:px-10 py-10 text-gray-900 dark:text-gray-100">
        {/* Breadcrumb */}
        <CourseBreadcrumb items={breadcrumbItems} className="mb-4" />

        {/* Title and Intro */}
        <h1 className="text-4xl font-bold mb-4">{lessonTitle}</h1>
        {lessonDescription && (
          <p className="mb-8 text-base">{lessonDescription}</p>
        )}

        {/* Dynamic Content Renderer with ref */}
        <div ref={contentRef}>
          {actualContentType === "quiz" && questions && questions.length > 0 ? (
            <Quiz questions={questions} />
          ) : block ? (
            <div className="mb-10">
              <AtomicContentRenderer block={block} />
            </div>
          ) : actualContentType === "quiz" ? (
            <div className="course-content-body mb-10">
              <p className="text-gray-500 italic">
                Quiz mode detected but no questions provided.
              </p>
            </div>
          ) : (
            <ContentRenderer
              type={actualContentType as "markdown" | "html"}
              htmlContent={htmlContent}
              mdContent={mdContent}
              className="mb-10"
            />
          )}
        </div>

        {/* Course Navigation */}
        <CourseNavigation
          previousLesson={navigationData.previousLesson}
          nextLesson={navigationData.nextLesson}
          currentLesson={navigationData.currentLesson}
          onMarkComplete={onMarkComplete || markAsComplete}
          onMarkIncomplete={onMarkIncomplete || markAsIncomplete}
          onNext={onNext}
          onPrevious={onPrevious}
        />
      </div>

      {/* Text Selection Chat */}
      <DifyChat
        type="text-selection"
        showHeader={true}
        allowFileUpload={false}
        triggerIcon={<Sparkles></Sparkles>}
        theme="light"
        title="AI助手"
        subtitle="选择文本进行提问"
        placeholder="对选中的内容有什么疑问吗？"
        maxWidth={450}
        maxHeight={400}
        userId={userInfo?.id || userInfo?.email || "anonymous-user"}
        initialMessage="解释一下这段文字"
        hoverInitialMessage="解释这个关键词"
        inputs={{
          prompt: `课程内容：

${mdContent || htmlContent || ""}

请基于以上课程内容回答用户的问题。如果用户选择了特定文本，请重点针对选中的内容进行解答。`,
        }}
      />
    </>
  );
};

export default CourseContent;
