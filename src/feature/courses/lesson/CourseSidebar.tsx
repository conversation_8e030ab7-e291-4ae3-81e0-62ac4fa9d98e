import React, { useState, useMemo, useEffect } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import SliderSearchAndFilter from "@/components/common/SliderSearchAndFilter";
import SidebarCollectionCategories from "./SidebarCollectionCategories";
import CourseActions from "./CourseActions";
import { CourseData } from "@/types/course";

// CourseSidebar组件的Props接口
interface CourseSidebarProps {
  courseData: CourseData;
  currentLessonId?: string;
  onLessonSelect?: (lessonId: string) => void;
  onBackClick?: () => void;
  onCourseComplete?: () => void;
}

const CourseSidebar: React.FC<CourseSidebarProps> = ({
  courseData,
  currentLessonId,
  onLessonSelect,
  onBackClick,
  onCourseComplete,
}) => {
  const router = useRouter();
  const [openSections, setOpenSections] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [lessonType, setLessonType] = useState("all");
  const [showFilters, setShowFilters] = useState(false);

  // 根据当前课程设置打开的section
  useEffect(() => {
    if (currentLessonId && courseData) {
      // 找到当前课程所在的section
      const currentSection = courseData.content.sections.find((section) =>
        section.lessons.some((lesson) => lesson.id === currentLessonId)
      );

      if (currentSection) {
        setOpenSections((prev) => {
          // 如果当前section还没有打开，则打开它
          if (!prev.includes(currentSection.id)) {
            return [...prev, currentSection.id];
          }
          return prev;
        });
      }
    } else if (courseData.content.sections.length > 0) {
      // 如果没有当前课程，默认打开第一个section
      setOpenSections([courseData.content.sections[0].id]);
    }
  }, [currentLessonId, courseData]);

  const toggleSection = (id: string) => {
    setOpenSections((prev) =>
      prev.includes(id) ? prev.filter((s) => s !== id) : [...prev, id]
    );
  };

  // 将CourseData的content.sections转换为SidebarCollectionCategories需要的格式
  const sections = courseData.content.sections.map((section) => ({
    id: section.id,
    title: section.title,
    lessons: section.lessons.map((lesson) => ({
      title: lesson.title,
      href: `/courses/${courseData.id}/${lesson.id}`, // 使用新的路由格式
      type: lesson.isLocked ? "premium" : "free",
      completed: lesson.isCompleted || false,
      current: lesson.id === currentLessonId,
      onClick: () => onLessonSelect?.(lesson.id), // 添加点击处理
    })),
  }));

  // Filter sections based on search and lesson type
  const filteredSections = useMemo(() => {
    return sections
      .map((section) => ({
        ...section,
        lessons: section.lessons.filter((lesson) => {
          // Filter by search query
          const matchesSearch =
            !searchQuery ||
            lesson.title.toLowerCase().includes(searchQuery.toLowerCase());

          // Filter by lesson type
          const matchesType =
            lessonType === "all" || lesson.type === lessonType;

          return matchesSearch && matchesType;
        }),
      }))
      .filter((section) => section.lessons.length > 0);
  }, [sections, searchQuery, lessonType]);

  // Count free lessons and filtered lessons
  const freeLessons = sections.reduce(
    (acc, section) =>
      acc + section.lessons.filter((lesson) => lesson.type === "free").length,
    0
  );

  const filteredLessons = filteredSections.reduce(
    (acc, section) => acc + section.lessons.length,
    0
  );

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      // Navigate to course roadmap
      router.push(`/courses/${courseData.id}/roadmap`);
    }
  };

  const handleCourseComplete = () => {
    if (onCourseComplete) {
      onCourseComplete();
    } else {
      console.log("Course marked as completed");
    }
  };

  return (
    <aside className="w-80 h-screen bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col mb-16">
      {/* Back */}
      <div
        className="flex bg-[#f3f4f6] z-50 w-full border-0 border-b border-solid border-gray-200 dark:border-gray-700"
        style={{ minHeight: "56px" }}
      >
        <div className="flex w-full items-center justify-start">
          <div
            className="content-default-emphasis ml-8 flex cursor-pointer items-center gap-2 font-bold"
            onClick={handleBackClick}
          >
            <ArrowLeft className="h-4 w-4 stroke-current" />
            <div>Back to Roadmap</div>
          </div>
        </div>
      </div>
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Fixed Header with Progress and Search */}
        <div className="p-4 flex-shrink-0">
          {/* Progress */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Progress
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {courseData.currentProgress.percentage}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${courseData.currentProgress.percentage}%` }}
              ></div>
            </div>
          </div>

          {/* Search and Filter */}
          <SliderSearchAndFilter
            searchValue={searchQuery}
            onSearchChange={setSearchQuery}
            placeholder="Search lessons..."
            filters={[
              {
                label: "Lesson Type",
                value: lessonType,
                options: [
                  { label: "All Lessons", value: "all" },
                  { label: `Free Lessons (${freeLessons})`, value: "free" },
                  { label: "Premium Lessons", value: "premium" },
                ],
                onChange: setLessonType,
              },
            ]}
            showFilters={showFilters}
            onToggleFilters={() => setShowFilters(!showFilters)}
            resultsCount={filteredLessons}
            compactMode={true}
          />
        </div>

        {/* Scrollable Sections */}
        <div className="scrollbar-none flex-1 overflow-y-auto pb-16">
          <SidebarCollectionCategories
            categories={filteredSections}
            openSections={openSections}
            onToggleSection={toggleSection}
          />

          {/* Course Actions */}
          <CourseActions
            courseId={courseData.id}
            courseName={courseData.title}
            isCompleted={courseData.currentProgress.percentage === 100}
            onMarkComplete={handleCourseComplete}
          />
        </div>
      </div>
    </aside>
  );
};

export default CourseSidebar;
