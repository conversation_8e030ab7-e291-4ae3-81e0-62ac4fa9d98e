/*
 * @Description: Course Navigation component for lesson navigation
 * @Author: Devin
 * @Date: 2025-07-23
 */
"use client";

import React, { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface CourseNavigationProps {
  previousLesson?: {
    title: string;
    href: string;
  };
  nextLesson?: {
    title: string;
    href: string;
  };
  currentLesson?: {
    title: string;
    isCompleted?: boolean;
  };
  onMarkComplete?: () => void;
  onMarkIncomplete?: () => void;
  onPrevious?: () => void;
  onNext?: () => void;
  className?: string;
}

export default function CourseNavigation({
  previousLesson,
  nextLesson,
  currentLesson,
  onMarkComplete,
  onMarkIncomplete,
  onPrevious,
  onNext,
  className = "",
}: CourseNavigationProps) {
  const [isMarkingComplete, setIsMarkingComplete] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const handlePrevious = async () => {
    setIsNavigating(true);
    try {
      if (onPrevious) {
        await onPrevious();
      } else if (previousLesson?.href) {
        window.location.href = previousLesson.href;
      }
    } finally {
      setIsNavigating(false);
    }
  };

  const handleNext = async () => {
    setIsNavigating(true);
    try {
      if (onNext) {
        await onNext();
      } else if (nextLesson?.href) {
        window.location.href = nextLesson.href;
      }
    } finally {
      setIsNavigating(false);
    }
  };

  const handleMarkComplete = async () => {
    setIsMarkingComplete(true);
    try {
      if (currentLesson?.isCompleted) {
        // 如果已完成，则取消完成
        if (onMarkIncomplete) {
          await onMarkIncomplete();
        }
      } else {
        // 如果未完成，则标记为完成
        if (onMarkComplete) {
          await onMarkComplete();
        }
      }
    } finally {
      setIsMarkingComplete(false);
    }
  };

  return (
    <div
      className={`mb-12 mt-12 flex min-h-18 flex-col justify-center bg-indigo-25 dark:bg-gray-800 lg:mb-0 lg:bg-transparent lg:dark:bg-transparent ${className}`}
      style={{ marginInline: "-40px", display: "flex" }}
    >
      {/* Main Navigation Row */}
      <div className="flex w-full px-10 py-2" style={{ minWidth: "256px" }}>
        {/* Previous Button */}
        <div className="lg:order-0 order-1 ml-auto mr-4 items-start leading-none lg:ml-0 lg:mr-0">
          <div className="flex justify-end">
            <Button
              variant="outline"
              size="default"
              className="m-0 h-11 navigation-text"
              aria-label="Previous button"
              onClick={handlePrevious}
              disabled={!previousLesson}
            >
              <ChevronLeft className="w-5 h-5" />
              <span>Back</span>
              <span className="tailwind-hidden ml-1.5 sm:inline">lesson</span>
            </Button>
          </div>
        </div>

        {/* Mark as Complete Button */}
        <div className="leading-none lg:order-1 lg:ml-auto">
          <Button
            variant="outline"
            size="default"
            className={`bg-transparent m-0 h-11 mb-0 sm:mr-2 ${
              currentLesson?.isCompleted
                ? "border-green-500 dark:border-green-400 text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900"
                : "border-indigo-500 dark:border-indigo-400 text-indigo-500 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900"
            }`}
            onClick={handleMarkComplete}
            disabled={isMarkingComplete}
          >
            <div className="flex items-center">
              <svg
                fill="none"
                height="18"
                viewBox="0 0 18 18"
                width="18"
                xmlns="http://www.w3.org/2000/svg"
                className="ml-1"
              >
                <rect
                  className={
                    currentLesson?.isCompleted
                      ? "stroke-green-500 dark:stroke-green-400"
                      : "stroke-indigo-500 dark:stroke-indigo-200"
                  }
                  height="16"
                  rx="0.5"
                  width="16"
                  x="0.5"
                  y="0.5"
                />
                {currentLesson?.isCompleted && (
                  <polyline
                    className="stroke-green-500 dark:stroke-green-400"
                    points="4 10.6 6.30769231 13 14 5"
                    strokeWidth="2.5"
                  />
                )}
              </svg>
            </div>
            <span
              className={`ml-3 tailwind-hidden sm:block ${
                currentLesson?.isCompleted
                  ? "text-green-600 dark:text-green-400"
                  : "text-indigo-500 dark:text-indigo-200"
              }`}
            >
              {isMarkingComplete
                ? "Updating..."
                : currentLesson?.isCompleted
                ? "Completed"
                : "Mark As Completed"}
            </span>
            <span
              className={`ml-3 block sm:hidden ${
                currentLesson?.isCompleted
                  ? "text-green-600 dark:text-green-400"
                  : "text-indigo-500 dark:text-indigo-200"
              }`}
            >
              {isMarkingComplete
                ? "..."
                : currentLesson?.isCompleted
                ? "Done"
                : "Complete"}
            </span>
          </Button>
        </div>

        {/* Next Button */}
        <div className="order-2 leading-none">
          <div className="flex justify-end">
            <Button
              variant="outline"
              size="default"
              className="bg-transparent m-0 h-11 mb-0 sm:mr-2 border-indigo-500 dark:border-indigo-400 text-indigo-500 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900"
              aria-label="Next button"
              onClick={handleNext}
              disabled={!nextLesson || isNavigating}
            >
              <span>
                {isNavigating ? "Loading..." : "Next"}
                <span className="tailwind-hidden sm:inline"></span>
              </span>
              {!isNavigating && <ChevronRight className="w-5 h-5" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Lesson Titles Row */}
      <div
        className="tailwind-hidden w-full px-10 lg:flex text-xs"
        style={{ minWidth: "256px" }}
      >
        <div className="mr-auto">
          <p className="caption-text mt-2 overflow-hidden text-ellipsis whitespace-nowrap text-gray-500 dark:text-gray-400 text-start">
            {previousLesson?.title || ""}
          </p>
        </div>
        <div className="ml-auto">
          <p className="caption-text mt-2 overflow-hidden text-ellipsis whitespace-nowrap text-gray-500 dark:text-gray-400 text-end">
            {nextLesson?.title || ""}
          </p>
        </div>
      </div>
    </div>
  );
}

// 便捷的 Hook 用于管理课程导航状态
export function useCourseNavigation() {
  const [isCompleted, setIsCompleted] = React.useState(false);

  const markAsComplete = React.useCallback(() => {
    setIsCompleted(true);
    // 这里可以添加 API 调用来保存完成状态
    console.log("Lesson marked as completed");
  }, []);

  const markAsIncomplete = React.useCallback(() => {
    setIsCompleted(false);
    // 这里可以添加 API 调用来更新完成状态
    console.log("Lesson marked as incomplete");
  }, []);

  const toggleComplete = React.useCallback(() => {
    setIsCompleted((prev) => {
      const newState = !prev;
      console.log(
        newState ? "Lesson marked as completed" : "Lesson marked as incomplete"
      );
      return newState;
    });
  }, []);

  return {
    isCompleted,
    markAsComplete,
    markAsIncomplete,
    toggleComplete,
    setIsCompleted,
  };
}
