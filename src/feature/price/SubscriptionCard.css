/* Flip animation for SubscriptionCard */
@keyframes flip {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}

.animate-flip {
  animation: flip 0.6s ease-in-out;
}

/* Alternative flip animation with Y-axis rotation */
@keyframes flipY {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}

.animate-flip-y {
  animation: flipY 0.6s ease-in-out;
}

/* 3D flip animation */
@keyframes flip3d {
  0% {
    transform: rotateY(0deg) rotateX(0deg);
  }
  25% {
    transform: rotateY(90deg) rotateX(0deg);
  }
  50% {
    transform: rotateY(180deg) rotateX(0deg);
  }
  75% {
    transform: rotateY(270deg) rotateX(0deg);
  }
  100% {
    transform: rotateY(360deg) rotateX(0deg);
  }
}

.animate-flip-3d {
  animation: flip3d 0.6s ease-in-out;
}
