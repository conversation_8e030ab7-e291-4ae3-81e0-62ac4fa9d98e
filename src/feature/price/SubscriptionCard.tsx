/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-20 14:12:01
 */
// SubscriptionCard.tsx
import React from "react";
import FluxIcon from "@/components/fluxIcon";
import "./SubscriptionCard.css";

interface SubscriptionCardProps {
  title: string;
  price: string;
  originalPrice: string; // Ensure this is included
  features: string[];
  discount: string; // Ensure this is included
  buttonText: string;
  className?: string; // Optional className for additional styles
  style?: React.CSSProperties; // Optional styles
  isPopular?: boolean; // To indicate if the card is popular
  label?: string; // New label prop
  billedText?: string;
  isFlipping?: boolean; // New prop to control flip animation
}

const SubscriptionCard: React.FC<SubscriptionCardProps> = ({
  title,
  price,
  originalPrice,
  features,
  discount,
  buttonText,
  className,
  style,
  isPopular,
  label,
  billedText,
  isFlipping = false,
}) => {
  return (
    <div
      className={`relative mb-6 w-full max-w-xs bg-white text-black border border-indigo-200 flex flex-col transition-transform duration-600 ease-in-out ${
        isFlipping ? "animate-flip" : ""
      } ${className}`}
      style={{
        transformStyle: "preserve-3d",
        ...style,
      }}
    >
      {/* Display the label if it exists */}
      {label && (
        <div className="absolute left-0 transform -translate-y-[101%] bg-[#ffffff] pl-1 py-1 text-xs bg-indigo-25 text-indigo-600">
          {label.toUpperCase()}
        </div>
      )}
      <div className="flex flex-col h-full flex-grow">
        <div className="flex items-center justify-between px-6 py-4 border-b border-indigo-200">
          <span className="font-medium">{title}</span>
          {discount && (
            <span className="ml-2 px-2 py-1 font-bold border border-teal-300 bg-teal-100 text-teal-700 rounded-full">
              {discount}
            </span>
          )}
        </div>
        <div className="flex flex-col items-center justify-center my-6">
          <div className="text-4xl leading-10 text-gray-700">${price}</div>
          <p className="text-xs leading-5 text-gray-500 mt-2">{billedText}</p>
          <p className="text-xs leading-5 text-gray-500 dark:text-gray-400 mt-2">
            Original Price: <strong>${originalPrice}</strong>
          </p>
        </div>
        <div className="flex justify-center px-4 pb-4">
          <button className="w-full h-11 rounded bg-indigo-500 text-white hover:bg-indigo-700">
            {buttonText}
          </button>
        </div>
        <div
          className="flex flex-col items-start px-4 pb-4 flex-grow"
          style={{ minHeight: "180px" }}
        >
          <div className="mb-4 font-medium">Plan includes:</div>
          {features.map((feature, index) => (
            <div key={index} className="flex items-center mb-2">
              <FluxIcon
                name="check"
                width={24}
                height={24}
                className="h-5 w-5 text-indigo-400"
              />
              <span className="ml-4">{feature}</span>
            </div>
          ))}
        </div>
        <div className="flex justify-center px-4 pb-6 mt-auto">
          <button className="w-full h-11 rounded text-gray-700 text-sm hover:bg-gray-50 flex items-center justify-center gap-2">
            <FluxIcon
              name="gift"
              width={16}
              height={16}
              className="h-4 w-4 text-gray-600"
            />
            Gift a Subscription
          </button>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionCard;
