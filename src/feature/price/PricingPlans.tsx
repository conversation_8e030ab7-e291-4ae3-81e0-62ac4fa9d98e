// pages/index.tsx
import React, { useState } from "react";
import SubscriptionCard from "./SubscriptionCard";
import { But<PERSON> } from "@/components/ui/button";
import { Box } from "@/components/ui/box";

// Updated pricing plans
const pricingPlans = [
  {
    name: "Standard",
    monthlyPrice: "59",
    billedMonthly: "59",
    annualPrice: "13",
    billedAnnually: "149",
    originalAnnualTotal: "298",
    twoYearPrice: "11",
    billedEveryTwoYears: "249",
    originalTwoYearTotal: "498",
    discount: "50% OFF", // Add discount
    features: [
      "900+ hands-on courses",
      "Completion certificates",
      "AI-powered learning",
      "The Pageflux AI Newsletter",
    ],
    buttonTextMonthly: "Get Started",
    buttonTextAnnual: "Claim Lifetime Discount",
    buttonTextTwoYear: "Claim Lifetime Discount",
    label: "", // No label for Standard
  },
  {
    name: "Premium",
    monthlyPrice: "99",
    billedMonthly: "99",
    annualPrice: "18",
    billedAnnually: "209",
    originalAnnualTotal: "418",
    twoYearPrice: "16",
    billedEveryTwoYears: "359",
    originalTwoYearTotal: "718",
    discount: "50% OFF", // Add discount
    features: [
      "310+ real-world projects",
      "Personalized Interview Prep",
      "3 AI Mock Interviews per month",
    ],
    buttonTextMonthly: "Get Started",
    buttonTextAnnual: "Claim Lifetime Discount",
    buttonTextTwoYear: "Claim Lifetime Discount",
    label: "Best for Interviews",
  },
  {
    name: "Premium Plus",
    monthlyPrice: "129",
    billedMonthly: "129",
    annualPrice: "21",
    billedAnnually: "249",
    originalAnnualTotal: "498",
    twoYearPrice: "17",
    billedEveryTwoYears: "399",
    originalTwoYearTotal: "798",
    discount: "50% OFF", // Add discount
    features: ["240+ AWS Cloud Labs", "7 AI Mock Interviews per month"],
    buttonTextMonthly: "Get Started",
    buttonTextAnnual: "Claim Lifetime Discount",
    buttonTextTwoYear: "Claim Lifetime Discount",
    label: "Best for Career Growth",
  },
];

const PricingPlans: React.FC = () => {
  const [billingPeriod, setBillingPeriod] = useState("monthly");
  const [isFlipping, setIsFlipping] = useState(false);

  const handleBillingPeriodChange = (newPeriod: string) => {
    if (newPeriod !== billingPeriod) {
      setIsFlipping(true);
      // Update content at the middle of the flip animation
      setTimeout(() => {
        setBillingPeriod(newPeriod);
      }, 300); // Half of the animation duration
      // End the flip animation
      setTimeout(() => {
        setIsFlipping(false);
      }, 600); // Full animation duration
    }
  };

  return (
    <div>
      {/* Billing Period Selection */}
      <Box className="flex justify-center items-center mb-16">
        <Box className="flex bg-gray-100 rounded-full p-1 border border-gray-200">
          <Button
            variant="ghost"
            onClick={() => handleBillingPeriodChange("monthly")}
            className={`
              px-6 py-2 rounded-full font-medium transition-all duration-300
              ${
                billingPeriod === "monthly"
                  ? "bg-white text-gray-900 shadow-sm border border-gray-300"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              }
            `}
          >
            Monthly
          </Button>
          <Button
            variant="ghost"
            onClick={() => handleBillingPeriodChange("annual")}
            className={`
              px-6 py-2 rounded-full font-medium transition-all duration-300
              ${
                billingPeriod === "annual"
                  ? "bg-white text-gray-900 shadow-sm border border-gray-300"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              }
            `}
          >
            Annual
          </Button>
          <Button
            variant="ghost"
            onClick={() => handleBillingPeriodChange("twoYear")}
            className={`
              px-6 py-2 rounded-full font-medium transition-all duration-300
              ${
                billingPeriod === "twoYear"
                  ? "bg-white text-gray-900 shadow-sm border border-gray-300"
                  : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
              }
            `}
          >
            Two Year
          </Button>
        </Box>
      </Box>

      {/* Pricing Plans */}
      <div className="flex justify-center px-4">
        <div className="flex">
          {pricingPlans.map((plan, index) => {
            const isMiddle = index === 1; // Middle card
            let price, billedText, buttonText, originalPrice, discount;

            if (billingPeriod === "monthly") {
              price = plan.monthlyPrice;
              billedText = `billed monthly $${plan.billedMonthly}`;
              buttonText = plan.buttonTextMonthly;
              originalPrice = plan.monthlyPrice; // Set original price for monthly
              discount = ""; // No discount for monthly
            } else if (billingPeriod === "annual") {
              price = plan.annualPrice;
              billedText = `billed annually $${plan.billedAnnually} $${plan.originalAnnualTotal}`;
              buttonText = plan.buttonTextAnnual;
              originalPrice = plan.originalAnnualTotal; // Set original price for annual
              discount = plan.discount; // Set discount
            } else {
              price = plan.twoYearPrice;
              billedText = `billed every 2 years $${plan.billedEveryTwoYears} $${plan.originalTwoYearTotal}`;
              buttonText = plan.buttonTextTwoYear;
              originalPrice = plan.originalTwoYearTotal; // Set original price for two-year
              discount = plan.discount; // Set discount
            }

            return (
              <SubscriptionCard
                key={`${plan.name}-${index}`}
                title={plan.name}
                price={price}
                originalPrice={originalPrice}
                billedText={billedText}
                features={plan.features}
                buttonText={buttonText}
                label={plan.label} // Pass the label
                isPopular={isMiddle}
                discount={discount} // Pass the discount
                isFlipping={isFlipping} // Pass the flip state
                className={`
                  transition-all duration-500 ease-in-out
                  ${
                    isMiddle ? "border-l-0 border-r-0 border-b-0 shadow-lg" : ""
                  }
                  ${isMiddle ? "transform z-10" : ""}
                `}
                style={{
                  height: "520px",
                  width: "320px",
                }}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PricingPlans;
