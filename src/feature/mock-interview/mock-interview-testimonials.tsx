"use client";

import React from "react";
import { Typography } from "@/components/ui/typography";
import Carousel from "@/components/ui/carousel";
import LogoCarousel from "@/components/common/logo-carousel";
import MockInterviewCard from "./mock-interview-card";
import { getCompanyLogos } from "@/mockdata/maindata";

import { Container } from "@/components/ui/container";
// Mock interview testimonials data
const testimonials = [
  {
    id: 1,
    quote:
      "This is wonderful! It's helpful for those who don't necessarily have much access to real people in the industry to practice mock interviews. Thanks so much",
    author: "<PERSON><PERSON>",
    position: "Software Engineer",
    company: "Google",
  },
  {
    id: 2,
    quote:
      "Awesome! This AI mock interviewer was far more effective than the $200 human coaches I used for practice.",
    author: "<PERSON><PERSON>",
    position: "Senior Developer",
    company: "Meta",
  },
  {
    id: 3,
    quote:
      "I really liked the experience with the AI interviewer. It cuts to the chase and gives me insightful feedback along the way. Great job.",
    author: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    position: "Tech Lead",
    company: "Amazon",
  },
  {
    id: 4,
    quote:
      "The AI interviewer provided incredibly realistic scenarios that helped me prepare for my actual interviews. Highly recommend!",
    author: "<PERSON>",
    position: "Frontend <PERSON>eloper",
    company: "Apple",
  },
  {
    id: 5,
    quote:
      "Amazing platform! The feedback was detailed and actionable. It helped me identify my weak points and improve significantly.",
    author: "Michael Rodriguez",
    position: "Full Stack Developer",
    company: "Netflix",
  },
  {
    id: 6,
    quote:
      "The system design interviews were particularly helpful. The AI asked follow-up questions just like a real interviewer would.",
    author: "Emily Johnson",
    position: "Senior Engineer",
    company: "Airbnb",
  },
];

const MockInterviewTestimonials: React.FC = () => {
  const companyLogos = getCompanyLogos();

  return (
    <div className="bg-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Container size="lg-plus">
          {/* Header */}
          <div className="text-center mb-12">
            <Typography variant="h3" className="font-bold text-gray-900 mb-4">
              <span className="text-blue-600">10,000+</span> Technical Mock
              Interviews Conducted
            </Typography>

            {/* Company Logos */}
            <div className="flex justify-center items-center gap-4">
              <Typography variant="large" className="">
                We have helped developers launch careers at
              </Typography>
              <LogoCarousel
                logos={companyLogos}
                speed={20}
                className="max-w-xl "
              />
            </div>
          </div>
          {/* Testimonials Carousel */}
          <div className="mt-16">
            <Carousel
              itemsPerSlide={3}
              showArrows={true}
              showIndicators={true}
              className="px-1 mx-8"
            >
              {testimonials.map((testimonial) => (
                <MockInterviewCard
                  key={testimonial.id}
                  quote={testimonial.quote}
                  author={testimonial.author}
                  className="h-full min-h-[240px]"
                />
              ))}
            </Carousel>
          </div>
        </Container>
      </div>
    </div>
  );
};

export default MockInterviewTestimonials;
