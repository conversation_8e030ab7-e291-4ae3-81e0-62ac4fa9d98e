"use client";

import React from "react";
import { Typography } from "@/components/ui/typography";
import { Container } from "@/components/ui/container";
import FluxIcon from "@/components/fluxIcon";

// Feature data
const features = [
  {
    id: 1,
    title: "AI Interview",
    description:
      "Experience real interviews with voice, text, and smart AI just like the real thing.",
    icon: (
      <FluxIcon
        name="ai-interview"
        width={32}
        height={32}
        className="fill-violet-900 dark:fill-gray-D100"
      />
    ),
  },
  {
    id: 2,
    title: "Immediate, Targeted Feedback",
    description:
      "Know what to fix and how—so every session gets you closer to the offer.",
    icon: (
      <FluxIcon
        name="target"
        width={32}
        height={32}
        className="fill-violet-900 dark:fill-gray-D100"
      />
    ),
  },
  {
    id: 3,
    title: "Practice Anytime & Save Big",
    description:
      "Take mock interviews at your convenience, without paying for expensive live interviewers.",
    icon: (
      <FluxIcon
        name="time-clock"
        width={28}
        height={28}
        className="fill-violet-900 dark:fill-gray-D100"
      />
    ),
  },
  {
    id: 4,
    title: "Immersive Experience",
    description:
      "Communicate your ideas your way: through code, diagram, or voice",
    icon: (
      <FluxIcon
        name="immersive"
        width={32}
        height={32}
        className="fill-violet-900 dark:fill-gray-D100"
      />
    ),
  },
];

const MockInterviewFeaturesGrid: React.FC = () => {
  return (
    <div className="bg-[#f5f5ff] pb-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Container size="lg-plus">
          <div className="flex flex-col items-center justify-center pt-4 pb-4">
            {/* Section Title */}
            <div className="mb-8 mt-10 text-center">
              <Typography
                variant="h3"
                className="text-2xl font-bold text-gray-900"
              >
                Built For Serious Interview Preparation
              </Typography>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 gap-4 text-lg sm:grid-cols-2 lg:grid-cols-4">
              {features.map((feature) => (
                <div
                  key={feature.id}
                  className="bg-white flex flex-col items-start justify-start p-4 rounded border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200"
                  style={{
                    minWidth: "230px",
                    maxWidth: "268px",
                    height: "240px",
                    borderRadius: "4px",
                  }}
                >
                  {/* Icon */}
                  <div className="mb-2">{feature.icon}</div>

                  {/* Title */}
                  <Typography
                    variant="h6"
                    className="mb-2 text-md  text-gray-900"
                  >
                    {feature.title}
                  </Typography>

                  {/* Description */}
                  <Typography
                    variant="small"
                    className="text-sm text-gray-700 leading-relaxed"
                  >
                    {feature.description}
                  </Typography>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </div>
    </div>
  );
};

export default MockInterviewFeaturesGrid;
