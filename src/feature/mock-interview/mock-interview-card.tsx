"use client";

import React from "react";
import FluxIcon from "@/components/fluxIcon";

interface MockInterviewCardProps {
  quote: string;
  author: string;
  className?: string;
}

const MockInterviewCard: React.FC<MockInterviewCardProps> = ({
  quote,
  author,
  className = "",
}) => {
  return (
    <div
      className={`rounded-lg py-3 px-4 shadow-sm border border-gray-100 flex flex-col h-full bg-gray-100 ${className}`}
    >
      {/* Top Section - Quote Icon (Fixed Height) */}
      <div className="h-8 flex items-start ">
        <FluxIcon
          name="leftqoute"
          width={24}
          height={24}
          className="opacity-40"
        />
      </div>

      {/* Middle Section - Quote Text (Flexible Height) */}
      <div className="flex-1 text-gray-700 leading-relaxed mb-1">{quote}</div>

      {/* Bottom Section - Author Info (Fixed Height) */}
      <div className="h-8 flex items-center justify-between">
        <div className=" text-gray-900 text-xs">{author}</div>
        <FluxIcon name="rightquote" width={32} height={32} />
      </div>
    </div>
  );
};

export default MockInterviewCard;
