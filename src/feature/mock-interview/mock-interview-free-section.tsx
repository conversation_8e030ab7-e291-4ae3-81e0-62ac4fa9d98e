"use client";

import React from "react";
import { Typography } from "@/components/ui/typography";
import { Container } from "@/components/ui/container";
import { Badge } from "@/components/ui/badge";

// Free interview data
const freeInterviews = [
  {
    id: 1,
    title: "Coding Patterns",
    category: "Coding Interview",
    description:
      "Gain insights and practical experience with coding patterns through targeted MCQs and coding problems, designed to match and challenge your expertise level.",
    bgColor: "bg-[#eff6ff]",
  },
  {
    id: 2,
    title: "YouTube",
    category: "System Design",
    description:
      "Learn to design a video streaming platform like YouTube by tackling requirements, core components, and high-level to detailed design challenges.",
    bgColor: "bg-[#fff2e0]",
  },
];

const MockInterviewFreeSection: React.FC = () => {
  return (
    <div className="bg-white pb-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <Container size="lg-plus">
          <div
            id="mock-interview-free-section"
            className="mb-14 mt-10 flex flex-col items-center justify-center px-4"
          >
            {/* Section Title */}
            <div className="mb-8 flex items-center justify-center">
              <Typography
                variant="h4"
                className="text-xl  sm:text-2xl text-gray-900 text-center"
              >
                Try MAANG-Level Interview Prep For Free
              </Typography>
            </div>

            {/* Free Interview Cards */}
            <div className="flex flex-col items-center justify-center gap-y-6 lg:flex-row lg:gap-x-6">
              {freeInterviews.map((interview) => (
                <div
                  key={interview.id}
                  className="bg-white border border-gray-200 flex cursor-pointer items-center justify-center rounded-xl shadow-sm transition duration-300 ease-linear hover:-translate-y-2 hover:shadow-lg"
                  style={{ height: "200px", maxWidth: "516px" }}
                >
                  {/* Left Section - Title and Category */}
                  <div
                    className={`flex flex-col items-center justify-center rounded-l-xl ${interview.bgColor}`}
                    style={{
                      minWidth: "170px",
                      width: "180px",
                      minHeight: "100%",
                    }}
                  >
                    <Typography variant="h6" className={`text-md font-bold`}>
                      {interview.title}
                    </Typography>
                    <Typography
                      variant="small"
                      className={`text-xs opacity-80`}
                    >
                      {interview.category}
                    </Typography>
                  </div>

                  {/* Right Section - Description and Badge */}
                  <div
                    className="flex min-h-full flex-col items-start justify-center gap-y-2 rounded-r-xl p-4 text-sm"
                    style={{ minHeight: "100%" }}
                  >
                    <Badge className="bg-teal-600 text-white text-xs ">
                      Free Interview
                    </Badge>
                    <Typography
                      variant="small"
                      className="text-sm  leading-relaxed"
                    >
                      {interview.description}
                    </Typography>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </div>
    </div>
  );
};

export default MockInterviewFreeSection;
