"use client";

import React, { useState, useMemo, useEffect, useRef } from "react";
import { Container } from "@/components/ui/container";
import { Typography } from "@/components/ui/typography";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import FluxIcon from "@/components/fluxIcon";
import {
  Search,
  X,
  Filter,
  ChevronDown,
  Clock,
  Bookmark,
  Rocket,
} from "lucide-react";

// Mock interview data types
interface MockInterview {
  id: string;
  title: string;
  description: string;
  category: string;
  duration: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  isFree: boolean;
  tags?: string[];
}

// Sample mock interview data
const mockInterviews: MockInterview[] = [
  // System Design
  {
    id: "1",
    title: "YouTube",
    description:
      "Learn to design a video streaming platform like YouTube by tackling functional and non-functional requirements, core components, and high-level to detailed design challenges.",
    category: "System Design",
    duration: "15 min",
    difficulty: "Intermediate",
    isFree: true,
    tags: ["Video Streaming", "Scalability", "CDN"],
  },
  {
    id: "2",
    title: "WhatsApp",
    description:
      "Design a messaging system like WhatsApp with real-time messaging, group chats, and multimedia sharing capabilities.",
    category: "System Design",
    duration: "20 min",
    difficulty: "Advanced",
    isFree: false,
    tags: ["Messaging", "Real-time", "WebSocket"],
  },
  {
    id: "3",
    title: "X (Twitter)",
    description:
      "Practice designing a system to handle millions of concurrent users posting tweets, following users, and viewing timelines.",
    category: "System Design",
    duration: "18 min",
    difficulty: "Intermediate",
    isFree: false,
    tags: ["Social Media", "Timeline", "Scalability"],
  },

  // Coding Interview
  {
    id: "4",
    title: "Coding Patterns",
    description:
      "Get a glimpse of the AI-powered mock interview with a coding challenge and feedback, designed to mirror real-world interviews, before upgrading to the full experience.",
    category: "Coding Interview",
    duration: "12 min",
    difficulty: "Beginner",
    isFree: true,
    tags: ["Algorithms", "Data Structures", "Problem Solving"],
  },
  {
    id: "5",
    title: "Array & String Problems",
    description:
      "Master fundamental array and string manipulation problems commonly asked in technical interviews.",
    category: "Coding Interview",
    duration: "25 min",
    difficulty: "Intermediate",
    isFree: false,
    tags: ["Arrays", "Strings", "Two Pointers"],
  },
  {
    id: "6",
    title: "Dynamic Programming",
    description:
      "Tackle complex dynamic programming problems with step-by-step guidance and optimization techniques.",
    category: "Coding Interview",
    duration: "30 min",
    difficulty: "Advanced",
    isFree: false,
    tags: ["DP", "Optimization", "Memoization"],
  },

  // MAANG+
  {
    id: "7",
    title: "Google SWE Interview",
    description:
      "Experience a realistic Google software engineer interview with coding challenges and system design questions.",
    category: "MAANG+",
    duration: "45 min",
    difficulty: "Advanced",
    isFree: false,
    tags: ["Google", "SWE", "Algorithms"],
  },
  {
    id: "8",
    title: "Meta Frontend Interview",
    description:
      "Practice Meta's frontend interview process with React, JavaScript, and system design components.",
    category: "MAANG+",
    duration: "40 min",
    difficulty: "Intermediate",
    isFree: false,
    tags: ["Meta", "Frontend", "React"],
  },

  // Behavioral Interview
  {
    id: "9",
    title: "Leadership & Teamwork",
    description:
      "Practice answering behavioral questions about leadership experiences and team collaboration.",
    category: "Behavioral Interview",
    duration: "20 min",
    difficulty: "Beginner",
    isFree: false,
    tags: ["Leadership", "Teamwork", "STAR Method"],
  },
  {
    id: "10",
    title: "Conflict Resolution",
    description:
      "Learn to articulate how you handle workplace conflicts and challenging situations.",
    category: "Behavioral Interview",
    duration: "15 min",
    difficulty: "Intermediate",
    isFree: false,
    tags: ["Conflict", "Communication", "Problem Solving"],
  },
];

// Available filter categories
const availableCategories = [
  "Free Interview",
  "System Design",
  "Coding Interview",
  "MAANG+",
  "Behavioral Interview",
  "Low Level Design",
  "API Design",
  "Artificial Intelligence",
  "DS/ML",
  "Full Stack Development",
  "Advanced System Design",
  "Generative AI System Design",
  "AWS Cloud",
];

interface MockInterviewSearchSectionProps {
  className?: string;
}

const MockInterviewSearchSection: React.FC<MockInterviewSearchSectionProps> = ({
  className = "",
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([
    "Free Interview",
  ]);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Filter interviews based on search and categories
  const filteredInterviews = useMemo(() => {
    let filtered = mockInterviews;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (interview) =>
          interview.title.toLowerCase().includes(query) ||
          interview.description.toLowerCase().includes(query) ||
          interview.category.toLowerCase().includes(query) ||
          interview.tags?.some((tag) => tag.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (selectedCategories.includes("Free Interview")) {
      filtered = filtered.filter((interview) => interview.isFree);
    } else if (selectedCategories.includes("Saved Interviews")) {
      // For now, return empty array since we don't have saved interviews functionality
      filtered = [];
    } else if (selectedCategories.length > 0) {
      // Filter by specific categories
      filtered = filtered.filter((interview) =>
        selectedCategories.includes(interview.category)
      );
    }
    // If no categories selected or "All Interviews" selected, show all

    return filtered;
  }, [searchQuery, selectedCategories]);

  // Group interviews by category for display when no filters are applied
  const interviewsByCategory = useMemo(() => {
    const grouped: Record<string, MockInterview[]> = {};

    filteredInterviews.forEach((interview) => {
      if (!grouped[interview.category]) {
        grouped[interview.category] = [];
      }
      grouped[interview.category].push(interview);
    });

    return grouped;
  }, [filteredInterviews]);

  // Check if we should show grouped by category (no specific filters applied)
  const shouldShowGrouped =
    selectedCategories.length === 0 ||
    (selectedCategories.length === 1 &&
      selectedCategories[0] === "Free Interview" &&
      !searchQuery.trim());

  const removeCategoryFilter = (category: string) => {
    setSelectedCategories((prev) => prev.filter((c) => c !== category));
  };

  return (
    <div className={`bg-gray-50 py-8 ${className}`}>
      <Container size="lg-plus">
        {/* Search and Filter Section */}
        <div className="mb-8 sticky top-16 bg-[#f9fafb] py-8 border-b border-gray-200">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between mb-6">
            <div className="flex gap-4">
              {/* Search Input */}
              <div className="relative flex-1 sm:w-96">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 transition-colors" />
                <Input
                  type="text"
                  placeholder="Search any mock interview here"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="h-8 pl-10 pr-10 w-full border border-gray-300 rounded-md bg-white text-sm placeholder:text-sm placeholder:text-gray-700  transition-all duration-200 hover:border-gray-400"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery("")}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-150"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
              {/* Dropdown Filter */}
              <div className="relative sm:w-96" ref={dropdownRef}>
                <button
                  type="button"
                  onClick={() => setDropdownOpen(!dropdownOpen)}
                  className="flex items-center justify-between px-4 py-[0.33rem] bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:border-gray-400 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 focus:ring-opacity-50 w-full transition-all duration-200"
                >
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border border-gray-400 rounded-sm flex items-center justify-center">
                      {selectedCategories.includes("Free Interview") ? (
                        <Rocket className="w-3 h-3 text-teal-600" />
                      ) : selectedCategories.includes("Saved Interviews") ? (
                        <Bookmark className="w-3 h-3 text-gray-400" />
                      ) : (
                        <div className="w-2 h-2 bg-gray-400 rounded-sm"></div>
                      )}
                    </div>
                    <span className="text-gray-700 text-sm">
                      {selectedCategories.includes("Free Interview")
                        ? "Free Interviews (2)"
                        : selectedCategories.includes("Saved Interviews")
                        ? "Saved Interviews (0)"
                        : "All Interviews"}
                    </span>
                  </div>
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </button>

                {/* Dropdown Menu */}
                {dropdownOpen && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10">
                    <div className="py-1 text-sm">
                      {/* All Interviews Option */}
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedCategories([]);
                          setDropdownOpen(false);
                        }}
                        className="flex items-center gap-2 w-full px-4 py-2 text-left hover:bg-gray-50"
                      >
                        <div className="w-4 h-4 border border-gray-400 rounded-sm flex items-center justify-center">
                          <div className="w-2 h-2 bg-gray-400 rounded-sm"></div>
                        </div>
                        <span className="text-gray-700">All Interviews</span>
                      </button>

                      {/* Saved Interviews Option */}
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedCategories(["Saved Interviews"]);
                          setDropdownOpen(false);
                        }}
                        className="flex items-center gap-2 w-full px-4 py-2 text-left hover:bg-gray-50"
                      >
                        <div className="w-4 h-4 border border-gray-400 rounded-sm flex items-center justify-center">
                          <Bookmark className="w-3 h-3 text-gray-400" />
                        </div>
                        <span className="text-gray-700">
                          Saved Interviews (0)
                        </span>
                      </button>

                      {/* Free Interviews Option */}
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedCategories(["Free Interview"]);
                          setDropdownOpen(false);
                        }}
                        className="flex items-center gap-2 w-full px-4 py-2 text-left hover:bg-gray-50"
                      >
                        <div className="w-4 h-4 border border-gray-400 rounded-sm flex items-center justify-center">
                          <Rocket className="w-3 h-3 text-teal-600" />
                        </div>
                        <span className="text-gray-700">
                          Free Interviews (2)
                        </span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Upgrade Link */}
            <div className="flex items-center">
              <a
                href="/unlimited"
                className="text-md  text-gray-900 hover:underline"
              >
                Upgrade
              </a>
              <FluxIcon
                name="info"
                width={24}
                height={24}
                className="content-default-default ml-1 h-5 w-5"
              />
            </div>
          </div>

          {/* Filter Tags */}
          <div className="flex flex-wrap gap-2">
            {/* Free Interview Tag - Always Show */}
            <Badge
              variant="outline"
              className={`flex items-center gap-1 px-3 py-1 h-8 rounded-full transition-colors cursor-pointer bg-white text-gray-700 hover:bg-gray-50 ${
                selectedCategories.includes("Free Interview")
                  ? "border-indigo-500"
                  : "border-gray-300"
              }`}
              onClick={() => {
                if (selectedCategories.includes("Free Interview")) {
                  removeCategoryFilter("Free Interview");
                } else {
                  setSelectedCategories((prev) => [...prev, "Free Interview"]);
                }
              }}
            >
              <Rocket className="w-3 h-3" />
              Free Interview
              {selectedCategories.includes("Free Interview") && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removeCategoryFilter("Free Interview");
                  }}
                  className="ml-1 hover:bg-gray-200 rounded-full p-0.5 transition-colors"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </Badge>
            <div className="mx-2 h-8 border-l border-r-0 border-solid border-gray-L300 dark:border-gray-D1100"></div>

            {/* All Other Category Tags - Maintain Order */}
            {availableCategories.slice(1).map((category) => {
              const isSelected = selectedCategories.includes(category);
              return (
                <Badge
                  key={category}
                  variant="outline"
                  className={`flex items-center gap-1 px-3 py-1 h-8 bg-white text-gray-700 rounded-full hover:bg-gray-50 cursor-pointer transition-colors ${
                    isSelected ? "border-indigo-500" : "border-gray-300"
                  }`}
                  onClick={() => {
                    if (isSelected) {
                      removeCategoryFilter(category);
                    } else {
                      setSelectedCategories((prev) => [...prev, category]);
                    }
                  }}
                >
                  {category}
                  {isSelected && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeCategoryFilter(category);
                      }}
                      className="ml-1 hover:bg-gray-200 rounded-full p-0.5 transition-colors"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  )}
                </Badge>
              );
            })}
          </div>
        </div>

        {/* Results Section */}
        <div>
          <div className="flex items-center text-gray-600 mb-6">
            <Filter className="h-3 w-3 mr-2" />
            <span className="text-xs">
              {filteredInterviews.length} Result
              {filteredInterviews.length !== 1 ? "s" : ""} Found
            </span>
          </div>

          {/* Grouped by Category Display */}
          {shouldShowGrouped ? (
            <div className="space-y-8">
              {Object.entries(interviewsByCategory).map(
                ([category, interviews]) => (
                  <div key={category}>
                    <Typography variant="h4" className="mb-4 text-gray-900">
                      {category}
                    </Typography>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                      {interviews.map((interview) => (
                        <div
                          key={interview.id}
                          className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1 cursor-pointer"
                        >
                          {/* Header */}
                          <div className="flex items-center justify-between mb-3">
                            <span className="text-sm text-gray-500">
                              {interview.category}
                            </span>
                            <button className="p-1 hover:bg-gray-100 rounded">
                              <Bookmark className="h-4 w-4 text-gray-400" />
                            </button>
                          </div>

                          {/* Content */}
                          <div className="mb-4">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold text-gray-900">
                                {interview.title}
                              </h3>
                              {interview.isFree && (
                                <Badge className="bg-teal-600 text-white text-xs px-2 py-0.5">
                                  Free Interview
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 line-clamp-3">
                              {interview.description}
                            </p>
                          </div>

                          {/* Footer */}
                          <div className="flex items-center text-gray-500">
                            <Clock className="h-4 w-4 mr-1" />
                            <span className="text-sm">
                              {interview.duration}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )
              )}
            </div>
          ) : (
            /* Regular Grid Display */
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredInterviews.map((interview) => (
                <div
                  key={interview.id}
                  className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1 cursor-pointer"
                >
                  {/* Header */}
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm text-gray-500">
                      {interview.category}
                    </span>
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <Bookmark className="h-4 w-4 text-gray-400" />
                    </button>
                  </div>

                  {/* Content */}
                  <div className="mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold text-gray-900">
                        {interview.title}
                      </h3>
                      {interview.isFree && (
                        <Badge className="bg-teal-600 text-white text-xs px-2 py-0.5">
                          Free Interview
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-3">
                      {interview.description}
                    </p>
                  </div>

                  {/* Footer */}
                  <div className="flex items-center text-gray-500">
                    <Clock className="h-4 w-4 mr-1" />
                    <span className="text-sm">{interview.duration}</span>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* No Results */}
          {filteredInterviews.length === 0 && (
            <div className="text-center py-12">
              <Typography variant="h6" className="text-gray-500 mb-2">
                No interviews found
              </Typography>
              <Typography variant="p" className="text-gray-400">
                Try adjusting your search or filters
              </Typography>
            </div>
          )}
        </div>
      </Container>
    </div>
  );
};

export default MockInterviewSearchSection;
