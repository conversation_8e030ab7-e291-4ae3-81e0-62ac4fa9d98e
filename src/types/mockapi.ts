/*
 * @Description: MockAPI 数据结构类型定义
 * @Author: Devin
 * @Date: 2025-08-05
 */

// MockAPI 课程数据主接口
export interface MockApiCourseData {
  id?: string; // 可选，因为有些数据可能没有这个字段
  deletion_time: string | null;
  ai_assistant_enabled: boolean;
  certificate_price: number;
  is_recommended_by_reader: boolean;
  is_reader_subscribed: boolean;
  pinned_by_reader: boolean;
  is_published: boolean;
  first_published_time: string;
  last_published_time: string;
  timestamp: string;
  total_courses: number;
  price: number;
  skills: string[];
  tags: string[];
  aggregated_widget_stats: MockApiWidgetStats;
  url_slug: string;
  authors: MockApiAuthor[];
  read_time: number;
  learner_tags: string[];
  level_one_learner_tags: string[];
  courses: MockApiCourse[];
}

// Widget 统计信息接口
export interface MockApiWidgetStats {
  MarkdownEditor?: number;
  codeExerciseCount?: number;
  codeRunnableCount?: number;
  codeSnippetCount?: number;
  illustrations?: number;
  MxGraphWidget?: number;
  RunJS?: number;
  Columns?: number;
  TerminalWidget?: number;
  WebpackBin?: number;
  Quiz?: number;
  CodeDrawing?: number;
  Code?: number;
  CanvasAnimation?: number;
  SlateHTML?: number;
  AINotepad?: number;
  PromptAI?: number;
  EditorCode?: number;
  Sandpack?: number;
  TabbedCode?: number;
  Matrix?: number;
  DrawIOWidget?: number;
  [key: string]: number | undefined;
}

// 作者信息接口
export interface MockApiAuthor {
  id?: string;
  name?: string;
  title?: string;
  avatar?: string;
  bio?: string;
}

// 课程详细信息接口
export interface MockApiCourse {
  title: string;
  summary: string;
  brief_summary: string;
  whatYouWillLearn: string[];
  target_audience: string;
  cover_image_url: string;
  creation_time: string;
  modified_time: string;
  published_time: string;
  categories: MockApiCategory[];
}

// 课程分类接口
export interface MockApiCategory {
  id: string;
  title: string;
  summary: string;
  pages: MockApiPage[];
}

// Quiz 选项接口
export interface MockApiQuestionOption {
  text: string;
  id: string;
  correct: boolean;
  explanation: {
    mdText: string;
    mdHtml: string;
  };
  mdHtml: string;
}

// Quiz 问题接口
export interface MockApiQuestion {
  questionText: string;
  questionTextHtml: string;
  questionOptions: MockApiQuestionOption[];
  multipleAnswers?: boolean;
}

// 课程页面接口
export interface MockApiPage {
  id: string;
  title: string;
  is_preview: boolean;
  slug: string;
  text: string;
  mdHtml: string;
  summary: Summary;
  questions?: MockApiQuestion[]; // 添加 questions 字段用于 Quiz 页面
}
export interface Summary {
  titleUpdated: boolean;
  title: string;
  description: string;
  tags: string[];
}

// API 响应接口
export interface MockApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 课程查询参数接口
export interface CourseQueryParams {
  id?: string;
  url_slug?: string;
  tags?: string[];
  skills?: string[];
  is_published?: boolean;
}

// 课程统计信息接口
export interface MockApiCourseStats {
  totalPages: number;
  totalCategories: number;
  previewPages: number;
  estimatedDuration: number; // 基于 read_time 计算的分钟数
  widgetCounts: MockApiWidgetStats;
}

// 课程搜索结果接口
export interface CourseSearchResult {
  courses: MockApiCourseData[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// 课程转换工具函数类型
export interface CourseConverter {
  /**
   * 将 MockAPI 数据转换为应用内部使用的课程数据格式
   */
  toInternalFormat(mockData: MockApiCourseData): any;

  /**
   * 计算课程统计信息
   */
  calculateStats(mockData: MockApiCourseData): MockApiCourseStats;

  /**
   * 提取课程的学习路径
   */
  extractLearningPath(mockData: MockApiCourseData): string[];
}

// 课程难度枚举
export enum CourseDifficulty {
  BEGINNER = "beginner",
  INTERMEDIATE = "intermediate",
  ADVANCED = "advanced",
}

// 课程状态枚举
export enum CourseStatus {
  DRAFT = "draft",
  PUBLISHED = "published",
  ARCHIVED = "archived",
}

// 课程类型枚举
export enum CourseType {
  INTERACTIVE = "interactive",
  VIDEO = "video",
  TEXT = "text",
  MIXED = "mixed",
}

// 所有类型已通过 export interface 和 export enum 导出
