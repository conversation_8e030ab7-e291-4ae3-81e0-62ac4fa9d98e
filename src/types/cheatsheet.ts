/*
 * @Description: Cheatsheet types and interfaces
 * @Author: Devin
 * @Date: 2025-07-31
 */

export type CheatsheetCategory = 
  | "System Design"
  | "Programming Language"
  | "Machine Learning"
  | "Cloud"
  | "Backend"
  | "Frontend"
  | "Data Science"
  | "DevOps"
  | "Interview Prep"
  | "API Design"
  | "Database"
  | "Security"
  | "Mobile"
  | "Web Development"
  | "CS101"
  | "Algorithms"
  | "Frameworks";

export type CheatsheetDifficulty = "Beginner" | "Intermediate" | "Advanced";

export interface Cheatsheet {
  id: string;
  title: string;
  description: string;
  category: CheatsheetCategory;
  difficulty: CheatsheetDifficulty;
  downloadCount: number;
  rating: number;
  ratingCount: number;
  tags: string[];
  fileSize: string;
  pages: number;
  lastUpdated: Date;
  thumbnail?: string;
  downloadUrl: string;
  previewUrl?: string;
  isFree: boolean;
  isPopular?: boolean;
  isNew?: boolean;
}

export interface CheatsheetFilter {
  categories: CheatsheetCategory[];
  difficulties: CheatsheetDifficulty[];
  isFree?: boolean;
  isPopular?: boolean;
  isNew?: boolean;
  minRating?: number;
}

export interface CheatsheetSearchProps {
  searchQuery: string;
  filters: CheatsheetFilter;
  sortBy: "popularity" | "rating" | "newest" | "downloads" | "alphabetical";
  sortOrder: "asc" | "desc";
}

export interface CheatsheetStats {
  totalCheatsheets: number;
  totalDownloads: number;
  averageRating: number;
  popularCategories: {
    category: CheatsheetCategory;
    count: number;
    percentage: number;
  }[];
}
