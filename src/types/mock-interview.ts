/*
 * @Description: Mock Interview Types and Interfaces
 * @Author: AI Assistant
 * @Date: 2025-07-30
 */

// Interview difficulty levels
export type InterviewDifficulty = "Easy" | "Medium" | "Hard";

// Interview categories
export type InterviewCategory = 
  | "System Design"
  | "Coding Interview" 
  | "MAANG+"
  | "Behavioral Interview"
  | "Low Level Design"
  | "API Design"
  | "Artificial Intelligence"
  | "DS/ML"
  | "Full Stack Development"
  | "Advanced System Design"
  | "Generative AI System Design"
  | "AWS Cloud";

// Interview status
export type InterviewStatus = "not_started" | "in_progress" | "completed" | "paused";

// Feedback score levels
export type FeedbackScore = "Excellent" | "Good" | "Average" | "Needs Improvement";

// Company types for MAANG+ interviews
export type CompanyType = 
  | "Google"
  | "Meta"
  | "Amazon"
  | "Apple"
  | "Netflix"
  | "Microsoft"
  | "LinkedIn"
  | "Oracle"
  | "Uber"
  | "Stripe";

// Interview question interface
export interface InterviewQuestion {
  id: string;
  title: string;
  description: string;
  difficulty: InterviewDifficulty;
  timeLimit: number; // in minutes
  hints?: string[];
  followUpQuestions?: string[];
  expectedApproach?: string[];
  codeTemplate?: string; // for coding interviews
  testCases?: TestCase[]; // for coding interviews
}

// Test case interface for coding interviews
export interface TestCase {
  id: string;
  input: string;
  expectedOutput: string;
  explanation?: string;
}

// Interview feedback interface
export interface InterviewFeedback {
  id: string;
  interviewId: string;
  overallScore: FeedbackScore;
  strengths: string[];
  areasForImprovement: string[];
  detailedFeedback: {
    problemSolving: FeedbackScore;
    communication: FeedbackScore;
    technicalKnowledge: FeedbackScore;
    codeQuality?: FeedbackScore; // for coding interviews
    systemDesignThinking?: FeedbackScore; // for system design interviews
  };
  recommendations: string[];
  nextSteps: string[];
  estimatedReadiness: "Ready" | "Almost Ready" | "Needs More Practice";
}

// Interview session interface
export interface InterviewSession {
  id: string;
  interviewId: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  status: InterviewStatus;
  currentQuestionIndex: number;
  responses: InterviewResponse[];
  timeSpent: number; // in seconds
  feedback?: InterviewFeedback;
}

// Interview response interface
export interface InterviewResponse {
  questionId: string;
  response: string;
  codeSubmission?: string; // for coding interviews
  timeSpent: number; // in seconds
  isCorrect?: boolean;
}

// Main interview interface
export interface MockInterview {
  id: string;
  title: string;
  description: string;
  category: InterviewCategory;
  difficulty: InterviewDifficulty;
  duration: number; // in minutes
  company?: CompanyType; // for MAANG+ interviews
  isFree: boolean;
  isPopular?: boolean;
  isNew?: boolean;
  tags: string[];
  questions: InterviewQuestion[];
  prerequisites?: string[];
  learningObjectives: string[];
  thumbnail?: string;
  completionCount: number;
  averageRating: number;
  ratingCount: number;
}

// Interview statistics interface
export interface InterviewStats {
  totalInterviews: number;
  completedInterviews: number;
  averageScore: number;
  strongestCategory: InterviewCategory;
  weakestCategory: InterviewCategory;
  totalTimeSpent: number; // in minutes
  improvementTrend: "improving" | "stable" | "declining";
}

// User interview progress interface
export interface UserInterviewProgress {
  userId: string;
  completedInterviews: string[]; // interview IDs
  inProgressInterviews: string[]; // interview IDs
  favoriteInterviews: string[]; // interview IDs
  stats: InterviewStats;
  recentSessions: InterviewSession[];
  achievements: Achievement[];
}

// Achievement interface
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt: Date;
  category: "completion" | "streak" | "performance" | "milestone";
}

// Interview filter interface
export interface InterviewFilter {
  categories: InterviewCategory[];
  difficulties: InterviewDifficulty[];
  companies: CompanyType[];
  duration: {
    min: number;
    max: number;
  };
  isFree?: boolean;
  isPopular?: boolean;
  isNew?: boolean;
}

// Interview search and filter props
export interface InterviewSearchProps {
  searchQuery: string;
  filters: InterviewFilter;
  sortBy: "popularity" | "difficulty" | "duration" | "rating" | "newest";
  sortOrder: "asc" | "desc";
}

// Testimonial interface
export interface Testimonial {
  id: string;
  name: string;
  role?: string;
  company?: string;
  avatar?: string;
  content: string;
  rating: number;
  featured?: boolean;
}

// Interview analytics interface
export interface InterviewAnalytics {
  totalConducted: number;
  averageCompletionRate: number;
  popularCategories: {
    category: InterviewCategory;
    count: number;
    percentage: number;
  }[];
  userSatisfactionScore: number;
  averageSessionDuration: number;
}
