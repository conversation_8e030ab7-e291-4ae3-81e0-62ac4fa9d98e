/*
 * Auto types aligned with docs/swagger.json (Pointer Center API)
 * Only use these new types moving forward
 */

// ===== Generic response wrappers =====
export interface BaseResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  timestamp?: string; // RFC 3339 date-time
}

export interface Paginated<T = any> {
  data: T[];
  total: number;
  page: number;
  page_size: number;
}

// Back-compat helpers for legacy code
export interface ApiResponse<T = any> extends BaseResponse<T> {
  // 一些旧代码会依赖 code 字段，这里作为可选字段保留兼容
  code?: number;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  total: number;
  page: number;
  page_size: number;
}

export interface BaseRequestParams {
  page?: number;
  page_size?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
  search?: string;
  filters?: Record<string, any>;
}

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// ===== Schemas from components/schemas =====

export interface User {
  id?: string; // uuid
  username?: string;
  email?: string; // email
  first_name?: string;
  last_name?: string;
  avatar?: string;
  bio?: string;
  phone?: string;
  company?: string;
  country?: string;
  github?: string;
  website?: string;
  role?: "user" | "admin" | "moderator";
  status?: "active" | "inactive" | "suspended";
  origin?: string;
  email_verified?: boolean;
  last_login?: string; // date-time
  created_at?: string; // date-time
  updated_at?: string; // date-time
}

export interface LearningPath {
  id?: string; // uuid
  creator_id?: string; // uuid
  goal?: string;
  goal_category?: string;
  title?: string;
  cover_image?: string;
  description?: string;
  path_type?: "personalized" | "standard" | "custom" | "adaptive";
  difficulty?: number; // 1-10
  estimated_times?: number;
  total_nodes?: number;
  suitable_for?: string; // JSON array (stringified)
  learning_outcomes?: string; // JSON array (stringified)
  is_public?: boolean;
  usage_count?: number;
  rating?: number; // float
  created_at?: string; // date-time
  updated_at?: string; // date-time
}

export interface LearningNode {
  id?: string; // uuid
  title?: string;
  description?: string;
  cover_image?: string; // Cover image URL
  estimated_times: number;
  difficulty?: number; // 1-10
  prerequisites?: string; // JSON array (stringified)
  status?: "draft" | "active" | "archived" | "deprecated";
  created_by?: string; // uuid
  updated_by?: string; // uuid
  created_at?: string; // date-time
  updated_at?: string; // date-time
  price?: number;
  certificate_price?: number;
  skills?: string[];
  tags?: string[];
  learner_tags?: string[];
  what_you_will_learn?: string[];
}

export interface AtomicContent {
  id?: string; // content flow ID for progress tracking
  type:
    | "text_explanation"
    | "code_snippet"
    | "diagram_description"
    | "flowchart_description"
    | "multiple_choice_quiz"
    | "fill_in_blank_quiz"
    | "practice_exercise"
    | "math_formula"
    | "interactive_quiz";
  order: number;
  data: Record<string, any>;
}

export interface ContentFlowBrief {
  id?: string; // content flow ID for progress tracking
  type: AtomicContent["type"];
  order: number;
  title?: string;
  description?: string;
}

export interface LearningLesson {
  id?: string; // Mongo ObjectID
  lesson_id?: string;
  title?: string;
  description?: string;
  type?:
    | "text"
    | "interactive"
    | "quiz"
    | "code"
    | "project"
    | "live"
    | "assignment";
  estimated_minutes?: number;
  difficulty?: number; // 1-10
  content_flow?: AtomicContent[];
  learning_objectives?: string[];
  common_misconceptions?: string[];
  extension_idea?: string;
  student_profile_association?: string;
  status?:
    | "draft"
    | "active"
    | "archived"
    | "deprecated"
    | "generating"
    | "failed";
  created_by?: string;
  updated_by?: string;
  created_at?: string; // date-time
  updated_at?: string; // date-time
}

// Requests for Learning Lessons
export interface CreateLearningLessonRequest {
  title: string;
  description?: string;
  type: LearningLesson["type"];
  estimated_minutes?: number;
  difficulty?: number; // 1-10
  content_flow?: AtomicContent[];
  learning_objectives?: string[];
  node_id?: string; // uuid
  order?: number;
}

export type UpdateLearningLessonRequest = Partial<CreateLearningLessonRequest>;

export interface GenerateLessonRequest {
  lesson_description: string;
  user_id?: string;
}

export interface UpdateLessonStatusRequest {
  status: "draft" | "active" | "archived"; // per swagger for lessons
}

export interface DuplicateLessonRequest {
  new_title?: string;
}

export interface StaticProfile {
  id?: string; // uuid
  user_id?: string; // uuid
  age?: number;
  gender?: string;
  preferred_language?: string;
  education_experience?: string;
  major?: string;
  graduation_year?: number;
  current_role?: string;
  industry?: string;
  work_experience?: number;
  learning_style?: string;
  study_time_per_week?: number;
  preferred_study_time?: string;
  learning_pace?: string;
  created_at?: string; // date-time
  updated_at?: string; // date-time
}

export interface TechCompetencyGraph {
  id?: string; // MongoDB ObjectID
  user_id?: string; // uuid
  nodes?: CompetencyNode[];
  edges?: CompetencyEdge[];
  created_at?: string; // date-time
  updated_at?: string; // date-time
}

export interface CompetencyNode {
  id?: string;
  name?: string;
  level?: number; // 0-5
  category?: string;
}

export interface CompetencyEdge {
  from?: string;
  to?: string;
  relationship?: "prerequisite" | "related" | "advanced";
}

export interface CreateStaticProfileRequest {
  age?: number;
  gender?: string;
  preferred_language?: string;
  education_experience?: string;
  major?: string;
  graduation_year?: number;
  current_role?: string;
  industry?: string;
  work_experience?: number;
  learning_style?: string;
  study_time_per_week?: number;
  preferred_study_time?: string;
  learning_pace?: string;
}

export type UpdateStaticProfileRequest = Partial<CreateStaticProfileRequest>;

export interface UpdateTechCompetencyRequest {
  action:
    | "add_node"
    | "update_node"
    | "remove_node"
    | "add_edge"
    | "remove_edge";
  node_data?: CompetencyNode;
  edge_data?: CompetencyEdge;
  node_id?: string;
  edge_id?: string;
}

// ===== Request bodies (from paths) =====

// Auth
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
}

export interface LoginRequest {
  email?: string;
  password?: string;
  id_token?: string;
  auth_code?: string;
  redirect_uri?: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface LoginResponseData {
  user?: User;
  access_token?: string;
  refresh_token?: string;
  expires_at?: string; // date-time
  is_new_user?: boolean;
  login_time?: string; // date-time
  message?: string;
}

export interface RefreshResponseData {
  access_token: string;
  expires_at: string; // date-time
}

// Learning Paths
export interface CreateLearningPathRequest {
  goal: string;
  goal_category?: string;
  title: string;
  description?: string;
  path_type?: "personalized" | "standard" | "custom" | "adaptive";
  difficulty?: number; // 1-10
  estimated_times?: number;
  suitable_for?: string[];
  learning_outcomes?: string[];
  is_public?: boolean;
}

export type UpdateLearningPathRequest = Partial<
  Omit<CreateLearningPathRequest, "goal" | "title">
> & {
  goal?: string;
  title?: string;
};

export interface GenerateLearningPathRequest {
  goal: string;
  goal_category?: string;
  difficulty_preference?: number; // 1-10
  time_commitment?: number; // hours/week
  learning_style?: string;
  background_info?: string;
}

// Learning Nodes
export interface CreateLearningNodeRequest {
  title: string;
  description?: string;
  estimated_times?: number;
  difficulty?: number; // 1-10
  prerequisites?: string[];
}

export type UpdateLearningNodeRequest = Partial<CreateLearningNodeRequest>;

export interface UpdateNodeStatusRequest {
  status: "draft" | "active" | "archived" | "deprecated";
}

export interface GenerateLearningNodeRequest {
  topic: string;
  learning_objective?: string;
  difficulty_level?: number; // 1-10
  estimated_times?: number;
  context?: string;
}

// New Learning Node APIs
export interface GenerateNodeAndLessonsRequest {
  node_description: string;
  lessons?: Array<{
    title?: string;
    description?: string;
    estimated_minutes?: number;
    type?: string;
  }>;
}

export interface GenerateNodeAndLessonsResponse {
  node: LearningNode;
  lessons: LearningLesson[];
}

export interface AddNodeToPathRequest {
  node_id: string; // uuid
  path_id: string; // uuid
  order?: number;
}

// User Master Path
export interface UserMasterPath {
  id?: string; // uuid
  user_id?: string; // uuid
  paths?: LearningPath[];
  total_estimated_times?: number;
  completion_percentage?: number;
  created_at?: string; // date-time
  updated_at?: string; // date-time
}

export interface CreateUserMasterPathRequest {
  learning_goal?: string;
  target_completion_date?: string; // date
  weekly_study_hours?: number;
}

export interface AddPathToMasterPathRequest {
  path_id: string; // uuid
  order?: number;
}

// Learning Node -> Generate Course (long running)
export interface GenerateCourseResult {
  success: boolean;
  node_id: string; // uuid
  generated_lessons: number;
}

export interface NodeLesson {
  id: string; // uuid
  node_id: string; // uuid
  lesson_id: string; // Mongo ID or string
  order_key?: string; // sortable rank key

  lesson_title?: string;
  lesson_description?: string;
  content_flow?: ContentFlowBrief[];
  created_at?: string; // date-time
  updated_at?: string; // date-time

  price?: number;
  certificate_price?: number;
  skills?: string[];
  tags?: string[];
  learner_tags?: string[];
  what_you_will_learn?: string[];
}

// ===== Personalize (Adjust) APIs =====
export type AdjustScope = "path" | "node" | "lesson";

export interface PersonalizeStartRequest {
  student_need: string;
  path_id: string; // uuid
  adjust_scope: AdjustScope;
  target_id?: string; // uuid, when scope is node/lesson
}

export interface PersonalizeStartResponse {
  conversation_id: string;
}

export interface PersonalizeStatusResponse {
  status: string;
  last_thought?: string;
  ask_question?: string;
  error?: string;
}

export interface PersonalizeReplyRequest {
  conversation_id: string;
  reply: string;
}

// Payment - Alipay
export interface AlipayQRCodeRequest {
  user_id: string; // uuid
  amount: number; // float
  subject: string;
  body?: string;
  order_id?: string;
  expire_time?: number; // minutes
  extra_data?: Record<string, any>;
}

export interface AlipayQRCodeResponse {
  out_trade_no?: string;
  qr_code?: string;
  expire_time?: string;
}

export interface AlipayStatusResponse {
  out_trade_no?: string;
  trade_no?: string;
  trade_status?: string;
  total_amount?: string;
  receipt_amount?: string;
  gmt_payment?: string;
}

// Payment - WeChat
export interface WeChatQRCodeRequest {
  user_id: string; // uuid
  total_fee: number; // in cents
  body: string;
  detail?: string;
  order_id?: string;
  expire_time?: number; // minutes
  extra_data?: Record<string, any>;
}

export interface WeChatQRCodeResponse {
  out_trade_no?: string;
  code_url?: string;
  expire_time?: string;
}

// ===== Health minimal response types (per swagger) =====
export interface HealthResponse {
  status: string; // healthy
  timestamp?: string; // date-time
  service?: string;
  version?: string;
}

export interface ReadyResponse {
  status: string; // ready
  checks: { database: string };
}

export interface LiveResponse {
  status: string; // alive
}

// ===== AI Goal Planning (server-provided schemas) =====
export interface SuggestedGoal {
  final_goal_description: string;
  focus_direction: string;
}

export interface GoalPlan {
  suggested_goals: SuggestedGoal[];
}

// ===== User Lesson Progress (based on docs/swagger_progress.json) =====
export interface UserLessonProgress {
  id?: string; // uuid
  user_id?: string; // uuid
  lesson_id?: string; // MongoDB ObjectID
  node_id?: string; // uuid
  learning_path_id?: string; // uuid
  is_completed?: boolean;
  completed_at?: string; // date-time
  score?: number; // float 0-100
  time_spent?: number; // minutes
  attempts?: number;
  last_accessed_at?: string; // date-time
  completed_content_flow_ids?: string[];
  created_at?: string; // date-time
  updated_at?: string; // date-time
}

export interface UpsertCompletedContentFlowRequest {
  path_id?: string; // uuid, optional, mutually exclusive with node_id
  node_id?: string; // uuid, optional, mutually exclusive with path_id
  lesson_id: string; // required
  content_flow_id: string; // required
  progress: number; // 0-1 之间的进度值
}

export interface RemoveCompletedContentFlowRequest {
  path_id?: string; // uuid, optional, mutually exclusive with node_id
  node_id?: string; // uuid, optional, mutually exclusive with path_id
  lesson_id: string; // required
  content_flow_id: string; // required
  progress: number; // 0-1 之间的进度值
}

export interface UserLessonProgressMap {
  [lesson_id: string]: UserLessonProgress;
}

export interface CompletedContentFlowsResponse {
  user_id: string; // uuid
  lesson_id: string;
  completed_content_flow_ids: string[];
  count: number;
}
