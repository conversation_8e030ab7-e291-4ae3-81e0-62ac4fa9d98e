/**
 * Payment related TypeScript types
 * These are lightweight definitions to satisfy compile-time checks and
 * represent common fields used by Alipay and WeChat payments.
 * Adjust/extend as backend contracts evolve.
 */

// ===== Common =====
export interface PaymentOrder {
  out_trade_no: string;
  total_amount: number;
  currency?: string; // e.g., CNY
  subject: string;
  description?: string;
  // optional scene/context
  metadata?: Record<string, any>;
}

export type PaymentStatus =
  | {
      out_trade_no: string;
      status: "pending" | "paid" | "failed" | "cancelled" | "refunded";
      amount?: number;
      currency?: string;
      transaction_id?: string;
      paid_at?: string; // date-time
      created_at?: string; // date-time
      updated_at?: string; // date-time
      reason?: string;
    }
  | Record<string, any>; // fallback for provider-specific fields

export interface PaymentCallbackResult {
  provider: "alipay" | "wechat";
  out_trade_no: string;
  status: "success" | "failed" | "pending";
  message?: string;
  data?: Record<string, any>;
}

export interface PaymentVerificationResult {
  valid: boolean;
  error_message?: string;
}

export interface PaymentStats {
  total_amount?: number;
  total_orders?: number;
  paid_orders?: number;
  refunded_orders?: number;
  by_method?: {
    alipay?: { amount?: number; count?: number };
    wechat?: { amount?: number; count?: number };
  };
  range?: { start_date?: string; end_date?: string };
}

// ===== Alipay =====
export interface AlipayQRCodeRequest {
  out_trade_no: string;
  total_amount: number;
  subject: string;
  description?: string;
}

export interface AlipayQRCodeResponse {
  qr_code?: string; // base64 or URL
  payment_url?: string;
  out_trade_no?: string;
}

export interface AlipayWebRequest {
  out_trade_no: string;
  total_amount: number;
  subject: string;
  return_url?: string;
}

export interface AlipayWebResponse {
  form_data?: string; // HTML form string for auto-submit
  payment_url?: string;
  out_trade_no?: string;
}

export interface AlipayStatusRequest {
  out_trade_no: string;
}

export interface AlipayStatusResponse {
  out_trade_no: string;
  trade_status: "WAIT_BUYER_PAY" | "TRADE_SUCCESS" | "TRADE_CLOSED" | "TRADE_FINISHED" | string;
  total_amount?: number;
  transaction_id?: string;
  pay_time?: string; // date-time
}

export type AlipayNotifyData = Record<string, string>;

// ===== WeChat Pay =====
export interface WechatJSAPIRequest {
  out_trade_no: string;
  total_amount: number; // in cents if required by backend
  description: string;
  openid: string;
  attach?: string;
}

export interface WechatJSAPIResponse {
  appId?: string;
  timeStamp?: string;
  nonceStr?: string;
  package?: string; // e.g., prepay_id=...
  signType?: string;
  paySign?: string;
}

export interface WechatQRCodeRequest {
  out_trade_no: string;
  total_amount: number; // in cents if required
  description: string;
}

export interface WechatQRCodeResponse {
  code_url?: string;
  qr_code?: string; // optional unified field
  out_trade_no?: string;
}

export interface WechatStatusRequest {
  out_trade_no: string;
}

export interface WechatStatusResponse {
  out_trade_no: string;
  trade_state:
    | "SUCCESS"
    | "REFUND"
    | "NOTPAY"
    | "CLOSED"
    | "REVOKED"
    | "USERPAYING"
    | "PAYERROR"
    | string;
  total_amount?: number;
  transaction_id?: string;
  success_time?: string; // date-time
}

export type WechatNotifyData = Record<string, string>;

