/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-23 10:20:44
 */

// 作者信息接口
export interface Author {
  id: string;
  name: string;
  title?: string;
  avatar?: string;
  bio?: string;
}

// 相关课程接口
export interface RelatedCourse {
  id: string;
  title: string;
  description?: string;
  href: string;
  difficulty?: "Beginner" | "Intermediate" | "Advanced";
  duration?: string;
}

// 免费资源接口
export interface FreeResource {
  id: string;
  title: string;
  description?: string;
  type: "article" | "video" | "tutorial" | "documentation" | "tool";
  url: string;
}

// FAQ接口
export interface FAQ {
  id: string;
  question: string;
  answer: string;
}

// Quiz 问题接口
export interface ApiQuestion {
  questionText: string;
  questionTextHtml: string;
  questionOptions: ApiQuestionOption[];
  multipleAnswers?: boolean;
}

export interface ApiQuestionOption {
  text: string;
  id: string;
  correct: boolean;
  explanation: {
    mdText: string;
    mdHtml: string;
  };
  mdHtml: string;
}

// 课程课时接口
export interface CourseLesson {
  id: string;
  title: string;
  description: string;
  href: string;
  duration: number; // 使用秒为单位
  isCompleted?: boolean;
  isLocked?: boolean;
  type: "lesson" | "quiz" | "exercise" | "project" | "challenge";
  htmlContent?: string;
}

// 课程章节接口
export interface CourseSection {
  id: string;
  title: string;
  description?: string;
  lessons: CourseLesson[];
  // 动态计算的进度信息
  progress?: {
    completedLessons: number;
    totalLessons: number;
    percentage: number;
  };
}

// 课程内容接口
export interface CourseContent {
  period: "day" | "week" | "month";
  sections: CourseSection[];
}

// 目录项接口
export interface TOCItem {
  id: string;
  title: string;
  level: number;
  href?: string;
}

// 课程导航接口
export interface CourseNavigation {
  previousLesson?: {
    id: string;
    title: string;
    href: string;
  };
  nextLesson?: {
    id: string;
    title: string;
    href: string;
  };
  currentLesson: {
    id: string;
    title: string;
    isCompleted: boolean;
  };
}

// 完整课程数据接口
export interface CourseData {
  id: string;
  title: string;
  description: string;

  // 学习目标
  whatYouWillLearn: string[];

  // 技能标签
  skills: string[];

  // 作者信息
  authors: Author[];

  // 是否由MAANG工程师开发
  developedByMAANG: boolean;

  // 基本信息
  instructor: string;
  duration: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  language: string;
  category: string;
  tags: string[];

  // 课程内容
  content: CourseContent;

  // 当前进度
  currentProgress: {
    completedLessons: string[];
    totalLessons: number;
    totalQuizzes: number;
    totalChallenges: number;
    percentage: number;
  };

  // 可选字段
  relatedCourses?: RelatedCourse[];
  freeResources?: FreeResource[];
  faq?: FAQ[];
}

// 侧边栏属性接口
export interface CourseSidebarProps {
  progress: number;
  sections: CourseSection[];
  currentLessonId?: string;
  onLessonSelect?: (lessonId: string) => void;
}

// 用于动态计算的辅助函数类型
export interface CourseStats {
  totalLessons: number;
  totalQuizzes: number;
  totalChallenges: number;
  completedLessons: number;
  completedQuizzes: number;
  completedChallenges: number;
  overallProgress: number;
}

// 计算课程统计信息的辅助函数
export function calculateCourseStats(content: CourseContent): CourseStats {
  let totalLessons = 0;
  let totalQuizzes = 0;
  let totalChallenges = 0;
  let completedLessons = 0;
  let completedQuizzes = 0;
  let completedChallenges = 0;

  content.sections.forEach((section) => {
    section.lessons.forEach((lesson) => {
      switch (lesson.type) {
        case "lesson":
        case "exercise":
        case "project":
          totalLessons++;
          if (lesson.isCompleted) completedLessons++;
          break;
        case "quiz":
          totalQuizzes++;
          if (lesson.isCompleted) completedQuizzes++;
          break;
        case "challenge":
          totalChallenges++;
          if (lesson.isCompleted) completedChallenges++;
          break;
      }
    });
  });

  const totalItems = totalLessons + totalQuizzes + totalChallenges;
  const completedItems =
    completedLessons + completedQuizzes + completedChallenges;
  const overallProgress =
    totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

  return {
    totalLessons,
    totalQuizzes,
    totalChallenges,
    completedLessons,
    completedQuizzes,
    completedChallenges,
    overallProgress,
  };
}

// 计算章节进度的辅助函数
export function calculateSectionProgress(
  section: CourseSection
): CourseSection {
  const completedLessons = section.lessons.filter(
    (lesson) => lesson.isCompleted
  ).length;
  const totalLessons = section.lessons.length;
  const percentage =
    totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;

  return {
    ...section,
    progress: {
      completedLessons,
      totalLessons,
      percentage,
    },
  };
}

// 分类信息部分接口
export interface CategoryInfoSection {
  title: string;
  description: string;
  features?: string[];
}

// 定义分类信息接口
export interface CategoryInfo {
  id: string;
  title: string;
  description: string;
  icon?: string;
  faqs?: FAQ[];
  infoSections?: CategoryInfoSection[];
}
