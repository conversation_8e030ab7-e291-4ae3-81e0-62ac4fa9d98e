/**
 * 用户画像相关类型定义
 * 包括静态画像和技术能力画像
 */

import type {
  ApiResponse,
  PaginatedResponse,
  BaseRequestParams,
} from "@/types/openapi";

// ===== 静态画像相关类型 =====

export interface StaticProfile {
  id: string;
  user_id: string;

  // Basic Information
  age?: number;
  gender?: string;
  preferred_language?: string;

  // Educational Background
  education_experience?: string;
  major?: string;
  graduation_year?: number;

  // Professional Background
  current_role?: string;
  industry?: string;
  work_experience?: number;

  // Learning Preferences
  learning_style?: string;
  study_time_per_week?: number;
  preferred_study_time?: string;
  learning_pace?: string;

  created_at: string;
  updated_at: string;
}

export interface CreateStaticProfileRequest {
  // Basic Information
  age?: number;
  gender?: string;
  preferred_language?: string;

  // Educational Background
  education_experience?: string;
  major?: string;
  graduation_year?: number;

  // Professional Background
  current_role?: string;
  industry?: string;
  work_experience?: number;

  // Learning Preferences
  learning_style?: string;
  study_time_per_week?: number;
  preferred_study_time?: string;
  learning_pace?: string;
}

export interface UpdateStaticProfileRequest
  extends Partial<CreateStaticProfileRequest> {}

export interface StaticProfileListParams extends BaseRequestParams {
  industry?: string;
  learning_style?: "visual" | "auditory" | "kinesthetic" | "reading";
  min_age?: number;
  max_age?: number;
  experience_level?: "entry" | "junior" | "mid" | "senior" | "expert";
}

// ===== 技术能力画像相关类型 =====

export interface TechCompetencyProfile {
  id: string;
  user_id: string;
  competencies: TechCompetency[];
  overall_level: "beginner" | "intermediate" | "advanced";
  last_updated: string;
  created_at: string;
  updated_at: string;
  staleness_score?: number; // 陈旧度评分（0-100）
  confidence_score?: number; // 置信度评分（0-100）
}

export interface TechCompetency {
  id: string;
  category: string; // 技术分类，如 'programming', 'database', 'cloud', 'devops'
  skill_name: string; // 具体技能名称
  level: "beginner" | "intermediate" | "advanced";
  confidence: number; // 置信度（0-100）
  last_practiced?: string; // 最后实践时间
  evidence_count: number; // 证据数量（学习记录、项目等）
  learning_path_suggestions?: string[]; // 推荐的学习路径
}

export interface UpdateTechCompetencyRequest {
  behavior_data: LearningBehavior[];
  context?: string;
}

export interface LearningBehavior {
  type:
    | "lesson_completed"
    | "project_finished"
    | "quiz_passed"
    | "code_submitted"
    | "resource_accessed";
  skill_tags: string[]; // 相关技能标签
  difficulty_level: "beginner" | "intermediate" | "advanced";
  performance_score?: number; // 表现评分（0-100）
  time_spent: number; // 花费时间（分钟）
  timestamp: string;
  metadata?: Record<string, any>; // 额外的行为数据
}

export interface CompetencyNode {
  id: string;
  skill_name: string;
  category: string;
  level: "beginner" | "intermediate" | "advanced";
  prerequisites?: string[]; // 前置技能
  related_skills?: string[]; // 相关技能
  learning_resources?: CompetencyResource[];
}

export interface CompetencyResource {
  id: string;
  type: "course" | "tutorial" | "documentation" | "practice";
  title: string;
  url?: string;
  difficulty_level: "beginner" | "intermediate" | "advanced";
  estimated_duration: number; // 预计学习时长（分钟）
}

export interface CompetencyStats {
  total_skills: number;
  beginner_count: number;
  intermediate_count: number;
  advanced_count: number;
  top_categories: Array<{
    category: string;
    skill_count: number;
    average_level: number;
  }>;
  recent_improvements: Array<{
    skill_name: string;
    previous_level: string;
    current_level: string;
    improved_at: string;
  }>;
  suggested_focus_areas: string[];
}

// ===== 画像分析和推荐相关类型 =====

export interface ProfileAnalysis {
  user_id: string;
  analysis_type:
    | "learning_style"
    | "skill_gap"
    | "career_path"
    | "learning_efficiency";
  insights: ProfileInsight[];
  recommendations: ProfileRecommendation[];
  confidence_score: number; // 分析置信度（0-100）
  generated_at: string;
}

export interface ProfileInsight {
  type: "strength" | "weakness" | "opportunity" | "trend";
  title: string;
  description: string;
  evidence: string[];
  impact_score: number; // 影响评分（0-100）
}

export interface ProfileRecommendation {
  type: "learning_path" | "skill_focus" | "time_management" | "resource";
  title: string;
  description: string;
  priority: "high" | "medium" | "low";
  estimated_impact: string;
  action_items: string[];
  resources?: CompetencyResource[];
}

// ===== 画像匹配和对比相关类型 =====

export interface ProfileMatch {
  user_id: string;
  target_profile_id: string;
  similarity_score: number; // 相似度评分（0-100）
  matching_areas: string[];
  different_areas: string[];
  recommendations: string[];
}

export interface ProfileComparison {
  user_profile: TechCompetencyProfile;
  benchmark_profile: TechCompetencyProfile;
  skill_gaps: Array<{
    skill_name: string;
    current_level: string;
    target_level: string;
    gap_score: number;
  }>;
  strengths: string[];
  improvement_areas: string[];
}

// ===== API 响应类型 =====

export type StaticProfileResponse = ApiResponse<StaticProfile>;
export type StaticProfileListResponse = PaginatedResponse<StaticProfile>;

export type TechCompetencyProfileResponse = ApiResponse<TechCompetencyProfile>;
export type CompetencyNodeListResponse = ApiResponse<CompetencyNode[]>;
export type CompetencyStatsResponse = ApiResponse<CompetencyStats>;

export type ProfileAnalysisResponse = ApiResponse<ProfileAnalysis>;
export type ProfileMatchResponse = ApiResponse<ProfileMatch>;
export type ProfileComparisonResponse = ApiResponse<ProfileComparison>;
