export interface Course {
  id: string;
  title: string;
  description: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  duration: string;
  url: string;
  tags: string[];
  price?: string;
  rating?: string;
  instructor?: string;
  students?: string;
  lessons?: number;
  challenges?: number;
  quizzes?: number;
  playgrounds?: number;
  exercises?: number;
  illustrations?: number;
  category?: string;
  image?: string;
  language: string; // Added language field
}

export interface CatalogData {
  courses: Course[];
  categories: string[];
  totalCourses: number;
  language: string;
  languageInfo: {
    name: string;
    icon: string;
    description: string;
    color: string;
  };
  stats: {
    courses: number;
    paths: number;
    cloudLabs: number;
    assessments: number;
    projects: number;
  };
}

// Keep backward compatibility
export type PythonCourse = Course;
export type PythonCatalogData = CatalogData;

export interface CourseFilter {
  searchTerm: string;
  category: string;
  difficulty: string;
  duration: string;
  price: string;
}

export interface CourseCategoryTab {
  id: string;
  label: string;
  count: number;
  active: boolean;
}
