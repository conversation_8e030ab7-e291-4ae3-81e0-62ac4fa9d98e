import axios, { AxiosError } from "axios";
import { showToast } from "./toast-utils";
import type {
  ApiResponse,
  BaseResponse,
  Paginated,
  PaginatedResponse,
} from "@/types/openapi";
import { HTTP_STATUS } from "@/types/openapi";
import i18n from "@/i18n/i18next-config";

/**
 * API错误响应接口 - 与后端统一响应格式保持一致
 */
export interface ApiErrorResponse extends ApiResponse<any> {
  success: false;
}

/**
 * 错误处理选项
 */
export interface ErrorHandlerOptions {
  /** 是否显示toast提示，默认为true */
  showToast?: boolean;
  /** 自定义错误消息，如果提供则优先使用 */
  customMessage?: string;
  /** 默认错误消息，当后端没有返回message时使用 */
  defaultMessage?: string;
  /** 是否抛出错误，默认为true */
  throwError?: boolean;
}

/**
 * 从错误对象中提取错误消息 - 支持后端统一响应格式
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 * @returns 错误消息
 */
export function extractErrorMessage(
  error: any,
  defaultMessage: string = "操作失败"
): string {
  // 如果是后端统一响应格式的错误
  if (
    error &&
    typeof error === "object" &&
    "success" in error &&
    "message" in error
  ) {
    return error.message || defaultMessage;
  }

  // 如果是axios错误
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<ApiResponse<any>>;

    // 优先使用后端返回的统一格式message
    if (axiosError.response?.data?.message) {
      return axiosError.response.data.message;
    }

    // 根据HTTP状态码返回相应消息
    if (axiosError.response?.status) {
      switch (axiosError.response.status) {
        case HTTP_STATUS.BAD_REQUEST:
          return i18n.t("errors.api.badRequest");
        case HTTP_STATUS.UNAUTHORIZED:
          return i18n.t("errors.api.unauthorized");
        case HTTP_STATUS.FORBIDDEN:
          return i18n.t("errors.api.forbidden");
        case HTTP_STATUS.NOT_FOUND:
          return i18n.t("errors.api.notFound");
        case HTTP_STATUS.CONFLICT:
          return i18n.t("errors.api.conflict");
        case 422:
          return i18n.t("errors.api.validationFailed");
        case 429:
          return i18n.t("errors.api.tooManyRequests");
        case HTTP_STATUS.INTERNAL_SERVER_ERROR:
          return i18n.t("errors.api.serverError");
        case 502:
          return i18n.t("errors.api.badGateway");
        case HTTP_STATUS.SERVICE_UNAVAILABLE:
          return i18n.t("errors.api.serviceUnavailable");
        case 504:
          return i18n.t("errors.api.timeout");
        default:
          return i18n.t("errors.api.serverErrorWithCode", {
            code: axiosError.response.status,
          });
      }
    }

    // 网络错误
    if (axiosError.code === "ECONNABORTED") {
      return i18n.t("errors.api.requestTimeout");
    }

    if (axiosError.code === "ERR_NETWORK") {
      return i18n.t("errors.api.networkError");
    }

    return `网络错误：${axiosError.message}`;
  }

  // 如果是普通Error对象
  if (error instanceof Error) {
    return error.message || defaultMessage;
  }

  // 如果是字符串
  if (typeof error === "string") {
    return error;
  }

  // 其他情况返回默认消息
  return defaultMessage;
}

/**
 * 统一的API错误处理函数
 * @param error 错误对象
 * @param options 处理选项
 * @returns 错误消息
 */
export function handleApiError(
  error: any,
  options: ErrorHandlerOptions = {}
): string {
  const {
    showToast: shouldShowToast = true,
    customMessage,
    defaultMessage = "操作失败",
    throwError = true,
  } = options;

  // 确定最终的错误消息
  const errorMessage =
    customMessage ||
    extractErrorMessage(
      error,
      defaultMessage || i18n.t("errors.common.operationFailed")
    );

  // 显示toast提示
  if (shouldShowToast) {
    showToast.error(errorMessage);
  }

  // 记录错误日志
  console.error("API Error:", {
    message: errorMessage,
    error: error instanceof Error ? error.stack : error,
    timestamp: new Date().toISOString(),
  });

  // 是否抛出错误
  if (throwError) {
    throw new Error(errorMessage);
  }

  return errorMessage;
}

/**
 * 处理API响应，检查是否成功 - 支持后端统一响应格式
 * @param response API响应
 * @param successMessage 成功消息
 * @param options 错误处理选项
 * @returns 响应数据
 */
export function handleApiResponse<T = any>(
  response: ApiResponse<T>,
  successMessage?: string,
  options: ErrorHandlerOptions = {}
): T {
  if (
    response.success &&
    (response.code === HTTP_STATUS.OK || response.code === HTTP_STATUS.CREATED)
  ) {
    if (successMessage) {
      showToast.success(successMessage);
    }
    return response.data as T;
  } else {
    // 使用后端返回的错误消息
    handleApiError(response, {
      ...options,
      defaultMessage: response.message || "操作失败",
    });
    throw new Error(response.message || "操作失败");
  }
}

/**
 * 处理分页API响应，检查是否成功 - 支持后端统一响应格式
 * @param response 分页API响应
 * @param successMessage 成功消息
 * @param options 错误处理选项
 * @returns 完整的分页响应对象
 */
export function handlePaginatedApiResponse<T = any>(
  response: PaginatedResponse<T>,
  successMessage?: string,
  options: ErrorHandlerOptions = {}
): PaginatedResponse<T> {
  if (response.success) {
    if (successMessage) {
      showToast.success(successMessage);
    }
    return response || { data: [], total: 0, page: 1, page_size: 10 };
  } else {
    // 使用后端返回的错误消息
    handleApiError(response, {
      ...options,
      defaultMessage: response.message || "获取数据失败",
    });
    throw new Error(response.message || "获取数据失败");
  }
}

/**
 * 检查API响应是否成功的工具函数
 * @param response API响应
 * @returns 是否成功
 */
export function isApiSuccess<T>(
  response: ApiResponse<T>
): response is ApiResponse<T> & { success: true } {
  return (
    response.success === true &&
    (response.code === HTTP_STATUS.OK || response.code === HTTP_STATUS.CREATED)
  );
}

/**
 * 检查API响应是否失败的工具函数
 * @param response API响应
 * @returns 是否失败
 */
export function isApiError<T>(
  response: ApiResponse<T>
): response is ApiErrorResponse {
  return response.success === false;
}

/**
 * 创建带有统一错误处理的异步函数包装器
 * @param asyncFn 异步函数
 * @param options 错误处理选项
 * @returns 包装后的函数
 */
export function withErrorHandler<T extends (...args: any[]) => Promise<any>>(
  asyncFn: T,
  options: ErrorHandlerOptions = {}
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await asyncFn(...args);
    } catch (error) {
      handleApiError(error, options);
    }
  }) as T;
}

/**
 * 简化的错误处理函数，专门用于业务组件的catch块
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 * @param shouldShowToast 是否显示toast，默认为true
 * @returns 错误消息
 */
export function handleUIError(
  error: any,
  defaultMessage: string = "操作失败",
  shouldShowToast: boolean = true
): string {
  const errorMessage = extractErrorMessage(error, defaultMessage);

  if (shouldShowToast) {
    showToast.error(errorMessage);
  }

  console.error("UI Error:", {
    message: errorMessage,
    originalError: error,
    timestamp: new Date().toISOString(),
  });

  return errorMessage;
}
