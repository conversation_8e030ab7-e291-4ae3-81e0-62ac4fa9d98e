/**
 * API 请求缓存管理器
 * 用于减少重复的 API 请求，特别是在 React 18 StrictMode 下
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  promise?: Promise<T>;
}

interface CacheOptions {
  ttl?: number; // 缓存时间（毫秒），默认 5 分钟
  maxSize?: number; // 最大缓存条目数，默认 100
}

class ApiCache {
  private cache = new Map<string, CacheEntry<any>>();
  private pendingRequests = new Map<string, Promise<any>>();
  private defaultTTL = 5 * 60 * 1000; // 5 分钟
  private maxSize = 100;

  constructor(options: CacheOptions = {}) {
    this.defaultTTL = options.ttl || this.defaultTTL;
    this.maxSize = options.maxSize || this.maxSize;
  }

  /**
   * 生成缓存键
   */
  private generateKey(url: string, params?: Record<string, any>): string {
    const paramStr = params ? JSON.stringify(params) : '';
    return `${url}:${paramStr}`;
  }

  /**
   * 检查缓存是否过期
   */
  private isExpired(entry: CacheEntry<any>, ttl: number): boolean {
    return Date.now() - entry.timestamp > ttl;
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry, this.defaultTTL)) {
        this.cache.delete(key);
      }
    }

    // 如果缓存仍然太大，删除最旧的条目
    if (this.cache.size > this.maxSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      const toDelete = entries.slice(0, entries.length - this.maxSize);
      toDelete.forEach(([key]) => this.cache.delete(key));
    }
  }

  /**
   * 获取缓存数据或执行请求
   */
  async get<T>(
    url: string,
    requestFn: () => Promise<T>,
    options: { params?: Record<string, any>; ttl?: number } = {}
  ): Promise<T> {
    const key = this.generateKey(url, options.params);
    const ttl = options.ttl || this.defaultTTL;

    // 检查是否有有效缓存
    const cached = this.cache.get(key);
    if (cached && !this.isExpired(cached, ttl)) {
      return cached.data;
    }

    // 检查是否有正在进行的请求
    const pendingRequest = this.pendingRequests.get(key);
    if (pendingRequest) {
      return pendingRequest;
    }

    // 执行新请求
    const requestPromise = requestFn()
      .then((data) => {
        // 缓存结果
        this.cache.set(key, {
          data,
          timestamp: Date.now(),
        });
        
        // 清理过期缓存
        this.cleanup();
        
        return data;
      })
      .finally(() => {
        // 清理正在进行的请求
        this.pendingRequests.delete(key);
      });

    // 记录正在进行的请求
    this.pendingRequests.set(key, requestPromise);

    return requestPromise;
  }

  /**
   * 手动设置缓存
   */
  set<T>(url: string, data: T, params?: Record<string, any>): void {
    const key = this.generateKey(url, params);
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * 删除特定缓存
   */
  delete(url: string, params?: Record<string, any>): void {
    const key = this.generateKey(url, params);
    this.cache.delete(key);
    this.pendingRequests.delete(key);
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
    this.pendingRequests.clear();
  }

  /**
   * 删除匹配模式的缓存
   */
  deletePattern(pattern: string): void {
    const regex = new RegExp(pattern);
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        this.pendingRequests.delete(key);
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    cacheSize: number;
    pendingRequests: number;
    keys: string[];
  } {
    return {
      cacheSize: this.cache.size,
      pendingRequests: this.pendingRequests.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// 创建全局缓存实例
export const apiCache = new ApiCache({
  ttl: 5 * 60 * 1000, // 5 分钟
  maxSize: 200, // 增加缓存大小以支持更多课程数据
});

// 专门用于课程数据的缓存实例（更长的缓存时间）
export const courseCache = new ApiCache({
  ttl: 15 * 60 * 1000, // 15 分钟
  maxSize: 100,
});

// 专门用于用户进度数据的缓存实例（较短的缓存时间）
export const progressCache = new ApiCache({
  ttl: 2 * 60 * 1000, // 2 分钟
  maxSize: 50,
});

/**
 * 缓存装饰器函数，用于包装 API 服务方法
 */
export function withCache<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  cacheInstance: ApiCache = apiCache,
  options: {
    keyGenerator?: (...args: Parameters<T>) => string;
    ttl?: number;
  } = {}
): T {
  return (async (...args: Parameters<T>) => {
    const key = options.keyGenerator 
      ? options.keyGenerator(...args)
      : `${fn.name}:${JSON.stringify(args)}`;
    
    return cacheInstance.get(
      key,
      () => fn(...args),
      { ttl: options.ttl }
    );
  }) as T;
}

/**
 * 清理特定类型的缓存
 */
export function clearCacheByType(type: 'lessons' | 'nodes' | 'progress' | 'all'): void {
  switch (type) {
    case 'lessons':
      apiCache.deletePattern('.*lesson.*');
      courseCache.deletePattern('.*lesson.*');
      break;
    case 'nodes':
      apiCache.deletePattern('.*node.*');
      courseCache.deletePattern('.*node.*');
      break;
    case 'progress':
      progressCache.clear();
      break;
    case 'all':
      apiCache.clear();
      courseCache.clear();
      progressCache.clear();
      break;
  }
}
