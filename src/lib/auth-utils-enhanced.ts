import type { User } from "@/types/openapi";

/**
 * 增强的授权工具类
 * 提供更多授权相关的便利方法
 */

// 角色权限映射
export const ROLE_PERMISSIONS = {
  admin: ["read", "write", "delete", "admin", "manage_users"],
  user: ["read", "write"],
  company: ["read", "write", "manage_company"],
} as const;

// 路由权限配置
export const PROTECTED_ROUTES = {
  "/admin": ["admin"],
  "/dashboard": ["admin", "company"],
  "/learn/home": ["user", "admin", "company"],
  "/profile": ["user", "admin", "company"],
} as const;

export type Role = keyof typeof ROLE_PERMISSIONS;
export type Permission = (typeof ROLE_PERMISSIONS)[Role][number];
export type ProtectedRoute = keyof typeof PROTECTED_ROUTES;

/**
 * 检查用户是否有特定权限
 */
export function hasPermission(
  userRole: string,
  permission: Permission
): boolean {
  const role = userRole as Role;
  const rolePermissions = ROLE_PERMISSIONS[role];
  if (!rolePermissions) return false;
  return (rolePermissions as readonly Permission[]).includes(permission);
}

/**
 * 检查用户是否可以访问特定路由
 */
export function canAccessRoute(userRole: string, route: string): boolean {
  const protectedRoute = route as ProtectedRoute;
  const allowedRoles = PROTECTED_ROUTES[protectedRoute];

  if (!allowedRoles) {
    // 如果路由不在保护列表中，默认允许访问
    return true;
  }

  return (allowedRoles as readonly Role[]).includes(userRole as Role);
}

/**
 * 获取用户可访问的路由列表
 */
export function getAccessibleRoutes(userRole: string): string[] {
  const role = userRole as Role;
  return Object.entries(PROTECTED_ROUTES)
    .filter(([_, allowedRoles]) =>
      (allowedRoles as readonly Role[]).includes(role)
    )
    .map(([route]) => route);
}

/**
 * 根据用户角色获取默认首页
 */
export function getDefaultHomePage(userRole: string): string {
  switch (userRole) {
    default:
      return "/learn/home";
  }
}

/**
 * 检查用户信息是否完整（基于 openapi.User）
 */
export function isUserInfoComplete(userInfo: User | null): boolean {
  if (!userInfo) return false;
  // 接受 id 或 email/username 任一存在即可视为基本完整
  return Boolean(userInfo.id || userInfo.email || userInfo.username);
}

/**
 * 获取角色显示名称
 */
export function getRoleDisplayName(
  role: string,
  locale: string = "zh"
): string {
  const roleNames = {
    zh: {
      admin: "管理员",
      user: "用户",
      company: "企业用户",
    },
    en: {
      admin: "Admin",
      user: "User",
      company: "Company",
    },
  };

  return roleNames[locale as keyof typeof roleNames]?.[role as Role] || role;
}

/**
 * 获取角色权限描述
 */
export function getRolePermissions(role: string): readonly Permission[] {
  return ROLE_PERMISSIONS[role as Role] || [];
}

/**
 * 检查是否为管理员角色
 */
export function isAdmin(userRole: string): boolean {
  return userRole === "admin";
}

/**
 * 检查是否为企业用户
 */
export function isCompany(userRole: string): boolean {
  return userRole === "company";
}

/**
 * 生成基于角色的菜单项
 */
export interface MenuItem {
  label: string;
  href: string;
  icon?: string;
  permission?: Permission;
  roles?: Role[];
}

export function getMenuItemsForRole(
  userRole: string,
  menuItems: MenuItem[]
): MenuItem[] {
  const role = userRole as Role;

  return menuItems.filter((item) => {
    // 如果指定了角色限制，检查用户角色
    if (item.roles && !item.roles.includes(role)) {
      return false;
    }

    // 如果指定了权限要求，检查用户权限
    if (item.permission && !hasPermission(userRole, item.permission)) {
      return false;
    }

    return true;
  });
}

/**
 * 授权装饰器工厂配置（用于页面组件）
 */
export interface AuthDecoratorOptions {
  requiredRole?: Role[];
  requiredPermission?: Permission;
  redirectTo?: string;
  fallback?: any; // React.ComponentType
}

/**
 * 路由守卫配置
 */
export interface RouteGuardConfig {
  path: string;
  requiredRoles?: Role[];
  requiredPermissions?: Permission[];
  redirectTo?: string;
}

export const ROUTE_GUARDS: RouteGuardConfig[] = [
  {
    path: "/admin/*",
    requiredRoles: ["admin"],
    redirectTo: "/unauthorized",
  },
  {
    path: "/dashboard",
    requiredRoles: ["admin", "company"],
    redirectTo: "/learn/home",
  },
  {
    path: "/learn/*",
    requiredRoles: ["user", "admin", "company"],
    redirectTo: "/",
  },
];

/**
 * 检查路由是否需要守卫
 */
export function shouldGuardRoute(
  path: string,
  userRole: string
): {
  shouldGuard: boolean;
  redirectTo?: string;
} {
  const guard = ROUTE_GUARDS.find((guard) => {
    if (guard.path.endsWith("/*")) {
      const basePath = guard.path.slice(0, -2);
      return path.startsWith(basePath);
    }
    return path === guard.path;
  });

  if (!guard) {
    return { shouldGuard: false };
  }

  const hasRequiredRole =
    guard.requiredRoles?.includes(userRole as Role) ?? true;
  const hasRequiredPermissions =
    guard.requiredPermissions?.every((permission) =>
      hasPermission(userRole, permission)
    ) ?? true;

  if (!hasRequiredRole || !hasRequiredPermissions) {
    return {
      shouldGuard: true,
      redirectTo: guard.redirectTo || "/",
    };
  }

  return { shouldGuard: false };
}
