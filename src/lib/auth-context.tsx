"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { showToast } from "@/lib/toast-utils";
import { userService } from "@/service/user";
import { AUTH_ERROR_EVENT } from "@/service/api";
import { extractErrorMessage } from "@/lib/api-error-handler";
import type { User } from "@/types/openapi";
import { getDefaultHomePage } from "@/lib/auth-utils-enhanced";

interface AuthContextType {
  loading: boolean; // 仅用于初始化加载
  emailLogin: (email: string, password: string) => Promise<boolean>;
  register: (
    fullName: string,
    email: string,
    password: string
  ) => Promise<boolean>;
  googleAuth: () => Promise<boolean>;
  logout: () => void;
  isAuthenticated: boolean;
  role: string | null;
  userInfo: User | null;
  // 操作特定的加载状态
  isEmailLoginLoading: boolean;
  isRegisterLoading: boolean;
  isGoogleAuthLoading: boolean;
  isLogout: boolean;
  setIsLogout: (isLogout: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [loading, setLoading] = useState(true); // 仅用于初始化
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [role, setRole] = useState<string | null>(null);
  const [userInfo, setUserInfo] = useState<User | null>(null);
  const [isLogout, setIsLogout] = useState(false);

  // 操作特定的加载状态
  const [isEmailLoginLoading, setIsEmailLoginLoading] = useState(false);
  const [isRegisterLoading, setIsRegisterLoading] = useState(false);
  const [isGoogleAuthLoading, setIsGoogleAuthLoading] = useState(false);

  const router = useRouter();

  // 监听认证错误事件
  useEffect(() => {
    const handleAuthError = () => {
      router.push("/");
    };

    window.addEventListener(AUTH_ERROR_EVENT, handleAuthError);
    return () => {
      window.removeEventListener(AUTH_ERROR_EVENT, handleAuthError);
    };
  }, [router]);

  // 初始化检查：调用 userService.me()，已在 service 层做去重，避免双调用
  useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        const response = await userService.me();
        if (!mounted) return;
        if (response && response.success && response.data) {
          setIsAuthenticated(true);
          setRole(response.data.role || "user");
          setUserInfo(response.data);
        } else {
          setIsAuthenticated(false);
          setRole(null);
          setUserInfo(null);
        }
      } catch (error) {
        if (!mounted) return;
        setIsAuthenticated(false);
        setRole(null);
        setUserInfo(null);
      } finally {
        if (mounted) setLoading(false);
      }
    })();
    return () => {
      mounted = false;
    };
  }, []);

  const emailLogin = async (
    email: string,
    password: string
  ): Promise<boolean> => {
    try {
      setIsEmailLoginLoading(true);

      // 验证输入
      if (!email.trim() || !password.trim()) {
        const errorMessage = extractErrorMessage(null, "请输入邮箱和密码");
        showToast.error(errorMessage);
        return false;
      }

      // 调用登录接口（邮箱/密码）
      const response = await userService.login({ email, password });

      if (!response.success) {
        const errorMessage = extractErrorMessage(
          null,
          response.message || "登录失败"
        );
        showToast.error(errorMessage);
        return false;
      }

      // 设置认证状态
      const user = response.data?.user;
      setIsAuthenticated(true);
      setRole(user?.role || "user");
      setUserInfo(user || null);

      showToast.success("登录成功");

      // 登录成功后自动跳转到对应的首页
      const defaultHomePage = getDefaultHomePage(user?.role || "user");
      router.push(defaultHomePage);

      return true;
    } catch (error) {
      console.error("AuthProvider: Email login error:", error);
      const errorMessage = extractErrorMessage(error, "登录失败，请重试");
      showToast.error(errorMessage);
      return false;
    } finally {
      setIsEmailLoginLoading(false);
    }
  };

  const register = async (
    fullName: string,
    email: string,
    password: string
  ): Promise<boolean> => {
    try {
      setIsRegisterLoading(true);

      // 验证输入
      if (!fullName.trim() || !email.trim() || !password.trim()) {
        const errorMessage = extractErrorMessage(null, "请填写完整信息");
        showToast.error(errorMessage);
        return false;
      }

      // 构建 RegisterRequest（根据 swagger）
      const [first_name, ...rest] = fullName.trim().split(/\s+/);
      const last_name = rest.join(" ");
      const username = email.split("@")[0] || first_name || "user";

      // 调用注册接口
      const response = await userService.register({
        username,
        email,
        password,
        first_name,
        last_name,
      });

      if (!response.success) {
        const errorMessage = extractErrorMessage(
          null,
          response.message || "注册失败"
        );
        showToast.error(errorMessage);
        return false;
      }

      // 如果自动登录失败，提示用户前往登录
      showToast.success("注册成功，请登录");
      return true;
    } catch (error) {
      console.error("AuthProvider: Register error:", error);
      const errorMessage = extractErrorMessage(error, "注册失败，请重试");
      showToast.error(errorMessage);
      return false;
    } finally {
      setIsRegisterLoading(false);
    }
  };

  const googleAuth = async (): Promise<boolean> => {
    try {
      setIsGoogleAuthLoading(true);
      showToast.error("暂未开放 Google 登录");
      return false;
    } catch (error) {
      console.error("AuthProvider: Google auth error:", error);
      const errorMessage = extractErrorMessage(error, "Google登录失败");
      showToast.error(errorMessage);
      return false;
    } finally {
      setIsGoogleAuthLoading(false);
    }
  };

  const logout = async () => {
    console.log("AuthProvider: Logout");

    try {
      // 调用后端登出接口，清除 httpOnly cookie
      await userService.logout();
      setIsLogout(true);
    } catch (error) {
      console.error("Logout error:", error);
      // 即使后端登出失败，也要清除前端状态
    }

    // 先跳转到首页，避免在受保护页面清除状态时触发路由守卫
    router.push("/");

    // 延迟清除前端认证状态，确保路由跳转完成
    setTimeout(() => {
      setIsAuthenticated(false);
      setRole(null);
      setUserInfo(null);
    }, 100);
  };

  return (
    <AuthContext.Provider
      value={{
        loading,
        emailLogin,
        register,
        googleAuth,
        logout,
        isAuthenticated,
        role,
        userInfo,
        isEmailLoginLoading,
        isRegisterLoading,
        isGoogleAuthLoading,
        isLogout,
        setIsLogout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
