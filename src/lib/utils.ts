import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import mammoth from "mammoth";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// 时间转换工具函数

/**
 * 将秒转换为人类可读的时间格式
 * @param seconds 秒数
 * @returns 格式化的时间字符串 (例如: "2h 30m", "45m", "1h 15m")
 */
export function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours === 0) {
    return `${minutes}m`;
  }

  if (remainingMinutes === 0) {
    return `${hours}h`;
  }

  return `${hours}h ${remainingMinutes}m`;
}

/**
 * 将秒转换为详细的时间格式
 * @param seconds 秒数
 * @returns 详细的时间字符串 (例如: "2h30m", "45m", "1h")
 */
export function formatDetailedDuration(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours === 0) {
    return `${minutes}m`;
  }

  if (remainingMinutes === 0) {
    return `${hours}h`;
  }

  return `${hours}h ${remainingMinutes}m`;
}

// 路由生成工具函数

/**
 * 根据课程类型生成路由前缀
 * @param type 课程类型
 * @returns 路由前缀
 */
export function getRoutePrefix(type: string): string {
  switch (type) {
    case "course":
      return "courses";
    case "cloudlab":
      return "cloudlabs";
    case "project":
      return "projects";
    case "path":
      return "paths";
    case "assessment":
      return "assessments";
    case "mockinterview":
      return "mockinterviews";
    default:
      return "courses";
  }
}

/**
 * 根据课程类型和ID生成完整的URL
 * @param type 课程类型
 * @param id 课程ID
 * @returns 完整的URL路径
 */
export function generateCourseUrl(type: string, id: string): string {
  const prefix = getRoutePrefix(type);
  return `/${prefix}/${id}`;
}

/**
 * 根据课程类型、课程ID和课程ID生成课程URL
 * @param type 课程类型
 * @param courseId 课程ID
 * @param lessonId 课程ID
 * @returns 课程URL路径
 */
export function generateLessonUrl(
  type: string,
  courseId: string,
  lessonId: string
): string {
  const prefix = getRoutePrefix(type);
  return `/${prefix}/${courseId}/${lessonId}`;
}

export const getPolicyFileUrl = (fileId: string): string => {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL;
  return `${baseUrl}/api/policy/download/${fileId}`;
};

/**
 * 格式化货币金额
 * @param amount 金额
 * @param currency 货币类型
 * @returns 格式化后的货币字符串
 */
export function formatCurrency(
  amount: number,
  currency: string = "CNY"
): string {
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

/**
 * 解析 DOCX 文件并提取纯文本
 * @param fileOrUrl {File | string} - 可以是本地 File 对象或远程 URL
 * @returns {Promise<string>} - 返回提取的纯文本
 */
export async function convertDocxToText(
  fileOrUrl: File | string
): Promise<string> {
  let arrayBuffer: ArrayBuffer;

  try {
    if (fileOrUrl instanceof File) {
      // 📂 处理本地上传的 DOCX 文件
      arrayBuffer = await fileOrUrl.arrayBuffer();
    } else if (typeof fileOrUrl === "string") {
      // 🌍 处理远程 DOCX 文件（URL）
      const response = await fetch(fileOrUrl);
      if (!response.ok) throw new Error("无法加载 DOCX 文件");
      arrayBuffer = await response.arrayBuffer();
    } else {
      throw new Error("无效的输入类型");
    }

    // 📜 使用 mammoth 解析 DOCX 并提取纯文本
    const { value: text } = await mammoth.extractRawText({ arrayBuffer });
    return text.trim(); // 去掉首尾空格
  } catch (error) {
    console.error("DOCX 转换失败:", error);
    return "";
  }
}

/**
 * 将 File 对象转换为 Base64 编码的字符串（不含 data: URL 前缀）
 * @param file - 要转换的 File 对象
 * @returns 返回一个 Promise，解析为 Base64 字符串
 */
export const fileToBase64 = (file: File): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const result = reader.result as string;
      const base64Data = result.split(",")[1];
      resolve(base64Data);
    };
    reader.onerror = (error) => reject(error);
  });
