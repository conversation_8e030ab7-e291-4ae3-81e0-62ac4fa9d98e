/**
 * 兼容性复制到剪贴板工具函数
 * 支持现代浏览器的 navigator.clipboard API 和传统的 document.execCommand 方法
 */

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns Promise<boolean> 复制是否成功
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    // 方法1: 尝试使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    }
    
    // 方法2: 使用传统的 document.execCommand 方法（兼容性更好）
    return fallbackCopyTextToClipboard(text);
  } catch (error) {
    console.warn('Clipboard API failed, trying fallback method:', error);
    // 如果现代方法失败，尝试传统方法
    return fallbackCopyTextToClipboard(text);
  }
}

/**
 * 传统的复制方法（兼容性更好）
 * @param text 要复制的文本
 * @returns boolean 复制是否成功
 */
function fallbackCopyTextToClipboard(text: string): boolean {
  try {
    // 创建一个临时的 textarea 元素
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 设置样式使其不可见
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';
    textArea.style.pointerEvents = 'none';
    
    // 添加到 DOM
    document.body.appendChild(textArea);
    
    // 选择文本
    textArea.focus();
    textArea.select();
    
    // 尝试复制
    const successful = document.execCommand('copy');
    
    // 清理：移除临时元素
    document.body.removeChild(textArea);
    
    return successful;
  } catch (error) {
    console.error('Fallback copy method failed:', error);
    return false;
  }
}

/**
 * 检查是否支持剪贴板功能
 * @returns boolean 是否支持
 */
export function isClipboardSupported(): boolean {
  return !!(
    (navigator.clipboard && window.isSecureContext) ||
    document.queryCommandSupported?.('copy')
  );
}
