/*
 * @Description: 用户相关工具函数
 * @Author: Devin
 * @Date: 2025-08-11
 */
import type { User } from "@/types/openapi";

/**
 * 获取用户显示名称
 * 优先级：first_name + last_name > username > email前缀 > "User"
 */
export function getUserDisplayName(userInfo: User | null | undefined): string {
  if (!userInfo) return "User";
  
  // 优先使用 first_name 和 last_name
  if (userInfo.first_name || userInfo.last_name) {
    return `${userInfo.first_name || ""} ${userInfo.last_name || ""}`.trim();
  }
  
  // 其次使用 username
  if (userInfo.username) {
    return userInfo.username;
  }
  
  // 再次使用 email 前缀
  if (userInfo.email) {
    return userInfo.email.split("@")[0];
  }
  
  return "User";
}

/**
 * 获取用户头像字母
 * 优先级：first_name首字母 > username首字母 > email首字母 > "U"
 */
export function getUserInitial(userInfo: User | null | undefined): string {
  if (!userInfo) return "U";
  
  // 优先使用 first_name 首字母
  if (userInfo.first_name) {
    return userInfo.first_name.charAt(0).toUpperCase();
  }
  
  // 其次使用 username 首字母
  if (userInfo.username) {
    return userInfo.username.charAt(0).toUpperCase();
  }
  
  // 再次使用 email 首字母
  if (userInfo.email) {
    return userInfo.email.charAt(0).toUpperCase();
  }
  
  return "U";
}

/**
 * 获取用户角色显示名称
 */
export function getUserRoleDisplayName(
  role?: string,
  locale: string = "zh"
): string {
  if (!role) return locale === "zh" ? "用户" : "User";
  
  const roleNames = {
    zh: {
      admin: "管理员",
      user: "用户",
      moderator: "版主",
      company: "企业用户",
    },
    en: {
      admin: "Admin",
      user: "User", 
      moderator: "Moderator",
      company: "Company",
    },
  };

  return roleNames[locale as keyof typeof roleNames]?.[role as keyof typeof roleNames.zh] || role;
}

/**
 * 检查用户信息是否完整
 */
export function isUserInfoComplete(userInfo: User | null | undefined): boolean {
  if (!userInfo) return false;
  // 基本信息完整性检查：至少需要 id 或 email/username
  return Boolean(userInfo.id || userInfo.email || userInfo.username);
}

/**
 * 获取用户完整信息字符串（用于调试或日志）
 */
export function getUserInfoString(userInfo: User | null | undefined): string {
  if (!userInfo) return "Anonymous User";
  
  const name = getUserDisplayName(userInfo);
  const email = userInfo.email ? ` (${userInfo.email})` : "";
  const role = userInfo.role ? ` [${getUserRoleDisplayName(userInfo.role, "en")}]` : "";
  
  return `${name}${email}${role}`;
}
