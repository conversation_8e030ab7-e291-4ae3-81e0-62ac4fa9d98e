# Copilot Instructions for PointerWeb

These rules brief AI coding agents on how to work productively in this repo. Keep changes aligned with the existing patterns and files referenced below.

## Architecture and routing
- Framework: Next.js 14 (App Router) with TypeScript; source lives under `src/` and path alias `@/*` maps to `src/*` (see `tsconfig.json`).
- Global providers are composed in `src/app/layout.tsx`: `I18nProvider` → `AuthProvider` → `AuthEnhancedProvider` → `MainLayout` + global `Toaster`.
- Locale is detected on the server via `src/i18n/server.ts#getLocaleOnServer` and applied as `<html lang={...}>` in `layout.tsx`.
- Client components that use hooks/navigation must start with `"use client"` (see `src/app/page.tsx`, `src/app/learn/home/<USER>

## Auth, guards, and navigation
- Auth state is provided by `src/lib/auth-context.tsx` (email/password login, register, logout) and enhanced by `src/components/auth/auth-provider-enhanced.tsx` for route guarding and role/permission checks.
- Protected routes are configured in `src/config/protected-routes.ts` via `PROTECTED_PATHS` (e.g., `/learn/home`, `/profile`). Add new protected paths there.
- Use the enhanced auth helpers rather than ad‑hoc logic:
  - `useAuthEnhanced().navigateWithAuth(path, fallback)` for guarded navigation.
  - `withPermission(Component, permission)` / `withRole(Component, roles)` HOCs for feature gating.
  - Default home redirection via `getDefaultHomePage` in `@/lib/auth-utils-enhanced`.

## API calls, proxy, and errors
- HTTP client is `axios` instance in `src/service/api.ts` with `baseURL = /api/v1` and `withCredentials: true` (httpOnly cookies). Do not manually attach tokens.
- Next rewrites proxy `/api/v1/:path*` to backend (see `next.config.js`), so use relative paths with the shared `api` instance.
- Global 401 handling: the response interceptor clears `useUserStore` and dispatches `window` event `auth:error`. Do not duplicate 401 handling in callers.
- Error handling conventions live in `src/lib/api-error-handler.ts`:
  - Use `handleApiResponse(resp, successMsg?)` to assert success and optionally toast.
  - Use `handleApiError(err, { showToast, customMessage })` or `withErrorHandler(asyncFn)` for unified UX.
  - Error messages are localized via i18n keys (`errors.api.*`). Add keys when introducing new status handling.

## Services and data shapes
- Domain services live in `src/service/*`. Prefer adding methods there and importing into features.
- Course data currently comes from Mock API adapters in `src/service/mockapi.service.ts` and `src/service/course.service.ts` (with in‑memory caching and helpers like `getCoursesByTab`, `searchCourses`, `calculateCourseStats`).
- Prefer OpenAPI-aligned types from `src/types/openapi.ts` for new endpoints and responses. Keep legacy `code` field compatibility if needed via `ApiResponse<T>`.

## State and UI patterns
- User/session state uses Zustand (`src/store/useUserStore.ts`, persisted under key `user-info`). Clear via `clearLoginInfo()`; do not manipulate cookies directly except as implemented there.
- UI stack: TailwindCSS + Radix UI + `sonner` toasts. Use `@/components/ui/*` and `@/components/sections/*` where possible. Global styles in `src/app/globals.css`.

## i18n usage
- Client i18n config: `src/i18n/i18next-config.ts` aggregates namespaces per locale with dynamic `require`.
- Server i18n utilities: `src/i18n/server.ts#useTranslation(lng, ns)` for RSC usage.
- Add/verify translation keys with scripts: `npm run check-i18n` and `npm run auto-gen-i18n` (see `src/i18n/auto-gen-i18n.js`).

## Developer workflows
- Run dev/build: `npm run dev`, `npm run build`, `npm start`. Docker supported via `docker-compose.yml`.
- When adding a protected page:
  1) Create the page under `src/app/...` (client if it uses hooks).
  2) Add its path prefix to `PROTECTED_PATHS`.
  3) Use `useAuthEnhanced` or HOCs for permissioned UI inside the page.
- When adding an API feature:
  1) Implement a typed method under `src/service/XYZ.service.ts` using the shared `api` instance.
  2) Wrap handling with `handleApiResponse/handleApiError` and surface only domain data upward.
  3) Add i18n error keys if you map new status codes.

## Concrete examples
- Service call pattern: `const { data } = await api.get<ApiResponse<MyType>>("/my/resource"); return handleApiResponse(data);`
- Guarded navigation: `const { navigateWithAuth } = useAuthEnhanced(); navigateWithAuth("/learn/home", "/");`
- Marking a page as client and using providers: prepend `"use client"` and rely on layout providers—do not re‑instantiate i18n/auth per page.

If any of the above is unclear (e.g., permission matrix in `auth-utils-enhanced` or Mock API pathing), ask to clarify and we’ll extend this guide.
