/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-01 14:02:14
 */
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  output: "standalone",
  experimental: {},
  // 优化流式响应
  experimental: {
    // 启用更好的流式渲染
    serverComponentsExternalPackages: [],
  },
  // 禁用压缩以改善流式体验
  compress: false,

  // 优化服务器配置
  serverRuntimeConfig: {
    // 禁用缓冲
    bodyParser: {
      sizeLimit: "10mb",
    },
  },
  images: {
    domains: ["placehold.co", "www.educative.io"],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // 禁用图片优化以解决生产环境 SVG 加载问题
    unoptimized: true,
  },
  webpack: (config) => {
    // 解决 mermaid 对 cytoscape 默认导入的问题：指向一个带 default 的本地 shim
    const path = require("path");
    config.resolve = config.resolve || {};
    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      cytoscape: path.resolve(__dirname, "src/shims/cytoscape-default.js"),
    };

    // 配置 SVG 处理
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: "@svgr/webpack",
          options: {
            svgoConfig: {
              plugins: [
                {
                  name: "preset-default",
                  params: {
                    overrides: {
                      // 保留重要属性
                      removeViewBox: false,
                      removeUselessStrokeAndFill: false,
                      removeUnknownsAndDefaults: false,
                      removeEmptyAttrs: false,
                      cleanupNumericValues: false,
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    });

    return config;
  },
  async rewrites() {
    return [
      {
        source: "/api/v1/:path*", // 代理所有 /v1/ 开头的请求
        destination: "http://localhost:8012/api/v1/:path*", // 代理到 8081
        // 端口
        // destination:
        //     'http://************:8012/api/v1/:path*',  // 代理到 8082 端口
      },
    ];
  },
};

module.exports = nextConfig;
